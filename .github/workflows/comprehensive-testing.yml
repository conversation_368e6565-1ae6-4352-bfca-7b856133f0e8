name: Comprehensive Testing Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run nightly performance tests
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  backend-unit-tests:
    name: Backend Unit Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_fantasy_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-cov pytest-xdist pytest-benchmark
    
    - name: Set up test environment
      working-directory: ./backend
      run: |
        cp .env.example .env.test
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_fantasy_db" >> .env.test
        echo "REDIS_URL=redis://localhost:6379/0" >> .env.test
        echo "TESTING=true" >> .env.test
    
    - name: Run unit tests with coverage
      working-directory: ./backend
      run: |
        pytest tests/ \
          --cov=app \
          --cov-report=xml \
          --cov-report=html \
          --cov-report=term-missing \
          --cov-fail-under=80 \
          --junit-xml=test-results.xml \
          -v \
          --tb=short \
          --maxfail=10 \
          -x tests/test_*.py
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: backend-test-results
        path: |
          backend/test-results.xml
          backend/htmlcov/

  backend-integration-tests:
    name: Backend Integration Tests
    runs-on: ubuntu-latest
    needs: backend-unit-tests
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_fantasy_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-asyncio httpx
    
    - name: Set up test environment
      working-directory: ./backend
      run: |
        cp .env.example .env.test
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_fantasy_db" >> .env.test
        echo "REDIS_URL=redis://localhost:6379/0" >> .env.test
        echo "TESTING=true" >> .env.test
    
    - name: Run integration tests
      working-directory: ./backend
      run: |
        pytest tests/test_*_integration.py \
          -v \
          --tb=short \
          --maxfail=5 \
          --junit-xml=integration-test-results.xml
    
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: backend-integration-test-results
        path: backend/integration-test-results.xml

  backend-e2e-tests:
    name: Backend E2E Tests
    runs-on: ubuntu-latest
    needs: backend-integration-tests
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_fantasy_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-asyncio httpx
    
    - name: Set up test environment
      working-directory: ./backend
      run: |
        cp .env.example .env.test
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_fantasy_db" >> .env.test
        echo "REDIS_URL=redis://localhost:6379/0" >> .env.test
        echo "TESTING=true" >> .env.test
    
    - name: Run E2E tests
      working-directory: ./backend
      run: |
        pytest tests/e2e/ \
          -v \
          --tb=short \
          --maxfail=3 \
          --junit-xml=e2e-test-results.xml \
          --timeout=300
    
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: backend-e2e-test-results
        path: backend/e2e-test-results.xml

  backend-performance-tests:
    name: Backend Performance Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || contains(github.event.head_commit.message, '[perf-test]')
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_fantasy_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark psutil memory-profiler
    
    - name: Set up test environment
      working-directory: ./backend
      run: |
        cp .env.example .env.test
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_fantasy_db" >> .env.test
        echo "REDIS_URL=redis://localhost:6379/0" >> .env.test
        echo "TESTING=true" >> .env.test
    
    - name: Run performance tests
      working-directory: ./backend
      run: |
        pytest tests/performance/ \
          -v \
          --tb=short \
          --benchmark-json=benchmark-results.json \
          --benchmark-histogram=benchmark-histogram \
          --junit-xml=performance-test-results.xml
    
    - name: Upload performance test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: backend-performance-test-results
        path: |
          backend/performance-test-results.xml
          backend/benchmark-results.json
          backend/benchmark-histogram.svg

  backend-data-validation-tests:
    name: Backend Data Validation Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_fantasy_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Set up test environment
      working-directory: ./backend
      run: |
        cp .env.example .env.test
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_fantasy_db" >> .env.test
        echo "TESTING=true" >> .env.test
    
    - name: Run data validation tests
      working-directory: ./backend
      run: |
        pytest tests/data_validation/ \
          -v \
          --tb=short \
          --junit-xml=data-validation-test-results.xml
    
    - name: Upload data validation test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: backend-data-validation-test-results
        path: backend/data-validation-test-results.xml

  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Run type checking
      working-directory: ./frontend
      run: npm run type-check
    
    - name: Run linting
      working-directory: ./frontend
      run: npm run lint
    
    - name: Run unit tests
      working-directory: ./frontend
      run: |
        npm test -- --coverage --watchAll=false --testResultsProcessor=jest-junit
      env:
        JEST_JUNIT_OUTPUT_DIR: ./test-results
        JEST_JUNIT_OUTPUT_NAME: frontend-test-results.xml
    
    - name: Upload frontend test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: frontend-test-results
        path: |
          frontend/test-results/
          frontend/coverage/

  frontend-e2e-tests:
    name: Frontend E2E Tests
    runs-on: ubuntu-latest
    needs: [backend-unit-tests, frontend-tests]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_fantasy_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install backend dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Install Playwright
      working-directory: ./frontend
      run: npx playwright install --with-deps
    
    - name: Set up test environment
      working-directory: ./backend
      run: |
        cp .env.example .env.test
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_fantasy_db" >> .env.test
        echo "REDIS_URL=redis://localhost:6379/0" >> .env.test
        echo "TESTING=true" >> .env.test
    
    - name: Start backend server
      working-directory: ./backend
      run: |
        python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 &
        sleep 10
      env:
        ENV_FILE: .env.test
    
    - name: Build frontend
      working-directory: ./frontend
      run: npm run build
    
    - name: Start frontend server
      working-directory: ./frontend
      run: |
        npm start &
        sleep 10
      env:
        NEXT_PUBLIC_API_URL: http://localhost:8000
    
    - name: Run E2E tests
      working-directory: ./frontend
      run: npx playwright test
      env:
        PLAYWRIGHT_BASE_URL: http://localhost:3000
    
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: frontend-e2e-test-results
        path: |
          frontend/playwright-report/
          frontend/test-results/

  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Set up Python for security scanning
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
    
    - name: Run Bandit security linter
      working-directory: ./backend
      run: |
        bandit -r app/ -f json -o bandit-results.json || true
    
    - name: Run Safety check
      working-directory: ./backend
      run: |
        safety check --json --output safety-results.json || true
    
    - name: Upload security test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-test-results
        path: |
          trivy-results.sarif
          backend/bandit-results.json
          backend/safety-results.json

  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [
      backend-unit-tests,
      backend-integration-tests,
      backend-e2e-tests,
      backend-data-validation-tests,
      frontend-tests,
      frontend-e2e-tests,
      security-tests
    ]
    if: always()
    
    steps:
    - name: Download all test artifacts
      uses: actions/download-artifact@v3
    
    - name: Generate test summary
      run: |
        echo "# Test Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "## Test Results" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Check if test result files exist and summarize
        if [ -f "backend-test-results/test-results.xml" ]; then
          echo "✅ Backend Unit Tests: Completed" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Backend Unit Tests: Failed or Missing" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ -f "backend-integration-test-results/integration-test-results.xml" ]; then
          echo "✅ Backend Integration Tests: Completed" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Backend Integration Tests: Failed or Missing" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ -f "backend-e2e-test-results/e2e-test-results.xml" ]; then
          echo "✅ Backend E2E Tests: Completed" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Backend E2E Tests: Failed or Missing" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ -f "backend-data-validation-test-results/data-validation-test-results.xml" ]; then
          echo "✅ Backend Data Validation Tests: Completed" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Backend Data Validation Tests: Failed or Missing" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ -f "frontend-test-results/frontend-test-results.xml" ]; then
          echo "✅ Frontend Tests: Completed" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Frontend Tests: Failed or Missing" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ -d "frontend-e2e-test-results" ]; then
          echo "✅ Frontend E2E Tests: Completed" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Frontend E2E Tests: Failed or Missing" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ -f "security-test-results/trivy-results.sarif" ]; then
          echo "✅ Security Tests: Completed" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Security Tests: Failed or Missing" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "## Performance Tests" >> $GITHUB_STEP_SUMMARY
        
        if [ -f "backend-performance-test-results/benchmark-results.json" ]; then
          echo "✅ Performance Tests: Completed (see artifacts for detailed results)" >> $GITHUB_STEP_SUMMARY
        else
          echo "ℹ️ Performance Tests: Skipped (run on schedule or with [perf-test] in commit message)" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "## Artifacts" >> $GITHUB_STEP_SUMMARY
        echo "- Test results and coverage reports are available in the workflow artifacts" >> $GITHUB_STEP_SUMMARY
        echo "- Performance benchmarks and security scan results are included when available" >> $GITHUB_STEP_SUMMARY