.PHONY: help setup dev-setup build up down logs clean test

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

setup: ## Initial project setup
	@echo "Setting up AI Fantasy Assistant..."
	@cp .env.example .env
	@chmod +x scripts/setup-dev.sh
	@./scripts/setup-dev.sh

dev-setup: ## Setup development environment without Docker
	@echo "Setting up local development environment..."
	@./scripts/setup-dev.sh

build: ## Build Docker images
	docker-compose build

up: ## Start all services
	docker-compose up -d

down: ## Stop all services
	docker-compose down

logs: ## View logs from all services
	docker-compose logs -f

clean: ## Clean up Docker resources
	docker-compose down -v
	docker system prune -f

test-backend: ## Run backend tests
	cd backend && python -m pytest

test-frontend: ## Run frontend tests
	cd frontend && npm test

test: test-backend test-frontend ## Run all tests

dev-backend: ## Start backend in development mode
	cd backend && source venv/bin/activate && uvicorn app.main:app --reload

dev-frontend: ## Start frontend in development mode
	cd frontend && npm run dev

dev-services: ## Start only database and Redis services
	docker-compose up -d postgres redis