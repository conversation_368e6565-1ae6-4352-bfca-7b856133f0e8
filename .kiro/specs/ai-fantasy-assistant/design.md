# Design Document

## Overview

The AI Fantasy Assistant is a locally-deployable application that provides intelligent fantasy football recommendations by integrating MFL data through community wrappers, applying configurable league rules, and leveraging optimization algorithms. The system follows a layered architecture with clear separation between data ingestion, business logic, and presentation layers.

The application will be built using a Python-first approach with FastAPI for the backend, Next.js for the frontend, PostgreSQL (or SQLite for development) for data persistence, and Redis for caching. The system emphasizes explainable AI, real-time updates, and can be run entirely on a local machine using Docker Compose for easy setup and deployment.

## Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[Next.js React App]
        PWA[Progressive Web App]
    end
    
    subgraph "API Gateway"
        API[FastAPI Backend]
    end
    
    subgraph "Core Services"
        Ingest[Data Ingestion Service]
        Rules[Rules Engine]
        Decision[Decision Engines]
        Alerts[Alert Service]
    end
    
    subgraph "Data Layer"
        DB[(PostgreSQL)]
        Cache[(Redis)]
        Files[File Storage S3]
    end
    
    subgraph "External Systems"
        MFL[MFL API]
        Wrappers[Python/R Wrappers]
        CSV[CSV/Excel Uploads]
    end
    
    subgraph "Background Jobs"
        ETL[ETL Jobs]
        Scheduler[Job Scheduler]
        Monitor[Monitoring]
    end
    
    UI --> API
    API --> Decision
    API --> Rules
    API --> Alerts
    
    Ingest --> MFL
    Ingest --> Wrappers
    Ingest --> CSV
    Ingest --> DB
    
    Decision --> DB
    Decision --> Cache
    Rules --> DB
    
    ETL --> Ingest
    Scheduler --> ETL
    Monitor --> API
    
    API --> Files
```

## Components and Interfaces

### 1. Data Ingestion Service

**Purpose:** Handles all external data sources and normalizes them into the canonical schema.

**Key Components:**
- **MFL Adapter:** Uses pymfl wrapper to fetch league data, rosters, scoring, transactions
- **File Upload Handler:** Processes CSV/Excel files for projections, rankings, ADP
- **Data Normalizer:** Converts various formats into canonical schema
- **Provenance Tracker:** Maintains data lineage and source attribution

**Interfaces:**
```python
class DataIngestionService:
    def ingest_mfl_data(self, league_id: str, api_key: str) -> IngestionResult
    def process_file_upload(self, file: UploadFile, data_type: str) -> IngestionResult
    def normalize_data(self, raw_data: Dict, source: str) -> NormalizedData
    def track_provenance(self, data: Any, source: str, timestamp: datetime) -> None
```

### 2. Rules Engine

**Purpose:** Manages league-specific configurations and applies them across decision engines.

**Key Components:**
- **Rule Parser:** Converts JSON configurations to executable constraints
- **Validation Engine:** Ensures rule consistency and completeness
- **Configuration Manager:** Handles rule updates and versioning

**Interfaces:**
```python
class RulesEngine:
    def load_league_rules(self, league_id: str) -> LeagueRules
    def validate_rules(self, rules: Dict) -> ValidationResult
    def apply_scoring_rules(self, player_stats: Dict, rules: ScoringRules) -> float
    def get_roster_constraints(self, rules: LeagueRules) -> RosterConstraints
```

### 3. Decision Engines

**Purpose:** Core AI/ML components that generate recommendations for different fantasy scenarios.

#### Projections Aggregator
- Combines multiple projection sources using weighted ensembles
- Implements backtesting to optimize source weights
- Provides uncertainty quantification

#### Keeper Optimizer
- Uses Integer Linear Programming (ILP) to maximize keeper value
- Considers opportunity cost and replacement level calculations
- Handles complex keeper rules (escalation, franchise tags, round restrictions)

#### Draft Assistant
- Monte Carlo simulations for draft scenario modeling
- Tiered player boards based on value over replacement
- Real-time pick recommendations with contingency planning

#### Trade Engine
- Identifies team needs and surpluses using roster analysis
- Generates Pareto-optimal trade suggestions
- Calculates trade fairness and acceptance probability

#### Lineup Optimizer
- Maximizes win probability using variance-adjusted projections
- Considers matchup context, weather, and injury risk
- Handles late-breaking news and lineup lock times

#### Waiver/FAAB Engine
- Evaluates free agent value using points over replacement
- Optimizes FAAB bid amounts considering budget constraints
- Identifies streaming opportunities and handcuff targets

**Interfaces:**
```python
class DecisionEngines:
    def get_keeper_recommendations(self, league_id: str) -> List[KeeperRecommendation]
    def get_draft_recommendations(self, league_id: str, available_players: List[str]) -> DraftBoard
    def suggest_trades(self, league_id: str, franchise_id: str) -> List[TradeProposal]
    def optimize_lineup(self, franchise_id: str, week: int) -> LineupRecommendation
    def get_waiver_targets(self, league_id: str, franchise_id: str) -> List[WaiverTarget]
```

### 4. Alert Service

**Purpose:** Manages notifications and real-time updates for users.

**Key Components:**
- **Deadline Monitor:** Tracks keeper, waiver, and lineup deadlines
- **News Processor:** Monitors player news and injury reports
- **Notification Engine:** Sends alerts via email, push notifications, or in-app

**Interfaces:**
```python
class AlertService:
    def schedule_deadline_alerts(self, league_id: str, deadlines: List[Deadline]) -> None
    def process_player_news(self, news: PlayerNews) -> List[Alert]
    def send_notification(self, user_id: str, alert: Alert) -> None
```

## Data Models

### Core Entities

```python
# League Configuration
class League(BaseModel):
    id: str
    name: str
    season: int
    scoring_rules: Dict[str, Any]
    roster_slots: List[RosterSlot]
    keeper_rules: Optional[KeeperRules]
    created_at: datetime
    updated_at: datetime

# Player Information
class Player(BaseModel):
    id: str
    name: str
    position: str
    team: str
    bye_week: Optional[int]
    injury_status: Optional[str]
    metadata: Dict[str, Any]

# Projections and Rankings
class Projection(BaseModel):
    player_id: str
    week: Optional[int]  # None for season-long
    source: str
    projected_points: float
    confidence_interval: Tuple[float, float]
    created_at: datetime

# Roster Management
class Roster(BaseModel):
    franchise_id: str
    league_id: str
    players: List[RosterPlayer]
    salary_cap: Optional[float]
    faab_budget: Optional[float]
    updated_at: datetime

# Recommendations
class Recommendation(BaseModel):
    id: str
    type: RecommendationType
    franchise_id: str
    title: str
    description: str
    rationale: str
    confidence: float
    alternatives: List[Alternative]
    expires_at: Optional[datetime]
    created_at: datetime
```

### Database Schema

The PostgreSQL schema includes tables for:
- **leagues:** League configuration and rules
- **franchises:** Team information and ownership
- **players:** Player master data with positions and teams
- **rosters:** Current roster compositions
- **projections:** Player projections from various sources
- **rankings:** Expert rankings and ADP data
- **transactions:** Trade and waiver history
- **recommendations:** Generated AI recommendations
- **alerts:** Scheduled and triggered notifications
- **audit_log:** Data provenance and change tracking

## Error Handling

### API Error Responses
- Standardized error format with error codes and messages
- Rate limiting with appropriate HTTP status codes
- Validation errors with field-specific details
- Authentication/authorization error handling

### Data Processing Errors
- Graceful handling of MFL API failures with retry logic
- File upload validation with detailed error messages
- Data inconsistency detection and resolution
- Fallback mechanisms for missing data

### Background Job Failures
- Dead letter queues for failed job processing
- Exponential backoff for transient failures
- Alert mechanisms for critical job failures
- Manual retry capabilities for operators

## Testing Strategy

### Unit Testing
- Comprehensive test coverage for decision engines
- Mock external API dependencies (MFL, file uploads)
- Test rule engine with various league configurations
- Validate data normalization and transformation logic

### Integration Testing
- End-to-end API testing with test databases
- MFL wrapper integration testing with sandbox data
- File upload processing with sample datasets
- Background job execution testing

### Performance Testing
- Load testing for concurrent user scenarios
- Database query optimization validation
- Cache effectiveness measurement
- API response time benchmarking

### User Acceptance Testing
- Recommendation accuracy validation against historical data
- User interface usability testing
- Mobile responsiveness testing
- Cross-browser compatibility verification

## Security Considerations

### Authentication & Authorization
- Single-user application with no password protection required
- Simple API key management for MFL integration stored in environment variables
- Basic credential storage for external service integration

### Data Protection
- Basic input validation and sanitization
- Secure file upload handling for CSV/Excel files
- Local data storage with standard file permissions

### Infrastructure Security
- Optional HTTPS for local deployment (self-signed certificates)
- Standard database connection security
- Regular dependency updates for security patches

## Performance & Scalability

### Local Caching Strategy
- Redis container for session and computation caching
- In-memory caching for frequently accessed league data
- File-based caching for external API responses
- Simple cache invalidation for real-time updates

### Database Optimization
- PostgreSQL with proper indexing for query performance
- Connection pooling for efficient database access
- SQLite option for single-user development
- Database migrations for schema management

### Background Processing
- Simple background job processing with Python threading
- Celery with Redis broker for more complex scenarios
- APScheduler for periodic tasks (data refresh, alerts)
- Queue management for time-sensitive operations

## Local Development & Deployment

### Local Setup
- Docker Compose for orchestrating all services locally
- PostgreSQL and Redis containers for data layer
- FastAPI development server with hot reload
- Next.js development server with hot module replacement
- Volume mounts for persistent data and code changes

### Development Environment
- Python virtual environment with pip/poetry for dependency management
- Node.js environment for frontend development
- Environment variables for configuration (`.env` files)
- Local file storage instead of S3 for development
- SQLite option for lightweight development database

### Production-Ready Local Deployment
- Docker Compose production configuration
- Nginx reverse proxy for serving static files
- SSL certificates for HTTPS (self-signed for local)
- Backup scripts for local database
- Log aggregation to local files

### Monitoring & Observability
- Simple logging to console and files
- Health check endpoints for all services
- Basic metrics collection with Prometheus (optional)
- Local dashboard for monitoring service status