#!/usr/bin/env bash
set -euo pipefail

# Stop the locally started backend and frontend
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

PID_DIR="${PID_DIR:-.pids}"
LOG_DIR="${LOG_DIR:-.logs}"

stop_proc() {
  local name=$1 pid_file=$2
  if [ -f "$pid_file" ]; then
    local pid
    pid=$(cat "$pid_file" || true)
    if [ -n "$pid" ] && ps -p "$pid" >/dev/null 2>&1; then
      echo "Stopping $name (pid=$pid)" | tee -a "$LOG_DIR/stop-local.log"
      kill "$pid" || true
      # give it a moment, then force if needed
      sleep 1
      if ps -p "$pid" >/dev/null 2>&1; then
        kill -9 "$pid" || true
      fi
    fi
    rm -f "$pid_file"
  else
    echo "$name is not running (no pid file)" | tee -a "$LOG_DIR/stop-local.log"
  fi
}

stop_proc "backend" "$PID_DIR/backend.pid"
stop_proc "frontend" "$PID_DIR/frontend.pid"

osascript -e 'display notification "Local app stopped." with title "MFL-ai"' || true
echo "Stopped. Logs are in $LOG_DIR"

