"""
Shared type definitions between backend and frontend
"""
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from pydantic import BaseModel
from enum import Enum

class RecommendationType(str, Enum):
    KEEPER = "keeper"
    DRAFT = "draft"
    TRADE = "trade"
    LINEUP = "lineup"
    WAIVER = "waiver"

class Position(str, Enum):
    QB = "QB"
    RB = "RB"
    WR = "WR"
    TE = "TE"
    K = "K"
    DST = "DST"
    FLEX = "FLEX"
    BENCH = "BENCH"

class BaseEntity(BaseModel):
    id: str
    created_at: datetime
    updated_at: datetime

class League(BaseEntity):
    name: str
    season: int
    scoring_rules: Dict[str, Any]
    roster_slots: List[Dict[str, Any]]
    keeper_rules: Optional[Dict[str, Any]] = None

class Player(BaseEntity):
    name: str
    position: Position
    team: str
    bye_week: Optional[int] = None
    injury_status: Optional[str] = None
    metadata: Dict[str, Any] = {}

class Projection(BaseModel):
    player_id: str
    week: Optional[int] = None  # None for season-long
    source: str
    projected_points: float
    confidence_interval: Tuple[float, float]
    created_at: datetime

class Recommendation(BaseEntity):
    type: RecommendationType
    franchise_id: str
    title: str
    description: str
    rationale: str
    confidence: float
    alternatives: List[Dict[str, Any]] = []
    expires_at: Optional[datetime] = None