
services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: fantasy_postgres_dev
    environment:
      POSTGRES_DB: fantasy_db
      POSTGRES_USER: fantasy
      POSTGRES_PASSWORD: fantasy
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      # init scripts disabled for quick start on macOS
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U fantasy -d fantasy_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - fantasy_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: fantasy_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - fantasy_network

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fantasy_backend_dev
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************************/fantasy_db
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
      - DEBUG=true
      - DISABLE_CUSTOM_MIDDLEWARE=1
      - MFL_API_KEY=${MFL_API_KEY}
      - MFL_LEAGUE_ID=${MFL_LEAGUE_ID}
      - MFL_USERNAME=${MFL_USERNAME}
      - MFL_PASSWORD=${MFL_PASSWORD}
      - MFL_USER_AGENT=${MFL_USER_AGENT:-ai-fantasy-assistant/1.0}
      - INTERNAL_API_BASE=http://host.docker.internal:8000
    # For a quick, stable start on macOS, run from the built image without bind mounts.
    # volumes:
    #   - ./backend:/app
    #   - ./shared:/app/shared
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000
    networks:
      - fantasy_network

  # Next.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: fantasy_frontend_dev
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    volumes:
      - ./frontend:/app
    depends_on:
      - backend
    command: sh -c "npm ci || npm install && npm run dev -- -H 0.0.0.0"
    networks:
      - fantasy_network

  # Celery Worker (for background jobs)
  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fantasy_worker_dev
    environment:
      - DATABASE_URL=******************************************/fantasy_db
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
      - DEBUG=true
      - MFL_API_KEY=${MFL_API_KEY}
      - MFL_LEAGUE_ID=${MFL_LEAGUE_ID}
    # For a quick, stable start on macOS, run from the built image without bind mounts.
    # volumes:
    #   - ./backend:/app
    #   - ./shared:/app/shared
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: celery -A app.core.celery:celery_app worker --loglevel=info --concurrency=4 --queues=default,data_refresh,alerts,file_processing,failed --hostname=worker@%h
    healthcheck:
      test: ["CMD", "celery", "-A", "app.core.celery:celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - fantasy_network

  # Celery Beat Scheduler (for periodic tasks)
  beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fantasy_beat_dev
    environment:
      - DATABASE_URL=******************************************/fantasy_db
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
      - DEBUG=true
      - MFL_API_KEY=${MFL_API_KEY}
      - MFL_LEAGUE_ID=${MFL_LEAGUE_ID}
    volumes:
      - beat_data:/tmp
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: celery -A app.core.celery:celery_app beat --loglevel=info --schedule=/tmp/celerybeat-schedule --pidfile=/tmp/celerybeat.pid
    healthcheck:
      test: ["CMD", "test", "-f", "/tmp/celerybeat.pid"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - fantasy_network

  # Celery Flower (optional monitoring UI)
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fantasy_flower_dev
    ports:
      - "5555:5555"
    environment:
      - DATABASE_URL=******************************************/fantasy_db
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
    depends_on:
      redis:
        condition: service_healthy
    command: celery -A app.core.celery:celery_app flower --port=5555
    profiles:
      - monitoring
    networks:
      - fantasy_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  beat_data:
    driver: local

networks:
  fantasy_network:
    driver: bridge