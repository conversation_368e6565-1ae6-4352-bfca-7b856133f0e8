# AI Fantasy Assistant

A locally-deployable fantasy football management application that provides intelligent recommendations using AI and optimization algorithms.

## Features

- MFL league data integration
- AI-powered keeper recommendations
- Draft assistance with real-time updates
- Trade analysis and suggestions
- Weekly lineup optimization
- Waiver wire and FAAB recommendations
- Real-time alerts and notifications

## Quick Start

### Prerequisites

- <PERSON><PERSON> and Docker Compose
- Python 3.11+ (for local development)
- Node.js 18+ (for local development)

### Development Setup

1. Clone the repository and navigate to the project directory

2. Copy environment configuration:
   ```bash
   cp .env.example .env
   ```

3. Start all services with Docker Compose:
   ```bash
   docker-compose up -d
   ```

4. The application will be available at:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Local Development (without Docker)

#### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Start the FastAPI server:
   ```bash
   uvicorn app.main:app --reload
   ```

#### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

## Project Structure

```
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Configuration and utilities
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── tasks/          # Background tasks
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/               # Next.js frontend
│   ├── src/
│   │   ├── app/           # Next.js app directory
│   │   ├── components/    # React components
│   │   ├── lib/           # Utilities and API client
│   │   └── types/         # TypeScript type definitions
│   ├── package.json
│   └── Dockerfile
├── shared/                # Shared types and utilities
├── docker-compose.yml     # Docker services configuration
└── README.md
```

## Configuration

Edit the `.env` file to configure:

- Database connection settings
- Redis connection settings
- MFL API credentials
- Application environment settings

## Next Steps

1. Configure your MFL API credentials in the `.env` file
2. Run database migrations (will be implemented in task 2)
3. Upload your league data and projections
4. Start receiving AI recommendations!

## Development

- Backend API documentation: http://localhost:8000/docs
- Frontend development server includes hot reload
- Backend development server includes auto-reload on code changes
- Database and Redis data persist in Docker volumes

## Support

This is a self-hosted application designed for personal use. Refer to the documentation and code comments for implementation details.