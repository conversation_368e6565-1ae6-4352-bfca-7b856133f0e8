# AI Fantasy Assistant

A locally-deployable fantasy football management application that provides intelligent recommendations using AI and optimization algorithms. The application integrates with MyFantasyLeague (MFL) APIs and Fantasy Football Calculator (FFC) to provide comprehensive ADP data, player rankings, and automated rookie detection.

## Features

### Core Features
- **MFL League Integration**: Full integration with MyFantasyLeague APIs for league data, rosters, and player information
- **Multi-Source ADP Data**: Combines ADP data from MFL and Fantasy Football Calculator with intelligent indexing
- **Automated Rookie Detection**: Automatically identifies rookies using MFL player status flags
- **Real-time Data Refresh**: Manual and automated refresh of master player lists with staleness indicators
- **AI-Powered Recommendations**: Keeper recommendations, trade analysis, and lineup optimization
- **Draft Assistance**: Real-time draft updates and player availability tracking
- **Waiver Wire & FAAB**: Smart waiver wire recommendations and FAAB bidding strategies

### Data Pipeline Features
- **Master Player List**: Centralized player database with automatic updates from multiple sources
- **ADP Index Calculation**: Composite ADP rankings from MFL and FFC data
- **Rookie Metadata**: Automatic rookie flagging based on MFL "status":"R" field
- **Data Freshness Tracking**: Visual indicators for data age and automatic refresh scheduling
- **Background Processing**: Celery-based task queue for data refreshes and API integrations

## Quick Start

### Prerequisites

- **Docker and Docker Compose** (required)
- **MFL API Credentials** (league ID, username, password)
- Python 3.11+ (for local development only)
- Node.js 18+ (for local development only)

### Docker Setup (Recommended)

The application is designed to run entirely in Docker containers with a full microservices architecture:

1. **Clone and Configure**:
   ```bash
   git clone <repository>
   cd MFL-ai
   cp .env.example .env
   ```

2. **Configure MFL Credentials** in `.env`:
   ```bash
   MFL_API_KEY=your_api_key
   MFL_LEAGUE_ID=your_league_id
   MFL_USERNAME=your_username
   MFL_PASSWORD=your_password
   ```

3. **Start All Services**:
   ```bash
   docker-compose up -d
   ```

4. **Access the Application**:
   - **Frontend**: http://localhost:3000
   - **Backend API**: http://localhost:8000
   - **API Documentation**: http://localhost:8000/docs
   - **Task Monitoring**: http://localhost:5555 (Celery Flower)

### Initial Data Setup

After starting the containers:

1. **Trigger Master List Refresh**: Visit the "All Players" page and click "Refresh Master" to populate the database with MFL player data and FFC ADP data
2. **Verify Rookie Detection**: Check that current-year rookies show the "R" badge
3. **Monitor Data Freshness**: The UI shows data age and staleness indicators

### Docker Architecture

The application uses a multi-container Docker setup:

- **PostgreSQL**: Primary database for player data, rankings, and league information
- **Redis**: Caching layer and Celery message broker
- **Backend (FastAPI)**: Core API server handling MFL integration and business logic
- **Frontend (Next.js)**: React-based web interface with real-time updates
- **Celery Worker**: Background task processing for data refreshes and API calls
- **Celery Beat**: Scheduled task scheduler for periodic data updates
- **Celery Flower**: Task monitoring and management interface

### Production Deployment

For production deployment, use the production compose file:

```bash
docker-compose -f docker-compose.prod.yml up -d
```

Production includes additional features:
- Nginx reverse proxy (optional)
- Enhanced logging and monitoring
- Optimized container builds
- Security hardening

### Local Development (Alternative)

For development without Docker, you'll need to manually run each service. See the original development setup in the commit history if needed, but Docker is the recommended approach.

## Project Structure

```
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes (/rankings, /players, /trades)
│   │   ├── core/           # Configuration, database, Celery setup
│   │   ├── models/         # SQLAlchemy database models
│   │   ├── services/       # Business logic and external API integration
│   │   ├── tasks/          # Celery background tasks
│   │   └── utils/          # Helper utilities
│   ├── requirements.txt    # Python dependencies
│   └── Dockerfile         # Backend container definition
├── frontend/               # Next.js frontend
│   ├── src/
│   │   ├── app/           # Next.js 13+ app directory routing
│   │   ├── components/    # React components and UI elements
│   │   ├── context/       # React context providers (MasterPlayersProvider)
│   │   ├── lib/           # API client, utilities, and constants
│   │   └── types/         # TypeScript type definitions
│   ├── package.json       # Node.js dependencies
│   └── Dockerfile        # Frontend container definition
├── shared/                # Shared types and utilities between services
├── docker-compose.yml     # Development environment
├── docker-compose.prod.yml # Production environment
├── .env.example          # Environment variable template
└── README.md
```

## Data Flow & Integration

### Master Player List Pipeline

1. **MFL Player Data**: Fetched from MFL API with complete player roster
2. **FFC ADP Data**: Retrieved from Fantasy Football Calculator rankings
3. **ADP Index Calculation**: Composite ranking combining MFL and FFC data
4. **Rookie Detection**: Automatic flagging based on MFL "status":"R" field
5. **Database Storage**: Normalized storage with metadata tracking
6. **Frontend Display**: Real-time updates with staleness indicators

### Background Processing

- **Celery Workers**: Handle API calls, data processing, and database updates
- **Scheduled Tasks**: Periodic refresh of player data and rankings
- **Queue Management**: Separate queues for different task types (data_refresh, alerts, file_processing)
- **Error Handling**: Automatic retry logic and failure queues

## Configuration

### Environment Variables

Configure the application using the `.env` file:

#### Required MFL Credentials
```bash
MFL_API_KEY=your_api_key_here          # MFL API key
MFL_LEAGUE_ID=your_league_id_here      # Your specific league ID
MFL_USERNAME=your_username             # MFL account username
MFL_PASSWORD=your_password             # MFL account password
MFL_USER_AGENT=ai-fantasy-assistant/1.0 # User agent for API calls
```

#### Database & Cache
```bash
DATABASE_URL=******************************************/fantasy_db
REDIS_URL=redis://redis:6379
```

#### Application Settings
```bash
ENVIRONMENT=development                # development/production
DEBUG=true                            # Enable debug mode
NEXT_PUBLIC_API_URL=http://localhost:8000  # Frontend API URL
```

#### Production Settings (docker-compose.prod.yml)
```bash
POSTGRES_PASSWORD=secure_password      # Secure database password
SECRET_KEY=your_secret_key            # Application secret key
CORS_ORIGINS=http://your-domain.com    # Allowed CORS origins
FLOWER_USER=admin                     # Flower monitoring username
FLOWER_PASSWORD=secure_password        # Flower monitoring password
```

## Key Features Explained

### Master Player List & ADP Integration

- **Multi-Source Data**: Combines MFL native ADP with Fantasy Football Calculator rankings
- **ADP Index**: Proprietary composite ranking that weights multiple sources
- **Real-Time Refresh**: Manual refresh button with progress indicators
- **Staleness Detection**: Visual indicators showing data age (fresh vs stale)
- **Rookie Auto-Detection**: Uses MFL "status":"R" field to automatically flag rookies

### Frontend Features

- **All Players Page**: Comprehensive player list with filtering, sorting, and search
- **Rookie Badges**: Visual "R" indicators for current-year rookies
- **Data Freshness**: Live countdown showing time since last update
- **Manual Refresh**: "Refresh Master" button triggers full data pipeline
- **Responsive Design**: Mobile-friendly interface with proper loading states

### Backend Architecture

- **FastAPI**: Modern async Python framework with automatic API documentation
- **SQLAlchemy**: ORM for database operations with proper relationship mapping
- **Celery**: Distributed task queue for background processing
- **Redis**: Fast caching and message broker
- **PostgreSQL**: Robust relational database for complex queries

## Usage

### First-Time Setup

1. **Configure MFL Credentials**: Add your league details to `.env`
2. **Start Services**: `docker-compose up -d`
3. **Initial Data Load**: Visit "All Players" → "Refresh Master"
4. **Verify Data**: Check that players show correct positions, teams, and rookie flags

### Daily Operations

- **Monitor Data Freshness**: Green = fresh (< 24hrs), Orange = stale
- **Manual Refresh**: Click "Refresh Master" to update ADP data
- **Background Updates**: Celery handles scheduled refreshes automatically
- **Error Monitoring**: Check Flower UI at http://localhost:5555

## Development & Monitoring

### Development URLs
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs (Swagger UI)
- **Task Monitoring**: http://localhost:5555 (Celery Flower)
- **Database**: localhost:5432 (PostgreSQL)
- **Cache**: localhost:6379 (Redis)

### Development Features
- **Hot Reload**: Frontend development server with instant updates
- **API Auto-reload**: Backend restarts automatically on code changes
- **Volume Mounting**: Code changes reflected immediately in containers
- **Debug Mode**: Detailed error messages and logging
- **Data Persistence**: Database and cache data persist between restarts

### Monitoring & Debugging
- **Celery Flower**: Real-time task monitoring, worker status, and queue management
- **API Documentation**: Interactive Swagger UI for testing endpoints
- **Container Logs**: `docker-compose logs [service]` for debugging
- **Database Access**: Direct PostgreSQL connection for data inspection

### Future Roadmap

#### Planned Features
- **Additional ADP Sources**: Support for up to 2 additional ranking services beyond MFL/FFC
- **Custom Rankings Upload**: CSV/Excel import for proprietary rankings
- **Advanced Analytics**: Historical ADP trends and volatility analysis
- **League-Specific Adjustments**: Custom scoring and roster configuration
- **Mobile App**: React Native companion app
- **AI Enhancement**: GPT integration for natural language queries

#### Technical Improvements
- **Real-Time Updates**: WebSocket integration for live data streams
- **Advanced Caching**: Multi-layer caching strategy for improved performance
- **Testing Suite**: Comprehensive unit and integration tests
- **CI/CD Pipeline**: Automated testing and deployment
- **Monitoring**: Prometheus/Grafana observability stack

## Troubleshooting

### Common Issues

1. **MFL API Errors**: Verify credentials and league ID in `.env`
2. **Database Connection**: Ensure PostgreSQL container is healthy
3. **Missing Rookie Flags**: Run "Refresh Master" to update player status
4. **Stale Data**: Check Celery worker logs for background task failures

### Support

This is a self-hosted application for personal use. Check:
- Container logs: `docker-compose logs [service]`
- API documentation: http://localhost:8000/docs
- Task monitoring: http://localhost:5555
- Database status: `docker-compose ps`

## Contributing

Contributions welcome! The application is built with modern development practices:
- **Backend**: FastAPI, SQLAlchemy, Celery, Python 3.11+
- **Frontend**: Next.js 13+, TypeScript, Tailwind CSS
- **Infrastructure**: Docker, PostgreSQL, Redis
- **Code Quality**: Type hints, ESLint, Prettier
