#!/bin/bash

# Fantasy Football AI Assistant - Deployment Script
# This script helps deploy the application in production mode

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_prompt() {
    echo -e "${BLUE}[PROMPT]${NC} $1"
}

# Show usage
show_usage() {
    echo "Fantasy Football AI Assistant - Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     - Initial setup and configuration"
    echo "  start     - Start the application"
    echo "  stop      - Stop the application"
    echo "  restart   - Restart the application"
    echo "  status    - Show application status"
    echo "  logs      - Show application logs"
    echo "  update    - Update and restart the application"
    echo "  backup    - Create a backup"
    echo "  restore   - Restore from backup"
    echo "  clean     - Clean up Docker resources"
    echo ""
    echo "Examples:"
    echo "  $0 setup     # Initial setup"
    echo "  $0 start     # Start application"
    echo "  $0 logs      # View logs"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    log_info "Prerequisites check passed"
}

# Setup environment
setup_environment() {
    log_info "Setting up environment..."
    
    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        log_info "Creating .env file from template (.env.example)..."
        cp .env.example .env

        # Generate secrets
        POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
        SECRET_KEY=$(openssl rand -base64 48)
        FLOWER_PASSWORD=$(openssl rand -base64 16 | tr -d "=+/" | cut -c1-12)

        # Append required production variables if not already present
        add_or_set_env_var() {
            local key="$1"; local value="$2"
            if grep -q "^${key}=" .env; then
                # Replace existing value safely (macOS-compatible)
                sed -i.bak "s#^${key}=.*#${key}=${value}#" .env && rm .env.bak
            else
                echo "${key}=${value}" >> .env
            fi
        }

        add_or_set_env_var POSTGRES_PASSWORD "$POSTGRES_PASSWORD"
        add_or_set_env_var SECRET_KEY "$SECRET_KEY"
        add_or_set_env_var FLOWER_USER "admin"
        add_or_set_env_var FLOWER_PASSWORD "$FLOWER_PASSWORD"
        # Default CORS and API URL if not provided
        add_or_set_env_var CORS_ORIGINS "http://localhost:3000"
        [ -z "$(grep -E '^NEXT_PUBLIC_API_URL=' .env || true)" ] && echo "NEXT_PUBLIC_API_URL=http://localhost:8000" >> .env

        log_warn "Please review .env and set the following values as needed:"
        log_warn "  - MFL_API_KEY: Your MFL API key"
        log_warn "  - MFL_LEAGUE_ID: Your MFL league ID"
        log_warn "  - NEXT_PUBLIC_API_URL: Your domain (if not localhost)"
        log_warn "  - CORS_ORIGINS: Allowed origins for CORS (comma-separated)"
        
        log_prompt "Press Enter after reviewing .env file..."
        read
    else
        log_info ".env file already exists"
    fi
    
    # Create required directories
    log_info "Creating required directories..."
    mkdir -p logs uploads backups config/ssl
    
    # Set proper permissions
    chmod 755 logs uploads backups
    
    log_info "Environment setup completed"
}

# Generate SSL certificates
generate_ssl_certs() {
    log_info "Generating self-signed SSL certificates..."
    
    if [ ! -f "config/ssl/cert.pem" ] || [ ! -f "config/ssl/key.pem" ]; then
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout config/ssl/key.pem \
            -out config/ssl/cert.pem \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
        
        log_info "SSL certificates generated"
    else
        log_info "SSL certificates already exist"
    fi
}

# Start application
start_application() {
    log_info "Starting Fantasy Football AI Assistant..."
    
    # Pull latest images
    docker-compose -f docker-compose.prod.yml pull
    
    # Build and start services
    docker-compose -f docker-compose.prod.yml up -d --build
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_service_health
    
    log_info "Application started successfully!"
    log_info "Frontend: http://localhost:3000"
    log_info "Backend API: http://localhost:8000"
    log_info "Flower (monitoring): http://localhost:5555"
}

# Stop application
stop_application() {
    log_info "Stopping Fantasy Football AI Assistant..."
    docker-compose -f docker-compose.prod.yml down
    log_info "Application stopped"
}

# Restart application
restart_application() {
    log_info "Restarting Fantasy Football AI Assistant..."
    stop_application
    start_application
}

# Show application status
show_status() {
    log_info "Application Status:"
    docker-compose -f docker-compose.prod.yml ps
    
    echo ""
    log_info "Service Health:"
    
    # Check backend health
    if curl -f http://localhost:8000/health &>/dev/null; then
        echo -e "  Backend: ${GREEN}Healthy${NC}"
    else
        echo -e "  Backend: ${RED}Unhealthy${NC}"
    fi
    
    # Check frontend health
    if curl -f http://localhost:3000 &>/dev/null; then
        echo -e "  Frontend: ${GREEN}Healthy${NC}"
    else
        echo -e "  Frontend: ${RED}Unhealthy${NC}"
    fi
    
    # Check database
    if docker-compose -f docker-compose.prod.yml exec -T postgres pg_isready -U fantasy -d fantasy_db &>/dev/null; then
        echo -e "  Database: ${GREEN}Healthy${NC}"
    else
        echo -e "  Database: ${RED}Unhealthy${NC}"
    fi
    
    # Check Redis
    if docker-compose -f docker-compose.prod.yml exec -T redis redis-cli ping | grep -q PONG; then
        echo -e "  Redis: ${GREEN}Healthy${NC}"
    else
        echo -e "  Redis: ${RED}Unhealthy${NC}"
    fi
}

# Show logs
show_logs() {
    local service="$1"
    
    if [ -n "$service" ]; then
        log_info "Showing logs for $service..."
        docker-compose -f docker-compose.prod.yml logs -f "$service"
    else
        log_info "Showing logs for all services..."
        docker-compose -f docker-compose.prod.yml logs -f
    fi
}

# Update application
update_application() {
    log_info "Updating Fantasy Football AI Assistant..."
    
    # Pull latest code (if using git)
    if [ -d ".git" ]; then
        log_info "Pulling latest code..."
        git pull
    fi
    
    # Pull latest images
    docker-compose -f docker-compose.prod.yml pull
    
    # Restart with new images
    restart_application
    
    log_info "Application updated successfully!"
}

# Create backup
create_backup() {
    log_info "Creating backup..."
    ./scripts/backup/backup.sh
}

# Restore from backup
restore_backup() {
    log_info "Restoring from backup..."
    ./scripts/backup/restore.sh "$1"
}

# Clean up Docker resources
clean_docker() {
    log_info "Cleaning up Docker resources..."
    
    log_prompt "This will remove unused Docker images, containers, and networks. Continue? (y/N):"
    read -r confirmation
    
    if [ "$confirmation" = "y" ] || [ "$confirmation" = "Y" ]; then
        docker system prune -f
        docker volume prune -f
        log_info "Docker cleanup completed"
    else
        log_info "Docker cleanup cancelled"
    fi
}

# Check service health
check_service_health() {
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/health &>/dev/null; then
            log_info "Backend health check passed"
            return 0
        fi
        
        log_info "Waiting for backend to be ready... (attempt $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_warn "Backend health check failed, but application may still be starting"
    return 1
}

# Main execution
main() {
    local command="$1"
    
    case "$command" in
        "setup")
            check_prerequisites
            setup_environment
            generate_ssl_certs
            log_info "Setup completed! Run '$0 start' to start the application."
            ;;
        "start")
            check_prerequisites
            start_application
            ;;
        "stop")
            stop_application
            ;;
        "restart")
            check_prerequisites
            restart_application
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$2"
            ;;
        "update")
            check_prerequisites
            update_application
            ;;
        "backup")
            create_backup
            ;;
        "restore")
            restore_backup "$2"
            ;;
        "clean")
            clean_docker
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"