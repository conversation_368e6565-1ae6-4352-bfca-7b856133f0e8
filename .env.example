# Database Configuration
DATABASE_URL=postgresql://fantasy:fantasy@localhost:5432/fantasy_db
DATABASE_URL_DEV=sqlite:///./fantasy.db

# Redis Configuration
REDIS_URL=redis://localhost:6379

# MFL API Configuration
MFL_API_KEY=your_mfl_api_key_here
MFL_LEAGUE_ID=your_league_id_here

# Application Configuration
ENVIRONMENT=development
DEBUG=true

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000