// Trade-related types

export interface PositionNeed {
  position: string
  need_level: number
  current_strength: number
  replacement_level: number
  depth_score: number
  injury_risk: number
  bye_week_coverage: number
}

export interface PositionSurplus {
  position: string
  surplus_level: number
  tradeable_players: string[]
  surplus_value: number
  depth_quality: number
}

export interface TeamAnalysis {
  franchise_id: string
  franchise_name: string
  needs: Record<string, PositionNeed>
  surpluses: Record<string, PositionSurplus>
  overall_strength: number
  win_probability: number
  trade_urgency: number
  metadata: Record<string, any>
}

export interface TradeProposal {
  trade_id: string
  team_a_id: string
  team_b_id: string
  team_a_gives: string[]
  team_a_receives: string[]
  team_b_gives: string[]
  team_b_receives: string[]
  trade_type: string
  fairness: string
  fairness_score: number
  acceptance_probability: number
  win_probability_impact_a: number
  win_probability_impact_b: number
  rationale: string
  metadata: Record<string, any>
  recommendation_id?: string
}

export interface TradeImpact {
  franchise_id: string
  pre_trade_strength: Record<string, number>
  post_trade_strength: Record<string, number>
  position_changes: Record<string, number>
  overall_impact: number
  win_probability_change: number
  risk_change: number
  depth_impact: Record<string, number>
  bye_week_impact: Record<number, number>
}

export interface TradeEvaluationRequest {
  team_a_id: string
  team_b_id: string
  team_a_gives: string[]
  team_b_gives: string[]
  season?: number
}

export interface TradeHistoryItem {
  id: string
  date: string
  team_a: string
  team_b: string
  players_traded: {
    team_a_gave: string[]
    team_b_gave: string[]
  }
  fairness_score: number
  outcome: 'completed' | 'rejected' | 'expired'
  notes?: string
}

export type TradeFairness = 'very_fair' | 'fair' | 'slightly_unfair' | 'unfair' | 'very_unfair'
export type TradeType = 'need_based' | 'surplus_based' | 'value_arbitrage' | 'win_now' | 'future_focused'