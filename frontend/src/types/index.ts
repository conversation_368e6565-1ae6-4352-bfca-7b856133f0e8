// Common types for the application

export interface League {
  id: string
  name: string
  season: number
  description?: string
  scoring_rules: Record<string, any>
  roster_slots: RosterSlot[]
  keeper_rules?: KeeperRules
  created_at: string
  updated_at: string
}

export interface Player {
  id: string
  name: string
  position: string
  team: string
  bye_week?: number
  injury_status?: string
  metadata: Record<string, any>
}

export interface RosterSlot {
  position: string
  count: number
}

export interface KeeperRules {
  max_keepers: number
  round_escalation: boolean
  franchise_tags: number
}

export interface Projection {
  player_id: string
  week?: number
  source: string
  projected_points: number
  confidence_interval: [number, number]
  created_at: string
}

export interface Recommendation {
  id: string
  type: string
  franchise_id: string
  title: string
  description: string
  rationale: string
  confidence: number
  alternatives: Alternative[]
  expires_at?: string
  created_at: string
}

export interface Alternative {
  title: string
  description: string
  confidence: number
}

export interface Franchise {
  id: string
  name: string
  owner_name: string
  league_id: string
  league_name: string
  mfl_franchise_id?: string
  salary_cap?: number
  faab_budget?: number
  faab_spent: number
  remaining_faab: number
  franchise_metadata: Record<string, any>
  is_active: boolean
  roster_size: number
  created_at: string
  updated_at: string
}

// Keeper-related types
export interface KeeperCandidate {
  player_id: string
  player_name: string
  position: string
  current_cost: number
  projected_points: number
  replacement_level: number
  value_over_replacement: number
  keeper_cost: number
  is_eligible: boolean
  constraints_violated: string[]
  metadata: Record<string, any>
}

export interface KeeperAlternative {
  player_id: string
  player_name: string
  position: string
  keeper_cost: number
  value_over_replacement: number
  reason: string
}

export interface KeeperRecommendation {
  player_id: string
  player_name: string
  position: string
  keeper_cost: number
  projected_points: number
  value_over_replacement: number
  confidence: number
  rationale: string
  alternatives: KeeperAlternative[]
  metadata: Record<string, any>
}

export interface KeeperScenario {
  scenario_name: string
  selected_keepers: KeeperRecommendation[]
  total_value: number
  remaining_budget?: number
  constraints_satisfied: boolean
  trade_offs: string[]
  metadata: Record<string, any>
}

export interface ReplacementLevels {
  league_id: string
  season: number
  replacement_levels: Record<string, number>
  calculation_timestamp: string
}

// Re-export draft types
export * from './draft'

// Re-export trade types
export * from './trades'

// Re-export lineup types
export * from './lineup'

// Re-export waiver types
export * from './waiver'