// Waiver wire related types

export interface FreeAgent {
  player_id: string
  player_name: string
  position: string
  team: string
  projected_points: number
  points_over_replacement: number
  ownership_percentage?: number
  recent_performance: number[]
  upcoming_matchups: string[]
  bye_week?: number
  injury_status: string
  target_type: string
  metadata: Record<string, any>
}

export interface DropCandidate {
  player_id: string
  player_name: string
  position: string
  projected_points: number
  drop_priority: number
  reason: string
}

export interface WaiverTarget {
  player_id: string
  player_name: string
  position: string
  target_type: string
  priority: string
  recommended_bid: number
  max_bid: number
  points_over_replacement: number
  weekly_upside: number
  rationale: string
  drop_candidates: DropCandidate[]
  streaming_weeks?: number[]
  confidence: number
  metadata: Record<string, any>
}

export interface StreamingOpportunity {
  position: string
  weeks: number[]
  targets: WaiverTarget[]
  total_value: number
  strategy: string
  confidence: number
}

export interface WaiverStrategy {
  strategy_name: string
  targets: WaiverTarget[]
  total_faab_allocation: number
  remaining_budget: number
  expected_value: number
  risk_level: string
  streaming_opportunities: StreamingOpportunity[]
  trade_offs: string[]
  metadata: Record<string, any>
}

export interface TeamNeeds {
  position: string
  need_level: string
  starter_strength: number
  depth_count: number
  required_starters: number
  avg_starter_points: number
}

export interface WaiverFilters {
  position?: string
  minProjectedPoints?: number
  targetType?: string
  maxResults?: number
}

export interface FAABOptimizationRequest {
  franchise_id: string
  target_player_ids: string[]
  budget_constraint?: number
}