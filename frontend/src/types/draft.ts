// Draft-specific types

export type DraftStrategy = 'VALUE_BASED' | 'POSITIONAL_NEED' | 'BEST_AVAILABLE' | 'ZERO_RB' | 'ROBUST_RB'

export interface PlayerTier {
  tier_number: number
  position: string
  players: string[]
  min_value: number
  max_value: number
  avg_value: number
  tier_break_threshold: number
}

export interface DraftBoardData {
  tiers: PlayerTier[]
  overall_rankings: string[]
  position_rankings: Record<string, string[]>
  value_over_replacement: Record<string, number>
  last_updated: string
  strategy: DraftStrategy
}

export interface DraftRecommendationData {
  player_id: string
  player_name: string
  position: string
  team: string
  projected_points: number
  value_over_replacement: number
  tier: number
  confidence: number
  rationale: string
  alternatives: string[]
  positional_need_score: number
  opportunity_cost: number
}

export interface DraftPick {
  round_number: number
  pick_number: number
  overall_pick: number
  franchise_id: string
  player_id?: string
  timestamp?: string
}

export interface DraftScenario {
  scenario_id: string
  picks: DraftPick[]
  final_roster: Record<string, string[]>
  projected_points: number
  win_probability: number
  strategy_score: number
}

export interface ContingencyPlan {
  scenario_name: string
  recommendations: DraftRecommendationData[]
}

export interface DraftPlayerInfo {
  id: string
  name: string
  position: string
  team: string
  bye_week?: number
  injury_status?: string
  projected_points: number
  value_over_replacement: number
  tier: number
  adp?: number
}