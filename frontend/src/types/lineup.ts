// Lineup-related types

export interface LineupPlayer {
  id: string
  name: string
  position: string
  team: string
  projected_points: number
  adjusted_projection: number
  variance: number
  floor: number
  ceiling: number
  injury_status?: string
  bye_week?: number
  is_locked: boolean
  lock_time?: string
  matchup_context?: MatchupContext
}

export interface MatchupContext {
  opponent_team: string
  opponent_rank_vs_position?: number
  weather_condition: string
  temperature?: number
  wind_speed?: number
  is_home_game: boolean
  game_total?: number
  spread?: number
  implied_team_total?: number
}

export interface LineupSlot {
  position: string
  player?: LineupPlayer
  is_flex: boolean
  is_required: boolean
}

export interface OptimalLineup {
  lineup: Record<string, string> // slot -> player_id
  projected_points: number
  win_probability: number
  confidence: number
  rationale: string
  alternatives: LineupAlternative[]
  risk_level: number
}

export interface LineupAlternative {
  lineup: Record<string, string>
  change: string
  win_probability: number
  projected_points: number
}

export interface StartSitRecommendation {
  type: string
  slot: string
  start_player_id: string
  sit_player_id?: string
  projected_improvement: number
  confidence: number
  rationale: string
}

export interface LineupLockAlert {
  type: string
  player_id: string
  player_name: string
  slot: string
  lock_time: string
  time_until_lock: string
  urgency: string
}

export interface NewsAdjustment {
  type: string
  slot: string
  old_player_id: string
  new_player_id: string
  news_items: string[]
  projected_improvement: number
  urgency: string
}

export interface LineupHistory {
  week: number
  season: number
  lineup: Record<string, string>
  projected_points: number
  actual_points?: number
  win_probability: number
  result?: 'win' | 'loss' | 'tie'
  created_at: string
}

export interface LineupOptimizationRequest {
  franchise_id: string
  week: number
  season?: number
  opponent_projection?: number
}

export interface LineupRecommendations {
  optimization: OptimalLineup
  start_sit_recommendations: StartSitRecommendation[]
  lock_alerts: LineupLockAlert[]
  generated_at: string
}