// Types for explanation and rationale system

export interface ConfidenceBreakdown {
  data_quality: number
  model_accuracy: number
  situational_factors: number
  overall_confidence: number
}

export interface KeyFactor {
  factor: string
  weight: number
  description: string
  value?: string | number
  impact?: 'positive' | 'negative' | 'neutral'
}

export interface AlternativeScenario {
  scenario: string
  description: string
  confidence: number
  trade_offs: string[]
  expected_outcome?: string
}

export interface SensitivityAnalysis {
  projection_variance?: string
  rule_changes?: string
  external_factors?: string
  key_variables?: Array<{
    variable: string
    impact: string
    sensitivity: 'low' | 'medium' | 'high'
  }>
}

export interface RecommendationExplanation {
  recommendation_id: string
  explanation: string
  methodology: string
  key_factors: KeyFactor[]
  assumptions: string[]
  limitations: string[]
  confidence_breakdown: ConfidenceBreakdown
  sensitivity_analysis?: SensitivityAnalysis
  alternative_scenarios: AlternativeScenario[]
  data_sources: string[]
  last_updated: string
}

export interface ExplanationRequest {
  include_alternatives: boolean
  include_supporting_data: boolean
  include_risk_analysis: boolean
  detail_level: 'basic' | 'standard' | 'detailed'
}

export type ConfidenceLevel = 'low' | 'medium' | 'high' | 'very_high'

export interface UncertaintyVisualization {
  confidence_level: ConfidenceLevel
  confidence_score: number
  uncertainty_range?: [number, number]
  risk_factors: string[]
  confidence_indicators: Array<{
    indicator: string
    status: 'good' | 'warning' | 'poor'
    description: string
  }>
}