import { createContext, useContext, useEffect, useMemo, useState } from 'react'
import { getKeepers } from '@/lib/preseason-api'

export type MasterPlayer = {
  player_id: string
  name: string
  position: string
  nfl_team: string
  adp_index?: { adp: number | null }
  sources?: { MFL?: { adp: number | null }, FFC?: { adp: number | null } }
}

export type MasterContextValue = {
  season: number
  items: MasterPlayer[]
  lastUpdated: string | null
  loading: boolean
  error: string | null
  // Manual refresh of master list
  refresh: (leagueId?: string) => Promise<void>
  // Preseason keeper info (loaded from our store, not MFL)
  leagueId: string | null
  keptIds: Set<string>
  keepersUpdatedAt: string | null
  reloadKeepers: () => Promise<void>
}

const MasterContext = createContext<MasterContextValue | undefined>(undefined)

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export function MasterPlayersProvider({ children }: { children: React.ReactNode }) {
  const [season] = useState<number>(new Date().getFullYear())
  const [items, setItems] = useState<MasterPlayer[]>([])
  const [lastUpdated, setLastUpdated] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Keeper context
  const [leagueId, setLeagueId] = useState<string | null>(null)
  const [keptIds, setKeptIds] = useState<Set<string>>(new Set())
  const [keepersUpdatedAt, setKeepersUpdatedAt] = useState<string | null>(null)

  const load = async () => {
    try {
      setLoading(true)
      setError(null)
      const url = `${API_BASE}/api/v1/master/players?season=${season}`
      const resp = await fetch(url)
      if (!resp.ok) throw new Error(`Master fetch failed: ${resp.status}`)
      const data = await resp.json()
      setItems(data.items || [])
      setLastUpdated(data.updated_at || null)
    } catch (e: any) {
      setError(e?.message || 'Failed to load master players')
      setItems([])
    } finally {
      setLoading(false)
    }
  }

  const refresh = async (leagueId?: string) => {
    try {
      setLoading(true)
      const url = `${API_BASE}/api/v1/master/players/refresh?season=${season}${leagueId ? `&league_id=${encodeURIComponent(leagueId)}` : ''}`
      const resp = await fetch(url, { method: 'POST' })
      if (!resp.ok) throw new Error(`Refresh failed: ${resp.status}`)
      await load()
    } catch (e: any) {
      setError(e?.message || 'Failed to refresh master players')
    } finally {
      setLoading(false)
    }
  }

  // Load master on season change
  useEffect(() => { load() }, [season])

  // Initialize league id from localStorage (client only)
  useEffect(() => {
    if (typeof window === 'undefined') return
    const saved = localStorage.getItem('mfl_league_id')
    if (saved) {
      setLeagueId(saved.startsWith('mfl_') ? saved : `mfl_${saved}`)
    }
  }, [])

  const reloadKeepers = async () => {
    if (!leagueId) return
    try {
      const payload = await getKeepers(leagueId)
      const raw = new Set<string>(payload.selections.map(s => String(s.player_id)))
      const both = new Set<string>()
      raw.forEach(id => {
        both.add(id)
        both.add(id.startsWith('mfl_') ? id.slice(4) : `mfl_${id}`)
      })
      setKeptIds(both)
      setKeepersUpdatedAt(payload.updated_at || null)
    } catch (e) {
      // non-fatal
      setKeptIds(new Set())
      setKeepersUpdatedAt(null)
    }
  }

  // Load keepers when league id is available
  useEffect(() => { reloadKeepers() }, [leagueId])

  const value: MasterContextValue = useMemo(() => ({
    season,
    items,
    lastUpdated,
    loading,
    error,
    refresh,
    leagueId,
    keptIds,
    keepersUpdatedAt,
    reloadKeepers,
  }), [season, items, lastUpdated, loading, error, leagueId, keptIds, keepersUpdatedAt])

  return <MasterContext.Provider value={value}>{children}</MasterContext.Provider>
}

export function useMasterPlayers(): MasterContextValue {
  const ctx = useContext(MasterContext)
  if (!ctx) throw new Error('useMasterPlayers must be used within MasterPlayersProvider')
  return ctx
}

