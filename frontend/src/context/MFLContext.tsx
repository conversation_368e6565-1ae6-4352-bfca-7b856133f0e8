'use client'

import React, { createContext, useContext, useEffect, useMemo, useState, useCallback, useRef } from 'react'
import { useMFL } from '@/hooks/useMFL'
import mflApi, { MFLLeague, MFLPlayer, MFLRoster, MFLStandings, MFLTransaction } from '@/lib/mfl-api'

export interface MFLContextValue {
  leagueId: string | null
  setLeagueId: (id: string | null) => void
  seasonYear: number
  setSeasonYear: (year: number) => void
  setCredentials: (username: string, password: string) => void
  loading: boolean
  error: string | null
  league: MFLLeague | null
  players: MFLPlayer[]
  rosters: MFLRoster[]
  standings: MFLStandings[]
  transactions: MFLTransaction[]
  myFranchiseId: string | null
  setMyFranchiseId: (id: string | null) => void
  lastRefreshed: number | null
  initialize: () => Promise<void>
  refreshLeague: () => Promise<void>
  loadTransactions: (days?: number) => Promise<void>
}

const MFLContext = createContext<MFLContextValue | undefined>(undefined)

export function MFLProvider({ children }: { children: React.ReactNode }) {
  const [leagueId, setLeagueIdState] = useState<string | null>(() => {
    if (typeof window !== 'undefined') {
      const initial = localStorage.getItem('mfl_league_id')
      if (initial) {
        try { mflApi.setLeague(initial) } catch {}
      }
      return initial
    }
    return null
  })
  const [transactions, setTransactions] = useState<MFLTransaction[]>([])
  const [myFranchiseId, setMyFranchiseIdState] = useState<string | null>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('mfl_franchise_id') || null
    }
    return null
  })
  const [lastRefreshed, setLastRefreshed] = useState<number | null>(null)
  const [seasonYear, setSeasonYearState] = useState<number>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('mfl_season_year')
      return saved ? parseInt(saved) : new Date().getFullYear()
    }
    return new Date().getFullYear()
  })

  // Initialize credentials and season from localStorage (do not override leagueId here)
  useEffect(() => {
    const savedCredentials = typeof window !== 'undefined' ? localStorage.getItem('mfl_credentials') : null
    if (savedCredentials) {
      try {
        const { username, password } = JSON.parse(savedCredentials)
        if (username && password) {
          mflApi.setCredentials(username, password)
        }
      } catch {}
    }
    // initialize API year
    try { (mflApi as any).setYear(seasonYear) } catch {}
  }, [seasonYear])

  // Wire useMFL to the selected league
  const {
    loading,
    error,
    league,
    players,
    rosters,
    standings,
    initialize: hookInitialize,
    loadLeague,
    loadPlayers,
    loadRosters,
    loadStandings,
    setLeague: setHookLeague,
  } = useMFL(leagueId || undefined)

  // keep mflApi in sync if leagueId changes
  useEffect(() => {
    if (leagueId) setHookLeague(leagueId)
  }, [leagueId, setHookLeague])

  const initialize = useCallback(async () => {
    if (!leagueId) return
    await hookInitialize()
    setLastRefreshed(Date.now())
  }, [leagueId, hookInitialize])

  // Auto-initialize once when a leagueId is present, regardless of which page loads first
  const didInitRef = useRef(false)
  useEffect(() => {
    const autoInit = async () => {
      if (!didInitRef.current && leagueId) {
        try {
          await hookInitialize()
          setLastRefreshed(Date.now())
        } finally {
          didInitRef.current = true
        }
      }
    }
    autoInit()
  }, [leagueId, hookInitialize])

  const refreshLeague = useCallback(async () => {
    if (!leagueId) return
    await loadLeague()
    await loadStandings()
    await loadPlayers()
    await loadRosters()
    setLastRefreshed(Date.now())
  }, [leagueId, loadLeague, loadPlayers, loadRosters, loadStandings])

  // Try to infer my franchise id from credentials and league owners if not set
  useEffect(() => {
    try {
      if (!myFranchiseId && league && Array.isArray((league as any).franchises)) {
        const creds = typeof window !== 'undefined' ? localStorage.getItem('mfl_credentials') : null
        if (creds) {
          const { username } = JSON.parse(creds)
          if (username) {
            const u = String(username).toLowerCase()
            const match = (league as any).franchises.find((f: any) => String(f.owner_name || '').toLowerCase().includes(u))
            if (match && match.id) {
              setMyFranchiseIdState(String(match.id))
              if (typeof window !== 'undefined') localStorage.setItem('mfl_franchise_id', String(match.id))
            }
          }
        }
      }
    } catch {}
  }, [league, myFranchiseId])

  const loadTransactions = useCallback(async (days?: number) => {
    try {
      if (leagueId) {
        mflApi.setLeague(leagueId)
      }
      const resp = await mflApi.getTransactions(days)
      const tx = resp?.transactions?.transaction || []
      setTransactions(Array.isArray(tx) ? tx : (tx ? [tx] : []))
    } catch (e) {
      // swallow in context, pages can display their own errors if needed
      setTransactions([])
    }
  }, [leagueId])

  const value = useMemo<MFLContextValue>(() => ({
    leagueId,
    setLeagueId: (id: string | null) => {
      setLeagueIdState(id)
      if (id) {
        localStorage.setItem('mfl_league_id', id)
        mflApi.setLeague(id)
      } else {
        localStorage.removeItem('mfl_league_id')
      }
    },
    seasonYear,
    setSeasonYear: (year: number) => {
      setSeasonYearState(year)
      localStorage.setItem('mfl_season_year', String(year))
      try { (mflApi as any).setYear(year) } catch {}
    },
    setCredentials: (username: string, password: string) => {
      mflApi.setCredentials(username, password)
      localStorage.setItem('mfl_credentials', JSON.stringify({ username, password }))
    },
    loading,
    error,
    league,
    players,
    rosters,
    standings,
    transactions,
    lastRefreshed,
    myFranchiseId,
    setMyFranchiseId: (id: string | null) => {
      setMyFranchiseIdState(id)
      if (typeof window !== 'undefined') {
        if (id) localStorage.setItem('mfl_franchise_id', id)
        else localStorage.removeItem('mfl_franchise_id')
      }
    },
    initialize,
    refreshLeague,
    loadTransactions,
  }), [leagueId, seasonYear, loading, error, league, players, rosters, standings, transactions, lastRefreshed, myFranchiseId, initialize, refreshLeague, loadTransactions])

  return (
    <MFLContext.Provider value={value}>
      {children}
    </MFLContext.Provider>
  )
}

export function useMFLContext(): MFLContextValue {
  const ctx = useContext(MFLContext)
  if (!ctx) throw new Error('useMFLContext must be used within MFLProvider')
  return ctx
}

