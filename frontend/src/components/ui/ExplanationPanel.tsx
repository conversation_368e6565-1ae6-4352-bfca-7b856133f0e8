'use client'

import { useState } from 'react'
import { 
  ChevronDownIcon, 
  ChevronUpIcon,
  QuestionMarkCircleIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CogIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import { RecommendationExplanation, KeyFactor, AlternativeScenario } from '@/types/explanation'
import ConfidenceIndicator from './ConfidenceIndicator'

interface ExplanationPanelProps {
  explanation: RecommendationExplanation
  isExpanded?: boolean
  onToggle?: () => void
  showToggle?: boolean
}

export default function ExplanationPanel({ 
  explanation, 
  isExpanded = false, 
  onToggle,
  showToggle = true 
}: ExplanationPanelProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'factors' | 'alternatives' | 'methodology'>('overview')

  const getFactorImpactColor = (impact?: string) => {
    switch (impact) {
      case 'positive': return 'text-green-600 bg-green-50'
      case 'negative': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getFactorImpactIcon = (impact?: string) => {
    switch (impact) {
      case 'positive': return '↗'
      case 'negative': return '↘'
      default: return '→'
    }
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: InformationCircleIcon },
    { id: 'factors', label: 'Key Factors', icon: ChartBarIcon },
    { id: 'alternatives', label: 'Alternatives', icon: DocumentTextIcon },
    { id: 'methodology', label: 'Methodology', icon: CogIcon }
  ]

  return (
    <div className="bg-white border border-gray-200 rounded-lg">
      {/* Header */}
      {showToggle && (
        <button
          onClick={onToggle}
          className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center space-x-2">
            <QuestionMarkCircleIcon className="h-5 w-5 text-blue-500" />
            <span className="font-medium text-gray-900">Why this recommendation?</span>
          </div>
          {isExpanded ? (
            <ChevronUpIcon className="h-5 w-5 text-gray-400" />
          ) : (
            <ChevronDownIcon className="h-5 w-5 text-gray-400" />
          )}
        </button>
      )}

      {/* Content */}
      {(isExpanded || !showToggle) && (
        <div className="border-t border-gray-200">
          {/* Tab Navigation */}
          <div className="flex border-b border-gray-200">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex-1 px-4 py-3 text-sm font-medium flex items-center justify-center space-x-2 transition-colors ${
                    activeTab === tab.id
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </div>

          {/* Tab Content */}
          <div className="p-4">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-4">
                {/* Main Explanation */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Explanation</h4>
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {explanation.explanation}
                  </p>
                </div>

                {/* Confidence Breakdown */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Confidence Analysis</h4>
                  <div className="grid grid-cols-2 gap-4">
                    {Object.entries(explanation.confidence_breakdown).map(([key, value]) => (
                      <div key={key} className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 capitalize">
                            {key.replace('_', ' ')}
                          </span>
                          <span className="text-sm font-medium">
                            {Math.round(value * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              value >= 0.8 ? 'bg-green-500' :
                              value >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${value * 100}%` }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Assumptions & Limitations */}
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Key Assumptions</h4>
                    <ul className="space-y-1">
                      {explanation.assumptions.slice(0, 3).map((assumption, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                          <span className="text-blue-500 mt-1">•</span>
                          <span>{assumption}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Limitations</h4>
                    <ul className="space-y-1">
                      {explanation.limitations.slice(0, 3).map((limitation, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                          <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                          <span>{limitation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Key Factors Tab */}
            {activeTab === 'factors' && (
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">
                    Factors Influencing This Recommendation
                  </h4>
                  <div className="space-y-3">
                    {explanation.key_factors
                      .sort((a, b) => b.weight - a.weight)
                      .map((factor, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-3">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 rounded text-xs font-medium ${getFactorImpactColor(factor.impact)}`}>
                                {getFactorImpactIcon(factor.impact)} {factor.factor}
                              </span>
                              <span className="text-sm text-gray-500">
                                {Math.round(factor.weight * 100)}% weight
                              </span>
                            </div>
                            {factor.value && (
                              <span className="text-sm font-medium text-gray-900">
                                {factor.value}
                              </span>
                            )}
                          </div>
                          
                          <p className="text-sm text-gray-600">
                            {factor.description}
                          </p>
                          
                          {/* Weight visualization */}
                          <div className="mt-2">
                            <div className="w-full bg-gray-200 rounded-full h-1">
                              <div
                                className="h-1 rounded-full bg-blue-500"
                                style={{ width: `${factor.weight * 100}%` }}
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>

                {/* Sensitivity Analysis */}
                {explanation.sensitivity_analysis && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Sensitivity Analysis</h4>
                    <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                      {explanation.sensitivity_analysis.key_variables?.map((variable, index) => (
                        <div key={index} className="flex items-center justify-between text-sm">
                          <span className="text-gray-700">{variable.variable}</span>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 rounded text-xs ${
                              variable.sensitivity === 'high' ? 'bg-red-100 text-red-700' :
                              variable.sensitivity === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                              'bg-green-100 text-green-700'
                            }`}>
                              {variable.sensitivity}
                            </span>
                            <span className="text-gray-600">{variable.impact}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Alternatives Tab */}
            {activeTab === 'alternatives' && (
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Alternative Scenarios</h4>
                  {explanation.alternative_scenarios.length === 0 ? (
                    <p className="text-gray-500 text-sm">No alternative scenarios available.</p>
                  ) : (
                    <div className="space-y-3">
                      {explanation.alternative_scenarios.map((scenario, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-start justify-between mb-2">
                            <h5 className="font-medium text-gray-900">{scenario.scenario}</h5>
                            <ConfidenceIndicator 
                              confidence={scenario.confidence} 
                              size="sm"
                            />
                          </div>
                          
                          <p className="text-sm text-gray-600 mb-3">
                            {scenario.description}
                          </p>

                          {scenario.expected_outcome && (
                            <div className="mb-3">
                              <span className="text-sm font-medium text-gray-700">Expected Outcome: </span>
                              <span className="text-sm text-gray-600">{scenario.expected_outcome}</span>
                            </div>
                          )}

                          {scenario.trade_offs.length > 0 && (
                            <div>
                              <span className="text-sm font-medium text-gray-700 mb-1 block">Trade-offs:</span>
                              <ul className="space-y-1">
                                {scenario.trade_offs.map((tradeOff, tradeOffIndex) => (
                                  <li key={tradeOffIndex} className="text-sm text-gray-600 flex items-start space-x-2">
                                    <span className="text-yellow-500 mt-1">•</span>
                                    <span>{tradeOff}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Methodology Tab */}
            {activeTab === 'methodology' && (
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Methodology</h4>
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {explanation.methodology}
                  </p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Data Sources</h4>
                  <div className="flex flex-wrap gap-2">
                    {explanation.data_sources.map((source, index) => (
                      <span 
                        key={index}
                        className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                      >
                        {source}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="text-xs text-gray-500 pt-2 border-t border-gray-200">
                  Last updated: {new Date(explanation.last_updated).toLocaleString()}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}