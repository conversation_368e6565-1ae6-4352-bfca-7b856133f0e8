'use client'

import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon, 
  XCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { ConfidenceLevel, UncertaintyVisualization } from '@/types/explanation'

interface ConfidenceIndicatorProps {
  confidence: number
  level?: ConfidenceLevel
  uncertainty?: UncertaintyVisualization
  showDetails?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export default function ConfidenceIndicator({ 
  confidence, 
  level, 
  uncertainty,
  showDetails = false,
  size = 'md'
}: ConfidenceIndicatorProps) {
  const getConfidenceLevel = (score: number): ConfidenceLevel => {
    if (score >= 0.9) return 'very_high'
    if (score >= 0.7) return 'high'
    if (score >= 0.5) return 'medium'
    return 'low'
  }

  const confidenceLevel = level || getConfidenceLevel(confidence)

  const getConfidenceConfig = (level: ConfidenceLevel) => {
    switch (level) {
      case 'very_high':
        return {
          color: 'text-green-700 bg-green-100',
          icon: CheckCircleIcon,
          label: 'Very High',
          description: 'High confidence in recommendation'
        }
      case 'high':
        return {
          color: 'text-green-600 bg-green-50',
          icon: CheckCircleIcon,
          label: 'High',
          description: 'Good confidence in recommendation'
        }
      case 'medium':
        return {
          color: 'text-yellow-600 bg-yellow-50',
          icon: ExclamationTriangleIcon,
          label: 'Medium',
          description: 'Moderate confidence, consider alternatives'
        }
      case 'low':
        return {
          color: 'text-red-600 bg-red-50',
          icon: XCircleIcon,
          label: 'Low',
          description: 'Low confidence, high uncertainty'
        }
    }
  }

  const config = getConfidenceConfig(confidenceLevel)
  const Icon = config.icon

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  }

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  return (
    <div className="space-y-2">
      {/* Main Confidence Badge */}
      <div className={`inline-flex items-center space-x-1 rounded-full font-medium ${config.color} ${sizeClasses[size]}`}>
        <Icon className={iconSizes[size]} />
        <span>{Math.round(confidence * 100)}%</span>
        {showDetails && <span className="ml-1">({config.label})</span>}
      </div>

      {/* Detailed Breakdown */}
      {showDetails && uncertainty && (
        <div className="mt-2 space-y-2">
          {/* Confidence Bar */}
          <div className="flex items-center space-x-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  confidenceLevel === 'very_high' ? 'bg-green-600' :
                  confidenceLevel === 'high' ? 'bg-green-500' :
                  confidenceLevel === 'medium' ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${confidence * 100}%` }}
              />
            </div>
            <span className="text-xs text-gray-600 min-w-[3rem]">
              {Math.round(confidence * 100)}%
            </span>
          </div>

          {/* Uncertainty Range */}
          {uncertainty.uncertainty_range && (
            <div className="text-xs text-gray-600">
              Range: {Math.round(uncertainty.uncertainty_range[0] * 100)}% - {Math.round(uncertainty.uncertainty_range[1] * 100)}%
            </div>
          )}

          {/* Confidence Indicators */}
          {uncertainty.confidence_indicators && uncertainty.confidence_indicators.length > 0 && (
            <div className="space-y-1">
              {uncertainty.confidence_indicators.map((indicator, index) => (
                <div key={index} className="flex items-center space-x-2 text-xs">
                  <div className={`w-2 h-2 rounded-full ${
                    indicator.status === 'good' ? 'bg-green-500' :
                    indicator.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <span className="text-gray-700">{indicator.indicator}</span>
                  <InformationCircleIcon 
                    className="h-3 w-3 text-gray-400" 
                    title={indicator.description}
                  />
                </div>
              ))}
            </div>
          )}

          {/* Risk Factors */}
          {uncertainty.risk_factors && uncertainty.risk_factors.length > 0 && (
            <div className="mt-2">
              <div className="text-xs font-medium text-gray-700 mb-1">Risk Factors:</div>
              <div className="space-y-1">
                {uncertainty.risk_factors.slice(0, 3).map((risk, index) => (
                  <div key={index} className="flex items-start space-x-1 text-xs text-gray-600">
                    <ExclamationTriangleIcon className="h-3 w-3 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <span>{risk}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}