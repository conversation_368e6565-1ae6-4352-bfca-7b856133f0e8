'use client'

import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon, 
  XCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { ConfidenceLevel, UncertaintyVisualization } from '@/types/explanation'

interface ConfidenceIndicatorProps {
  confidence: number
  level?: ConfidenceLevel
  uncertainty?: UncertaintyVisualization
  showDetails?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export default function ConfidenceIndicator({ 
  confidence, 
  level, 
  uncertainty,
  showDetails = false,
  size = 'md'
}: ConfidenceIndicatorProps) {
  const getConfidenceLevel = (score: number): ConfidenceLevel => {
    if (score >= 0.9) return 'very_high'
    if (score >= 0.7) return 'high'
    if (score >= 0.5) return 'medium'
    return 'low'
  }

  const confidenceLevel = level || getConfidenceLevel(confidence)

  const getConfidenceConfig = (level: ConfidenceLevel) => {
    switch (level) {
      case 'very_high':
        return {
          color: 'text-green-700 bg-green-100',
          icon: CheckCircleIcon,
          label: 'Very High',
          description: 'High confidence in recommendation'
        }
      case 'high':
        return {
          color: 'text-green-600 bg-green-50',
          icon: CheckCircleIcon,
          label: 'High',
          description: 'Good confidence in recommendation'
        }
      case 'medium':
        return {
          color: 'text-yellow-600 bg-yellow-50',
          icon: ExclamationTriangleIcon,
          label: 'Medium',
          description: 'Moderate confidence, consider alternatives'
        }
      case 'low':
        return {
          color: 'text-red-600 bg-red-50',
          icon: XCircleIcon,
          label: 'Low',
          description: 'Low confidence, high uncertainty'
        }
    }
  }

  const config = getConfidenceConfig(confidenceLevel)
  const Icon = config.icon

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  }

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  return (
    <div className="space-y-2">
      {/* Main Confidence Badge */}
      <div className={`inline-flex items-center space-x-1 rounded-full font-medium ${config.color} ${sizeClasses[size]}`}>
        <Icon className={iconSizes[size]} />
        <span>{Math.round(confidence * 100)}%</span>
        {showDetails && <span className="ml-1">({config.label})</span>}
      </div>

      {/* Detailed Breakdown */}
      {showDetails && uncertainty && (
        <div className="mt-2 space-y-3">
          {/* Confidence Bar with Uncertainty Range */}
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs text-gray-600">
              <span>Confidence Level</span>
              <span>{Math.round(confidence * 100)}%</span>
            </div>
            <div className="relative">
              <div className="flex-1 bg-gray-200 rounded-full h-3">
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${
                    confidenceLevel === 'very_high' ? 'bg-green-600' :
                    confidenceLevel === 'high' ? 'bg-green-500' :
                    confidenceLevel === 'medium' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${confidence * 100}%` }}
                />
                {/* Uncertainty Range Overlay */}
                {uncertainty.uncertainty_range && (
                  <div
                    className="absolute top-0 h-3 bg-black bg-opacity-20 rounded-full"
                    style={{
                      left: `${Math.min(uncertainty.uncertainty_range[0] * 100, 100)}%`,
                      width: `${Math.min(Math.abs(uncertainty.uncertainty_range[1] - uncertainty.uncertainty_range[0]) * 100, 100)}%`
                    }}
                    title={`Uncertainty range: ${Math.round(uncertainty.uncertainty_range[0] * 100)}% - ${Math.round(uncertainty.uncertainty_range[1] * 100)}%`}
                  />
                )}
              </div>
              {uncertainty.uncertainty_range && (
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>{Math.round(uncertainty.uncertainty_range[0] * 100)}%</span>
                  <span>{Math.round(uncertainty.uncertainty_range[1] * 100)}%</span>
                </div>
              )}
            </div>
          </div>

          {/* Confidence Breakdown */}
          {uncertainty.confidence_indicators && uncertainty.confidence_indicators.length > 0 && (
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-700">Confidence Factors:</div>
              <div className="grid grid-cols-1 gap-2">
                {uncertainty.confidence_indicators.map((indicator, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        indicator.status === 'good' ? 'bg-green-500' :
                        indicator.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                      }`} />
                      <span className="text-gray-700 font-medium">{indicator.indicator}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${
                        indicator.status === 'good' ? 'bg-green-100 text-green-700' :
                        indicator.status === 'warning' ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700'
                      }`}>
                        {indicator.status}
                      </span>
                      <InformationCircleIcon
                        className="h-3 w-3 text-gray-400 cursor-help"
                        title={indicator.description}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Risk Factors with Enhanced Display */}
          {uncertainty.risk_factors && uncertainty.risk_factors.length > 0 && (
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-700">Risk Factors:</div>
              <div className="space-y-1">
                {uncertainty.risk_factors.slice(0, 4).map((risk, index) => (
                  <div key={index} className="flex items-start space-x-2 p-2 bg-yellow-50 border-l-2 border-yellow-300 rounded-r text-xs">
                    <ExclamationTriangleIcon className="h-3 w-3 text-yellow-600 mt-0.5 flex-shrink-0" />
                    <span className="text-yellow-800">{risk}</span>
                  </div>
                ))}
                {uncertainty.risk_factors.length > 4 && (
                  <div className="text-xs text-gray-500 italic">
                    +{uncertainty.risk_factors.length - 4} more risk factors
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Uncertainty Range Summary */}
          {uncertainty.uncertainty_range && (
            <div className="p-2 bg-blue-50 border border-blue-200 rounded text-xs">
              <div className="font-medium text-blue-800 mb-1">Uncertainty Range</div>
              <div className="text-blue-700">
                Expected range: {Math.round(uncertainty.uncertainty_range[0] * 100)}% - {Math.round(uncertainty.uncertainty_range[1] * 100)}%
              </div>
              <div className="text-blue-600 text-xs mt-1">
                Confidence may vary by ±{Math.round(Math.abs(uncertainty.uncertainty_range[1] - uncertainty.uncertainty_range[0]) * 50)}% based on changing conditions
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}