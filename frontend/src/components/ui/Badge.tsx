import React from 'react'
import { positionColorClasses, teamColorClasses, franchiseStyle, NEUTRAL_FA } from '@/lib/colors'

export type BadgeType = 'position' | 'team' | 'franchise' | 'neutral' | 'custom'

interface BadgeProps {
  type: BadgeType
  value?: string | null
  label?: string
  franchiseId?: string | null
  className?: string
  size?: 'xs' | 'sm' | 'md'
}

export const Badge: React.FC<BadgeProps> = ({ type, value, label, franchiseId, className = '', size = 'xs' }) => {
  const text = (label ?? value ?? '').toString() || '—'
  const pad = size === 'md' ? 'px-3 py-1' : size === 'sm' ? 'px-2 py-0.5' : 'px-2 py-0.5'
  const base = `inline-flex items-center rounded text-xs font-medium ${pad}`

  if (type === 'position') {
    const c = positionColorClasses(value || undefined)
    return <span className={`${base} ${c.primary} ${c.text} ${className}`}>{text}</span>
  }
  if (type === 'team') {
    const c = teamColorClasses(value || undefined)
    return <span className={`${base} ${c.primary} ${c.text} ${className}`}>{text}</span>
  }
  if (type === 'franchise') {
    const style = franchiseStyle(franchiseId || undefined)
    return <span className={`${base} ${className}`} style={style as React.CSSProperties}>{text}</span>
  }
  if (type === 'neutral') {
    return <span className={`${base} ${NEUTRAL_FA.primary} ${NEUTRAL_FA.text} ${className}`}>{text}</span>
  }
  // custom fallback
  return <span className={`${base} ${className}`}>{text}</span>
}

export default Badge

