'use client'

import { useState } from 'react'
import { 
  ChevronDownIcon, 
  ChevronUpIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { AlternativeScenario } from '@/types/explanation'
import ConfidenceIndicator from './ConfidenceIndicator'

interface AlternativeComparisonProps {
  primaryRecommendation: {
    title: string
    confidence: number
    description: string
    key_metrics?: Record<string, any>
  }
  alternatives: AlternativeScenario[]
  onSelectAlternative?: (scenario: AlternativeScenario) => void
  showComparison?: boolean
}

export default function AlternativeComparison({ 
  primaryRecommendation,
  alternatives,
  onSelectAlternative,
  showComparison = true
}: AlternativeComparisonProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [selectedAlternative, setSelectedAlternative] = useState<AlternativeScenario | null>(null)

  if (alternatives.length === 0) {
    return null
  }

  const handleSelectAlternative = (scenario: AlternativeScenario) => {
    setSelectedAlternative(scenario)
    onSelectAlternative?.(scenario)
  }

  const getComparisonMetrics = (alternative: AlternativeScenario) => {
    // Extract comparison metrics from the alternative scenario
    // This would be populated by the backend with specific metrics
    return {
      confidence_diff: alternative.confidence - primaryRecommendation.confidence,
      risk_level: alternative.trade_offs.length > 2 ? 'high' : alternative.trade_offs.length > 0 ? 'medium' : 'low'
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg">
      {/* Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center space-x-2">
          <ArrowRightIcon className="h-5 w-5 text-gray-400" />
          <span className="font-medium text-gray-900">
            Alternative Options ({alternatives.length})
          </span>
        </div>
        {isExpanded ? (
          <ChevronUpIcon className="h-5 w-5 text-gray-400" />
        ) : (
          <ChevronDownIcon className="h-5 w-5 text-gray-400" />
        )}
      </button>

      {/* Content */}
      {isExpanded && (
        <div className="border-t border-gray-200 p-4">
          {/* Primary Recommendation Summary */}
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-blue-900">Current Recommendation</h4>
              <ConfidenceIndicator 
                confidence={primaryRecommendation.confidence} 
                size="sm"
              />
            </div>
            <p className="text-sm text-blue-800">{primaryRecommendation.description}</p>
          </div>

          {/* Alternatives List */}
          <div className="space-y-3">
            {alternatives.map((alternative, index) => {
              const metrics = getComparisonMetrics(alternative)
              const isSelected = selectedAlternative?.scenario === alternative.scenario
              
              return (
                <div 
                  key={index}
                  className={`border rounded-lg p-4 transition-all ${
                    isSelected 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h5 className="font-medium text-gray-900">
                          {alternative.scenario}
                        </h5>
                        {isSelected && (
                          <CheckCircleIcon className="h-4 w-4 text-blue-600" />
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        {alternative.description}
                      </p>
                    </div>
                    
                    <div className="flex flex-col items-end space-y-2">
                      <ConfidenceIndicator 
                        confidence={alternative.confidence} 
                        size="sm"
                      />
                      {onSelectAlternative && (
                        <button
                          onClick={() => handleSelectAlternative(alternative)}
                          className={`px-3 py-1 text-xs rounded transition-colors ${
                            isSelected
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                          }`}
                        >
                          {isSelected ? 'Selected' : 'Select'}
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Comparison Metrics */}
                  {showComparison && (
                    <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-600">Confidence vs Primary:</span>
                        <span className={`font-medium ${
                          metrics.confidence_diff > 0 ? 'text-green-600' : 
                          metrics.confidence_diff < 0 ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {metrics.confidence_diff > 0 ? '+' : ''}
                          {Math.round(metrics.confidence_diff * 100)}%
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-600">Risk Level:</span>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          metrics.risk_level === 'high' ? 'bg-red-100 text-red-700' :
                          metrics.risk_level === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-green-100 text-green-700'
                        }`}>
                          {metrics.risk_level}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Expected Outcome */}
                  {alternative.expected_outcome && (
                    <div className="mb-3">
                      <span className="text-sm font-medium text-gray-700">Expected Outcome: </span>
                      <span className="text-sm text-gray-600">{alternative.expected_outcome}</span>
                    </div>
                  )}

                  {/* Trade-offs */}
                  {alternative.trade_offs.length > 0 && (
                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-1">Trade-offs:</div>
                      <div className="space-y-1">
                        {alternative.trade_offs.slice(0, 3).map((tradeOff, tradeOffIndex) => (
                          <div key={tradeOffIndex} className="flex items-start space-x-2 text-sm text-gray-600">
                            <XCircleIcon className="h-3 w-3 text-red-500 mt-1 flex-shrink-0" />
                            <span>{tradeOff}</span>
                          </div>
                        ))}
                        {alternative.trade_offs.length > 3 && (
                          <div className="text-xs text-gray-500 ml-5">
                            +{alternative.trade_offs.length - 3} more trade-offs
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>

          {/* Comparison Summary */}
          {selectedAlternative && showComparison && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <h5 className="font-medium text-gray-900 mb-2">Comparison Summary</h5>
              <div className="text-sm text-gray-700">
                <p>
                  <strong>{selectedAlternative.scenario}</strong> vs <strong>Current Recommendation</strong>:
                </p>
                <ul className="mt-2 space-y-1 ml-4">
                  <li>
                    Confidence: {selectedAlternative.confidence > primaryRecommendation.confidence ? 'Higher' : 'Lower'} 
                    ({Math.abs(Math.round((selectedAlternative.confidence - primaryRecommendation.confidence) * 100))}% difference)
                  </li>
                  <li>
                    Trade-offs: {selectedAlternative.trade_offs.length} considerations
                  </li>
                  {selectedAlternative.expected_outcome && (
                    <li>Expected outcome: {selectedAlternative.expected_outcome}</li>
                  )}
                </ul>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}