'use client'

import { useState } from 'react'
import { QuestionMarkCircleIcon } from '@heroicons/react/24/outline'
import { RecommendationExplanation, ExplanationRequest } from '@/types/explanation'
import ExplanationPanel from './ExplanationPanel'
import { LoadingSpinner } from './LoadingSpinner'

interface WhyButtonProps {
  recommendationId: string
  onExplain?: (request: ExplanationRequest) => Promise<RecommendationExplanation>
  explanation?: RecommendationExplanation
  size?: 'sm' | 'md' | 'lg'
  variant?: 'button' | 'link' | 'icon'
  className?: string
}

export default function WhyButton({ 
  recommendationId,
  onExplain,
  explanation: providedExplanation,
  size = 'md',
  variant = 'button',
  className = ''
}: WhyButtonProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [explanation, setExplanation] = useState<RecommendationExplanation | null>(providedExplanation || null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleToggle = async () => {
    if (!isExpanded && !explanation && onExplain) {
      setLoading(true)
      setError(null)
      
      try {
        const request: ExplanationRequest = {
          include_alternatives: true,
          include_supporting_data: true,
          include_risk_analysis: true,
          detail_level: 'standard'
        }
        
        const result = await onExplain(request)
        setExplanation(result)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load explanation')
      } finally {
        setLoading(false)
      }
    }
    
    setIsExpanded(!isExpanded)
  }

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-2',
    lg: 'text-base px-4 py-2'
  }

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  const getButtonContent = () => {
    if (loading) {
      return (
        <>
          <LoadingSpinner size="sm" />
          <span>Loading...</span>
        </>
      )
    }

    switch (variant) {
      case 'icon':
        return <QuestionMarkCircleIcon className={iconSizes[size]} />
      case 'link':
        return (
          <>
            <QuestionMarkCircleIcon className={iconSizes[size]} />
            <span>Why?</span>
          </>
        )
      default:
        return (
          <>
            <QuestionMarkCircleIcon className={iconSizes[size]} />
            <span>Explain</span>
          </>
        )
    }
  }

  const getButtonClasses = () => {
    const baseClasses = 'inline-flex items-center space-x-1 transition-colors'
    
    switch (variant) {
      case 'icon':
        return `${baseClasses} p-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-full ${className}`
      case 'link':
        return `${baseClasses} text-blue-600 hover:text-blue-700 underline ${className}`
      default:
        return `${baseClasses} ${sizeClasses[size]} bg-blue-100 text-blue-700 hover:bg-blue-200 rounded-lg font-medium ${className}`
    }
  }

  return (
    <div className="space-y-3">
      <button
        onClick={handleToggle}
        className={getButtonClasses()}
        disabled={loading}
        title="Get detailed explanation for this recommendation"
      >
        {getButtonContent()}
      </button>

      {error && (
        <div className="text-red-600 text-sm bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="font-medium">Failed to load explanation</div>
          <div>{error}</div>
        </div>
      )}

      {isExpanded && explanation && (
        <ExplanationPanel 
          explanation={explanation}
          isExpanded={true}
          showToggle={false}
        />
      )}
    </div>
  )
}