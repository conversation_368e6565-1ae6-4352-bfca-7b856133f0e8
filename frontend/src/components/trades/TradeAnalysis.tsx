'use client'

import { useState, useEffect } from 'react'
import { TradeAPI } from '@/lib/trade-api'
import { TradeProposal } from '@/types/trades'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import WhyButton from '@/components/ui/WhyButton'
import ConfidenceIndicator from '@/components/ui/ConfidenceIndicator'
import { ExplanationAPI } from '@/lib/explanation-api'
import { RecommendationExplanation, ExplanationRequest, UncertaintyVisualization } from '@/types/explanation'

interface TradeAnalysisProps {
  franchiseId: string
  leagueId: string
}

export function TradeAnalysis({ franchiseId, leagueId }: TradeAnalysisProps) {
  const [suggestions, setSuggestions] = useState<TradeProposal[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const computeConfidence = (s: TradeProposal) => {
    // Derive a confidence value from acceptance probability and fairness score (closer to 0 is fair)
    const fairnessComponent = 1 - Math.min(1, Math.max(0, Math.abs(s.fairness_score)))
    const ap = Math.min(1, Math.max(0, s.acceptance_probability))
    return Math.min(1, Math.max(0, 0.6 * ap + 0.4 * fairnessComponent))
  }

  useEffect(() => {
    loadTradeSuggestions()
  }, [franchiseId, leagueId])

  const loadTradeSuggestions = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await TradeAPI.getTradesSuggestions(franchiseId, leagueId)
      setSuggestions(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load trade suggestions')
    } finally {
      setLoading(false)
    }
  }

  const getFairnessColor = (fairness: string) => {
    switch (fairness) {
      case 'very_fair': return 'text-green-600 bg-green-100'
      case 'fair': return 'text-green-500 bg-green-50'
      case 'slightly_unfair': return 'text-yellow-600 bg-yellow-100'
      case 'unfair': return 'text-orange-600 bg-orange-100'
      case 'very_unfair': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTradeTypeIcon = (tradeType: string) => {
    switch (tradeType) {
      case 'need_based': return '🎯'
      case 'surplus_based': return '📈'
      case 'value_arbitrage': return '💰'
      case 'win_now': return '🏆'
      case 'future_focused': return '🔮'
      default: return '🔄'
    }
  }

  const handleExplain = (suggestionId: string) => async (request: ExplanationRequest): Promise<RecommendationExplanation> => {
    return ExplanationAPI.getExplanation(suggestionId, request)
  }

  const getUncertaintyVisualization = (suggestion: TradeProposal): UncertaintyVisualization => {
    const confidence = computeConfidence(suggestion)
    return {
      confidence_level: confidence > 0.8 ? 'high' : confidence > 0.6 ? 'medium' : 'low',
      confidence_score: confidence,
      uncertainty_range: [
        suggestion.acceptance_probability * 0.8,
        suggestion.acceptance_probability * 1.2
      ],
      risk_factors: [
        suggestion.fairness === 'unfair' || suggestion.fairness === 'very_unfair' ? 'Trade fairness concerns' : '',
        Math.abs(suggestion.win_probability_impact_a) < 0.02 ? 'Minimal impact on win probability' : '',
        'Market conditions may change',
        'Player values may fluctuate'
      ].filter(Boolean),
      confidence_indicators: [
        {
          indicator: 'Trade Fairness',
          status: suggestion.fairness === 'fair' || suggestion.fairness === 'very_fair' ? 'good' : 'warning',
          description: 'How fair the trade is for both parties'
        },
        {
          indicator: 'Win Probability Impact',
          status: Math.abs(suggestion.win_probability_impact_a) > 0.05 ? 'good' : 'warning',
          description: 'Expected impact on your win probability'
        },
        {
          indicator: 'Acceptance Likelihood',
          status: suggestion.acceptance_probability > 0.6 ? 'good' : 'poor',
          description: 'Likelihood the other team accepts'
        }
      ]
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return <ErrorMessage message={error} />
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Trade Suggestions</h2>
        <button
          onClick={loadTradeSuggestions}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Refresh Suggestions
        </button>
      </div>

      {suggestions.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <div className="text-4xl mb-4">🤝</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Trade Suggestions</h3>
          <p className="text-gray-600">
            No beneficial trade opportunities found at this time. Check back later or adjust your roster needs.
          </p>
        </div>
      ) : (
        <div className="grid gap-6">
          {suggestions.map((suggestion) => (
            <div key={suggestion.trade_id} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getTradeTypeIcon(suggestion.trade_type)}</span>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Trade with {suggestion.team_b_id}
                    </h3>
                    <p className="text-sm text-gray-600 capitalize">
                      {suggestion.trade_type.replace('_', ' ')} trade
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <ConfidenceIndicator
                    confidence={computeConfidence(suggestion)}
                    uncertainty={getUncertaintyVisualization(suggestion)}
                    size="sm"
                  />
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getFairnessColor(suggestion.fairness)}`}>
                    {suggestion.fairness.replace('_', ' ')}
                  </span>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {Math.round(suggestion.acceptance_probability * 100)}% likely
                    </div>
                    <div className="text-xs text-gray-500">acceptance</div>
                  </div>
                  {suggestion.recommendation_id && (
                    <WhyButton
                      recommendationId={suggestion.recommendation_id}
                      onExplain={handleExplain(suggestion.recommendation_id)}
                      variant="icon"
                      size="sm"
                    />
                  )}
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-6 mb-4">
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">You Give:</h4>
                  <div className="space-y-2">
                    {suggestion.team_a_gives.map((playerId) => (
                      <div key={playerId} className="flex items-center space-x-2 p-2 bg-red-50 rounded">
                        <span className="text-red-600">→</span>
                        <span className="text-sm font-medium">{playerId}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">You Receive:</h4>
                  <div className="space-y-2">
                    {suggestion.team_a_receives.map((playerId) => (
                      <div key={playerId} className="flex items-center space-x-2 p-2 bg-green-50 rounded">
                        <span className="text-green-600">←</span>
                        <span className="text-sm font-medium">{playerId}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-6 mb-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">Impact on Your Team:</h4>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm font-medium ${
                      suggestion.win_probability_impact_a > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {suggestion.win_probability_impact_a > 0 ? '+' : ''}
                      {(suggestion.win_probability_impact_a * 100).toFixed(1)}%
                    </span>
                    <span className="text-sm text-gray-600">win probability</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">Fairness Score:</h4>
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          Math.abs(suggestion.fairness_score) < 0.2 ? 'bg-green-500' :
                          Math.abs(suggestion.fairness_score) < 0.4 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${Math.min(100, (1 - Math.abs(suggestion.fairness_score)) * 100)}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600">
                      {(suggestion.fairness_score * 100).toFixed(0)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium text-gray-900 mb-2">Analysis:</h4>
                <p className="text-sm text-gray-700">{suggestion.rationale}</p>
              </div>

              <div className="flex justify-end space-x-3 mt-4">
                <button className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  View Details
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  Propose Trade
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}