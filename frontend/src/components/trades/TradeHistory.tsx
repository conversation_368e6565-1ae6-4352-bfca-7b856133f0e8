'use client'

import { useState, useEffect } from 'react'
import { TradeAPI } from '@/lib/trade-api'
import { TradeHistoryItem } from '@/types/trades'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'

interface TradeHistoryProps {
  franchiseId: string
  leagueId: string
}

export function TradeHistory({ franchiseId, leagueId }: TradeHistoryProps) {
  const [history, setHistory] = useState<TradeHistoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<'all' | 'completed' | 'rejected' | 'expired'>('all')

  useEffect(() => {
    loadTradeHistory()
  }, [franchiseId])

  const loadTradeHistory = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await TradeAPI.getTradeHistory(franchiseId)
      setHistory(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load trade history')
    } finally {
      setLoading(false)
    }
  }

  const getOutcomeColor = (outcome: string) => {
    switch (outcome) {
      case 'completed': return 'text-green-600 bg-green-100'
      case 'rejected': return 'text-red-600 bg-red-100'
      case 'expired': return 'text-gray-600 bg-gray-100'
      default: return 'text-blue-600 bg-blue-100'
    }
  }

  const getOutcomeIcon = (outcome: string) => {
    switch (outcome) {
      case 'completed': return '✅'
      case 'rejected': return '❌'
      case 'expired': return '⏰'
      default: return '📋'
    }
  }

  const getFairnessColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600'
    if (score >= 0.6) return 'text-blue-600'
    if (score >= 0.4) return 'text-yellow-600'
    return 'text-red-600'
  }

  const filteredHistory = history.filter(item => 
    filter === 'all' || item.outcome === filter
  )

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return <ErrorMessage message={error} />
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Trade History</h2>
        <div className="flex space-x-2">
          {['all', 'completed', 'rejected', 'expired'].map((filterOption) => (
            <button
              key={filterOption}
              onClick={() => setFilter(filterOption as any)}
              className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                filter === filterOption
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {filteredHistory.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <div className="text-4xl mb-4">📋</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Trade History</h3>
          <p className="text-gray-600">
            {filter === 'all' 
              ? "You haven't made any trades yet. Start by exploring trade suggestions!"
              : `No ${filter} trades found. Try a different filter.`
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredHistory.map((trade) => (
            <div key={trade.id} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getOutcomeIcon(trade.outcome)}</span>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Trade with {trade.team_b}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {new Date(trade.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getOutcomeColor(trade.outcome)}`}>
                    {trade.outcome}
                  </span>
                  <div className="text-right">
                    <div className={`text-sm font-medium ${getFairnessColor(trade.fairness_score)}`}>
                      {(trade.fairness_score * 100).toFixed(0)}%
                    </div>
                    <div className="text-xs text-gray-500">fairness</div>
                  </div>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-6 mb-4">
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">You Gave:</h4>
                  <div className="space-y-2">
                    {trade.players_traded.team_a_gave.map((playerId, index) => (
                      <div key={index} className="flex items-center space-x-2 p-2 bg-red-50 rounded">
                        <span className="text-red-600">→</span>
                        <span className="text-sm font-medium">{playerId}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">You Received:</h4>
                  <div className="space-y-2">
                    {trade.players_traded.team_b_gave.map((playerId, index) => (
                      <div key={index} className="flex items-center space-x-2 p-2 bg-green-50 rounded">
                        <span className="text-green-600">←</span>
                        <span className="text-sm font-medium">{playerId}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {trade.notes && (
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Notes:</h4>
                  <p className="text-sm text-gray-700">{trade.notes}</p>
                </div>
              )}

              <div className="flex justify-end space-x-3 mt-4 pt-4 border-t">
                <button className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  View Details
                </button>
                {trade.outcome === 'completed' && (
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Analyze Impact
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Trade Statistics */}
      {history.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Trade Statistics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">{history.length}</div>
              <div className="text-sm text-gray-600">Total Trades</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {history.filter(t => t.outcome === 'completed').length}
              </div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {history.filter(t => t.outcome === 'rejected').length}
              </div>
              <div className="text-sm text-gray-600">Rejected</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {history.length > 0 ? (
                  (history.reduce((sum, t) => sum + t.fairness_score, 0) / history.length * 100).toFixed(0)
                ) : 0}%
              </div>
              <div className="text-sm text-gray-600">Avg Fairness</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}