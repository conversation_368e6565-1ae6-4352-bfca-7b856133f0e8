'use client'

import { useState } from 'react'
import { TradeAPI } from '@/lib/trade-api'
import { TradeProposal as TradeProposalType, TradeEvaluationRequest } from '@/types/trades'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import WhyButton from '@/components/ui/WhyButton'
import ConfidenceIndicator from '@/components/ui/ConfidenceIndicator'
import { ExplanationAPI } from '@/lib/explanation-api'
import { RecommendationExplanation, ExplanationRequest, UncertaintyVisualization } from '@/types/explanation'

interface TradeProposalProps {
  franchiseId: string
  leagueId: string
}

export function TradeProposal({ franchiseId, leagueId }: TradeProposalProps) {
  const [evaluation, setEvaluation] = useState<TradeProposalType | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const [formData, setFormData] = useState({
    teamBId: '',
    teamAGives: [''],
    teamBGives: [''],
    season: 2024
  })

  const handleAddPlayer = (team: 'A' | 'B') => {
    const field = team === 'A' ? 'teamAGives' : 'teamBGives'
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }))
  }

  const handleRemovePlayer = (team: 'A' | 'B', index: number) => {
    const field = team === 'A' ? 'teamAGives' : 'teamBGives'
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }))
  }

  const handlePlayerChange = (team: 'A' | 'B', index: number, value: string) => {
    const field = team === 'A' ? 'teamAGives' : 'teamBGives'
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((player, i) => i === index ? value : player)
    }))
  }

  const handleEvaluate = async () => {
    if (!formData.teamBId || formData.teamAGives.some(p => !p) || formData.teamBGives.some(p => !p)) {
      setError('Please fill in all required fields')
      return
    }

    try {
      setLoading(true)
      setError(null)
      
      const request: TradeEvaluationRequest = {
        team_a_id: franchiseId,
        team_b_id: formData.teamBId,
        team_a_gives: formData.teamAGives.filter(p => p),
        team_b_gives: formData.teamBGives.filter(p => p),
        season: formData.season
      }
      
      const result = await TradeAPI.evaluateTrade(request)
      setEvaluation(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to evaluate trade')
    } finally {
      setLoading(false)
    }
  }

  const getFairnessColor = (fairness: string) => {
    switch (fairness) {
      case 'very_fair': return 'text-green-600 bg-green-100'
      case 'fair': return 'text-green-500 bg-green-50'
      case 'slightly_unfair': return 'text-yellow-600 bg-yellow-100'
      case 'unfair': return 'text-orange-600 bg-orange-100'
      case 'very_unfair': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const handleExplain = async (request: ExplanationRequest): Promise<RecommendationExplanation> => {
    if (!evaluation?.recommendation_id) {
      throw new Error('No recommendation ID available')
    }
    return ExplanationAPI.getExplanation(evaluation.recommendation_id, request)
  }

  const getUncertaintyVisualization = (evaluation: TradeProposalType): UncertaintyVisualization => {
    const fairnessComponent = 1 - Math.min(1, Math.max(0, Math.abs(evaluation.fairness_score)))
    const ap = Math.min(1, Math.max(0, evaluation.acceptance_probability))
    const confidence = Math.min(1, Math.max(0, 0.6 * ap + 0.4 * fairnessComponent))
    return {
      confidence_level: confidence > 0.8 ? 'high' : confidence > 0.6 ? 'medium' : 'low',
      confidence_score: confidence,
      uncertainty_range: [
        evaluation.acceptance_probability * 0.8,
        evaluation.acceptance_probability * 1.2
      ],
      risk_factors: [
        evaluation.fairness === 'unfair' || evaluation.fairness === 'very_unfair' ? 'Trade fairness concerns' : '',
        Math.abs(evaluation.win_probability_impact_a) < 0.02 ? 'Minimal impact on win probability' : '',
        'Market conditions may change'
      ].filter(Boolean),
      confidence_indicators: [
        {
          indicator: 'Trade Fairness',
          status: evaluation.fairness === 'fair' || evaluation.fairness === 'very_fair' ? 'good' : 'warning',
          description: 'How fair the trade is for both parties'
        },
        {
          indicator: 'Win Probability Impact',
          status: Math.abs(evaluation.win_probability_impact_a) > 0.05 ? 'good' : 'warning',
          description: 'Expected impact on your win probability'
        },
        {
          indicator: 'Acceptance Likelihood',
          status: evaluation.acceptance_probability > 0.6 ? 'good' : 'poor',
          description: 'Likelihood the other team accepts'
        }
      ]
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Evaluate Trade Proposal</h2>
        <p className="text-gray-600">
          Enter the details of a potential trade to get a comprehensive analysis
        </p>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="space-y-6">
          {/* Trading Partner */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trading Partner
            </label>
            <input
              type="text"
              value={formData.teamBId}
              onChange={(e) => setFormData(prev => ({ ...prev, teamBId: e.target.value }))}
              placeholder="Enter team/franchise ID"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {/* Your Players */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">You Give</h3>
                <button
                  onClick={() => handleAddPlayer('A')}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  + Add Player
                </button>
              </div>
              
              <div className="space-y-3">
                {formData.teamAGives.map((player, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={player}
                      onChange={(e) => handlePlayerChange('A', index, e.target.value)}
                      placeholder="Enter player ID or name"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    {formData.teamAGives.length > 1 && (
                      <button
                        onClick={() => handleRemovePlayer('A', index)}
                        className="text-red-600 hover:text-red-700 p-1"
                      >
                        ✕
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Their Players */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">You Receive</h3>
                <button
                  onClick={() => handleAddPlayer('B')}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  + Add Player
                </button>
              </div>
              
              <div className="space-y-3">
                {formData.teamBGives.map((player, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={player}
                      onChange={(e) => handlePlayerChange('B', index, e.target.value)}
                      placeholder="Enter player ID or name"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    {formData.teamBGives.length > 1 && (
                      <button
                        onClick={() => handleRemovePlayer('B', index)}
                        className="text-red-600 hover:text-red-700 p-1"
                      >
                        ✕
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {error && <ErrorMessage message={error} />}

          <div className="flex justify-end">
            <button
              onClick={handleEvaluate}
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? <LoadingSpinner size="sm" /> : 'Evaluate Trade'}
            </button>
          </div>
        </div>
      </div>

      {/* Evaluation Results */}
      {evaluation && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">Trade Evaluation Results</h3>
            <div className="flex items-center space-x-3">
              <ConfidenceIndicator
                confidence={getUncertaintyVisualization(evaluation).confidence_score}
                uncertainty={getUncertaintyVisualization(evaluation)}
                showDetails={true}
                size="sm"
              />
              {evaluation.recommendation_id && (
                <WhyButton
                  recommendationId={evaluation.recommendation_id}
                  onExplain={handleExplain}
                  variant="button"
                  size="sm"
                />
              )}
            </div>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6 mb-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">
                {Math.round(evaluation.acceptance_probability * 100)}%
              </div>
              <div className="text-sm text-gray-600">Acceptance Probability</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className={`text-2xl font-bold ${
                evaluation.win_probability_impact_a > 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {evaluation.win_probability_impact_a > 0 ? '+' : ''}
                {(evaluation.win_probability_impact_a * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Your Win Probability Change</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getFairnessColor(evaluation.fairness)}`}>
                {evaluation.fairness.replace('_', ' ')}
              </span>
              <div className="text-sm text-gray-600 mt-1">Trade Fairness</div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Trade Analysis:</h4>
              <p className="text-gray-700">{evaluation.rationale}</p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Impact on Your Team:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Win Probability:</span>
                    <span className={evaluation.win_probability_impact_a > 0 ? 'text-green-600' : 'text-red-600'}>
                      {evaluation.win_probability_impact_a > 0 ? '+' : ''}
                      {(evaluation.win_probability_impact_a * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Impact on Their Team:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Win Probability:</span>
                    <span className={evaluation.win_probability_impact_b > 0 ? 'text-green-600' : 'text-red-600'}>
                      {evaluation.win_probability_impact_b > 0 ? '+' : ''}
                      {(evaluation.win_probability_impact_b * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                Save Analysis
              </button>
              <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                Propose Trade
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}