'use client'

import { useState, useEffect } from 'react'
import { TradeAPI } from '@/lib/trade-api'
import { TeamAnalysis } from '@/types/trades'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'

interface TeamNeedAnalysisProps {
  franchiseId: string
  leagueId: string
}

export function TeamNeedAnalysis({ franchiseId, leagueId }: TeamNeedAnalysisProps) {
  const [teamAnalysis, setTeamAnalysis] = useState<TeamAnalysis | null>(null)
  const [leagueAnalysis, setLeagueAnalysis] = useState<Record<string, TeamAnalysis>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeView, setActiveView] = useState<'my-team' | 'league'>('my-team')

  useEffect(() => {
    loadAnalysis()
  }, [franchiseId, leagueId])

  const loadAnalysis = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const [teamData, leagueData] = await Promise.all([
        TradeAPI.getTeamAnalysis(franchiseId),
        TradeAPI.analyzeLeagueTeams(leagueId)
      ])
      
      setTeamAnalysis(teamData)
      setLeagueAnalysis(leagueData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load team analysis')
    } finally {
      setLoading(false)
    }
  }

  const getNeedLevelColor = (level: number) => {
    if (level >= 0.8) return 'bg-red-500'
    if (level >= 0.6) return 'bg-orange-500'
    if (level >= 0.4) return 'bg-yellow-500'
    if (level >= 0.2) return 'bg-blue-500'
    return 'bg-green-500'
  }

  const getNeedLevelText = (level: number) => {
    if (level >= 0.8) return 'Critical'
    if (level >= 0.6) return 'High'
    if (level >= 0.4) return 'Moderate'
    if (level >= 0.2) return 'Low'
    return 'None'
  }

  const getSurplusLevelColor = (level: number) => {
    if (level >= 0.8) return 'bg-green-500'
    if (level >= 0.6) return 'bg-blue-500'
    if (level >= 0.4) return 'bg-yellow-500'
    return 'bg-gray-400'
  }

  const renderPositionAnalysis = (analysis: TeamAnalysis) => {
    const positions = ['QB', 'RB', 'WR', 'TE', 'K', 'DST']
    
    return (
      <div className="space-y-6">
        {/* Needs */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-4">Position Needs</h4>
          <div className="grid gap-4">
            {positions.map(position => {
              const need = analysis.needs[position]
              if (!need) return null
              
              return (
                <div key={position} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="font-medium text-gray-900">{position}</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium text-white ${getNeedLevelColor(need.need_level)}`}>
                        {getNeedLevelText(need.need_level)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {need.current_strength.toFixed(1)} pts
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-gray-600">Depth Score</div>
                      <div className="font-medium">{(need.depth_score * 100).toFixed(0)}%</div>
                    </div>
                    <div>
                      <div className="text-gray-600">Injury Risk</div>
                      <div className="font-medium">{(need.injury_risk * 100).toFixed(0)}%</div>
                    </div>
                    <div>
                      <div className="text-gray-600">Bye Coverage</div>
                      <div className="font-medium">{(need.bye_week_coverage * 100).toFixed(0)}%</div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Surpluses */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-4">Position Surpluses</h4>
          <div className="grid gap-4">
            {positions.map(position => {
              const surplus = analysis.surpluses[position]
              if (!surplus) return null
              
              return (
                <div key={position} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="font-medium text-gray-900">{position}</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium text-white ${getSurplusLevelColor(surplus.surplus_level)}`}>
                        {(surplus.surplus_level * 100).toFixed(0)}% surplus
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {surplus.surplus_value.toFixed(1)} surplus pts
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm">
                      <span className="text-gray-600">Tradeable Players: </span>
                      <span className="font-medium">{surplus.tradeable_players.length}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">Depth Quality: </span>
                      <span className="font-medium">{(surplus.depth_quality * 100).toFixed(0)}%</span>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return <ErrorMessage message={error} />
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Team Need Analysis</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => setActiveView('my-team')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              activeView === 'my-team'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            My Team
          </button>
          <button
            onClick={() => setActiveView('league')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              activeView === 'league'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            League Overview
          </button>
        </div>
      </div>

      {activeView === 'my-team' && teamAnalysis && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="mb-6">
            <h3 className="text-xl font-bold text-gray-900 mb-2">{teamAnalysis.franchise_name}</h3>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">
                  {teamAnalysis.overall_strength.toFixed(1)}
                </div>
                <div className="text-sm text-gray-600">Overall Strength</div>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">
                  {(teamAnalysis.win_probability * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">Win Probability</div>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">
                  {(teamAnalysis.trade_urgency * 100).toFixed(0)}%
                </div>
                <div className="text-sm text-gray-600">Trade Urgency</div>
              </div>
            </div>
          </div>
          
          {renderPositionAnalysis(teamAnalysis)}
        </div>
      )}

      {activeView === 'league' && (
        <div className="space-y-4">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4">League Trade Landscape</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Team</th>
                    <th className="text-center py-2">Strength</th>
                    <th className="text-center py-2">Win %</th>
                    <th className="text-center py-2">Trade Urgency</th>
                    <th className="text-center py-2">Top Need</th>
                    <th className="text-center py-2">Top Surplus</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.values(leagueAnalysis).map((team) => {
                    const topNeed = Object.entries(team.needs).reduce((max, [pos, need]) => 
                      need.need_level > (max?.need_level || 0) ? { position: pos, ...need } : max, 
                      null as any
                    )
                    const topSurplus = Object.entries(team.surpluses).reduce((max, [pos, surplus]) => 
                      surplus.surplus_level > (max?.surplus_level || 0) ? { position: pos, ...surplus } : max, 
                      null as any
                    )
                    
                    return (
                      <tr key={team.franchise_id} className="border-b hover:bg-gray-50">
                        <td className="py-3 font-medium">{team.franchise_name}</td>
                        <td className="py-3 text-center">{team.overall_strength.toFixed(1)}</td>
                        <td className="py-3 text-center">{(team.win_probability * 100).toFixed(1)}%</td>
                        <td className="py-3 text-center">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            team.trade_urgency > 0.7 ? 'bg-red-100 text-red-800' :
                            team.trade_urgency > 0.4 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {(team.trade_urgency * 100).toFixed(0)}%
                          </span>
                        </td>
                        <td className="py-3 text-center">
                          {topNeed ? (
                            <span className={`px-2 py-1 rounded text-xs font-medium text-white ${getNeedLevelColor(topNeed.need_level)}`}>
                              {topNeed.position}
                            </span>
                          ) : '-'}
                        </td>
                        <td className="py-3 text-center">
                          {topSurplus ? (
                            <span className={`px-2 py-1 rounded text-xs font-medium text-white ${getSurplusLevelColor(topSurplus.surplus_level)}`}>
                              {topSurplus.position}
                            </span>
                          ) : '-'}
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}