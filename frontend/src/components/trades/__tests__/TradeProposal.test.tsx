import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { TradeProposal } from '../TradeProposal'
import { TradeAPI } from '@/lib/trade-api'

// Mock the TradeAPI
jest.mock('@/lib/trade-api')
const mockTradeAPI = TradeAPI as jest.Mocked<typeof TradeAPI>

// Mock UI components
jest.mock('@/components/ui/LoadingSpinner', () => {
  return function MockLoadingSpinner({ size }: { size?: string }) {
    return <div data-testid="loading-spinner">Loading {size || 'default'}</div>
  }
})

jest.mock('@/components/ui/ErrorMessage', () => {
  return function MockErrorMessage({ message }: { message: string }) {
    return <div data-testid="error-message">{message}</div>
  }
})

const mockTradeEvaluation = {
  trade_id: 'trade_123',
  team_a_id: 'franchise_1',
  team_b_id: 'franchise_2',
  team_a_gives: ['player_1'],
  team_a_receives: ['player_2'],
  team_b_gives: ['player_2'],
  team_b_receives: ['player_1'],
  trade_type: 'need_based',
  fairness: 'fair',
  fairness_score: 0.1,
  acceptance_probability: 0.75,
  win_probability_impact_a: 0.05,
  win_probability_impact_b: 0.03,
  rationale: 'This trade addresses positional needs for both teams.',
  metadata: {}
}

describe('TradeProposal', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders trade proposal form', () => {
    render(<TradeProposal franchiseId="franchise_1" leagueId="league_1" />)
    
    expect(screen.getByText('Evaluate Trade Proposal')).toBeInTheDocument()
    expect(screen.getByText('Enter the details of a potential trade to get a comprehensive analysis')).toBeInTheDocument()
    
    // Check form fields
    expect(screen.getByLabelText('Trading Partner')).toBeInTheDocument()
    expect(screen.getByText('You Give')).toBeInTheDocument()
    expect(screen.getByText('You Receive')).toBeInTheDocument()
    expect(screen.getByText('Evaluate Trade')).toBeInTheDocument()
  })

  it('allows adding and removing players', () => {
    render(<TradeProposal franchiseId="franchise_1" leagueId="league_1" />)
    
    // Initially should have one input for each team
    expect(screen.getAllByPlaceholderText('Enter player ID or name')).toHaveLength(2)
    
    // Add player to "You Give"
    const addPlayerButtons = screen.getAllByText('+ Add Player')
    fireEvent.click(addPlayerButtons[0])
    
    expect(screen.getAllByPlaceholderText('Enter player ID or name')).toHaveLength(3)
    
    // Add player to "You Receive"
    fireEvent.click(addPlayerButtons[1])
    
    expect(screen.getAllByPlaceholderText('Enter player ID or name')).toHaveLength(4)
    
    // Remove a player (X buttons should appear when there are multiple players)
    const removeButtons = screen.getAllByText('✕')
    expect(removeButtons.length).toBeGreaterThan(0)
    
    fireEvent.click(removeButtons[0])
    expect(screen.getAllByPlaceholderText('Enter player ID or name')).toHaveLength(3)
  })

  it('validates form before submission', async () => {
    render(<TradeProposal franchiseId="franchise_1" leagueId="league_1" />)
    
    const evaluateButton = screen.getByText('Evaluate Trade')
    fireEvent.click(evaluateButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument()
    })
    
    expect(screen.getByText('Please fill in all required fields')).toBeInTheDocument()
    expect(mockTradeAPI.evaluateTrade).not.toHaveBeenCalled()
  })

  it('submits trade evaluation successfully', async () => {
    mockTradeAPI.evaluateTrade.mockResolvedValue(mockTradeEvaluation)
    
    render(<TradeProposal franchiseId="franchise_1" leagueId="league_1" />)
    
    // Fill in the form
    const tradingPartnerInput = screen.getByLabelText('Trading Partner')
    fireEvent.change(tradingPartnerInput, { target: { value: 'franchise_2' } })
    
    const playerInputs = screen.getAllByPlaceholderText('Enter player ID or name')
    fireEvent.change(playerInputs[0], { target: { value: 'player_1' } })
    fireEvent.change(playerInputs[1], { target: { value: 'player_2' } })
    
    const evaluateButton = screen.getByText('Evaluate Trade')
    fireEvent.click(evaluateButton)
    
    await waitFor(() => {
      expect(mockTradeAPI.evaluateTrade).toHaveBeenCalledWith({
        team_a_id: 'franchise_1',
        team_b_id: 'franchise_2',
        team_a_gives: ['player_1'],
        team_b_gives: ['player_2'],
        season: 2024
      })
    })
  })

  it('displays evaluation results', async () => {
    mockTradeAPI.evaluateTrade.mockResolvedValue(mockTradeEvaluation)
    
    render(<TradeProposal franchiseId="franchise_1" leagueId="league_1" />)
    
    // Fill and submit form
    const tradingPartnerInput = screen.getByLabelText('Trading Partner')
    fireEvent.change(tradingPartnerInput, { target: { value: 'franchise_2' } })
    
    const playerInputs = screen.getAllByPlaceholderText('Enter player ID or name')
    fireEvent.change(playerInputs[0], { target: { value: 'player_1' } })
    fireEvent.change(playerInputs[1], { target: { value: 'player_2' } })
    
    const evaluateButton = screen.getByText('Evaluate Trade')
    fireEvent.click(evaluateButton)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Evaluation Results')).toBeInTheDocument()
    })
    
    // Check evaluation metrics
    expect(screen.getByText('75%')).toBeInTheDocument() // Acceptance probability
    expect(screen.getByText('+5.0%')).toBeInTheDocument() // Win probability change
    expect(screen.getByText('fair')).toBeInTheDocument() // Fairness
    
    // Check rationale
    expect(screen.getByText('This trade addresses positional needs for both teams.')).toBeInTheDocument()
    
    // Check action buttons
    expect(screen.getByText('Save Analysis')).toBeInTheDocument()
    expect(screen.getByText('Propose Trade')).toBeInTheDocument()
  })

  it('handles API errors during evaluation', async () => {
    mockTradeAPI.evaluateTrade.mockRejectedValue(new Error('Evaluation failed'))
    
    render(<TradeProposal franchiseId="franchise_1" leagueId="league_1" />)
    
    // Fill and submit form
    const tradingPartnerInput = screen.getByLabelText('Trading Partner')
    fireEvent.change(tradingPartnerInput, { target: { value: 'franchise_2' } })
    
    const playerInputs = screen.getAllByPlaceholderText('Enter player ID or name')
    fireEvent.change(playerInputs[0], { target: { value: 'player_1' } })
    fireEvent.change(playerInputs[1], { target: { value: 'player_2' } })
    
    const evaluateButton = screen.getByText('Evaluate Trade')
    fireEvent.click(evaluateButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument()
    })
    
    expect(screen.getByText('Evaluation failed')).toBeInTheDocument()
  })

  it('shows loading state during evaluation', async () => {
    mockTradeAPI.evaluateTrade.mockImplementation(() => new Promise(() => {}))
    
    render(<TradeProposal franchiseId="franchise_1" leagueId="league_1" />)
    
    // Fill and submit form
    const tradingPartnerInput = screen.getByLabelText('Trading Partner')
    fireEvent.change(tradingPartnerInput, { target: { value: 'franchise_2' } })
    
    const playerInputs = screen.getAllByPlaceholderText('Enter player ID or name')
    fireEvent.change(playerInputs[0], { target: { value: 'player_1' } })
    fireEvent.change(playerInputs[1], { target: { value: 'player_2' } })
    
    const evaluateButton = screen.getByText('Evaluate Trade')
    fireEvent.click(evaluateButton)
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    expect(evaluateButton).toBeDisabled()
  })

  it('updates player inputs correctly', () => {
    render(<TradeProposal franchiseId="franchise_1" leagueId="league_1" />)
    
    const playerInputs = screen.getAllByPlaceholderText('Enter player ID or name')
    
    fireEvent.change(playerInputs[0], { target: { value: 'test_player_1' } })
    fireEvent.change(playerInputs[1], { target: { value: 'test_player_2' } })
    
    expect(playerInputs[0]).toHaveValue('test_player_1')
    expect(playerInputs[1]).toHaveValue('test_player_2')
  })

  it('displays correct fairness colors in results', async () => {
    const veryFairEvaluation = { ...mockTradeEvaluation, fairness: 'very_fair' }
    mockTradeAPI.evaluateTrade.mockResolvedValue(veryFairEvaluation)
    
    render(<TradeProposal franchiseId="franchise_1" leagueId="league_1" />)
    
    // Fill and submit form
    const tradingPartnerInput = screen.getByLabelText('Trading Partner')
    fireEvent.change(tradingPartnerInput, { target: { value: 'franchise_2' } })
    
    const playerInputs = screen.getAllByPlaceholderText('Enter player ID or name')
    fireEvent.change(playerInputs[0], { target: { value: 'player_1' } })
    fireEvent.change(playerInputs[1], { target: { value: 'player_2' } })
    
    const evaluateButton = screen.getByText('Evaluate Trade')
    fireEvent.click(evaluateButton)
    
    await waitFor(() => {
      expect(screen.getByText('very fair')).toBeInTheDocument()
    })
  })
})