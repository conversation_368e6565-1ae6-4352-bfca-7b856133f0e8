import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { TradeHistory } from '../TradeHistory'
import { TradeAPI } from '@/lib/trade-api'

// Mock the TradeAPI
jest.mock('@/lib/trade-api')
const mockTradeAPI = TradeAPI as jest.Mocked<typeof TradeAPI>

// Mock UI components
jest.mock('@/components/ui/LoadingSpinner', () => {
  return function MockLoadingSpinner() {
    return <div data-testid="loading-spinner">Loading...</div>
  }
})

jest.mock('@/components/ui/ErrorMessage', () => {
  return function MockErrorMessage({ message }: { message: string }) {
    return <div data-testid="error-message">{message}</div>
  }
})

const mockTradeHistory = [
  {
    id: '1',
    date: '2024-10-15',
    team_a: 'Team Alpha',
    team_b: 'Team Beta',
    players_traded: {
      team_a_gave: ['player_1', 'player_2'],
      team_b_gave: ['player_3']
    },
    fairness_score: 0.85,
    outcome: 'completed' as const,
    notes: 'Successful trade that helped both teams'
  },
  {
    id: '2',
    date: '2024-10-10',
    team_a: 'Team Alpha',
    team_b: 'Team Gamma',
    players_traded: {
      team_a_gave: ['player_4'],
      team_b_gave: ['player_5', 'player_6']
    },
    fairness_score: 0.65,
    outcome: 'rejected' as const,
    notes: 'Trade was rejected by Team Gamma'
  },
  {
    id: '3',
    date: '2024-10-05',
    team_a: 'Team Alpha',
    team_b: 'Team Delta',
    players_traded: {
      team_a_gave: ['player_7'],
      team_b_gave: ['player_8']
    },
    fairness_score: 0.45,
    outcome: 'expired' as const
  }
]

describe('TradeHistory', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders loading state initially', () => {
    mockTradeAPI.getTradeHistory.mockImplementation(() => new Promise(() => {}))
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('renders trade history successfully', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue(mockTradeHistory)
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade History')).toBeInTheDocument()
    })

    // Check that trades are displayed
    expect(screen.getByText('Trade with Team Beta')).toBeInTheDocument()
    expect(screen.getByText('Trade with Team Gamma')).toBeInTheDocument()
    expect(screen.getByText('Trade with Team Delta')).toBeInTheDocument()
  })

  it('renders empty state when no history available', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue([])
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('No Trade History')).toBeInTheDocument()
    })

    expect(screen.getByText("You haven't made any trades yet. Start by exploring trade suggestions!")).toBeInTheDocument()
  })

  it('filters trades by outcome', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue(mockTradeHistory)
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade History')).toBeInTheDocument()
    })

    // Initially all trades should be visible
    expect(screen.getByText('Trade with Team Beta')).toBeInTheDocument()
    expect(screen.getByText('Trade with Team Gamma')).toBeInTheDocument()
    expect(screen.getByText('Trade with Team Delta')).toBeInTheDocument()

    // Filter by completed
    fireEvent.click(screen.getByText('Completed'))
    expect(screen.getByText('Trade with Team Beta')).toBeInTheDocument()
    expect(screen.queryByText('Trade with Team Gamma')).not.toBeInTheDocument()
    expect(screen.queryByText('Trade with Team Delta')).not.toBeInTheDocument()

    // Filter by rejected
    fireEvent.click(screen.getByText('Rejected'))
    expect(screen.queryByText('Trade with Team Beta')).not.toBeInTheDocument()
    expect(screen.getByText('Trade with Team Gamma')).toBeInTheDocument()
    expect(screen.queryByText('Trade with Team Delta')).not.toBeInTheDocument()

    // Filter by expired
    fireEvent.click(screen.getByText('Expired'))
    expect(screen.queryByText('Trade with Team Beta')).not.toBeInTheDocument()
    expect(screen.queryByText('Trade with Team Gamma')).not.toBeInTheDocument()
    expect(screen.getByText('Trade with Team Delta')).toBeInTheDocument()

    // Back to all
    fireEvent.click(screen.getByText('All'))
    expect(screen.getByText('Trade with Team Beta')).toBeInTheDocument()
    expect(screen.getByText('Trade with Team Gamma')).toBeInTheDocument()
    expect(screen.getByText('Trade with Team Delta')).toBeInTheDocument()
  })

  it('displays correct outcome icons and colors', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue(mockTradeHistory)
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade History')).toBeInTheDocument()
    })

    // Check outcome badges
    expect(screen.getByText('completed')).toBeInTheDocument()
    expect(screen.getByText('rejected')).toBeInTheDocument()
    expect(screen.getByText('expired')).toBeInTheDocument()

    // Check that completed trade has green styling
    const completedBadge = screen.getByText('completed')
    expect(completedBadge).toHaveClass('text-green-600', 'bg-green-100')

    // Check that rejected trade has red styling
    const rejectedBadge = screen.getByText('rejected')
    expect(rejectedBadge).toHaveClass('text-red-600', 'bg-red-100')
  })

  it('displays fairness scores with correct colors', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue(mockTradeHistory)
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade History')).toBeInTheDocument()
    })

    // Check fairness scores
    expect(screen.getByText('85%')).toBeInTheDocument() // High fairness - green
    expect(screen.getByText('65%')).toBeInTheDocument() // Medium fairness - blue
    expect(screen.getByText('45%')).toBeInTheDocument() // Low fairness - red
  })

  it('displays player lists correctly', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue(mockTradeHistory)
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade History')).toBeInTheDocument()
    })

    // Check that all players are displayed
    expect(screen.getByText('player_1')).toBeInTheDocument()
    expect(screen.getByText('player_2')).toBeInTheDocument()
    expect(screen.getByText('player_3')).toBeInTheDocument()
    expect(screen.getByText('player_4')).toBeInTheDocument()
    expect(screen.getByText('player_5')).toBeInTheDocument()
    expect(screen.getByText('player_6')).toBeInTheDocument()
    expect(screen.getByText('player_7')).toBeInTheDocument()
    expect(screen.getByText('player_8')).toBeInTheDocument()
  })

  it('displays trade notes when available', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue(mockTradeHistory)
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade History')).toBeInTheDocument()
    })

    // Check that notes are displayed
    expect(screen.getByText('Successful trade that helped both teams')).toBeInTheDocument()
    expect(screen.getByText('Trade was rejected by Team Gamma')).toBeInTheDocument()
  })

  it('displays trade statistics correctly', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue(mockTradeHistory)
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Statistics')).toBeInTheDocument()
    })

    // Check statistics
    expect(screen.getByText('3')).toBeInTheDocument() // Total trades
    expect(screen.getByText('1')).toBeInTheDocument() // Completed trades
    expect(screen.getByText('1')).toBeInTheDocument() // Rejected trades
    
    // Check average fairness (85 + 65 + 45) / 3 = 65%
    expect(screen.getByText('65%')).toBeInTheDocument()
  })

  it('handles API errors gracefully', async () => {
    mockTradeAPI.getTradeHistory.mockRejectedValue(new Error('Failed to load history'))
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument()
    })

    expect(screen.getByText('Failed to load history')).toBeInTheDocument()
  })

  it('formats dates correctly', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue(mockTradeHistory)
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade History')).toBeInTheDocument()
    })

    // Check formatted dates
    expect(screen.getByText('October 15, 2024')).toBeInTheDocument()
    expect(screen.getByText('October 10, 2024')).toBeInTheDocument()
    expect(screen.getByText('October 5, 2024')).toBeInTheDocument()
  })

  it('shows different action buttons based on trade outcome', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue(mockTradeHistory)
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade History')).toBeInTheDocument()
    })

    // All trades should have "View Details" button
    const viewDetailsButtons = screen.getAllByText('View Details')
    expect(viewDetailsButtons).toHaveLength(3)

    // Only completed trades should have "Analyze Impact" button
    const analyzeImpactButtons = screen.getAllByText('Analyze Impact')
    expect(analyzeImpactButtons).toHaveLength(1)
  })

  it('applies correct filter styling', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue(mockTradeHistory)
    
    render(<TradeHistory franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade History')).toBeInTheDocument()
    })

    const allFilter = screen.getByText('All')
    const completedFilter = screen.getByText('Completed')

    // Initially "All" should be active
    expect(allFilter).toHaveClass('bg-blue-600', 'text-white')
    expect(completedFilter).toHaveClass('bg-gray-200', 'text-gray-700')

    // Click "Completed"
    fireEvent.click(completedFilter)

    // Now "Completed" should be active
    expect(completedFilter).toHaveClass('bg-blue-600', 'text-white')
    expect(allFilter).toHaveClass('bg-gray-200', 'text-gray-700')
  })

  it('calls API with correct parameters', async () => {
    mockTradeAPI.getTradeHistory.mockResolvedValue([])
    
    render(<TradeHistory franchiseId="test_franchise" leagueId="test_league" />)
    
    await waitFor(() => {
      expect(mockTradeAPI.getTradeHistory).toHaveBeenCalledWith('test_franchise')
    })
  })
})