import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { TradeAnalysis } from '../TradeAnalysis'
import { TradeAPI } from '@/lib/trade-api'

// Mock the TradeAPI
jest.mock('@/lib/trade-api')
const mockTradeAPI = TradeAPI as jest.Mocked<typeof TradeAPI>

// Mock UI components
jest.mock('@/components/ui/LoadingSpinner', () => {
  return function MockLoadingSpinner() {
    return <div data-testid="loading-spinner">Loading...</div>
  }
})

jest.mock('@/components/ui/ErrorMessage', () => {
  return function MockErrorMessage({ message }: { message: string }) {
    return <div data-testid="error-message">{message}</div>
  }
})

const mockTradeProposals = [
  {
    trade_id: 'trade_1',
    team_a_id: 'franchise_1',
    team_b_id: 'Team Beta',
    team_a_gives: ['player_1', 'player_2'],
    team_a_receives: ['player_3'],
    team_b_gives: ['player_3'],
    team_b_receives: ['player_1', 'player_2'],
    trade_type: 'need_based',
    fairness: 'fair',
    fairness_score: 0.1,
    acceptance_probability: 0.75,
    win_probability_impact_a: 0.05,
    win_probability_impact_b: 0.03,
    rationale: 'This trade addresses your RB depth while giving up WR surplus.',
    metadata: {}
  },
  {
    trade_id: 'trade_2',
    team_a_id: 'franchise_1',
    team_b_id: 'Team Gamma',
    team_a_gives: ['player_4'],
    team_a_receives: ['player_5', 'player_6'],
    team_b_gives: ['player_5', 'player_6'],
    team_b_receives: ['player_4'],
    trade_type: 'value_arbitrage',
    fairness: 'very_fair',
    fairness_score: 0.05,
    acceptance_probability: 0.85,
    win_probability_impact_a: 0.08,
    win_probability_impact_b: 0.07,
    rationale: 'Excellent value trade that helps both teams significantly.',
    metadata: {}
  }
]

describe('TradeAnalysis', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders loading state initially', () => {
    mockTradeAPI.getTradesSuggestions.mockImplementation(() => new Promise(() => {}))
    
    render(<TradeAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('renders trade suggestions successfully', async () => {
    mockTradeAPI.getTradesSuggestions.mockResolvedValue(mockTradeProposals)
    
    render(<TradeAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Suggestions')).toBeInTheDocument()
    })

    // Check that trade proposals are rendered
    expect(screen.getByText('Trade with Team Beta')).toBeInTheDocument()
    expect(screen.getByText('Trade with Team Gamma')).toBeInTheDocument()
    
    // Check trade details
    expect(screen.getByText('75% likely')).toBeInTheDocument()
    expect(screen.getByText('85% likely')).toBeInTheDocument()
  })

  it('renders empty state when no suggestions available', async () => {
    mockTradeAPI.getTradesSuggestions.mockResolvedValue([])
    
    render(<TradeAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('No Trade Suggestions')).toBeInTheDocument()
    })

    expect(screen.getByText('No beneficial trade opportunities found at this time. Check back later or adjust your roster needs.')).toBeInTheDocument()
  })

  it('handles API errors gracefully', async () => {
    mockTradeAPI.getTradesSuggestions.mockRejectedValue(new Error('API Error'))
    
    render(<TradeAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument()
    })

    expect(screen.getByText('API Error')).toBeInTheDocument()
  })

  it('refreshes suggestions when refresh button is clicked', async () => {
    mockTradeAPI.getTradesSuggestions.mockResolvedValue(mockTradeProposals)
    
    render(<TradeAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Suggestions')).toBeInTheDocument()
    })

    const refreshButton = screen.getByText('Refresh Suggestions')
    fireEvent.click(refreshButton)

    expect(mockTradeAPI.getTradesSuggestions).toHaveBeenCalledTimes(2)
  })

  it('displays correct fairness colors and labels', async () => {
    mockTradeAPI.getTradesSuggestions.mockResolvedValue(mockTradeProposals)
    
    render(<TradeAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Suggestions')).toBeInTheDocument()
    })

    // Check fairness labels
    expect(screen.getByText('fair')).toBeInTheDocument()
    expect(screen.getByText('very fair')).toBeInTheDocument()
  })

  it('displays correct trade type icons', async () => {
    mockTradeAPI.getTradesSuggestions.mockResolvedValue(mockTradeProposals)
    
    render(<TradeAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Suggestions')).toBeInTheDocument()
    })

    // Check that trade type text is displayed
    expect(screen.getByText('need based trade')).toBeInTheDocument()
    expect(screen.getByText('value arbitrage trade')).toBeInTheDocument()
  })

  it('displays win probability impacts correctly', async () => {
    mockTradeAPI.getTradesSuggestions.mockResolvedValue(mockTradeProposals)
    
    render(<TradeAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Suggestions')).toBeInTheDocument()
    })

    // Check win probability displays
    expect(screen.getByText('+5.0%')).toBeInTheDocument()
    expect(screen.getByText('+8.0%')).toBeInTheDocument()
  })

  it('displays player lists correctly', async () => {
    mockTradeAPI.getTradesSuggestions.mockResolvedValue(mockTradeProposals)
    
    render(<TradeAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Suggestions')).toBeInTheDocument()
    })

    // Check that player IDs are displayed
    expect(screen.getByText('player_1')).toBeInTheDocument()
    expect(screen.getByText('player_2')).toBeInTheDocument()
    expect(screen.getByText('player_3')).toBeInTheDocument()
    expect(screen.getByText('player_4')).toBeInTheDocument()
    expect(screen.getByText('player_5')).toBeInTheDocument()
    expect(screen.getByText('player_6')).toBeInTheDocument()
  })

  it('displays action buttons', async () => {
    mockTradeAPI.getTradesSuggestions.mockResolvedValue(mockTradeProposals)
    
    render(<TradeAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Suggestions')).toBeInTheDocument()
    })

    // Check action buttons
    const viewDetailsButtons = screen.getAllByText('View Details')
    const proposeTradeButtons = screen.getAllByText('Propose Trade')
    
    expect(viewDetailsButtons).toHaveLength(2)
    expect(proposeTradeButtons).toHaveLength(2)
  })

  it('calls API with correct parameters', async () => {
    mockTradeAPI.getTradesSuggestions.mockResolvedValue([])
    
    render(<TradeAnalysis franchiseId="test_franchise" leagueId="test_league" />)
    
    await waitFor(() => {
      expect(mockTradeAPI.getTradesSuggestions).toHaveBeenCalledWith(
        'test_franchise',
        'test_league',
        2024,
        10
      )
    })
  })
})