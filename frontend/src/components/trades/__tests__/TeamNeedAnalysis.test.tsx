import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { TeamNeedAnalysis } from '../TeamNeedAnalysis'
import { TradeAPI } from '@/lib/trade-api'

// Mock the TradeAPI
jest.mock('@/lib/trade-api')
const mockTradeAPI = TradeAPI as jest.Mocked<typeof TradeAPI>

// Mock UI components
jest.mock('@/components/ui/LoadingSpinner', () => {
  return function MockLoadingSpinner() {
    return <div data-testid="loading-spinner">Loading...</div>
  }
})

jest.mock('@/components/ui/ErrorMessage', () => {
  return function MockErrorMessage({ message }: { message: string }) {
    return <div data-testid="error-message">{message}</div>
  }
})

const mockTeamAnalysis = {
  franchise_id: 'franchise_1',
  franchise_name: 'Team Alpha',
  needs: {
    RB: {
      position: 'RB',
      need_level: 0.8,
      current_strength: 45.5,
      replacement_level: 12.0,
      depth_score: 0.3,
      injury_risk: 0.6,
      bye_week_coverage: 0.4
    },
    WR: {
      position: 'WR',
      need_level: 0.4,
      current_strength: 65.2,
      replacement_level: 15.0,
      depth_score: 0.7,
      injury_risk: 0.2,
      bye_week_coverage: 0.8
    }
  },
  surpluses: {
    QB: {
      position: 'QB',
      surplus_level: 0.6,
      tradeable_players: ['qb_1', 'qb_2'],
      surplus_value: 25.5,
      depth_quality: 0.8
    }
  },
  overall_strength: 285.7,
  win_probability: 0.65,
  trade_urgency: 0.7,
  metadata: {}
}

const mockLeagueAnalysis = {
  franchise_1: mockTeamAnalysis,
  franchise_2: {
    franchise_id: 'franchise_2',
    franchise_name: 'Team Beta',
    needs: {
      QB: {
        position: 'QB',
        need_level: 0.9,
        current_strength: 35.0,
        replacement_level: 18.0,
        depth_score: 0.1,
        injury_risk: 0.8,
        bye_week_coverage: 0.2
      }
    },
    surpluses: {
      RB: {
        position: 'RB',
        surplus_level: 0.7,
        tradeable_players: ['rb_1', 'rb_2', 'rb_3'],
        surplus_value: 30.0,
        depth_quality: 0.9
      }
    },
    overall_strength: 245.3,
    win_probability: 0.45,
    trade_urgency: 0.9,
    metadata: {}
  }
}

describe('TeamNeedAnalysis', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders loading state initially', () => {
    mockTradeAPI.getTeamAnalysis.mockImplementation(() => new Promise(() => {}))
    mockTradeAPI.analyzeLeagueTeams.mockImplementation(() => new Promise(() => {}))
    
    render(<TeamNeedAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('renders team need analysis successfully', async () => {
    mockTradeAPI.getTeamAnalysis.mockResolvedValue(mockTeamAnalysis)
    mockTradeAPI.analyzeLeagueTeams.mockResolvedValue(mockLeagueAnalysis)
    
    render(<TeamNeedAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Team Need Analysis')).toBeInTheDocument()
    })

    // Check team overview
    expect(screen.getByText('Team Alpha')).toBeInTheDocument()
    expect(screen.getByText('285.7')).toBeInTheDocument() // Overall strength
    expect(screen.getByText('65.0%')).toBeInTheDocument() // Win probability
    expect(screen.getByText('70%')).toBeInTheDocument() // Trade urgency
  })

  it('switches between My Team and League Overview tabs', async () => {
    mockTradeAPI.getTeamAnalysis.mockResolvedValue(mockTeamAnalysis)
    mockTradeAPI.analyzeLeagueTeams.mockResolvedValue(mockLeagueAnalysis)
    
    render(<TeamNeedAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Team Need Analysis')).toBeInTheDocument()
    })

    // Initially on My Team tab
    expect(screen.getByText('Team Alpha')).toBeInTheDocument()
    
    // Switch to League Overview
    fireEvent.click(screen.getByText('League Overview'))
    expect(screen.getByText('League Trade Landscape')).toBeInTheDocument()
    expect(screen.getByText('Team Beta')).toBeInTheDocument()
    
    // Switch back to My Team
    fireEvent.click(screen.getByText('My Team'))
    expect(screen.getByText('Team Alpha')).toBeInTheDocument()
  })

  it('displays position needs correctly', async () => {
    mockTradeAPI.getTeamAnalysis.mockResolvedValue(mockTeamAnalysis)
    mockTradeAPI.analyzeLeagueTeams.mockResolvedValue(mockLeagueAnalysis)
    
    render(<TeamNeedAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Position Needs')).toBeInTheDocument()
    })

    // Check RB need (critical level)
    expect(screen.getByText('Critical')).toBeInTheDocument()
    expect(screen.getByText('45.5 pts')).toBeInTheDocument()
    
    // Check need metrics
    expect(screen.getByText('30%')).toBeInTheDocument() // Depth score
    expect(screen.getByText('60%')).toBeInTheDocument() // Injury risk
    expect(screen.getByText('40%')).toBeInTheDocument() // Bye coverage
  })

  it('displays position surpluses correctly', async () => {
    mockTradeAPI.getTeamAnalysis.mockResolvedValue(mockTeamAnalysis)
    mockTradeAPI.analyzeLeagueTeams.mockResolvedValue(mockLeagueAnalysis)
    
    render(<TeamNeedAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Position Surpluses')).toBeInTheDocument()
    })

    // Check QB surplus
    expect(screen.getByText('60% surplus')).toBeInTheDocument()
    expect(screen.getByText('25.5 surplus pts')).toBeInTheDocument()
    expect(screen.getByText('2')).toBeInTheDocument() // Tradeable players count
    expect(screen.getByText('80%')).toBeInTheDocument() // Depth quality
  })

  it('displays league overview table correctly', async () => {
    mockTradeAPI.getTeamAnalysis.mockResolvedValue(mockTeamAnalysis)
    mockTradeAPI.analyzeLeagueTeams.mockResolvedValue(mockLeagueAnalysis)
    
    render(<TeamNeedAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Team Need Analysis')).toBeInTheDocument()
    })

    // Switch to League Overview
    fireEvent.click(screen.getByText('League Overview'))
    
    await waitFor(() => {
      expect(screen.getByText('League Trade Landscape')).toBeInTheDocument()
    })

    // Check table headers
    expect(screen.getByText('Team')).toBeInTheDocument()
    expect(screen.getByText('Strength')).toBeInTheDocument()
    expect(screen.getByText('Win %')).toBeInTheDocument()
    expect(screen.getByText('Trade Urgency')).toBeInTheDocument()
    expect(screen.getByText('Top Need')).toBeInTheDocument()
    expect(screen.getByText('Top Surplus')).toBeInTheDocument()

    // Check team data
    expect(screen.getByText('Team Alpha')).toBeInTheDocument()
    expect(screen.getByText('Team Beta')).toBeInTheDocument()
    expect(screen.getByText('285.7')).toBeInTheDocument()
    expect(screen.getByText('245.3')).toBeInTheDocument()
  })

  it('handles API errors gracefully', async () => {
    mockTradeAPI.getTeamAnalysis.mockRejectedValue(new Error('API Error'))
    mockTradeAPI.analyzeLeagueTeams.mockRejectedValue(new Error('API Error'))
    
    render(<TeamNeedAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument()
    })

    expect(screen.getByText('API Error')).toBeInTheDocument()
  })

  it('applies correct need level colors', async () => {
    mockTradeAPI.getTeamAnalysis.mockResolvedValue(mockTeamAnalysis)
    mockTradeAPI.analyzeLeagueTeams.mockResolvedValue(mockLeagueAnalysis)
    
    render(<TeamNeedAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Position Needs')).toBeInTheDocument()
    })

    // Critical need should have red background
    const criticalBadge = screen.getByText('Critical')
    expect(criticalBadge).toHaveClass('bg-red-500')
    
    // Moderate need should have yellow background
    const moderateBadge = screen.getByText('Moderate')
    expect(moderateBadge).toHaveClass('bg-yellow-500')
  })

  it('applies correct surplus level colors', async () => {
    mockTradeAPI.getTeamAnalysis.mockResolvedValue(mockTeamAnalysis)
    mockTradeAPI.analyzeLeagueTeams.mockResolvedValue(mockLeagueAnalysis)
    
    render(<TeamNeedAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Position Surpluses')).toBeInTheDocument()
    })

    // 60% surplus should have blue background
    const surplusBadge = screen.getByText('60% surplus')
    expect(surplusBadge).toHaveClass('bg-blue-500')
  })

  it('calls APIs with correct parameters', async () => {
    mockTradeAPI.getTeamAnalysis.mockResolvedValue(mockTeamAnalysis)
    mockTradeAPI.analyzeLeagueTeams.mockResolvedValue(mockLeagueAnalysis)
    
    render(<TeamNeedAnalysis franchiseId="test_franchise" leagueId="test_league" />)
    
    await waitFor(() => {
      expect(mockTradeAPI.getTeamAnalysis).toHaveBeenCalledWith('test_franchise', 2024)
      expect(mockTradeAPI.analyzeLeagueTeams).toHaveBeenCalledWith('test_league', 2024)
    })
  })

  it('applies correct tab styling', async () => {
    mockTradeAPI.getTeamAnalysis.mockResolvedValue(mockTeamAnalysis)
    mockTradeAPI.analyzeLeagueTeams.mockResolvedValue(mockLeagueAnalysis)
    
    render(<TeamNeedAnalysis franchiseId="franchise_1" leagueId="league_1" />)
    
    await waitFor(() => {
      expect(screen.getByText('Team Need Analysis')).toBeInTheDocument()
    })

    const myTeamTab = screen.getByText('My Team')
    const leagueTab = screen.getByText('League Overview')

    // Initially My Team should be active
    expect(myTeamTab).toHaveClass('bg-blue-600', 'text-white')
    expect(leagueTab).toHaveClass('bg-gray-200', 'text-gray-700')

    // Click League Overview
    fireEvent.click(leagueTab)

    // Now League Overview should be active
    expect(leagueTab).toHaveClass('bg-blue-600', 'text-white')
    expect(myTeamTab).toHaveClass('bg-gray-200', 'text-gray-700')
  })
})