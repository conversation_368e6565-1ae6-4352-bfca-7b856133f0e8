import { useQuery } from 'react-query'
import { 
  CalendarIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { api } from '@/lib/api'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface UpcomingDeadlinesProps {
  leagueId: string | null
  franchiseId: string | null
}

interface Deadline {
  id: string
  name: string
  alert_type: string
  target_datetime: string
  advance_notice_hours: number[]
  is_active: boolean
  schedule_data: {
    description?: string
    importance?: string
  }
}

export default function UpcomingDeadlines({ leagueId, franchiseId }: UpcomingDeadlinesProps) {
  const { data: schedules, isLoading } = useQuery<Deadline[]>(
    ['alert-schedules', leagueId, franchiseId],
    async () => {
      const response = await api.get('/alerts/schedules', {
        params: {
          ...(leagueId && { league_id: leagueId }),
          ...(franchiseId && { franchise_id: franchiseId }),
          active_only: true
        }
      })
      return response.data
    },
    {
      enabled: !!leagueId,
      refetchInterval: 60000 // Refetch every minute
    }
  )

  const getDeadlineIcon = (alertType: string) => {
    const iconClass = "h-5 w-5 flex-shrink-0"
    
    switch (alertType.toLowerCase()) {
      case 'keeper_deadline':
        return <CalendarIcon className={`${iconClass} text-yellow-500`} />
      case 'waiver_deadline':
        return <ClockIcon className={`${iconClass} text-blue-500`} />
      case 'lineup_deadline':
        return <ExclamationTriangleIcon className={`${iconClass} text-red-500`} />
      case 'trade_deadline':
        return <CalendarIcon className={`${iconClass} text-green-500`} />
      default:
        return <CalendarIcon className={`${iconClass} text-gray-500`} />
    }
  }

  const getTimeUntilDeadline = (targetDateTime: string) => {
    const target = new Date(targetDateTime)
    const now = new Date()
    const diffInMs = target.getTime() - now.getTime()
    
    if (diffInMs < 0) {
      return { text: 'Overdue', color: 'text-red-600', urgent: true }
    }
    
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60))
    const diffInDays = Math.floor(diffInHours / 24)
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60))
      return { 
        text: `${diffInMinutes}m`, 
        color: 'text-red-600', 
        urgent: true 
      }
    } else if (diffInHours < 24) {
      return { 
        text: `${diffInHours}h`, 
        color: diffInHours < 6 ? 'text-red-600' : 'text-orange-600',
        urgent: diffInHours < 6
      }
    } else if (diffInDays < 7) {
      return { 
        text: `${diffInDays}d`, 
        color: diffInDays < 2 ? 'text-orange-600' : 'text-blue-600',
        urgent: false
      }
    } else {
      return { 
        text: target.toLocaleDateString(), 
        color: 'text-gray-600',
        urgent: false
      }
    }
  }

  const formatDeadlineName = (name: string, alertType: string) => {
    if (name && name !== alertType) return name
    
    switch (alertType.toLowerCase()) {
      case 'keeper_deadline':
        return 'Keeper Selections Due'
      case 'waiver_deadline':
        return 'Waiver Claims Due'
      case 'lineup_deadline':
        return 'Lineup Changes Due'
      case 'trade_deadline':
        return 'Trade Deadline'
      default:
        return name || 'Upcoming Deadline'
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Upcoming Deadlines</h2>
        </div>
        <div className="p-6 flex justify-center">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  // Sort schedules by target datetime
  const sortedSchedules = (schedules || [])
    .sort((a, b) => new Date(a.target_datetime).getTime() - new Date(b.target_datetime).getTime())
    .slice(0, 5) // Show only next 5 deadlines

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">Upcoming Deadlines</h2>
      </div>
      
      <div className="divide-y divide-gray-200">
        {sortedSchedules.length === 0 ? (
          <div className="p-6 text-center">
            <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No upcoming deadlines</h3>
            <p className="mt-1 text-sm text-gray-500">
              All caught up! Check back later.
            </p>
          </div>
        ) : (
          sortedSchedules.map((schedule) => {
            const timeInfo = getTimeUntilDeadline(schedule.target_datetime)
            
            return (
              <div key={schedule.id} className="p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start space-x-3">
                  {getDeadlineIcon(schedule.alert_type)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {formatDeadlineName(schedule.name, schedule.alert_type)}
                      </h3>
                      <span className={`text-sm font-medium ${timeInfo.color}`}>
                        {timeInfo.text}
                      </span>
                    </div>
                    
                    {schedule.schedule_data?.description && (
                      <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                        {schedule.schedule_data.description}
                      </p>
                    )}
                    
                    <div className="mt-2 flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        {new Date(schedule.target_datetime).toLocaleString()}
                      </span>
                      
                      {timeInfo.urgent && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Urgent
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )
          })
        )}
      </div>
      
      {sortedSchedules.length > 0 && (
        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
          <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
            View all deadlines →
          </button>
        </div>
      )}
    </div>
  )
}