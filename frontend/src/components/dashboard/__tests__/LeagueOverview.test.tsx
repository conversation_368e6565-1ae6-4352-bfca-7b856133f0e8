import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import LeagueOverview from '../LeagueOverview'
import { League, Franchise } from '@/types'

const mockLeague: League = {
  id: 'league1',
  name: 'Test League',
  season: 2024,
  description: 'A test league for fantasy football',
  scoring_rules: {
    ppr: true,
    passing_td: 4,
    rushing_td: 6
  },
  roster_slots: [
    { position: 'QB', count: 1 },
    { position: 'RB', count: 2 },
    { position: 'WR', count: 2 }
  ],
  keeper_rules: {
    max_keepers: 3,
    round_escalation: true,
    franchise_tags: 1
  },
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

const mockFranchise: Franchise = {
  id: 'franchise1',
  name: 'Test Team',
  owner_name: 'Test Owner',
  league_id: 'league1',
  league_name: 'Test League',
  salary_cap: 200,
  faab_budget: 100,
  faab_spent: 25,
  remaining_faab: 75,
  franchise_metadata: {},
  is_active: true,
  roster_size: 15,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

describe('LeagueOverview', () => {
  it('renders league information correctly', () => {
    render(<LeagueOverview league={mockLeague} franchise={mockFranchise} />)

    expect(screen.getByText('League Overview')).toBeInTheDocument()
    expect(screen.getByText('Test League')).toBeInTheDocument()
    expect(screen.getByText('2024 Season')).toBeInTheDocument()
    expect(screen.getByText('A test league for fantasy football')).toBeInTheDocument()
  })

  it('displays scoring type correctly', () => {
    render(<LeagueOverview league={mockLeague} franchise={mockFranchise} />)

    expect(screen.getByText('PPR Scoring')).toBeInTheDocument()
  })

  it('shows roster slot configuration', () => {
    render(<LeagueOverview league={mockLeague} franchise={mockFranchise} />)

    expect(screen.getByText('1 QB, 2 RB, 2 WR')).toBeInTheDocument()
  })

  it('displays keeper rules when present', () => {
    render(<LeagueOverview league={mockLeague} franchise={mockFranchise} />)

    expect(screen.getByText('Max 3 keepers')).toBeInTheDocument()
    expect(screen.getByText('Round escalation enabled')).toBeInTheDocument()
  })

  it('shows franchise information when provided', () => {
    render(<LeagueOverview league={mockLeague} franchise={mockFranchise} />)

    expect(screen.getByText('Your Franchise')).toBeInTheDocument()
    expect(screen.getByText('Test Team')).toBeInTheDocument()
    expect(screen.getByText('Test Owner')).toBeInTheDocument()
    expect(screen.getByText('15')).toBeInTheDocument() // roster size
  })

  it('displays financial information correctly', () => {
    render(<LeagueOverview league={mockLeague} franchise={mockFranchise} />)

    expect(screen.getByText('$200')).toBeInTheDocument() // salary cap
    expect(screen.getByText('$100')).toBeInTheDocument() // faab budget
    expect(screen.getByText('$75')).toBeInTheDocument() // remaining faab
  })

  it('works without franchise information', () => {
    render(<LeagueOverview league={mockLeague} />)

    expect(screen.getByText('League Overview')).toBeInTheDocument()
    expect(screen.getByText('Test League')).toBeInTheDocument()
    expect(screen.queryByText('Your Franchise')).not.toBeInTheDocument()
  })
})