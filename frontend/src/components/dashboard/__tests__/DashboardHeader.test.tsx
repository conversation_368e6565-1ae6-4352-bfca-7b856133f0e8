import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import DashboardHeader from '../DashboardHeader'
import { League, Franchise } from '@/types'

const mockLeagues: League[] = [
  {
    id: 'league1',
    name: 'Test League',
    season: 2024,
    scoring_rules: {},
    roster_slots: [],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
]

const mockFranchises: Franchise[] = [
  {
    id: 'franchise1',
    name: 'Test Team',
    owner_name: 'Test Owner',
    league_id: 'league1',
    league_name: 'Test League',
    faab_spent: 0,
    remaining_faab: 100,
    franchise_metadata: {},
    is_active: true,
    roster_size: 0,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
]

describe('DashboardHeader', () => {
  const mockOnLeagueChange = jest.fn()
  const mockOnFranchiseChange = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the header with title', () => {
    render(
      <DashboardHeader
        leagues={mockLeagues}
        franchises={mockFranchises}
        selectedLeague="league1"
        selectedFranchise="franchise1"
        onLeagueChange={mockOnLeagueChange}
        onFranchiseChange={mockOnFranchiseChange}
      />
    )

    expect(screen.getByText('AI Fantasy Assistant')).toBeInTheDocument()
    expect(screen.getByText('Your intelligent fantasy football management dashboard')).toBeInTheDocument()
  })

  it('displays selected league and franchise', () => {
    render(
      <DashboardHeader
        leagues={mockLeagues}
        franchises={mockFranchises}
        selectedLeague="league1"
        selectedFranchise="franchise1"
        onLeagueChange={mockOnLeagueChange}
        onFranchiseChange={mockOnFranchiseChange}
      />
    )

    expect(screen.getByText('Test League (2024)')).toBeInTheDocument()
    expect(screen.getByText('Test Team')).toBeInTheDocument()
  })

  it('shows placeholder text when no league is selected', () => {
    render(
      <DashboardHeader
        leagues={mockLeagues}
        franchises={mockFranchises}
        selectedLeague={null}
        selectedFranchise={null}
        onLeagueChange={mockOnLeagueChange}
        onFranchiseChange={mockOnFranchiseChange}
      />
    )

    expect(screen.getByText('Select league...')).toBeInTheDocument()
  })

  it('calls onLeagueChange when league is selected', () => {
    render(
      <DashboardHeader
        leagues={mockLeagues}
        franchises={mockFranchises}
        selectedLeague={null}
        selectedFranchise={null}
        onLeagueChange={mockOnLeagueChange}
        onFranchiseChange={mockOnFranchiseChange}
      />
    )

    // Click on league selector
    const leagueButton = screen.getByText('Select league...')
    fireEvent.click(leagueButton)

    // Click on league option
    const leagueOption = screen.getByText('Test League (2024)')
    fireEvent.click(leagueOption)

    expect(mockOnLeagueChange).toHaveBeenCalledWith('league1')
  })
})