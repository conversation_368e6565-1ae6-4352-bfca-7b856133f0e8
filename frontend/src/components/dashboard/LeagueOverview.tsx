import { League, Franchise } from '@/types'
import { 
  TrophyIcon, 
  UserGroupIcon, 
  CurrencyDollarIcon,
  CalendarIcon 
} from '@heroicons/react/24/outline'

interface LeagueOverviewProps {
  league: League
  franchise?: Franchise
}

export default function LeagueOverview({ league, franchise }: LeagueOverviewProps) {
  const formatCurrency = (amount: number | null | undefined) => {
    if (!amount) return 'N/A'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const getRosterSlotSummary = () => {
    if (!league.roster_slots || league.roster_slots.length === 0) {
      return 'Standard roster'
    }
    
    const slots = league.roster_slots.reduce((acc: Record<string, number>, slot: any) => {
      acc[slot.position] = slot.count
      return acc
    }, {})
    
    return Object.entries(slots)
      .map(([position, count]) => `${count} ${position}`)
      .join(', ')
  }

  const getScoringType = () => {
    if (!league.scoring_rules) return 'Standard'
    
    const rules = league.scoring_rules
    if (rules.ppr || rules.reception_points) return 'PPR'
    if (rules.half_ppr) return 'Half PPR'
    return 'Standard'
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">League Overview</h2>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* League Information */}
          <div className="space-y-4">
            <div className="flex items-start">
              <TrophyIcon className="h-5 w-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-gray-900">{league.name}</h3>
                <p className="text-sm text-gray-600">{league.season} Season</p>
                {league.description && (
                  <p className="text-sm text-gray-500 mt-1">{league.description}</p>
                )}
              </div>
            </div>

            <div className="flex items-start">
              <UserGroupIcon className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-gray-900">League Format</h4>
                <p className="text-sm text-gray-600">{getScoringType()} Scoring</p>
                <p className="text-sm text-gray-500">{getRosterSlotSummary()}</p>
              </div>
            </div>

            {league.keeper_rules && (
              <div className="flex items-start">
                <CalendarIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Keeper Rules</h4>
                  <p className="text-sm text-gray-600">
                    Max {league.keeper_rules.max_keepers} keepers
                  </p>
                  {league.keeper_rules.round_escalation && (
                    <p className="text-sm text-gray-500">Round escalation enabled</p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Franchise Information */}
          {franchise && (
            <div className="space-y-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Your Franchise</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-blue-700">Team Name:</span>
                    <span className="text-sm font-medium text-blue-900">{franchise.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-blue-700">Owner:</span>
                    <span className="text-sm font-medium text-blue-900">{franchise.owner_name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-blue-700">Roster Size:</span>
                    <span className="text-sm font-medium text-blue-900">{franchise.roster_size}</span>
                  </div>
                </div>
              </div>

              {(franchise.salary_cap || franchise.faab_budget) && (
                <div className="flex items-start">
                  <CurrencyDollarIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-gray-900">Financial Status</h4>
                    {franchise.salary_cap && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Salary Cap:</span>
                        <span className="font-medium">{formatCurrency(Number(franchise.salary_cap))}</span>
                      </div>
                    )}
                    {franchise.faab_budget && (
                      <>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">FAAB Budget:</span>
                          <span className="font-medium">{formatCurrency(Number(franchise.faab_budget))}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Remaining:</span>
                          <span className="font-medium text-green-600">
                            {formatCurrency(Number(franchise.remaining_faab))}
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}