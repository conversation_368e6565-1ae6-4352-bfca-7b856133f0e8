import { useState } from 'react'
import { Recommendation } from '@/types'
import { 
  LightBulbIcon,
  ChevronRightIcon,
  StarIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface RecentRecommendationsProps {
  recommendations: Recommendation[]
  isLoading: boolean
}

export default function RecentRecommendations({ 
  recommendations, 
  isLoading 
}: RecentRecommendationsProps) {
  const [expandedRec, setExpandedRec] = useState<string | null>(null)

  const getRecommendationIcon = (type: string) => {
    const iconClass = "h-5 w-5 flex-shrink-0"
    
    switch (type.toLowerCase()) {
      case 'keeper':
        return <StarIcon className={`${iconClass} text-yellow-500`} />
      case 'draft':
        return <LightBulbIcon className={`${iconClass} text-blue-500`} />
      case 'trade':
        return <ChevronRightIcon className={`${iconClass} text-green-500`} />
      case 'lineup':
        return <ClockIcon className={`${iconClass} text-purple-500`} />
      case 'waiver':
        return <StarIcon className={`${iconClass} text-orange-500`} />
      default:
        return <LightBulbIcon className={`${iconClass} text-gray-500`} />
    }
  }

  const getConfidenceStars = (confidence: number) => {
    const stars = Math.round(confidence * 5)
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i}>
        {i < stars ? (
          <StarIconSolid className="h-4 w-4 text-yellow-400" />
        ) : (
          <StarIcon className="h-4 w-4 text-gray-300" />
        )}
      </span>
    ))
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    return date.toLocaleDateString()
  }

  const toggleExpanded = (recId: string) => {
    setExpandedRec(expandedRec === recId ? null : recId)
  }

  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Recent Recommendations</h2>
        </div>
        <div className="p-6 flex justify-center">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900">Recent Recommendations</h2>
          {recommendations.length > 0 && (
            <span className="text-sm text-gray-500">
              {recommendations.length} active
            </span>
          )}
        </div>
      </div>
      
      <div className="divide-y divide-gray-200">
        {recommendations.length === 0 ? (
          <div className="p-6 text-center">
            <LightBulbIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No recommendations</h3>
            <p className="mt-1 text-sm text-gray-500">
              Check back later for AI-powered recommendations.
            </p>
          </div>
        ) : (
          recommendations.map((rec) => (
            <div key={rec.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-start space-x-3">
                {getRecommendationIcon(rec.type)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                        {rec.type}
                      </span>
                      <div className="flex items-center space-x-1">
                        {getConfidenceStars(rec.confidence)}
                      </div>
                    </div>
                    <span className="text-xs text-gray-500">
                      {formatTimeAgo(rec.created_at)}
                    </span>
                  </div>
                  
                  <h3 className="mt-1 text-sm font-medium text-gray-900">
                    {rec.title}
                  </h3>
                  
                  <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                    {rec.description}
                  </p>

                  {expandedRec === rec.id && (
                    <div className="mt-3 space-y-2">
                      <div className="bg-gray-50 rounded-md p-3">
                        <h4 className="text-xs font-medium text-gray-900 uppercase tracking-wide mb-1">
                          Rationale
                        </h4>
                        <p className="text-sm text-gray-700">{rec.rationale}</p>
                      </div>
                      
                      {rec.alternatives && rec.alternatives.length > 0 && (
                        <div className="bg-blue-50 rounded-md p-3">
                          <h4 className="text-xs font-medium text-blue-900 uppercase tracking-wide mb-2">
                            Alternatives
                          </h4>
                          <div className="space-y-1">
                            {rec.alternatives.slice(0, 2).map((alt, index) => (
                              <div key={index} className="text-sm text-blue-800">
                                <span className="font-medium">{alt.title}:</span> {alt.description}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="mt-3 flex items-center justify-between">
                    <button
                      onClick={() => toggleExpanded(rec.id)}
                      className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                    >
                      {expandedRec === rec.id ? 'Show less' : 'Show details'}
                    </button>
                    
                    {rec.expires_at && (
                      <span className="text-xs text-orange-600">
                        Expires {formatTimeAgo(rec.expires_at)}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
      
      {recommendations.length > 0 && (
        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
          <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
            View all recommendations →
          </button>
        </div>
      )}
    </div>
  )
}