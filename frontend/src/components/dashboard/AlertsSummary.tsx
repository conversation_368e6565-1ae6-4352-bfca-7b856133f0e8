import { 
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface AlertsSummaryProps {
  alertsSummary?: {
    urgent: number
    high: number
    medium: number
    low: number
    total: number
  }
  isLoading: boolean
}

export default function AlertsSummary({ alertsSummary, isLoading }: AlertsSummaryProps) {
  const getAlertIcon = (priority: string) => {
    const iconClass = "h-5 w-5"
    
    switch (priority) {
      case 'urgent':
        return <XCircleIcon className={`${iconClass} text-red-500`} />
      case 'high':
        return <ExclamationTriangleIcon className={`${iconClass} text-orange-500`} />
      case 'medium':
        return <InformationCircleIcon className={`${iconClass} text-blue-500`} />
      case 'low':
        return <CheckCircleIcon className={`${iconClass} text-green-500`} />
      default:
        return <InformationCircleIcon className={`${iconClass} text-gray-500`} />
    }
  }

  const getAlertColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'medium':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Alerts</h2>
        </div>
        <div className="p-6 flex justify-center">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  const alerts = [
    { priority: 'urgent', count: alertsSummary?.urgent || 0, label: 'Urgent' },
    { priority: 'high', count: alertsSummary?.high || 0, label: 'High Priority' },
    { priority: 'medium', count: alertsSummary?.medium || 0, label: 'Medium Priority' },
    { priority: 'low', count: alertsSummary?.low || 0, label: 'Low Priority' }
  ]

  const totalAlerts = alertsSummary?.total || 0

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900">Alerts</h2>
          {totalAlerts > 0 && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
              {totalAlerts} active
            </span>
          )}
        </div>
      </div>
      
      <div className="p-6">
        {totalAlerts === 0 ? (
          <div className="text-center">
            <CheckCircleIcon className="mx-auto h-12 w-12 text-green-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">All clear!</h3>
            <p className="mt-1 text-sm text-gray-500">
              No active alerts at this time.
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {alerts.map(({ priority, count, label }) => (
              count > 0 && (
                <div
                  key={priority}
                  className={`flex items-center justify-between p-3 rounded-lg border ${getAlertColor(priority)}`}
                >
                  <div className="flex items-center space-x-3">
                    {getAlertIcon(priority)}
                    <span className="text-sm font-medium">{label}</span>
                  </div>
                  <span className="text-lg font-bold">{count}</span>
                </div>
              )
            ))}
            
            <div className="pt-3 border-t border-gray-200">
              <button className="w-full text-sm text-blue-600 hover:text-blue-800 font-medium text-center">
                View all alerts →
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}