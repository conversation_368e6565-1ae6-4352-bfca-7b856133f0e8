'use client'

import { useState } from 'react'
import { DraftRecommendationData } from '@/types/draft'
import { 
  StarIcon, 
  ChartBarIcon, 
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'
import WhyButton from '@/components/ui/WhyButton'
import ConfidenceIndicator from '@/components/ui/ConfidenceIndicator'
import AlternativeComparison from '@/components/ui/AlternativeComparison'
import { ExplanationAPI } from '@/lib/explanation-api'
import { RecommendationExplanation, ExplanationRequest, UncertaintyVisualization } from '@/types/explanation'

interface DraftRecommendationProps {
  recommendation: DraftRecommendationData
  onSelectPlayer: () => void
  isDraftActive: boolean
  recommendationId?: string
}

export default function DraftRecommendation({ 
  recommendation, 
  onSelectPlayer, 
  isDraftActive,
  recommendationId 
}: DraftRecommendationProps) {
  const [showDetails, setShowDetails] = useState(false)
  const [showAlternatives, setShowAlternatives] = useState(false)

  // Create uncertainty visualization data
  const uncertaintyData: UncertaintyVisualization = {
    confidence_level: recommendation.confidence >= 0.8 ? 'high' : 
                     recommendation.confidence >= 0.6 ? 'medium' : 'low',
    confidence_score: recommendation.confidence,
    uncertainty_range: [
      Math.max(0, recommendation.confidence - 0.1),
      Math.min(1, recommendation.confidence + 0.1)
    ],
    risk_factors: [
      'Player injury risk',
      'Draft position uncertainty',
      'Tier break timing'
    ],
    confidence_indicators: [
      {
        indicator: 'Projection Quality',
        status: recommendation.confidence > 0.7 ? 'good' : 'warning',
        description: 'Quality of underlying projections'
      },
      {
        indicator: 'Positional Need',
        status: recommendation.positional_need_score > 0.5 ? 'good' : 'poor',
        description: 'How well this pick addresses team needs'
      },
      {
        indicator: 'Value Opportunity',
        status: recommendation.value_over_replacement > 10 ? 'good' : 'warning',
        description: 'Value compared to replacement level'
      }
    ]
  }

  const handleExplain = async (request: ExplanationRequest): Promise<RecommendationExplanation> => {
    if (!recommendationId) {
      throw new Error('No recommendation ID available')
    }
    return ExplanationAPI.getExplanation(recommendationId, request)
  }

  // Convert alternatives to the format expected by AlternativeComparison
  const alternativeScenarios = recommendation.alternatives.map((altId, index) => ({
    scenario: `Alternative ${index + 1}`,
    description: `Consider drafting player ${altId} instead`,
    confidence: recommendation.confidence * 0.9, // Slightly lower confidence for alternatives
    trade_offs: [
      'Different positional value',
      'May not address immediate needs',
      'Different risk profile'
    ],
    expected_outcome: `Similar value with different team construction impact`
  }))

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50'
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  const getConfidenceStars = (confidence: number) => {
    const stars = Math.round(confidence * 5)
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i}>
        {i < stars ? (
          <StarIconSolid className="h-4 w-4 text-yellow-400" />
        ) : (
          <StarIcon className="h-4 w-4 text-gray-300" />
        )}
      </span>
    ))
  }

  const getTierColor = (tier: number) => {
    if (tier <= 2) return 'bg-green-100 text-green-800'
    if (tier <= 4) return 'bg-blue-100 text-blue-800'
    if (tier <= 6) return 'bg-yellow-100 text-yellow-800'
    return 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">
            Recommended Pick
          </h2>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(recommendation.confidence)}`}>
            {Math.round(recommendation.confidence * 100)}% confidence
          </div>
        </div>
      </div>

      {/* Player Info */}
      <div className="px-6 py-4">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-xl font-bold text-gray-900">
                {recommendation.player_name}
              </h3>
              <span className={`px-2 py-1 rounded text-sm font-medium ${getTierColor(recommendation.tier)}`}>
                Tier {recommendation.tier}
              </span>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
              <span className="font-medium">{recommendation.position}</span>
              <span>{recommendation.team}</span>
            </div>

            {/* Enhanced Confidence Display */}
            <div className="mb-3">
              <ConfidenceIndicator 
                confidence={recommendation.confidence}
                uncertainty={uncertaintyData}
                showDetails={true}
                size="md"
              />
            </div>
          </div>

          {isDraftActive && (
            <button
              onClick={onSelectPlayer}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium transition-colors"
            >
              Draft Player
            </button>
          )}
        </div>

        {/* Key Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="text-sm text-gray-600">Projected Points</div>
            <div className="text-lg font-bold text-gray-900">
              {recommendation.projected_points.toFixed(1)}
            </div>
          </div>
          
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="text-sm text-blue-600">Value Over Replacement</div>
            <div className="text-lg font-bold text-blue-700">
              +{recommendation.value_over_replacement.toFixed(1)}
            </div>
          </div>
        </div>

        {/* Rationale with Explanation Button */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <InformationCircleIcon className="h-5 w-5 text-blue-500" />
              <span className="font-medium text-gray-900">Why This Pick?</span>
            </div>
            {recommendationId && (
              <WhyButton 
                recommendationId={recommendationId}
                onExplain={handleExplain}
                variant="link"
                size="sm"
              />
            )}
          </div>
          <p className="text-gray-700 text-sm leading-relaxed">
            {recommendation.rationale}
          </p>
        </div>

        {/* Advanced Details Toggle */}
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 text-sm font-medium mb-4"
        >
          <ChartBarIcon className="h-4 w-4" />
          <span>Advanced Analysis</span>
          {showDetails ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
        </button>

        {/* Advanced Details */}
        {showDetails && (
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Positional Need Score:</span>
                <span className="ml-2 font-medium">
                  {recommendation.positional_need_score.toFixed(2)}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Opportunity Cost:</span>
                <span className="ml-2 font-medium">
                  {recommendation.opportunity_cost.toFixed(1)} pts
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Alternatives */}
        {recommendation.alternatives.length > 0 && (
          <AlternativeComparison
            primaryRecommendation={{
              title: recommendation.player_name,
              confidence: recommendation.confidence,
              description: recommendation.rationale,
              key_metrics: {
                projected_points: recommendation.projected_points,
                value_over_replacement: recommendation.value_over_replacement
              }
            }}
            alternatives={alternativeScenarios}
            onSelectAlternative={(scenario) => {
              console.log('Selected alternative scenario:', scenario)
              // In real implementation, this would update the draft recommendation
            }}
            showComparison={true}
          />
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-3 bg-gray-50 rounded-b-lg">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Recommendation updated in real-time</span>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>Live</span>
          </div>
        </div>
      </div>
    </div>
  )
}