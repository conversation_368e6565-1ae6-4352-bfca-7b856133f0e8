'use client'

import { useState, useEffect } from 'react'
import { DraftStrategy, DraftScenario } from '@/types/draft'
import { api } from '@/lib/api'
import { 
  PlayIcon, 
  ArrowPathIcon, 
  ChartBarIcon,
  TrophyIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface DraftSimulationProps {
  leagueId: string
  franchiseId: string
  strategy: DraftStrategy
}

export default function DraftSimulation({ 
  leagueId, 
  franchiseId, 
  strategy 
}: DraftSimulationProps) {
  const [scenarios, setScenarios] = useState<DraftScenario[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [numSimulations, setNumSimulations] = useState(1000)
  const [selectedScenario, setSelectedScenario] = useState<DraftScenario | null>(null)
  const [error, setError] = useState<string | null>(null)

  const runSimulation = async () => {
    setIsRunning(true)
    setError(null)
    
    try {
      const response = await api.post('/draft/simulate', {
        league_id: leagueId,
        franchise_id: franchiseId,
        num_simulations: numSimulations,
        season: 2024,
        strategy: strategy
      })
      
      setScenarios(response.data)
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to run simulation')
    } finally {
      setIsRunning(false)
    }
  }

  const getScenarioRank = (scenario: DraftScenario) => {
    const rank = scenarios.findIndex(s => s.scenario_id === scenario.scenario_id) + 1
    return rank
  }

  const getScenarioColor = (rank: number) => {
    if (rank <= 3) return 'bg-green-50 border-green-200'
    if (rank <= 10) return 'bg-blue-50 border-blue-200'
    if (rank <= 25) return 'bg-yellow-50 border-yellow-200'
    return 'bg-gray-50 border-gray-200'
  }

  const formatRosterComposition = (roster: Record<string, string[]>) => {
    return Object.entries(roster).map(([position, players]) => ({
      position,
      count: players.length
    }))
  }

  return (
    <div className="bg-white rounded-lg shadow border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">
            Draft Simulation
          </h2>
          <div className="flex items-center space-x-2">
            <select
              value={numSimulations}
              onChange={(e) => setNumSimulations(Number(e.target.value))}
              className="text-sm border border-gray-300 rounded px-2 py-1"
              disabled={isRunning}
            >
              <option value={100}>100 sims</option>
              <option value={500}>500 sims</option>
              <option value={1000}>1,000 sims</option>
              <option value={5000}>5,000 sims</option>
            </select>
            
            <button
              onClick={runSimulation}
              disabled={isRunning}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunning ? (
                <>
                  <ArrowPathIcon className="h-4 w-4 animate-spin" />
                  <span>Running...</span>
                </>
              ) : (
                <>
                  <PlayIcon className="h-4 w-4" />
                  <span>Run Simulation</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {isRunning && (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <LoadingSpinner />
              <p className="mt-2 text-sm text-gray-600">
                Running {numSimulations.toLocaleString()} simulations...
              </p>
            </div>
          </div>
        )}

        {scenarios.length > 0 && !isRunning && (
          <div>
            {/* Summary Stats */}
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="bg-green-50 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <TrophyIcon className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-green-800">Best Scenario</span>
                </div>
                <div className="mt-1">
                  <div className="text-lg font-bold text-green-900">
                    {scenarios[0]?.projected_points.toFixed(1)} pts
                  </div>
                  <div className="text-sm text-green-700">
                    {(scenarios[0]?.win_probability * 100).toFixed(1)}% win rate
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <ChartBarIcon className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">Average</span>
                </div>
                <div className="mt-1">
                  <div className="text-lg font-bold text-blue-900">
                    {(scenarios.reduce((sum, s) => sum + s.projected_points, 0) / scenarios.length).toFixed(1)} pts
                  </div>
                  <div className="text-sm text-blue-700">
                    {(scenarios.reduce((sum, s) => sum + s.win_probability, 0) / scenarios.length * 100).toFixed(1)}% win rate
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <UserGroupIcon className="h-5 w-5 text-gray-600" />
                  <span className="text-sm font-medium text-gray-800">Scenarios</span>
                </div>
                <div className="mt-1">
                  <div className="text-lg font-bold text-gray-900">
                    {scenarios.length}
                  </div>
                  <div className="text-sm text-gray-700">
                    {strategy.replace('_', ' ')} strategy
                  </div>
                </div>
              </div>
            </div>

            {/* Top Scenarios */}
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900">Top 10 Scenarios</h3>
              
              {scenarios.slice(0, 10).map((scenario) => {
                const rank = getScenarioRank(scenario)
                const rosterComposition = formatRosterComposition(scenario.final_roster)
                
                return (
                  <div
                    key={scenario.scenario_id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedScenario?.scenario_id === scenario.scenario_id
                        ? 'border-blue-500 bg-blue-50'
                        : getScenarioColor(rank)
                    }`}
                    onClick={() => setSelectedScenario(
                      selectedScenario?.scenario_id === scenario.scenario_id ? null : scenario
                    )}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm font-medium text-gray-500">
                          #{rank}
                        </div>
                        
                        <div>
                          <div className="font-medium text-gray-900">
                            {scenario.projected_points.toFixed(1)} projected points
                          </div>
                          <div className="text-sm text-gray-600">
                            {(scenario.win_probability * 100).toFixed(1)}% win probability
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900">
                            Strategy Score
                          </div>
                          <div className="text-sm text-gray-600">
                            {scenario.strategy_score.toFixed(2)}
                          </div>
                        </div>

                        <div className="text-sm text-gray-500">
                          {rosterComposition.map(({ position, count }) => (
                            <span key={position} className="mr-2">
                              {position}: {count}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Expanded Details */}
                    {selectedScenario?.scenario_id === scenario.scenario_id && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="grid grid-cols-2 gap-6">
                          {/* Draft Picks */}
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Draft Picks</h4>
                            <div className="space-y-1 max-h-32 overflow-y-auto">
                              {scenario.picks.slice(0, 8).map((pick) => (
                                <div key={pick.overall_pick} className="text-sm">
                                  <span className="text-gray-500">
                                    {pick.round_number}.{pick.pick_number}:
                                  </span>
                                  <span className="ml-2 text-gray-900">
                                    {pick.player_id ? `Player ${pick.player_id.slice(-4)}` : 'TBD'}
                                  </span>
                                </div>
                              ))}
                              {scenario.picks.length > 8 && (
                                <div className="text-sm text-gray-500">
                                  ... and {scenario.picks.length - 8} more picks
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Final Roster */}
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Final Roster</h4>
                            <div className="space-y-1">
                              {Object.entries(scenario.final_roster).map(([position, players]) => (
                                <div key={position} className="text-sm">
                                  <span className="text-gray-500">{position}:</span>
                                  <span className="ml-2 text-gray-900">
                                    {players.length} players
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {scenarios.length === 0 && !isRunning && !error && (
          <div className="text-center py-8">
            <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Simulations Yet
            </h3>
            <p className="text-gray-600 mb-4">
              Run a Monte Carlo simulation to see potential draft outcomes
            </p>
            <button
              onClick={runSimulation}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Run Your First Simulation
            </button>
          </div>
        )}
      </div>
    </div>
  )
}