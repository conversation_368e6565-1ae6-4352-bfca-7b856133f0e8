'use client'

import { useState, useMemo } from 'react'
import { DraftBoardData, DraftPlayerInfo } from '@/types/draft'
import { Player } from '@/types'
import { 
  ChevronDownIcon, 
  ChevronUpIcon,
  StarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface DraftBoardProps {
  draftBoard: DraftBoardData
  onPlayerSelect: (playerId: string) => void
  currentPick: number
  isDraftActive: boolean
}

interface TierDisplayProps {
  tier: any
  players: DraftPlayerInfo[]
  onPlayerSelect: (playerId: string) => void
  isDraftActive: boolean
  isExpanded: boolean
  onToggleExpanded: () => void
}

function TierDisplay({ 
  tier, 
  players, 
  onPlayerSelect, 
  isDraftActive, 
  isExpanded, 
  onToggleExpanded 
}: TierDisplayProps) {
  const tierColor = getTierColor(tier.tier_number)
  
  return (
    <div className="border border-gray-200 rounded-lg mb-4 bg-white shadow-sm">
      {/* Tier Header */}
      <div 
        className={`px-4 py-3 ${tierColor} cursor-pointer flex items-center justify-between`}
        onClick={onToggleExpanded}
      >
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <span className="font-bold text-lg">Tier {tier.tier_number}</span>
            <span className="text-sm opacity-75">
              {tier.position} • {players.length} players
            </span>
          </div>
          <div className="text-sm opacity-75">
            Avg: {tier.avg_value.toFixed(1)} pts
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-sm opacity-75">
            {tier.min_value.toFixed(1)} - {tier.max_value.toFixed(1)} pts
          </span>
          {isExpanded ? (
            <ChevronUpIcon className="h-5 w-5" />
          ) : (
            <ChevronDownIcon className="h-5 w-5" />
          )}
        </div>
      </div>

      {/* Player List */}
      {isExpanded && (
        <div className="divide-y divide-gray-100">
          {players.map((player, index) => (
            <PlayerRow
              key={player.id}
              player={player}
              rank={index + 1}
              onSelect={() => onPlayerSelect(player.id)}
              isDraftActive={isDraftActive}
            />
          ))}
        </div>
      )}
    </div>
  )
}

interface PlayerRowProps {
  player: DraftPlayerInfo
  rank: number
  onSelect: () => void
  isDraftActive: boolean
}

function PlayerRow({ player, rank, onSelect, isDraftActive }: PlayerRowProps) {
  const positionColor = getPositionColor(player.position)
  
  return (
    <div 
      className={`px-4 py-3 hover:bg-gray-50 flex items-center justify-between ${
        isDraftActive ? 'cursor-pointer' : 'cursor-default'
      }`}
      onClick={isDraftActive ? onSelect : undefined}
    >
      <div className="flex items-center space-x-4 flex-1">
        <div className="text-sm text-gray-500 w-8">
          #{rank}
        </div>
        
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <span className="font-medium text-gray-900">{player.name}</span>
            {player.injury_status && (
              <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
            )}
          </div>
          <div className="text-sm text-gray-500">
            {player.team} • Bye: {player.bye_week || 'N/A'}
          </div>
        </div>

        <div className={`px-2 py-1 rounded text-xs font-medium ${positionColor}`}>
          {player.position}
        </div>
      </div>

      <div className="flex items-center space-x-4 text-sm">
        <div className="text-right">
          <div className="font-medium">{player.projected_points.toFixed(1)}</div>
          <div className="text-gray-500">proj</div>
        </div>
        
        <div className="text-right">
          <div className="font-medium text-blue-600">
            +{player.value_over_replacement.toFixed(1)}
          </div>
          <div className="text-gray-500">VOR</div>
        </div>

        {player.adp && (
          <div className="text-right">
            <div className="font-medium">{player.adp.toFixed(1)}</div>
            <div className="text-gray-500">ADP</div>
          </div>
        )}

        {isDraftActive && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              onSelect()
            }}
            className="px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700 font-medium"
          >
            Draft
          </button>
        )}
      </div>
    </div>
  )
}

export default function DraftBoard({ 
  draftBoard, 
  onPlayerSelect, 
  currentPick, 
  isDraftActive 
}: DraftBoardProps) {
  const [expandedTiers, setExpandedTiers] = useState<Set<number>>(new Set([1, 2]))
  const [selectedPosition, setSelectedPosition] = useState<string>('ALL')
  const [searchTerm, setSearchTerm] = useState('')

  // Mock player data - in real implementation, this would come from API
  const mockPlayers: Record<string, DraftPlayerInfo> = useMemo(() => {
    const players: Record<string, DraftPlayerInfo> = {}
    
    draftBoard.overall_rankings.forEach((playerId, index) => {
      const vor = draftBoard.value_over_replacement[playerId] || 0
      const tier = draftBoard.tiers.find(t => t.players.includes(playerId))?.tier_number || 99
      
      players[playerId] = {
        id: playerId,
        name: `Player ${playerId.slice(-4)}`,
        position: ['QB', 'RB', 'WR', 'TE', 'K', 'DEF'][index % 6],
        team: ['KC', 'BUF', 'CIN', 'SF', 'PHI', 'DAL'][index % 6],
        bye_week: (index % 14) + 4,
        projected_points: 200 - (index * 2) + (index % 20),
        value_over_replacement: vor,
        tier: tier,
        adp: index + 1 + (index % 10)
      }
    })
    
    return players
  }, [draftBoard])

  const positions = ['ALL', 'QB', 'RB', 'WR', 'TE', 'K', 'DEF']

  const filteredTiers = useMemo(() => {
    return draftBoard.tiers
      .map(tier => {
        let filteredPlayers = tier.players
          .map(playerId => mockPlayers[playerId])
          .filter(Boolean)

        // Filter by position
        if (selectedPosition !== 'ALL') {
          filteredPlayers = filteredPlayers.filter(p => p.position === selectedPosition)
        }

        // Filter by search term
        if (searchTerm) {
          filteredPlayers = filteredPlayers.filter(p => 
            p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            p.team.toLowerCase().includes(searchTerm.toLowerCase())
          )
        }

        return {
          ...tier,
          filteredPlayers
        }
      })
      .filter(tier => tier.filteredPlayers.length > 0)
  }, [draftBoard.tiers, mockPlayers, selectedPosition, searchTerm])

  const toggleTierExpanded = (tierNumber: number) => {
    const newExpanded = new Set(expandedTiers)
    if (newExpanded.has(tierNumber)) {
      newExpanded.delete(tierNumber)
    } else {
      newExpanded.add(tierNumber)
    }
    setExpandedTiers(newExpanded)
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Draft Board</h2>
          <div className="text-sm text-gray-500">
            Last updated: {new Date(draftBoard.last_updated).toLocaleTimeString()}
          </div>
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search players..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div className="flex space-x-1">
            {positions.map(position => (
              <button
                key={position}
                onClick={() => setSelectedPosition(position)}
                className={`px-3 py-2 text-sm font-medium rounded-md ${
                  selectedPosition === position
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {position}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Tiers */}
      <div className="p-6 max-h-96 overflow-y-auto">
        {filteredTiers.map(tier => (
          <TierDisplay
            key={tier.tier_number}
            tier={tier}
            players={tier.filteredPlayers}
            onPlayerSelect={onPlayerSelect}
            isDraftActive={isDraftActive}
            isExpanded={expandedTiers.has(tier.tier_number)}
            onToggleExpanded={() => toggleTierExpanded(tier.tier_number)}
          />
        ))}

        {filteredTiers.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No players found matching your criteria
          </div>
        )}
      </div>
    </div>
  )
}

function getTierColor(tierNumber: number): string {
  const colors = [
    'bg-green-100 text-green-800',    // Tier 1
    'bg-blue-100 text-blue-800',     // Tier 2  
    'bg-yellow-100 text-yellow-800', // Tier 3
    'bg-orange-100 text-orange-800', // Tier 4
    'bg-red-100 text-red-800',       // Tier 5+
  ]
  
  return colors[Math.min(tierNumber - 1, colors.length - 1)]
}

function getPositionColor(position: string): string {
  const colors: Record<string, string> = {
    QB: 'bg-red-100 text-red-800',
    RB: 'bg-green-100 text-green-800',
    WR: 'bg-blue-100 text-blue-800',
    TE: 'bg-yellow-100 text-yellow-800',
    K: 'bg-purple-100 text-purple-800',
    DEF: 'bg-gray-100 text-gray-800'
  }
  
  return colors[position] || 'bg-gray-100 text-gray-800'
}