'use client'

import { useState, useEffect } from 'react'
import { ClockIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'

interface DraftTimerProps {
  deadline: Date
  onTimeExpired: () => void
  pickNumber?: number
  franchiseName?: string
}

export default function DraftTimer({ 
  deadline, 
  onTimeExpired, 
  pickNumber, 
  franchiseName 
}: DraftTimerProps) {
  const [timeRemaining, setTimeRemaining] = useState<number>(0)
  const [isExpired, setIsExpired] = useState(false)

  useEffect(() => {
    const updateTimer = () => {
      const now = new Date().getTime()
      const deadlineTime = deadline.getTime()
      const remaining = Math.max(0, deadlineTime - now)
      
      setTimeRemaining(remaining)
      
      if (remaining === 0 && !isExpired) {
        setIsExpired(true)
        onTimeExpired()
      }
    }

    // Update immediately
    updateTimer()
    
    // Update every second
    const interval = setInterval(updateTimer, 1000)
    
    return () => clearInterval(interval)
  }, [deadline, onTimeExpired, isExpired])

  const formatTime = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(totalSeconds / 60)
    const seconds = totalSeconds % 60
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const getTimerColor = () => {
    const totalTime = 2 * 60 * 1000 // 2 minutes in milliseconds
    const percentage = timeRemaining / totalTime
    
    if (percentage > 0.5) return 'text-green-600 bg-green-50 border-green-200'
    if (percentage > 0.25) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getProgressWidth = () => {
    const totalTime = 2 * 60 * 1000 // 2 minutes in milliseconds
    const percentage = Math.max(0, Math.min(100, (timeRemaining / totalTime) * 100))
    return `${percentage}%`
  }

  const getProgressColor = () => {
    const totalTime = 2 * 60 * 1000 // 2 minutes in milliseconds
    const percentage = timeRemaining / totalTime
    
    if (percentage > 0.5) return 'bg-green-500'
    if (percentage > 0.25) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  if (isExpired) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center space-x-3">
          <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
          <div>
            <h3 className="font-medium text-red-800">Time Expired</h3>
            <p className="text-sm text-red-600">
              {franchiseName ? `${franchiseName}'s` : 'The'} pick time has expired
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`border rounded-lg p-4 ${getTimerColor()}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <ClockIcon className="h-5 w-5" />
          <span className="font-medium">
            {pickNumber ? `Pick ${pickNumber}` : 'Current Pick'}
          </span>
          {franchiseName && (
            <span className="text-sm opacity-75">• {franchiseName}</span>
          )}
        </div>
        
        <div className="text-2xl font-bold font-mono">
          {formatTime(timeRemaining)}
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full transition-all duration-1000 ${getProgressColor()}`}
          style={{ width: getProgressWidth() }}
        />
      </div>

      {/* Warning Messages */}
      {timeRemaining <= 30000 && timeRemaining > 0 && (
        <div className="mt-2 text-sm font-medium">
          ⚠️ Less than 30 seconds remaining!
        </div>
      )}
      
      {timeRemaining <= 10000 && timeRemaining > 0 && (
        <div className="mt-1 text-sm font-medium animate-pulse">
          🚨 Auto-pick in {Math.ceil(timeRemaining / 1000)} seconds
        </div>
      )}
    </div>
  )
}