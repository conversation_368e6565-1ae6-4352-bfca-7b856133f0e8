import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import DraftSimulation from '../DraftSimulation'
import { api } from '@/lib/api'
import { DraftScenario } from '@/types/draft'

// Mock the API
jest.mock('@/lib/api')
const mockedApi = api as jest.Mocked<typeof api>

const mockScenarios: DraftScenario[] = [
  {
    scenario_id: 'scenario1',
    picks: [
      {
        round_number: 1,
        pick_number: 1,
        overall_pick: 1,
        franchise_id: 'franchise1',
        player_id: 'player1'
      },
      {
        round_number: 1,
        pick_number: 12,
        overall_pick: 12,
        franchise_id: 'franchise1',
        player_id: 'player2'
      }
    ],
    final_roster: {
      QB: ['player1'],
      RB: ['player2', 'player3'],
      WR: ['player4', 'player5'],
      TE: ['player6']
    },
    projected_points: 1850.5,
    win_probability: 0.75,
    strategy_score: 8.5
  },
  {
    scenario_id: 'scenario2',
    picks: [
      {
        round_number: 1,
        pick_number: 1,
        overall_pick: 1,
        franchise_id: 'franchise1',
        player_id: 'player7'
      }
    ],
    final_roster: {
      QB: ['player7'],
      RB: ['player8'],
      WR: ['player9']
    },
    projected_points: 1820.3,
    win_probability: 0.68,
    strategy_score: 7.8
  }
]

describe('DraftSimulation', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders simulation interface', () => {
    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    expect(screen.getByText('Draft Simulation')).toBeInTheDocument()
    expect(screen.getByText('Run Simulation')).toBeInTheDocument()
    expect(screen.getByDisplayValue('1,000 sims')).toBeInTheDocument()
  })

  it('shows empty state initially', () => {
    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    expect(screen.getByText('No Simulations Yet')).toBeInTheDocument()
    expect(screen.getByText('Run a Monte Carlo simulation to see potential draft outcomes')).toBeInTheDocument()
  })

  it('runs simulation when button is clicked', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockScenarios })

    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    const runButton = screen.getByText('Run Simulation')
    fireEvent.click(runButton)

    expect(screen.getByText('Running...')).toBeInTheDocument()

    await waitFor(() => {
      expect(mockedApi.post).toHaveBeenCalledWith('/draft/simulate', {
        league_id: 'league1',
        franchise_id: 'franchise1',
        num_simulations: 1000,
        season: 2024,
        strategy: 'VALUE_BASED'
      })
    })
  })

  it('displays simulation results', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockScenarios })

    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    const runButton = screen.getByText('Run Simulation')
    fireEvent.click(runButton)

    await waitFor(() => {
      expect(screen.getByText('1850.5 pts')).toBeInTheDocument()
      expect(screen.getByText('75.0% win rate')).toBeInTheDocument()
      expect(screen.getByText('Top 10 Scenarios')).toBeInTheDocument()
    })
  })

  it('shows summary statistics', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockScenarios })

    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    const runButton = screen.getByText('Run Simulation')
    fireEvent.click(runButton)

    await waitFor(() => {
      expect(screen.getByText('Best Scenario')).toBeInTheDocument()
      expect(screen.getByText('Average')).toBeInTheDocument()
      expect(screen.getByText('Scenarios')).toBeInTheDocument()
      
      // Check average calculation
      const avgPoints = (1850.5 + 1820.3) / 2
      expect(screen.getByText(`${avgPoints.toFixed(1)} pts`)).toBeInTheDocument()
    })
  })

  it('allows changing number of simulations', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockScenarios })

    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    const select = screen.getByDisplayValue('1,000 sims')
    fireEvent.change(select, { target: { value: '5000' } })

    const runButton = screen.getByText('Run Simulation')
    fireEvent.click(runButton)

    await waitFor(() => {
      expect(mockedApi.post).toHaveBeenCalledWith('/draft/simulate', {
        league_id: 'league1',
        franchise_id: 'franchise1',
        num_simulations: 5000,
        season: 2024,
        strategy: 'VALUE_BASED'
      })
    })
  })

  it('expands scenario details when clicked', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockScenarios })

    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    const runButton = screen.getByText('Run Simulation')
    fireEvent.click(runButton)

    await waitFor(() => {
      const scenarioCard = screen.getByText('1850.5 projected points').closest('div')
      fireEvent.click(scenarioCard!)
    })

    await waitFor(() => {
      expect(screen.getByText('Draft Picks')).toBeInTheDocument()
      expect(screen.getByText('Final Roster')).toBeInTheDocument()
      expect(screen.getByText('1.1:')).toBeInTheDocument() // Round.Pick format
    })
  })

  it('handles API errors gracefully', async () => {
    mockedApi.post.mockRejectedValueOnce({
      response: { data: { detail: 'Simulation failed' } }
    })

    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    const runButton = screen.getByText('Run Simulation')
    fireEvent.click(runButton)

    await waitFor(() => {
      expect(screen.getByText('Simulation failed')).toBeInTheDocument()
    })
  })

  it('disables controls while simulation is running', async () => {
    // Mock a delayed response
    mockedApi.post.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({ data: mockScenarios }), 100))
    )

    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    const runButton = screen.getByText('Run Simulation')
    const select = screen.getByDisplayValue('1,000 sims')

    fireEvent.click(runButton)

    // Controls should be disabled while running
    expect(runButton).toBeDisabled()
    expect(select).toBeDisabled()
    expect(screen.getByText('Running...')).toBeInTheDocument()

    await waitFor(() => {
      expect(runButton).not.toBeDisabled()
      expect(select).not.toBeDisabled()
    })
  })

  it('shows scenario rankings correctly', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockScenarios })

    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    const runButton = screen.getByText('Run Simulation')
    fireEvent.click(runButton)

    await waitFor(() => {
      expect(screen.getByText('#1')).toBeInTheDocument()
      expect(screen.getByText('#2')).toBeInTheDocument()
    })
  })

  it('limits display to top 10 scenarios', async () => {
    const manyScenarios = Array.from({ length: 15 }, (_, i) => ({
      ...mockScenarios[0],
      scenario_id: `scenario${i}`,
      projected_points: 1800 - i * 10,
      win_probability: 0.7 - i * 0.02
    }))

    mockedApi.post.mockResolvedValueOnce({ data: manyScenarios })

    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    const runButton = screen.getByText('Run Simulation')
    fireEvent.click(runButton)

    await waitFor(() => {
      expect(screen.getByText('#1')).toBeInTheDocument()
      expect(screen.getByText('#10')).toBeInTheDocument()
      expect(screen.queryByText('#11')).not.toBeInTheDocument()
    })
  })

  it('shows roster composition in scenario cards', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockScenarios })

    render(
      <DraftSimulation
        leagueId="league1"
        franchiseId="franchise1"
        strategy="VALUE_BASED"
      />
    )

    const runButton = screen.getByText('Run Simulation')
    fireEvent.click(runButton)

    await waitFor(() => {
      expect(screen.getByText('QB: 1')).toBeInTheDocument()
      expect(screen.getByText('RB: 2')).toBeInTheDocument()
      expect(screen.getByText('WR: 2')).toBeInTheDocument()
      expect(screen.getByText('TE: 1')).toBeInTheDocument()
    })
  })
})