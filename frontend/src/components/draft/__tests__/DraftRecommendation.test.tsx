import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import DraftRecommendation from '../DraftRecommendation'
import { DraftRecommendationData } from '@/types/draft'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { beforeEach } from 'node:test'
import { describe } from 'node:test'

const mockRecommendation: DraftRecommendationData = {
  player_id: 'player1',
  player_name: '<PERSON>',
  position: 'RB',
  team: 'SF',
  projected_points: 285.5,
  value_over_replacement: 45.2,
  tier: 1,
  confidence: 0.85,
  rationale: '<PERSON><PERSON><PERSON><PERSON><PERSON> offers exceptional value as the top RB with elite dual-threat capability. His projected points significantly exceed replacement level, making him the clear choice at this draft position.',
  alternatives: ['player2', 'player3', 'player4'],
  positional_need_score: 2.5,
  opportunity_cost: 8.3
}

const mockOnSelectPlayer = jest.fn()

describe('DraftRecommendation', () => {
  beforeEach(() => {
    mockOnSelectPlayer.mockClear()
  })

  it('renders recommendation with player information', () => {
    render(
      <DraftRecommendation
        recommendation={mockRecommendation}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    expect(screen.getByText('Christian McCaffrey')).toBeInTheDocument()
    expect(screen.getByText('RB')).toBeInTheDocument()
    expect(screen.getByText('SF')).toBeInTheDocument()
    expect(screen.getByText('Tier 1')).toBeInTheDocument()
  })

  it('displays confidence rating correctly', () => {
    render(
      <DraftRecommendation
        recommendation={mockRecommendation}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    expect(screen.getByText('85% confidence')).toBeInTheDocument()
    expect(screen.getByText('Confidence Rating')).toBeInTheDocument()
    
    // Should show star rating elements
    const starContainer = screen.getByText('Confidence Rating').previousElementSibling
    expect(starContainer).toBeInTheDocument()
  })

  it('shows projected points and VOR', () => {
    render(
      <DraftRecommendation
        recommendation={mockRecommendation}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    expect(screen.getByText('285.5')).toBeInTheDocument()
    expect(screen.getByText('+45.2')).toBeInTheDocument()
    expect(screen.getByText('Projected Points')).toBeInTheDocument()
    expect(screen.getByText('Value Over Replacement')).toBeInTheDocument()
  })

  it('displays rationale text', () => {
    render(
      <DraftRecommendation
        recommendation={mockRecommendation}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    expect(screen.getByText(/McCaffrey offers exceptional value/)).toBeInTheDocument()
    expect(screen.getByText('Why This Pick?')).toBeInTheDocument()
  })

  it('shows draft button when draft is active', () => {
    render(
      <DraftRecommendation
        recommendation={mockRecommendation}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    const draftButton = screen.getByText('Draft Player')
    expect(draftButton).toBeInTheDocument()
    expect(draftButton).not.toBeDisabled()
  })

  it('hides draft button when draft is inactive', () => {
    render(
      <DraftRecommendation
        recommendation={mockRecommendation}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={false}
      />
    )

    expect(screen.queryByText('Draft Player')).not.toBeInTheDocument()
  })

  it('calls onSelectPlayer when draft button is clicked', () => {
    render(
      <DraftRecommendation
        recommendation={mockRecommendation}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    const draftButton = screen.getByText('Draft Player')
    fireEvent.click(draftButton)

    expect(mockOnSelectPlayer).toHaveBeenCalledTimes(1)
  })

  it('toggles advanced analysis section', () => {
    render(
      <DraftRecommendation
        recommendation={mockRecommendation}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    const advancedButton = screen.getByText('Advanced Analysis')
    
    // Advanced details should be hidden initially
    expect(screen.queryByText('Positional Need Score:')).not.toBeInTheDocument()
    
    // Click to show advanced details
    fireEvent.click(advancedButton)
    
    expect(screen.getByText('Positional Need Score:')).toBeInTheDocument()
    expect(screen.getByText('2.50')).toBeInTheDocument()
    expect(screen.getByText('Opportunity Cost:')).toBeInTheDocument()
    expect(screen.getByText('8.3 pts')).toBeInTheDocument()
  })

  it('toggles alternatives section', () => {
    render(
      <DraftRecommendation
        recommendation={mockRecommendation}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    const alternativesButton = screen.getByText('Alternative Options (3)')
    
    // Alternatives should be hidden initially
    expect(screen.queryByText('Alternative #1')).not.toBeInTheDocument()
    
    // Click to show alternatives
    fireEvent.click(alternativesButton)
    
    expect(screen.getByText('Alternative #1')).toBeInTheDocument()
    expect(screen.getByText('Alternative #2')).toBeInTheDocument()
    expect(screen.getByText('Alternative #3')).toBeInTheDocument()
  })

  it('shows live indicator', () => {
    render(
      <DraftRecommendation
        recommendation={mockRecommendation}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    expect(screen.getByText('Live')).toBeInTheDocument()
    expect(screen.getByText('Recommendation updated in real-time')).toBeInTheDocument()
  })

  it('applies correct confidence color coding', () => {
    // Test high confidence (green)
    const highConfidenceRec = { ...mockRecommendation, confidence: 0.9 }
    const { rerender } = render(
      <DraftRecommendation
        recommendation={highConfidenceRec}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    let confidenceBadge = screen.getByText('90% confidence')
    expect(confidenceBadge).toHaveClass('text-green-600')

    // Test medium confidence (yellow)
    const mediumConfidenceRec = { ...mockRecommendation, confidence: 0.65 }
    rerender(
      <DraftRecommendation
        recommendation={mediumConfidenceRec}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    confidenceBadge = screen.getByText('65% confidence')
    expect(confidenceBadge).toHaveClass('text-yellow-600')

    // Test low confidence (red)
    const lowConfidenceRec = { ...mockRecommendation, confidence: 0.45 }
    rerender(
      <DraftRecommendation
        recommendation={lowConfidenceRec}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    confidenceBadge = screen.getByText('45% confidence')
    expect(confidenceBadge).toHaveClass('text-red-600')
  })

  it('handles recommendations with no alternatives', () => {
    const noAlternativesRec = { ...mockRecommendation, alternatives: [] }
    
    render(
      <DraftRecommendation
        recommendation={noAlternativesRec}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    expect(screen.queryByText(/Alternative Options/)).not.toBeInTheDocument()
  })

  it('limits alternatives display to 3 items', () => {
    const manyAlternativesRec = { 
      ...mockRecommendation, 
      alternatives: ['p1', 'p2', 'p3', 'p4', 'p5'] 
    }
    
    render(
      <DraftRecommendation
        recommendation={manyAlternativesRec}
        onSelectPlayer={mockOnSelectPlayer}
        isDraftActive={true}
      />
    )

    const alternativesButton = screen.getByText('Alternative Options (5)')
    fireEvent.click(alternativesButton)
    
    expect(screen.getByText('Alternative #1')).toBeInTheDocument()
    expect(screen.getByText('Alternative #2')).toBeInTheDocument()
    expect(screen.getByText('Alternative #3')).toBeInTheDocument()
    expect(screen.queryByText('Alternative #4')).not.toBeInTheDocument()
  })
})