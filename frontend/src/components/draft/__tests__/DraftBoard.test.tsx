import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import DraftBoard from '../DraftBoard'
import { DraftBoardData } from '@/types/draft'

const mockDraftBoard: DraftBoardData = {
  tiers: [
    {
      tier_number: 1,
      position: 'RB',
      players: ['player1', 'player2'],
      min_value: 15.0,
      max_value: 18.5,
      avg_value: 16.75,
      tier_break_threshold: 2.0
    },
    {
      tier_number: 2,
      position: 'WR',
      players: ['player3', 'player4'],
      min_value: 12.0,
      max_value: 14.5,
      avg_value: 13.25,
      tier_break_threshold: 1.5
    }
  ],
  overall_rankings: ['player1', 'player2', 'player3', 'player4'],
  position_rankings: {
    RB: ['player1', 'player2'],
    WR: ['player3', 'player4']
  },
  value_over_replacement: {
    player1: 18.5,
    player2: 15.0,
    player3: 14.5,
    player4: 12.0
  },
  last_updated: '2024-01-01T12:00:00Z',
  strategy: 'VALUE_BASED'
}

const mockOnPlayerSelect = jest.fn()

describe('DraftBoard', () => {
  beforeEach(() => {
    mockOnPlayerSelect.mockClear()
  })

  it('renders draft board with tiers', () => {
    render(
      <DraftBoard
        draftBoard={mockDraftBoard}
        onPlayerSelect={mockOnPlayerSelect}
        currentPick={1}
        isDraftActive={true}
      />
    )

    expect(screen.getByText('Draft Board')).toBeInTheDocument()
    expect(screen.getByText('Tier 1')).toBeInTheDocument()
    expect(screen.getByText('Tier 2')).toBeInTheDocument()
  })

  it('shows tier information correctly', () => {
    render(
      <DraftBoard
        draftBoard={mockDraftBoard}
        onPlayerSelect={mockOnPlayerSelect}
        currentPick={1}
        isDraftActive={true}
      />
    )

    expect(screen.getByText('RB • 2 players')).toBeInTheDocument()
    expect(screen.getByText('WR • 2 players')).toBeInTheDocument()
    expect(screen.getByText('Avg: 16.8 pts')).toBeInTheDocument()
    expect(screen.getByText('Avg: 13.3 pts')).toBeInTheDocument()
  })

  it('expands and collapses tiers when clicked', async () => {
    render(
      <DraftBoard
        draftBoard={mockDraftBoard}
        onPlayerSelect={mockOnPlayerSelect}
        currentPick={1}
        isDraftActive={true}
      />
    )

    // Tier 1 should be expanded by default
    expect(screen.getByText('Player 0001')).toBeInTheDocument()

    // Click to collapse tier 1
    const tier1Header = screen.getByText('Tier 1').closest('div')
    fireEvent.click(tier1Header!)

    await waitFor(() => {
      expect(screen.queryByText('Player 0001')).not.toBeInTheDocument()
    })
  })

  it('filters players by position', async () => {
    render(
      <DraftBoard
        draftBoard={mockDraftBoard}
        onPlayerSelect={mockOnPlayerSelect}
        currentPick={1}
        isDraftActive={true}
      />
    )

    // Click RB filter
    const rbButton = screen.getByText('RB')
    fireEvent.click(rbButton)

    await waitFor(() => {
      // Should only show RB tier
      expect(screen.getByText('Tier 1')).toBeInTheDocument()
      expect(screen.queryByText('Tier 2')).not.toBeInTheDocument()
    })
  })

  it('searches players by name', async () => {
    render(
      <DraftBoard
        draftBoard={mockDraftBoard}
        onPlayerSelect={mockOnPlayerSelect}
        currentPick={1}
        isDraftActive={true}
      />
    )

    const searchInput = screen.getByPlaceholderText('Search players...')
    fireEvent.change(searchInput, { target: { value: '0001' } })

    await waitFor(() => {
      // Should filter to only show matching players
      expect(screen.getByText('Player 0001')).toBeInTheDocument()
    })
  })

  it('calls onPlayerSelect when draft button is clicked', async () => {
    render(
      <DraftBoard
        draftBoard={mockDraftBoard}
        onPlayerSelect={mockOnPlayerSelect}
        currentPick={1}
        isDraftActive={true}
      />
    )

    // Find and click a draft button
    const draftButtons = screen.getAllByText('Draft')
    fireEvent.click(draftButtons[0])

    expect(mockOnPlayerSelect).toHaveBeenCalledWith('player1')
  })

  it('disables draft buttons when draft is not active', () => {
    render(
      <DraftBoard
        draftBoard={mockDraftBoard}
        onPlayerSelect={mockOnPlayerSelect}
        currentPick={1}
        isDraftActive={false}
      />
    )

    // Draft buttons should not be present when draft is inactive
    expect(screen.queryByText('Draft')).not.toBeInTheDocument()
  })

  it('displays player statistics correctly', () => {
    render(
      <DraftBoard
        draftBoard={mockDraftBoard}
        onPlayerSelect={mockOnPlayerSelect}
        currentPick={1}
        isDraftActive={true}
      />
    )

    // Check that VOR values are displayed
    expect(screen.getByText('+18.5')).toBeInTheDocument()
    expect(screen.getByText('+15.0')).toBeInTheDocument()
  })

  it('shows last updated timestamp', () => {
    render(
      <DraftBoard
        draftBoard={mockDraftBoard}
        onPlayerSelect={mockOnPlayerSelect}
        currentPick={1}
        isDraftActive={true}
      />
    )

    expect(screen.getByText(/Last updated:/)).toBeInTheDocument()
  })

  it('handles empty search results', async () => {
    render(
      <DraftBoard
        draftBoard={mockDraftBoard}
        onPlayerSelect={mockOnPlayerSelect}
        currentPick={1}
        isDraftActive={true}
      />
    )

    const searchInput = screen.getByPlaceholderText('Search players...')
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } })

    await waitFor(() => {
      expect(screen.getByText('No players found matching your criteria')).toBeInTheDocument()
    })
  })
})