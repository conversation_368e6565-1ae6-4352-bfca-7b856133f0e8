import { render, screen, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import DraftTimer from '../DraftTimer'

const mockOnTimeExpired = jest.fn()

// Mock Date to control time
const mockDate = new Date('2024-01-01T12:00:00Z')
const originalDate = Date

beforeAll(() => {
  global.Date = jest.fn(() => mockDate) as any
  global.Date.getTime = originalDate.getTime
  global.Date.now = jest.fn(() => mockDate.getTime())
})

afterAll(() => {
  global.Date = originalDate
})

describe('DraftTimer', () => {
  beforeEach(() => {
    mockOnTimeExpired.mockClear()
    jest.clearAllTimers()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.runOnlyPendingTimers()
    jest.useRealTimers()
  })

  it('renders timer with correct initial time', () => {
    const deadline = new Date(mockDate.getTime() + 120000) // 2 minutes from now
    
    render(
      <DraftTimer
        deadline={deadline}
        onTimeExpired={mockOnTimeExpired}
        pickNumber={1}
        franchiseName="Test Team"
      />
    )

    expect(screen.getByText('2:00')).toBeInTheDocument()
    expect(screen.getByText('Pick 1')).toBeInTheDocument()
    expect(screen.getByText('• Test Team')).toBeInTheDocument()
  })

  it('updates countdown every second', () => {
    const deadline = new Date(mockDate.getTime() + 120000) // 2 minutes from now
    
    render(
      <DraftTimer
        deadline={deadline}
        onTimeExpired={mockOnTimeExpired}
      />
    )

    expect(screen.getByText('2:00')).toBeInTheDocument()

    // Advance time by 1 second
    act(() => {
      jest.advanceTimersByTime(1000)
    })

    expect(screen.getByText('1:59')).toBeInTheDocument()
  })

  it('shows warning when less than 30 seconds remain', () => {
    const deadline = new Date(mockDate.getTime() + 25000) // 25 seconds from now
    
    render(
      <DraftTimer
        deadline={deadline}
        onTimeExpired={mockOnTimeExpired}
      />
    )

    expect(screen.getByText('⚠️ Less than 30 seconds remaining!')).toBeInTheDocument()
  })

  it('shows auto-pick warning when less than 10 seconds remain', () => {
    const deadline = new Date(mockDate.getTime() + 8000) // 8 seconds from now
    
    render(
      <DraftTimer
        deadline={deadline}
        onTimeExpired={mockOnTimeExpired}
      />
    )

    expect(screen.getByText('🚨 Auto-pick in 8 seconds')).toBeInTheDocument()
  })

  it('calls onTimeExpired when timer reaches zero', () => {
    const deadline = new Date(mockDate.getTime() + 1000) // 1 second from now
    
    render(
      <DraftTimer
        deadline={deadline}
        onTimeExpired={mockOnTimeExpired}
      />
    )

    // Advance time past deadline
    act(() => {
      jest.advanceTimersByTime(2000)
    })

    expect(mockOnTimeExpired).toHaveBeenCalledTimes(1)
  })

  it('shows expired state when time is up', () => {
    const deadline = new Date(mockDate.getTime() - 1000) // 1 second ago
    
    render(
      <DraftTimer
        deadline={deadline}
        onTimeExpired={mockOnTimeExpired}
        franchiseName="Test Team"
      />
    )

    expect(screen.getByText('Time Expired')).toBeInTheDocument()
    expect(screen.getByText("Test Team's pick time has expired")).toBeInTheDocument()
  })

  it('shows correct timer colors based on remaining time', () => {
    // Green zone (> 50% time remaining)
    const deadline1 = new Date(mockDate.getTime() + 90000) // 1.5 minutes from now
    const { rerender } = render(
      <DraftTimer
        deadline={deadline1}
        onTimeExpired={mockOnTimeExpired}
      />
    )

    let timerContainer = screen.getByText('1:30').closest('div')
    expect(timerContainer).toHaveClass('text-green-600')

    // Yellow zone (25-50% time remaining)
    const deadline2 = new Date(mockDate.getTime() + 45000) // 45 seconds from now
    rerender(
      <DraftTimer
        deadline={deadline2}
        onTimeExpired={mockOnTimeExpired}
      />
    )

    timerContainer = screen.getByText('0:45').closest('div')
    expect(timerContainer).toHaveClass('text-yellow-600')

    // Red zone (< 25% time remaining)
    const deadline3 = new Date(mockDate.getTime() + 20000) // 20 seconds from now
    rerender(
      <DraftTimer
        deadline={deadline3}
        onTimeExpired={mockOnTimeExpired}
      />
    )

    timerContainer = screen.getByText('0:20').closest('div')
    expect(timerContainer).toHaveClass('text-red-600')
  })

  it('formats time correctly for different durations', () => {
    // Test various time formats
    const testCases = [
      { seconds: 125, expected: '2:05' },
      { seconds: 60, expected: '1:00' },
      { seconds: 59, expected: '0:59' },
      { seconds: 5, expected: '0:05' },
      { seconds: 0, expected: '0:00' }
    ]

    testCases.forEach(({ seconds, expected }) => {
      const deadline = new Date(mockDate.getTime() + seconds * 1000)
      const { rerender } = render(
        <DraftTimer
          deadline={deadline}
          onTimeExpired={mockOnTimeExpired}
        />
      )

      expect(screen.getByText(expected)).toBeInTheDocument()
    })
  })

  it('handles past deadline gracefully', () => {
    const deadline = new Date(mockDate.getTime() - 60000) // 1 minute ago
    
    render(
      <DraftTimer
        deadline={deadline}
        onTimeExpired={mockOnTimeExpired}
      />
    )

    expect(screen.getByText('Time Expired')).toBeInTheDocument()
    expect(mockOnTimeExpired).toHaveBeenCalledTimes(1)
  })
})