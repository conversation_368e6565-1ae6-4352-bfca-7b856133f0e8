import { useState, useEffect } from 'react'
import { ClockIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'

interface DeadlineCountdownProps {
  deadline: Date
  onDeadlineReached?: () => void
}

interface TimeRemaining {
  days: number
  hours: number
  minutes: number
  seconds: number
  total: number
}

export default function DeadlineCountdown({ deadline, onDeadlineReached }: DeadlineCountdownProps) {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({ 
    days: 0, 
    hours: 0, 
    minutes: 0, 
    seconds: 0, 
    total: 0 
  })

  useEffect(() => {
    const calculateTimeRemaining = () => {
      const now = new Date().getTime()
      const deadlineTime = deadline.getTime()
      const difference = deadlineTime - now

      if (difference <= 0) {
        setTimeRemaining({ days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 })
        if (onDeadlineReached) {
          onDeadlineReached()
        }
        return
      }

      const days = Math.floor(difference / (1000 * 60 * 60 * 24))
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((difference % (1000 * 60)) / 1000)

      setTimeRemaining({ days, hours, minutes, seconds, total: difference })
    }

    // Calculate immediately
    calculateTimeRemaining()

    // Update every second
    const interval = setInterval(calculateTimeRemaining, 1000)

    return () => clearInterval(interval)
  }, [deadline, onDeadlineReached])

  const getUrgencyLevel = () => {
    const totalHours = timeRemaining.total / (1000 * 60 * 60)
    
    if (totalHours <= 0) return 'expired'
    if (totalHours <= 24) return 'critical'
    if (totalHours <= 72) return 'urgent'
    if (totalHours <= 168) return 'warning' // 1 week
    return 'normal'
  }

  const getUrgencyStyles = () => {
    const urgency = getUrgencyLevel()
    
    switch (urgency) {
      case 'expired':
        return {
          container: 'bg-red-100 border-red-300 text-red-900',
          icon: 'text-red-600',
          pulse: 'animate-pulse'
        }
      case 'critical':
        return {
          container: 'bg-red-50 border-red-200 text-red-900',
          icon: 'text-red-500',
          pulse: 'animate-pulse'
        }
      case 'urgent':
        return {
          container: 'bg-yellow-50 border-yellow-200 text-yellow-900',
          icon: 'text-yellow-500',
          pulse: ''
        }
      case 'warning':
        return {
          container: 'bg-orange-50 border-orange-200 text-orange-900',
          icon: 'text-orange-500',
          pulse: ''
        }
      default:
        return {
          container: 'bg-blue-50 border-blue-200 text-blue-900',
          icon: 'text-blue-500',
          pulse: ''
        }
    }
  }

  const formatDeadline = () => {
    return deadline.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      timeZoneName: 'short'
    })
  }

  const styles = getUrgencyStyles()
  const urgency = getUrgencyLevel()

  if (urgency === 'expired') {
    return (
      <div className={`inline-flex items-center px-4 py-2 rounded-lg border-2 ${styles.container} ${styles.pulse}`}>
        <ExclamationTriangleIcon className={`h-5 w-5 ${styles.icon} mr-2`} />
        <div className="text-center">
          <div className="text-sm font-semibold">Keeper Deadline</div>
          <div className="text-xs">EXPIRED</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`inline-flex items-center px-4 py-2 rounded-lg border-2 ${styles.container} ${styles.pulse}`}>
      <ClockIcon className={`h-5 w-5 ${styles.icon} mr-3`} />
      <div>
        <div className="text-sm font-semibold mb-1">Keeper Deadline</div>
        
        {/* Countdown Display */}
        <div className="flex items-center space-x-2 text-xs">
          {timeRemaining.days > 0 && (
            <div className="text-center">
              <div className="font-bold text-lg leading-none">{timeRemaining.days}</div>
              <div className="text-xs">day{timeRemaining.days !== 1 ? 's' : ''}</div>
            </div>
          )}
          
          {(timeRemaining.days > 0 || timeRemaining.hours > 0) && (
            <>
              {timeRemaining.days > 0 && <span className="text-gray-400">:</span>}
              <div className="text-center">
                <div className="font-bold text-lg leading-none">{timeRemaining.hours}</div>
                <div className="text-xs">hr{timeRemaining.hours !== 1 ? 's' : ''}</div>
              </div>
            </>
          )}
          
          {urgency === 'critical' && (
            <>
              <span className="text-gray-400">:</span>
              <div className="text-center">
                <div className="font-bold text-lg leading-none">{timeRemaining.minutes}</div>
                <div className="text-xs">min</div>
              </div>
              <span className="text-gray-400">:</span>
              <div className="text-center">
                <div className="font-bold text-lg leading-none">{timeRemaining.seconds}</div>
                <div className="text-xs">sec</div>
              </div>
            </>
          )}
          
          {urgency !== 'critical' && timeRemaining.days === 0 && (
            <>
              <span className="text-gray-400">:</span>
              <div className="text-center">
                <div className="font-bold text-lg leading-none">{timeRemaining.minutes}</div>
                <div className="text-xs">min</div>
              </div>
            </>
          )}
        </div>
        
        {/* Deadline Date */}
        <div className="text-xs mt-1 opacity-75">
          Due: {formatDeadline()}
        </div>
      </div>
    </div>
  )
}