import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import Sc<PERSON><PERSON>Comparison from '../ScenarioComparison'
import { KeeperScenar<PERSON> } from '@/lib/keeper-api'

const mockScenarios: KeeperScenario[] = [
  {
    scenario_name: 'Optimal',
    selected_keepers: [
      {
        player_id: '1',
        player_name: '<PERSON>',
        position: 'RB',
        keeper_cost: 1,
        projected_points: 285.2,
        value_over_replacement: 105.2,
        confidence: 0.88,
        rationale: 'Exceptional value with 105.2 points over replacement, excellent keeper value at round 1.',
        alternatives: [
          {
            player_id: '2',
            player_name: '<PERSON><PERSON><PERSON><PERSON>',
            position: 'RB',
            keeper_cost: 3,
            value_over_replacement: 85.4,
            reason: 'Alternative RB option'
          }
        ],
        metadata: {}
      },
      {
        player_id: '3',
        player_name: 'Tyreek Hill',
        position: 'WR',
        keeper_cost: 2,
        projected_points: 265.4,
        value_over_replacement: 115.4,
        confidence: 0.85,
        rationale: 'Exceptional value with 115.4 points over replacement, excellent keeper value at round 2.',
        alternatives: [],
        metadata: {}
      }
    ],
    total_value: 220.6,
    remaining_budget: 150,
    constraints_satisfied: true,
    trade_offs: ['Passed on <PERSON> (70.5 VOR) due to cost/constraint considerations'],
    metadata: {
      total_cost: 3,
      max_keepers: 3,
      objective_weights: { vor: 0.8, cost: 0.2 },
      solver_status: 'OPTIMAL',
      generation_timestamp: '2024-08-19T12:00:00Z'
    }
  },
  {
    scenario_name: 'Value-Focused',
    selected_keepers: [
      {
        player_id: '3',
        player_name: 'Tyreek Hill',
        position: 'WR',
        keeper_cost: 2,
        projected_points: 265.4,
        value_over_replacement: 115.4,
        confidence: 0.85,
        rationale: 'Highest value over replacement player available.',
        alternatives: [],
        metadata: {}
      }
    ],
    total_value: 115.4,
    remaining_budget: 200,
    constraints_satisfied: true,
    trade_offs: [],
    metadata: {
      total_cost: 2,
      max_keepers: 3,
      objective_weights: { vor: 1.0, cost: 0.0 },
      solver_status: 'OPTIMAL',
      generation_timestamp: '2024-08-19T12:00:00Z'
    }
  },
  {
    scenario_name: 'Cost-Efficient',
    selected_keepers: [
      {
        player_id: '1',
        player_name: 'Christian McCaffrey',
        position: 'RB',
        keeper_cost: 1,
        projected_points: 285.2,
        value_over_replacement: 105.2,
        confidence: 0.88,
        rationale: 'Best value at lowest cost.',
        alternatives: [],
        metadata: {}
      }
    ],
    total_value: 105.2,
    remaining_budget: 250,
    constraints_satisfied: true,
    trade_offs: [],
    metadata: {
      total_cost: 1,
      max_keepers: 3,
      objective_weights: { vor: 0.3, cost: 0.7 },
      solver_status: 'OPTIMAL',
      generation_timestamp: '2024-08-19T12:00:00Z'
    }
  }
]

const mockOnScenarioSelect = jest.fn()

describe('ScenarioComparison', () => {
  beforeEach(() => {
    mockOnScenarioSelect.mockClear()
  })

  it('renders scenario comparison interface', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    expect(screen.getByText('Scenario Comparison')).toBeInTheDocument()
    expect(screen.getByText('Compare different keeper optimization strategies')).toBeInTheDocument()
  })

  it('displays all scenarios', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    expect(screen.getByText('Optimal')).toBeInTheDocument()
    expect(screen.getByText('Value-Focused')).toBeInTheDocument()
    expect(screen.getByText('Cost-Efficient')).toBeInTheDocument()
  })

  it('shows recommended badge for first scenario', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    expect(screen.getByText('Recommended')).toBeInTheDocument()
  })

  it('displays scenario descriptions correctly', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    expect(screen.getByText('Balanced approach maximizing value while considering cost')).toBeInTheDocument()
    expect(screen.getByText('Prioritizes highest value over replacement players')).toBeInTheDocument()
    expect(screen.getByText('Minimizes total keeper costs while maintaining value')).toBeInTheDocument()
  })

  it('displays scenario stats correctly', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    // Optimal scenario stats
    expect(screen.getByText('220.6 VOR')).toBeInTheDocument()
    expect(screen.getByText('2 keepers')).toBeInTheDocument()
    expect(screen.getByText('Total Cost: 3 rounds')).toBeInTheDocument()
    expect(screen.getByText('Remaining Budget: $150')).toBeInTheDocument()

    // Value-Focused scenario stats
    expect(screen.getByText('115.4 VOR')).toBeInTheDocument()
    expect(screen.getByText('1 keeper')).toBeInTheDocument()
  })

  it('handles scenario selection', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    const valueFocusedScenario = screen.getByText('Value-Focused').closest('.rounded-lg')
    fireEvent.click(valueFocusedScenario!)

    expect(mockOnScenarioSelect).toHaveBeenCalledWith('Value-Focused')
  })

  it('expands and collapses scenario details', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    // Optimal should be expanded by default
    expect(screen.getByText('Selected Keepers')).toBeInTheDocument()
    expect(screen.getByText('Christian McCaffrey')).toBeInTheDocument()
    expect(screen.getByText('Tyreek Hill')).toBeInTheDocument()

    // Click to collapse
    const collapseButton = screen.getAllByRole('button')[0] // First chevron button
    fireEvent.click(collapseButton)

    // Should not show expanded details
    expect(screen.queryByText('Selected Keepers')).not.toBeInTheDocument()
  })

  it('displays selected keeper details in expanded view', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    // Should show keeper details
    expect(screen.getByText('Christian McCaffrey')).toBeInTheDocument()
    expect(screen.getByText('285.2')).toBeInTheDocument() // Projected points
    expect(screen.getByText('105.2')).toBeInTheDocument() // VOR
    expect(screen.getByText('Rd 1')).toBeInTheDocument() // Cost
    expect(screen.getByText('88%')).toBeInTheDocument() // Confidence
  })

  it('displays trade-offs when present', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    expect(screen.getByText('Trade-offs')).toBeInTheDocument()
    expect(screen.getByText('Passed on Josh Allen (70.5 VOR) due to cost/constraint considerations')).toBeInTheDocument()
  })

  it('displays top keeper rationale', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    expect(screen.getByText('Top Keeper Rationale')).toBeInTheDocument()
    expect(screen.getByText('Exceptional value with 105.2 points over replacement, excellent keeper value at round 1.')).toBeInTheDocument()
  })

  it('shows constraint satisfaction status', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    // All mock scenarios have constraints_satisfied: true
    const validStatuses = screen.getAllByText('Valid')
    expect(validStatuses.length).toBeGreaterThan(0)
  })

  it('displays quick comparison summary', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    expect(screen.getByText('Quick Comparison')).toBeInTheDocument()
    expect(screen.getByText('Highest Value: Optimal')).toBeInTheDocument() // 220.6 VOR
    expect(screen.getByText('Lowest Cost: Cost-Efficient')).toBeInTheDocument() // 1 round
    expect(screen.getByText('Most Keepers: Optimal')).toBeInTheDocument() // 2 keepers
  })

  it('shows empty state when no scenarios provided', () => {
    render(
      <ScenarioComparison
        scenarios={[]}
        selectedScenario=""
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    expect(screen.getByText('No scenarios available')).toBeInTheDocument()
    expect(screen.getByText('Scenarios will appear here once keeper candidates are loaded.')).toBeInTheDocument()
  })

  it('highlights selected scenario correctly', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Value-Focused"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    const valueFocusedScenario = screen.getByText('Value-Focused').closest('.rounded-lg')
    expect(valueFocusedScenario).toHaveClass('bg-green-50', 'border-green-200', 'text-green-900')
  })

  it('handles scenarios without remaining budget', () => {
    const scenariosWithoutBudget = mockScenarios.map(s => ({
      ...s,
      remaining_budget: undefined
    }))

    render(
      <ScenarioComparison
        scenarios={scenariosWithoutBudget}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    // Should not show remaining budget
    expect(screen.queryByText(/Remaining Budget/)).not.toBeInTheDocument()
  })

  it('expands Value-Focused scenario when clicked', () => {
    render(
      <ScenarioComparison
        scenarios={mockScenarios}
        selectedScenario="Optimal"
        onScenarioSelect={mockOnScenarioSelect}
      />
    )

    // Find and click the expand button for Value-Focused scenario
    const expandButtons = screen.getAllByRole('button')
    const valueFocusedExpandButton = expandButtons.find(button => 
      button.closest('.p-6')?.textContent?.includes('Value-Focused')
    )
    
    fireEvent.click(valueFocusedExpandButton!)

    // Should show Tyreek Hill details
    expect(screen.getByText('Tyreek Hill')).toBeInTheDocument()
  })
})