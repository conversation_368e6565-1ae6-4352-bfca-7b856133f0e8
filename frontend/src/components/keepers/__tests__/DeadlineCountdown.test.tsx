import { render, screen, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import DeadlineCountdown from '../DeadlineCountdown'

// Mock Date.now() to control time
const mockNow = jest.spyOn(Date, 'now')

describe('DeadlineCountdown', () => {
  beforeEach(() => {
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
    mockNow.mockRestore()
  })

  it('renders countdown interface', () => {
    const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    
    render(<DeadlineCountdown deadline={futureDate} />)

    expect(screen.getByText('Keeper Deadline')).toBeInTheDocument()
  })

  it('displays days and hours for deadline more than 24 hours away', () => {
    const futureDate = new Date(Date.now() + 2 * 24 * 60 * 60 * 1000 + 5 * 60 * 60 * 1000) // 2 days 5 hours from now
    
    render(<DeadlineCountdown deadline={futureDate} />)

    expect(screen.getByText('2')).toBeInTheDocument()
    expect(screen.getByText('days')).toBeInTheDocument()
    expect(screen.getByText('5')).toBeInTheDocument()
    expect(screen.getByText('hrs')).toBeInTheDocument()
  })

  it('displays hours and minutes for deadline within 24 hours', () => {
    const futureDate = new Date(Date.now() + 5 * 60 * 60 * 1000 + 30 * 60 * 1000) // 5 hours 30 minutes from now
    
    render(<DeadlineCountdown deadline={futureDate} />)

    expect(screen.getByText('5')).toBeInTheDocument()
    expect(screen.getByText('hrs')).toBeInTheDocument()
    expect(screen.getByText('30')).toBeInTheDocument()
    expect(screen.getByText('min')).toBeInTheDocument()
  })

  it('displays minutes and seconds for critical deadline (within 1 hour)', () => {
    const futureDate = new Date(Date.now() + 30 * 60 * 1000 + 45 * 1000) // 30 minutes 45 seconds from now
    
    render(<DeadlineCountdown deadline={futureDate} />)

    expect(screen.getByText('30')).toBeInTheDocument()
    expect(screen.getByText('min')).toBeInTheDocument()
    expect(screen.getByText('45')).toBeInTheDocument()
    expect(screen.getByText('sec')).toBeInTheDocument()
  })

  it('shows expired state for past deadline', () => {
    const pastDate = new Date(Date.now() - 60 * 1000) // 1 minute ago
    
    render(<DeadlineCountdown deadline={pastDate} />)

    expect(screen.getByText('EXPIRED')).toBeInTheDocument()
  })

  it('updates countdown every second', () => {
    const futureDate = new Date(Date.now() + 65 * 1000) // 1 minute 5 seconds from now
    
    render(<DeadlineCountdown deadline={futureDate} />)

    // Initially should show 1 minute 5 seconds
    expect(screen.getByText('1')).toBeInTheDocument()
    expect(screen.getByText('5')).toBeInTheDocument()

    // Advance time by 1 second
    act(() => {
      jest.advanceTimersByTime(1000)
    })

    // Should now show 1 minute 4 seconds
    expect(screen.getByText('1')).toBeInTheDocument()
    expect(screen.getByText('4')).toBeInTheDocument()
  })

  it('applies correct urgency styling for critical deadline', () => {
    const criticalDate = new Date(Date.now() + 30 * 60 * 1000) // 30 minutes from now
    
    render(<DeadlineCountdown deadline={criticalDate} />)

    const container = screen.getByText('Keeper Deadline').closest('div')
    expect(container).toHaveClass('bg-red-50', 'border-red-200', 'text-red-900')
    expect(container).toHaveClass('animate-pulse')
  })

  it('applies correct urgency styling for urgent deadline', () => {
    const urgentDate = new Date(Date.now() + 48 * 60 * 60 * 1000) // 48 hours from now
    
    render(<DeadlineCountdown deadline={urgentDate} />)

    const container = screen.getByText('Keeper Deadline').closest('div')
    expect(container).toHaveClass('bg-yellow-50', 'border-yellow-200', 'text-yellow-900')
  })

  it('applies correct urgency styling for warning deadline', () => {
    const warningDate = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) // 5 days from now
    
    render(<DeadlineCountdown deadline={warningDate} />)

    const container = screen.getByText('Keeper Deadline').closest('div')
    expect(container).toHaveClass('bg-orange-50', 'border-orange-200', 'text-orange-900')
  })

  it('applies correct urgency styling for normal deadline', () => {
    const normalDate = new Date(Date.now() + 10 * 24 * 60 * 60 * 1000) // 10 days from now
    
    render(<DeadlineCountdown deadline={normalDate} />)

    const container = screen.getByText('Keeper Deadline').closest('div')
    expect(container).toHaveClass('bg-blue-50', 'border-blue-200', 'text-blue-900')
  })

  it('displays formatted deadline date', () => {
    const deadline = new Date('2024-08-30T23:59:59')
    
    render(<DeadlineCountdown deadline={deadline} />)

    expect(screen.getByText(/Due:/)).toBeInTheDocument()
    expect(screen.getByText(/Aug 30/)).toBeInTheDocument()
  })

  it('calls onDeadlineReached when deadline is reached', () => {
    const onDeadlineReached = jest.fn()
    const futureDate = new Date(Date.now() + 1000) // 1 second from now
    
    render(<DeadlineCountdown deadline={futureDate} onDeadlineReached={onDeadlineReached} />)

    // Advance time past the deadline
    act(() => {
      jest.advanceTimersByTime(2000)
    })

    expect(onDeadlineReached).toHaveBeenCalled()
  })

  it('handles singular vs plural time units correctly', () => {
    const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000) // 1 day 1 hour from now
    
    render(<DeadlineCountdown deadline={futureDate} />)

    expect(screen.getByText('day')).toBeInTheDocument() // singular
    expect(screen.getByText('hr')).toBeInTheDocument() // singular
  })

  it('handles multiple days correctly', () => {
    const futureDate = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now
    
    render(<DeadlineCountdown deadline={futureDate} />)

    expect(screen.getByText('3')).toBeInTheDocument()
    expect(screen.getByText('days')).toBeInTheDocument() // plural
  })

  it('shows expired styling with exclamation icon', () => {
    const pastDate = new Date(Date.now() - 60 * 1000) // 1 minute ago
    
    render(<DeadlineCountdown deadline={pastDate} />)

    const container = screen.getByText('EXPIRED').closest('div')
    expect(container).toHaveClass('bg-red-100', 'border-red-300', 'text-red-900')
    expect(container).toHaveClass('animate-pulse')
  })

  it('cleans up interval on unmount', () => {
    const futureDate = new Date(Date.now() + 60 * 1000)
    const clearIntervalSpy = jest.spyOn(global, 'clearInterval')
    
    const { unmount } = render(<DeadlineCountdown deadline={futureDate} />)
    
    unmount()
    
    expect(clearIntervalSpy).toHaveBeenCalled()
    clearIntervalSpy.mockRestore()
  })
})