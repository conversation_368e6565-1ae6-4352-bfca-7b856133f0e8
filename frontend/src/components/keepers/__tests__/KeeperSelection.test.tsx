import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import KeeperSelection from '../KeeperSelection'
import { KeeperCandidate } from '@/lib/keeper-api'

const mockCandidates: KeeperCandidate[] = [
  {
    player_id: '1',
    player_name: '<PERSON>',
    position: 'QB',
    current_cost: 8,
    projected_points: 320.5,
    replacement_level: 250.0,
    value_over_replacement: 70.5,
    keeper_cost: 7,
    is_eligible: true,
    constraints_violated: [],
    metadata: {
      team: 'BUF',
      bye_week: 7,
      injury_status: 'HEALTHY',
      projection_confidence: 0.15,
      source_count: 5
    }
  },
  {
    player_id: '2',
    player_name: '<PERSON>',
    position: 'RB',
    current_cost: 2,
    projected_points: 285.2,
    replacement_level: 180.0,
    value_over_replacement: 105.2,
    keeper_cost: 1,
    is_eligible: true,
    constraints_violated: [],
    metadata: {
      team: 'SF',
      bye_week: 9,
      injury_status: 'HEALTHY',
      projection_confidence: 0.12,
      source_count: 6
    }
  },
  {
    player_id: '3',
    player_name: '<PERSON>',
    position: 'WR',
    current_cost: 5,
    projected_points: 245.8,
    replacement_level: 150.0,
    value_over_replacement: 95.8,
    keeper_cost: 4,
    is_eligible: false,
    constraints_violated: ['Player is OUT'],
    metadata: {
      team: 'LAR',
      bye_week: 6,
      injury_status: 'OUT',
      projection_confidence: 0.25,
      source_count: 4
    }
  }
]

const mockOnSelectionChange = jest.fn()

describe('KeeperSelection', () => {
  beforeEach(() => {
    mockOnSelectionChange.mockClear()
  })

  it('renders keeper selection interface', () => {
    render(
      <KeeperSelection 
        candidates={mockCandidates} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    expect(screen.getByText('Eligible Players')).toBeInTheDocument()
    expect(screen.getByText('Select players to keep for next season')).toBeInTheDocument()
  })

  it('displays all candidates by default', () => {
    render(
      <KeeperSelection 
        candidates={mockCandidates} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    expect(screen.getByText('Josh Allen')).toBeInTheDocument()
    expect(screen.getByText('Christian McCaffrey')).toBeInTheDocument()
    expect(screen.getByText('Cooper Kupp')).toBeInTheDocument()
  })

  it('filters to eligible players only when toggle is enabled', () => {
    render(
      <KeeperSelection 
        candidates={mockCandidates} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    // Should show eligible players by default (toggle is on)
    expect(screen.getByText('Josh Allen')).toBeInTheDocument()
    expect(screen.getByText('Christian McCaffrey')).toBeInTheDocument()
    expect(screen.queryByText('Cooper Kupp')).not.toBeInTheDocument()

    // Turn off eligible only filter
    const eligibleToggle = screen.getByLabelText('Show eligible only')
    fireEvent.click(eligibleToggle)

    // Now should show all players
    expect(screen.getByText('Josh Allen')).toBeInTheDocument()
    expect(screen.getByText('Christian McCaffrey')).toBeInTheDocument()
    expect(screen.getByText('Cooper Kupp')).toBeInTheDocument()
  })

  it('filters by position correctly', () => {
    render(
      <KeeperSelection 
        candidates={mockCandidates} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    // Filter to QB only
    const positionSelect = screen.getByDisplayValue('All Positions')
    fireEvent.change(positionSelect, { target: { value: 'QB' } })

    expect(screen.getByText('Josh Allen')).toBeInTheDocument()
    expect(screen.queryByText('Christian McCaffrey')).not.toBeInTheDocument()
  })

  it('sorts candidates correctly', () => {
    render(
      <KeeperSelection 
        candidates={mockCandidates} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    // Default sort should be by VOR (Christian McCaffrey first with 105.2)
    const playerElements = screen.getAllByText(/Josh Allen|Christian McCaffrey/)
    expect(playerElements[0]).toHaveTextContent('Christian McCaffrey')

    // Change sort to projected points
    const sortSelect = screen.getByDisplayValue('Value Over Replacement')
    fireEvent.change(sortSelect, { target: { value: 'points' } })

    // Josh Allen should be first now (320.5 points)
    const updatedPlayerElements = screen.getAllByText(/Josh Allen|Christian McCaffrey/)
    expect(updatedPlayerElements[0]).toHaveTextContent('Josh Allen')
  })

  it('handles player selection correctly', async () => {
    render(
      <KeeperSelection 
        candidates={mockCandidates} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    // Select Josh Allen
    const joshAllenCheckbox = screen.getAllByRole('checkbox')[1] // First is the eligible-only toggle
    fireEvent.click(joshAllenCheckbox)

    await waitFor(() => {
      expect(mockOnSelectionChange).toHaveBeenCalledWith([mockCandidates[0]])
    })

    // Should show selection summary
    expect(screen.getByText('1 player selected')).toBeInTheDocument()
  })

  it('handles multiple player selections', async () => {
    render(
      <KeeperSelection 
        candidates={mockCandidates} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    // Select both eligible players
    const checkboxes = screen.getAllByRole('checkbox')
    fireEvent.click(checkboxes[1]) // Josh Allen
    fireEvent.click(checkboxes[2]) // Christian McCaffrey

    await waitFor(() => {
      expect(mockOnSelectionChange).toHaveBeenLastCalledWith([
        mockCandidates[0], 
        mockCandidates[1]
      ])
    })

    expect(screen.getByText('2 players selected')).toBeInTheDocument()
  })

  it('clears selection when clear button is clicked', async () => {
    render(
      <KeeperSelection 
        candidates={mockCandidates} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    // Select a player first
    const checkbox = screen.getAllByRole('checkbox')[1]
    fireEvent.click(checkbox)

    // Click clear selection
    const clearButton = screen.getByText('Clear selection')
    fireEvent.click(clearButton)

    await waitFor(() => {
      expect(mockOnSelectionChange).toHaveBeenLastCalledWith([])
    })

    expect(screen.queryByText('1 player selected')).not.toBeInTheDocument()
  })

  it('displays constraint violations for ineligible players', () => {
    render(
      <KeeperSelection 
        candidates={mockCandidates} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    // Turn off eligible only filter to see Cooper Kupp
    const eligibleToggle = screen.getByLabelText('Show eligible only')
    fireEvent.click(eligibleToggle)

    expect(screen.getByText('Player is OUT')).toBeInTheDocument()
  })

  it('displays player stats correctly', () => {
    render(
      <KeeperSelection 
        candidates={mockCandidates} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    // Check Josh Allen's stats
    expect(screen.getByText('320.5')).toBeInTheDocument() // Projected points
    expect(screen.getByText('70.5')).toBeInTheDocument() // VOR
    expect(screen.getByText('Rd 7')).toBeInTheDocument() // Keeper cost
  })

  it('shows empty state when no candidates match filters', () => {
    render(
      <KeeperSelection 
        candidates={mockCandidates} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    // Filter to a position that doesn't exist
    const positionSelect = screen.getByDisplayValue('All Positions')
    fireEvent.change(positionSelect, { target: { value: 'K' } })

    expect(screen.getByText('No players found')).toBeInTheDocument()
    expect(screen.getByText('Try adjusting your filters to see more players.')).toBeInTheDocument()
  })

  it('handles empty candidates array', () => {
    render(
      <KeeperSelection 
        candidates={[]} 
        onSelectionChange={mockOnSelectionChange} 
      />
    )

    expect(screen.getByText('No players found')).toBeInTheDocument()
  })
})