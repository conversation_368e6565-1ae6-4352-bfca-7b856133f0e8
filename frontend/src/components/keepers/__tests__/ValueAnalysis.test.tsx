import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import ValueAnalysis from '../ValueAnalysis'
import { KeeperCandidate, KeeperScenario } from '@/lib/keeper-api'

const mockCandidates: KeeperCandidate[] = [
  {
    player_id: '1',
    player_name: '<PERSON>',
    position: 'QB',
    current_cost: 8,
    projected_points: 320.5,
    replacement_level: 250.0,
    value_over_replacement: 70.5,
    keeper_cost: 7,
    is_eligible: true,
    constraints_violated: [],
    metadata: {
      team: 'BUF',
      bye_week: 7,
      injury_status: 'HEALTHY',
      projection_confidence: 0.15,
      source_count: 5
    }
  },
  {
    player_id: '2',
    player_name: '<PERSON>',
    position: 'RB',
    current_cost: 2,
    projected_points: 285.2,
    replacement_level: 180.0,
    value_over_replacement: 105.2,
    keeper_cost: 1,
    is_eligible: true,
    constraints_violated: [],
    metadata: {
      team: 'SF',
      bye_week: 9,
      injury_status: 'HEALTHY',
      projection_confidence: 0.12,
      source_count: 6
    }
  },
  {
    player_id: '3',
    player_name: '<PERSON>',
    position: 'WR',
    current_cost: 5,
    projected_points: 245.8,
    replacement_level: 150.0,
    value_over_replacement: 95.8,
    keeper_cost: 4,
    is_eligible: false,
    constraints_violated: ['Player is OUT'],
    metadata: {
      team: 'LAR',
      bye_week: 6,
      injury_status: 'OUT',
      projection_confidence: 0.25,
      source_count: 4
    }
  },
  {
    player_id: '4',
    player_name: 'Tyreek Hill',
    position: 'WR',
    current_cost: 3,
    projected_points: 265.4,
    replacement_level: 150.0,
    value_over_replacement: 115.4,
    keeper_cost: 2,
    is_eligible: true,
    constraints_violated: [],
    metadata: {
      team: 'MIA',
      bye_week: 10,
      injury_status: 'HEALTHY',
      projection_confidence: 0.18,
      source_count: 5
    }
  }
]

const mockScenario: KeeperScenario = {
  scenario_name: 'Optimal',
  selected_keepers: [
    {
      player_id: '4',
      player_name: 'Tyreek Hill',
      position: 'WR',
      keeper_cost: 2,
      projected_points: 265.4,
      value_over_replacement: 115.4,
      confidence: 0.85,
      rationale: 'Exceptional value with 115.4 points over replacement, excellent keeper value at round 2.',
      alternatives: [],
      metadata: {}
    },
    {
      player_id: '2',
      player_name: 'Christian McCaffrey',
      position: 'RB',
      keeper_cost: 1,
      projected_points: 285.2,
      value_over_replacement: 105.2,
      confidence: 0.88,
      rationale: 'Exceptional value with 105.2 points over replacement, excellent keeper value at round 1.',
      alternatives: [],
      metadata: {}
    }
  ],
  total_value: 220.6,
  remaining_budget: 150,
  constraints_satisfied: true,
  trade_offs: [],
  metadata: {
    total_cost: 3,
    max_keepers: 3,
    objective_weights: { vor: 0.8, cost: 0.2 },
    solver_status: 'OPTIMAL',
    generation_timestamp: '2024-08-19T12:00:00Z'
  }
}

describe('ValueAnalysis', () => {
  it('renders value analysis interface', () => {
    render(<ValueAnalysis candidates={mockCandidates} />)

    expect(screen.getByText('Value Analysis')).toBeInTheDocument()
    expect(screen.getByText('Surplus calculations and position breakdown')).toBeInTheDocument()
  })

  it('displays overall metrics correctly', () => {
    render(<ValueAnalysis candidates={mockCandidates} />)

    // Should show 3 eligible players (excluding Cooper Kupp)
    expect(screen.getByText('3')).toBeInTheDocument()
    expect(screen.getByText('Eligible Players')).toBeInTheDocument()

    // Should show best VOR (Tyreek Hill: 115.4)
    expect(screen.getByText('115.4')).toBeInTheDocument()
    expect(screen.getByText('Best VOR')).toBeInTheDocument()

    // Should show average VOR
    const avgVor = (70.5 + 105.2 + 115.4) / 3
    expect(screen.getByText(avgVor.toFixed(1))).toBeInTheDocument()
    expect(screen.getByText('Avg VOR')).toBeInTheDocument()

    // Should show average cost
    const avgCost = (7 + 1 + 2) / 3
    expect(screen.getByText(avgCost.toFixed(1))).toBeInTheDocument()
    expect(screen.getByText('Avg Cost')).toBeInTheDocument()
  })

  it('displays position breakdown correctly', () => {
    render(<ValueAnalysis candidates={mockCandidates} />)

    expect(screen.getByText('Position Breakdown')).toBeInTheDocument()

    // Should show QB position (1 player)
    expect(screen.getByText('QB')).toBeInTheDocument()
    expect(screen.getByText('1 player')).toBeInTheDocument()

    // Should show WR position (1 eligible player - Tyreek Hill)
    expect(screen.getByText('WR')).toBeInTheDocument()

    // Should show RB position (1 player)
    expect(screen.getByText('RB')).toBeInTheDocument()
  })

  it('displays selected scenario analysis when provided', () => {
    render(<ValueAnalysis candidates={mockCandidates} selectedScenario={mockScenario} />)

    expect(screen.getByText('Selected Scenario: Optimal')).toBeInTheDocument()

    // Should show total value
    expect(screen.getByText('220.6 VOR')).toBeInTheDocument()

    // Should show total cost
    expect(screen.getByText('3 rounds')).toBeInTheDocument()

    // Should show value efficiency
    const efficiency = 220.6 / 3
    expect(screen.getByText(efficiency.toFixed(2) + ' VOR/Rd')).toBeInTheDocument()

    // Should show selected keepers
    expect(screen.getByText('Selected Keepers:')).toBeInTheDocument()
    expect(screen.getByText('Tyreek Hill (WR)')).toBeInTheDocument()
    expect(screen.getByText('Christian McCaffrey (RB)')).toBeInTheDocument()
  })

  it('calculates surplus vs average correctly', () => {
    render(<ValueAnalysis candidates={mockCandidates} selectedScenario={mockScenario} />)

    // Average VOR for eligible players: (70.5 + 105.2 + 115.4) / 3 = 97.03
    // Selected scenario has 2 keepers, so expected average: 97.03 * 2 = 194.07
    // Actual selected value: 220.6
    // Surplus: 220.6 - 194.07 = 26.53
    const avgVor = (70.5 + 105.2 + 115.4) / 3
    const expectedAvg = avgVor * 2
    const surplus = 220.6 - expectedAvg

    expect(screen.getByText(`+${surplus.toFixed(1)}`)).toBeInTheDocument()
  })

  it('shows empty state when no candidates provided', () => {
    render(<ValueAnalysis candidates={[]} />)

    expect(screen.getByText('No data available')).toBeInTheDocument()
    expect(screen.getByText('No eligible keeper candidates found for analysis.')).toBeInTheDocument()
  })

  it('shows empty state when no eligible candidates', () => {
    const ineligibleCandidates = mockCandidates.map(c => ({
      ...c,
      is_eligible: false
    }))

    render(<ValueAnalysis candidates={ineligibleCandidates} />)

    expect(screen.getByText('No data available')).toBeInTheDocument()
  })

  it('handles scenario without remaining budget', () => {
    const scenarioWithoutBudget = {
      ...mockScenario,
      remaining_budget: undefined
    }

    render(<ValueAnalysis candidates={mockCandidates} selectedScenario={scenarioWithoutBudget} />)

    // Should still show other metrics
    expect(screen.getByText('220.6 VOR')).toBeInTheDocument()
    expect(screen.getByText('3 rounds')).toBeInTheDocument()
  })

  it('sorts position breakdown by best value descending', () => {
    render(<ValueAnalysis candidates={mockCandidates} />)

    const positionElements = screen.getAllByText(/QB|RB|WR/)
    
    // WR should be first (Tyreek Hill: 115.4 VOR)
    // RB should be second (Christian McCaffrey: 105.2 VOR)  
    // QB should be third (Josh Allen: 70.5 VOR)
    expect(positionElements[0]).toHaveTextContent('WR')
  })

  it('displays correct best and average values for each position', () => {
    render(<ValueAnalysis candidates={mockCandidates} />)

    // For WR position (only Tyreek Hill eligible): best = avg = 115.4
    const wrSection = screen.getByText('WR').closest('.bg-gray-50')
    expect(wrSection).toHaveTextContent('115.4') // Best value
    expect(wrSection).toHaveTextContent('115.4') // Avg value (same as best since only 1 player)

    // For QB position (Josh Allen): best = avg = 70.5
    const qbSection = screen.getByText('QB').closest('.bg-gray-50')
    expect(qbSection).toHaveTextContent('70.5')

    // For RB position (Christian McCaffrey): best = avg = 105.2
    const rbSection = screen.getByText('RB').closest('.bg-gray-50')
    expect(rbSection).toHaveTextContent('105.2')
  })
})