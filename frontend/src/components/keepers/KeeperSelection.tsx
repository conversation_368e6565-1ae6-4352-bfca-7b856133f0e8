import { useState, useMemo } from 'react'
import {
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { KeeperCandidate } from '@/lib/keeper-api'
import WhyButton from '@/components/ui/WhyButton'
import ConfidenceIndicator from '@/components/ui/ConfidenceIndicator'
import { ExplanationAPI } from '@/lib/explanation-api'
import { RecommendationExplanation, ExplanationRequest, UncertaintyVisualization } from '@/types/explanation'

interface KeeperSelectionProps {
  candidates: KeeperCandidate[]
  onSelectionChange: (selectedCandidates: KeeperCandidate[]) => void
  recommendationId?: string
}

export default function KeeperSelection({ candidates, onSelectionChange, recommendationId }: KeeperSelectionProps) {
  const [selectedPlayers, setSelectedPlayers] = useState<Set<string>>(new Set())
  const [sortBy, setSortBy] = useState<'vor' | 'cost' | 'points' | 'name'>('vor')
  const [filterPosition, setFilterPosition] = useState<string>('all')
  const [showEligibleOnly, setShowEligibleOnly] = useState(true)

  // Filter and sort candidates
  const filteredAndSortedCandidates = useMemo(() => {
    let filtered = candidates

    // Filter by eligibility
    if (showEligibleOnly) {
      filtered = filtered.filter(c => c.is_eligible)
    }

    // Filter by position
    if (filterPosition !== 'all') {
      filtered = filtered.filter(c => c.position === filterPosition)
    }

    // Sort candidates
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'vor':
          return b.value_over_replacement - a.value_over_replacement
        case 'cost':
          return a.keeper_cost - b.keeper_cost
        case 'points':
          return b.projected_points - a.projected_points
        case 'name':
          return a.player_name.localeCompare(b.player_name)
        default:
          return 0
      }
    })

    return sorted
  }, [candidates, sortBy, filterPosition, showEligibleOnly])

  // Get unique positions for filter
  const positions = useMemo(() => {
    const posSet = new Set(candidates.map(c => c.position))
    return Array.from(posSet).sort()
  }, [candidates])

  const handlePlayerToggle = (playerId: string) => {
    const newSelected = new Set(selectedPlayers)
    if (newSelected.has(playerId)) {
      newSelected.delete(playerId)
    } else {
      newSelected.add(playerId)
    }
    setSelectedPlayers(newSelected)
    
    // Notify parent of selection change
    const selectedCandidates = candidates.filter(c => newSelected.has(c.player_id))
    onSelectionChange(selectedCandidates)
  }

  const getEligibilityIcon = (candidate: KeeperCandidate) => {
    if (candidate.is_eligible) {
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />
    } else {
      return <XCircleIcon className="h-5 w-5 text-red-500" />
    }
  }

  const getValueColor = (vor: number) => {
    if (vor >= 50) return 'text-green-600 font-semibold'
    if (vor >= 20) return 'text-green-500'
    if (vor >= 10) return 'text-yellow-600'
    if (vor >= 0) return 'text-gray-600'
    return 'text-red-500'
  }

  const handleExplain = async (request: ExplanationRequest): Promise<RecommendationExplanation> => {
    if (!recommendationId) {
      throw new Error('No recommendation ID available')
    }
    return ExplanationAPI.getExplanation(recommendationId, request)
  }

  const getUncertaintyVisualization = (candidate: KeeperCandidate): UncertaintyVisualization => {
    const confidence = candidate.metadata.confidence || 0.7
    return {
      confidence_level: confidence > 0.8 ? 'high' : confidence > 0.6 ? 'medium' : 'low',
      confidence_score: confidence,
      uncertainty_range: [candidate.value_over_replacement * 0.85, candidate.value_over_replacement * 1.15],
      risk_factors: candidate.constraints_violated,
      confidence_indicators: [
        {
          indicator: 'Projection Quality',
          status: confidence > 0.7 ? 'good' : 'warning',
          description: 'Quality of underlying projections'
        },
        {
          indicator: 'Keeper Eligibility',
          status: candidate.is_eligible ? 'good' : 'poor',
          description: 'Player meets keeper requirements'
        },
        {
          indicator: 'Value Opportunity',
          status: candidate.value_over_replacement > 20 ? 'good' : 'warning',
          description: 'Value compared to replacement level'
        }
      ]
    }
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Eligible Players</h2>
            <p className="mt-1 text-sm text-gray-500">
              Select players to keep for next season
            </p>
          </div>
          {recommendationId && (
            <WhyButton
              recommendationId={recommendationId}
              onExplain={handleExplain}
              variant="button"
              size="sm"
            />
          )}
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex flex-wrap items-center gap-4">
          {/* Sort By */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Sort by:</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="vor">Value Over Replacement</option>
              <option value="points">Projected Points</option>
              <option value="cost">Keeper Cost</option>
              <option value="name">Player Name</option>
            </select>
          </div>

          {/* Position Filter */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Position:</label>
            <select
              value={filterPosition}
              onChange={(e) => setFilterPosition(e.target.value)}
              className="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Positions</option>
              {positions.map(pos => (
                <option key={pos} value={pos}>{pos}</option>
              ))}
            </select>
          </div>

          {/* Eligible Only Toggle */}
          <div className="flex items-center">
            <input
              id="eligible-only"
              type="checkbox"
              checked={showEligibleOnly}
              onChange={(e) => setShowEligibleOnly(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="eligible-only" className="ml-2 text-sm text-gray-700">
              Show eligible only
            </label>
          </div>
        </div>
      </div>

      {/* Player List */}
      <div className="divide-y divide-gray-200">
        {filteredAndSortedCandidates.length === 0 ? (
          <div className="px-6 py-8 text-center">
            <InformationCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No players found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your filters to see more players.
            </p>
          </div>
        ) : (
          filteredAndSortedCandidates.map((candidate) => (
            <div
              key={candidate.player_id}
              className={`px-6 py-4 hover:bg-gray-50 cursor-pointer ${
                selectedPlayers.has(candidate.player_id) ? 'bg-blue-50' : ''
              }`}
              onClick={() => handlePlayerToggle(candidate.player_id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Selection Checkbox */}
                  <input
                    type="checkbox"
                    checked={selectedPlayers.has(candidate.player_id)}
                    onChange={() => handlePlayerToggle(candidate.player_id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />

                  {/* Player Info */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="text-sm font-medium text-gray-900">
                        {candidate.player_name}
                      </h3>
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        {candidate.position}
                      </span>
                      <span className="text-xs text-gray-500">
                        {candidate.metadata.team}
                      </span>
                      {getEligibilityIcon(candidate)}
                      <ConfidenceIndicator
                        confidence={candidate.metadata.confidence || 0.7}
                        uncertainty={getUncertaintyVisualization(candidate)}
                        size="sm"
                      />
                    </div>

                    {/* Constraints Violations */}
                    {candidate.constraints_violated.length > 0 && (
                      <div className="mt-1 flex items-center space-x-1">
                        <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
                        <span className="text-xs text-yellow-600">
                          {candidate.constraints_violated.join(', ')}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Stats */}
                <div className="flex items-center space-x-6 text-sm">
                  <div className="text-center">
                    <div className="text-gray-500">Projected</div>
                    <div className="font-medium">{candidate.projected_points.toFixed(1)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-gray-500">VOR</div>
                    <div className={getValueColor(candidate.value_over_replacement)}>
                      {candidate.value_over_replacement.toFixed(1)}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-gray-500">Cost</div>
                    <div className="font-medium">Rd {candidate.keeper_cost}</div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Selection Summary */}
      {selectedPlayers.size > 0 && (
        <div className="px-6 py-4 bg-blue-50 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              {selectedPlayers.size} player{selectedPlayers.size !== 1 ? 's' : ''} selected
            </span>
            <button
              onClick={() => {
                setSelectedPlayers(new Set())
                onSelectionChange([])
              }}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Clear selection
            </button>
          </div>
        </div>
      )}
    </div>
  )
}