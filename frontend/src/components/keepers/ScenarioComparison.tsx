import { useState } from 'react'
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { KeeperScenario } from '@/lib/keeper-api'
import WhyButton from '@/components/ui/WhyButton'
import ConfidenceIndicator from '@/components/ui/ConfidenceIndicator'
import AlternativeComparison from '@/components/ui/AlternativeComparison'
import { ExplanationAPI } from '@/lib/explanation-api'
import { RecommendationExplanation, ExplanationRequest, AlternativeScenario } from '@/types/explanation'

interface ScenarioComparisonProps {
  scenarios: KeeperScenario[]
  selectedScenario: string
  onScenarioSelect: (scenarioName: string) => void
  recommendationId?: string
}

export default function ScenarioComparison({
  scenarios,
  selectedScenario,
  onScenarioSelect,
  recommendationId
}: ScenarioComparisonProps) {
  const [expandedScenarios, setExpandedScenarios] = useState<Set<string>>(new Set(['Optimal']))

  const handleExplain = async (request: ExplanationRequest): Promise<RecommendationExplanation> => {
    if (!recommendationId) {
      throw new Error('No recommendation ID available')
    }
    return ExplanationAPI.getExplanation(recommendationId, request)
  }

  const getAlternativeScenarios = (): AlternativeScenario[] => {
    return scenarios.slice(1).map(scenario => ({
      scenario: scenario.scenario_name,
      description: getScenarioDescription(scenario.scenario_name),
      confidence: scenario.metadata.confidence || 0.8,
      trade_offs: scenario.trade_offs,
      expected_outcome: `${scenario.total_value.toFixed(1)} VOR with ${scenario.selected_keepers.length} keepers`
    }))
  }

  const toggleScenarioExpansion = (scenarioName: string) => {
    const newExpanded = new Set(expandedScenarios)
    if (newExpanded.has(scenarioName)) {
      newExpanded.delete(scenarioName)
    } else {
      newExpanded.add(scenarioName)
    }
    setExpandedScenarios(newExpanded)
  }

  const getScenarioColor = (scenarioName: string) => {
    switch (scenarioName) {
      case 'Optimal':
        return 'bg-blue-50 border-blue-200 text-blue-900'
      case 'Value-Focused':
        return 'bg-green-50 border-green-200 text-green-900'
      case 'Cost-Efficient':
        return 'bg-yellow-50 border-yellow-200 text-yellow-900'
      case 'Position-Balanced':
        return 'bg-purple-50 border-purple-200 text-purple-900'
      case 'Conservative':
        return 'bg-gray-50 border-gray-200 text-gray-900'
      default:
        return 'bg-gray-50 border-gray-200 text-gray-900'
    }
  }

  const getScenarioDescription = (scenarioName: string) => {
    switch (scenarioName) {
      case 'Optimal':
        return 'Balanced approach maximizing value while considering cost'
      case 'Value-Focused':
        return 'Prioritizes highest value over replacement players'
      case 'Cost-Efficient':
        return 'Minimizes total keeper costs while maintaining value'
      case 'Position-Balanced':
        return 'Ensures representation across all major positions'
      case 'Conservative':
        return 'Lower risk approach with projection uncertainty adjustment'
      default:
        return 'Custom scenario configuration'
    }
  }

  if (scenarios.length === 0) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="text-center">
          <InformationCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No scenarios available</h3>
          <p className="mt-1 text-sm text-gray-500">
            Scenarios will appear here once keeper candidates are loaded.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Scenario Comparison</h2>
            <p className="mt-1 text-sm text-gray-500">
              Compare different keeper optimization strategies
            </p>
          </div>
          {recommendationId && scenarios.length > 0 && (
            <div className="flex items-center space-x-3">
              <ConfidenceIndicator
                confidence={scenarios[0].metadata.confidence || 0.8}
                showDetails={true}
                size="sm"
              />
              <WhyButton
                recommendationId={recommendationId}
                onExplain={handleExplain}
                variant="button"
                size="sm"
              />
            </div>
          )}
        </div>
      </div>

      <div className="divide-y divide-gray-200">
        {scenarios.map((scenario, index) => {
          const isSelected = scenario.scenario_name === selectedScenario
          const isExpanded = expandedScenarios.has(scenario.scenario_name)
          const colorClasses = getScenarioColor(scenario.scenario_name)

          return (
            <div key={scenario.scenario_name} className="p-6">
              {/* Scenario Header */}
              <div 
                className={`rounded-lg border-2 p-4 cursor-pointer transition-all ${
                  isSelected ? colorClasses : 'bg-white border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => onScenarioSelect(scenario.scenario_name)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      {isSelected && <CheckCircleIcon className="h-5 w-5 text-blue-600" />}
                      <h3 className="text-lg font-medium">
                        {scenario.scenario_name}
                        {index === 0 && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            Recommended
                          </span>
                        )}
                      </h3>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="text-lg font-semibold">
                        {scenario.total_value.toFixed(1)} VOR
                      </div>
                      <div className="text-sm text-gray-500">
                        {scenario.selected_keepers.length} keeper{scenario.selected_keepers.length !== 1 ? 's' : ''}
                      </div>
                    </div>
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleScenarioExpansion(scenario.scenario_name)
                      }}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {isExpanded ? (
                        <ChevronDownIcon className="h-5 w-5" />
                      ) : (
                        <ChevronRightIcon className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>

                <p className="mt-2 text-sm text-gray-600">
                  {getScenarioDescription(scenario.scenario_name)}
                </p>

                {/* Quick Stats */}
                <div className="mt-3 flex items-center space-x-6 text-sm">
                  <div>
                    <span className="text-gray-500">Total Cost: </span>
                    <span className="font-medium">
                      {scenario.metadata.total_cost} rounds
                    </span>
                  </div>
                  {scenario.remaining_budget !== undefined && (
                    <div>
                      <span className="text-gray-500">Remaining Budget: </span>
                      <span className="font-medium">
                        ${scenario.remaining_budget.toFixed(0)}
                      </span>
                    </div>
                  )}
                  <div className="flex items-center space-x-1">
                    {scenario.constraints_satisfied ? (
                      <CheckCircleIcon className="h-4 w-4 text-green-500" />
                    ) : (
                      <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
                    )}
                    <span className={scenario.constraints_satisfied ? 'text-green-600' : 'text-yellow-600'}>
                      {scenario.constraints_satisfied ? 'Valid' : 'Constraints violated'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Expanded Details */}
              {isExpanded && (
                <div className="mt-4 space-y-4">
                  {/* Selected Keepers */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Selected Keepers</h4>
                    <div className="space-y-2">
                      {scenario.selected_keepers.map((keeper) => (
                        <div 
                          key={keeper.player_id}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center space-x-3">
                            <span className="font-medium text-gray-900">
                              {keeper.player_name}
                            </span>
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                              {keeper.position}
                            </span>
                          </div>
                          <div className="flex items-center space-x-4 text-sm">
                            <div className="text-center">
                              <div className="font-medium">{keeper.projected_points.toFixed(1)}</div>
                              <div className="text-xs text-gray-500">Proj</div>
                            </div>
                            <div className="text-center">
                              <div className="font-medium text-green-600">
                                {keeper.value_over_replacement.toFixed(1)}
                              </div>
                              <div className="text-xs text-gray-500">VOR</div>
                            </div>
                            <div className="text-center">
                              <div className="font-medium">Rd {keeper.keeper_cost}</div>
                              <div className="text-xs text-gray-500">Cost</div>
                            </div>
                            <div className="text-center">
                              <div className="font-medium">{(keeper.confidence * 100).toFixed(0)}%</div>
                              <div className="text-xs text-gray-500">Conf</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Trade-offs */}
                  {scenario.trade_offs.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Trade-offs</h4>
                      <div className="space-y-1">
                        {scenario.trade_offs.map((tradeOff, idx) => (
                          <div key={idx} className="flex items-start space-x-2 text-sm text-gray-600">
                            <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                            <span>{tradeOff}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Rationale for top keeper */}
                  {scenario.selected_keepers.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">
                        Top Keeper Rationale
                      </h4>
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <div className="font-medium text-sm text-blue-900 mb-1">
                          {scenario.selected_keepers[0].player_name}
                        </div>
                        <div className="text-sm text-blue-800">
                          {scenario.selected_keepers[0].rationale}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* Alternative Comparison */}
      {scenarios.length > 1 && recommendationId && (
        <div className="px-6 py-4 border-t border-gray-200">
          <AlternativeComparison
            primaryRecommendation={{
              title: scenarios[0].scenario_name,
              confidence: scenarios[0].metadata.confidence || 0.8,
              description: getScenarioDescription(scenarios[0].scenario_name),
              key_metrics: {
                total_value: scenarios[0].total_value,
                keeper_count: scenarios[0].selected_keepers.length,
                total_cost: scenarios[0].metadata.total_cost
              }
            }}
            alternatives={getAlternativeScenarios()}
            showComparison={true}
          />
        </div>
      )}

      {/* Comparison Summary */}
      {scenarios.length > 1 && (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Quick Comparison</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Highest Value: </span>
              <span className="font-medium">
                {scenarios.reduce((max, s) => s.total_value > max.total_value ? s : max).scenario_name}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Lowest Cost: </span>
              <span className="font-medium">
                {scenarios.reduce((min, s) => s.metadata.total_cost < min.metadata.total_cost ? s : min).scenario_name}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Most Keepers: </span>
              <span className="font-medium">
                {scenarios.reduce((max, s) => s.selected_keepers.length > max.selected_keepers.length ? s : max).scenario_name}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}