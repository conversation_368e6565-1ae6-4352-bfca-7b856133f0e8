import { useMemo } from 'react'
import { 
  ChartBarIcon, 
  TrophyIcon, 
  CurrencyDollarIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { KeeperCandidate, KeeperScenario } from '@/lib/keeper-api'
import WhyButton from '@/components/ui/WhyButton'
import ConfidenceIndicator from '@/components/ui/ConfidenceIndicator'
import { ExplanationAPI } from '@/lib/explanation-api'
import { RecommendationExplanation, ExplanationRequest, UncertaintyVisualization } from '@/types/explanation'
import Badge from '@/components/ui/Badge'

interface ValueAnalysisProps {
  candidates: KeeperCandidate[]
  selectedScenario?: KeeperScenario
  recommendationId?: string
}

export default function ValueAnalysis({ candidates, selectedScenario, recommendationId }: ValueAnalysisProps) {
  // Calculate value metrics
  const valueMetrics = useMemo(() => {
    if (!candidates.length) return null

    const eligibleCandidates = candidates.filter(c => c.is_eligible)
    
    if (!eligibleCandidates.length) return null

    const totalValue = eligibleCandidates.reduce((sum, c) => sum + c.value_over_replacement, 0)
    const avgValue = totalValue / eligibleCandidates.length
    const maxValue = Math.max(...eligibleCandidates.map(c => c.value_over_replacement))
    const minCost = Math.min(...eligibleCandidates.map(c => c.keeper_cost))
    const avgCost = eligibleCandidates.reduce((sum, c) => sum + c.keeper_cost, 0) / eligibleCandidates.length

    // Position breakdown
    const positionBreakdown = eligibleCandidates.reduce((acc, candidate) => {
      const pos = candidate.position
      if (!acc[pos]) {
        acc[pos] = { count: 0, totalValue: 0, avgValue: 0, bestValue: 0 }
      }
      acc[pos].count++
      acc[pos].totalValue += candidate.value_over_replacement
      acc[pos].avgValue = acc[pos].totalValue / acc[pos].count
      acc[pos].bestValue = Math.max(acc[pos].bestValue, candidate.value_over_replacement)
      return acc
    }, {} as Record<string, { count: number; totalValue: number; avgValue: number; bestValue: number }>)

    return {
      totalCandidates: eligibleCandidates.length,
      totalValue,
      avgValue,
      maxValue,
      minCost,
      avgCost,
      positionBreakdown
    }
  }, [candidates])

  // Calculate surplus analysis for selected scenario
  const surplusAnalysis = useMemo(() => {
    if (!selectedScenario || !valueMetrics) return null

    const selectedValue = selectedScenario.total_value
    const selectedCost = selectedScenario.selected_keepers.reduce((sum, k) => sum + k.keeper_cost, 0)
    const avgCostPerRound = selectedCost / selectedScenario.selected_keepers.length

    return {
      selectedValue,
      selectedCost,
      avgCostPerRound,
      valueEfficiency: selectedValue / selectedCost,
      surplusVsAvg: selectedValue - (valueMetrics.avgValue * selectedScenario.selected_keepers.length)
    }
  }, [selectedScenario, valueMetrics])

  // Create uncertainty visualization for selected scenario
  const uncertaintyData: UncertaintyVisualization | undefined = selectedScenario ? {
    confidence_level: selectedScenario.total_value > ((valueMetrics?.avgValue ?? 0) * selectedScenario.selected_keepers.length) ? 'high' : 'medium',
    confidence_score: selectedScenario.constraints_satisfied ? 0.85 : 0.65,
    risk_factors: [
      'Projection accuracy',
      'Keeper rule changes',
      'Player injury risk'
    ],
    confidence_indicators: [
      {
        indicator: 'Value Optimization',
        status: selectedScenario.total_value > 50 ? 'good' : 'warning',
        description: 'Total value over replacement'
      },
      {
        indicator: 'Constraint Compliance',
        status: selectedScenario.constraints_satisfied ? 'good' : 'poor',
        description: 'Meets all league keeper rules'
      }
    ]
  } : undefined

  const handleExplain = async (request: ExplanationRequest): Promise<RecommendationExplanation> => {
    if (!recommendationId) {
      throw new Error('No recommendation ID available')
    }
    return ExplanationAPI.getExplanation(recommendationId, request)
  }

  if (!valueMetrics) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="text-center">
          <InformationCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No data available</h3>
          <p className="mt-1 text-sm text-gray-500">
            No eligible keeper candidates found for analysis.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">Value Analysis</h2>
        <p className="mt-1 text-sm text-gray-500">
          Surplus calculations and position breakdown
        </p>
      </div>

      <div className="p-6 space-y-6">
        {/* Overall Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg mx-auto mb-2">
              <ChartBarIcon className="w-5 h-5 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {valueMetrics.totalCandidates}
            </div>
            <div className="text-sm text-gray-500">Eligible Players</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-lg mx-auto mb-2">
              <TrophyIcon className="w-5 h-5 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {valueMetrics.maxValue.toFixed(1)}
            </div>
            <div className="text-sm text-gray-500">Best VOR</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 bg-yellow-100 rounded-lg mx-auto mb-2">
              <CurrencyDollarIcon className="w-5 h-5 text-yellow-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {valueMetrics.avgValue.toFixed(1)}
            </div>
            <div className="text-sm text-gray-500">Avg VOR</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 bg-purple-100 rounded-lg mx-auto mb-2">
              <span className="text-purple-600 font-bold text-sm">Rd</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {valueMetrics.avgCost.toFixed(1)}
            </div>
            <div className="text-sm text-gray-500">Avg Cost</div>
          </div>
        </div>

        {/* Position Breakdown */}
        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-3">Position Breakdown</h3>
          <div className="space-y-3">
            {Object.entries(valueMetrics.positionBreakdown)
              .sort(([,a], [,b]) => b.bestValue - a.bestValue)
              .map(([position, stats]) => (
                <div key={position} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Badge type="position" value={position} size="sm" />
                    <span className="text-sm text-gray-600">
                      {stats.count} player{stats.count !== 1 ? 's' : ''}
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="text-center">
                      <div className="font-medium text-gray-900">
                        {stats.bestValue.toFixed(1)}
                      </div>
                      <div className="text-xs text-gray-500">Best</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-gray-900">
                        {stats.avgValue.toFixed(1)}
                      </div>
                      <div className="text-xs text-gray-500">Avg</div>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Selected Scenario Analysis */}
        {selectedScenario && surplusAnalysis && (
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-3">
              Selected Scenario: {selectedScenario.scenario_name}
            </h3>
            <div className="bg-blue-50 rounded-lg p-4 space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-600">Total Value</div>
                  <div className="text-lg font-semibold text-blue-900">
                    {surplusAnalysis.selectedValue.toFixed(1)} VOR
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Total Cost</div>
                  <div className="text-lg font-semibold text-blue-900">
                    {surplusAnalysis.selectedCost} rounds
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Value Efficiency</div>
                  <div className="text-lg font-semibold text-blue-900">
                    {surplusAnalysis.valueEfficiency.toFixed(2)} VOR/Rd
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Surplus vs Avg</div>
                  <div className={`text-lg font-semibold ${
                    surplusAnalysis.surplusVsAvg >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {surplusAnalysis.surplusVsAvg >= 0 ? '+' : ''}{surplusAnalysis.surplusVsAvg.toFixed(1)}
                  </div>
                </div>
              </div>

              {/* Selected Players */}
              <div className="mt-4">
                <div className="text-sm text-gray-600 mb-2">Selected Keepers:</div>
                <div className="space-y-1">
                  {selectedScenario.selected_keepers.map((keeper) => (
                    <div key={keeper.player_id} className="flex items-center justify-between text-sm">
                      <span className="font-medium flex items-center gap-2">
                        {keeper.player_name}
                        <Badge type="position" value={keeper.position} size="xs" />
                      </span>
                      <span className="text-gray-600">
                        {keeper.value_over_replacement.toFixed(1)} VOR, Rd {keeper.keeper_cost}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}