'use client'

import { useState } from 'react'
import { StartSitRecommendation } from '../../types/lineup'

interface StartSitRecommendationsProps {
  recommendations: StartSitRecommendation[]
  franchiseId: string
  week: number
}

export const StartSitRecommendations: React.FC<StartSitRecommendationsProps> = ({
  recommendations,
  franchiseId,
  week
}) => {
  const [expandedRec, setExpandedRec] = useState<number | null>(null)

  // Mock player data - in real app would come from API
  const playerData = {
    player_1: { name: '<PERSON>', position: 'QB', team: 'BUF' },
    player_2: { name: '<PERSON>', position: 'RB', team: 'SF' },
    player_3: { name: '<PERSON>', position: 'RB', team: 'TEN' },
    player_4: { name: 'Tyreek Hill', position: 'WR', team: 'MI<PERSON>' },
    player_5: { name: '<PERSON><PERSON><PERSON>', position: '<PERSON>', team: '<PERSON><PERSON>' },
    player_6: { name: '<PERSON>', position: 'T<PERSON>', team: '<PERSON>' },
    player_7: { name: '<PERSON><PERSON>-<PERSON>', position: '<PERSON>', team: 'DET' },
    player_8: { name: '<PERSON>', position: '<PERSON>', team: '<PERSON><PERSON>' },
    player_9: { name: 'San Francisco', position: 'DEF', team: 'SF' },
    player_10: { name: 'Tua Tagovailoa', position: 'QB', team: 'MIA' },
    player_11: { name: 'Tony Pollard', position: 'RB', team: 'DAL' },
    player_12: { name: '<PERSON> <PERSON>', position: 'WR', team: 'TB' }
  }

  const getPlayerInfo = (playerId: string) => {
    return playerData[playerId as keyof typeof playerData] || { 
      name: 'Unknown Player', 
      position: 'N/A', 
      team: 'N/A' 
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100'
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getImprovementColor = (improvement: number) => {
    if (improvement > 3) return 'text-green-600'
    if (improvement > 1) return 'text-yellow-600'
    return 'text-gray-600'
  }

  if (recommendations.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Start/Sit Recommendations</h2>
        <div className="text-center py-8">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Your lineup is already optimal!</h3>
          <p className="text-gray-600">
            No start/sit changes recommended for Week {week}. Your current lineup maximizes your win probability.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Start/Sit Recommendations</h2>
        <div className="text-sm text-gray-600">
          Week {week} • {recommendations.length} recommendation{recommendations.length !== 1 ? 's' : ''}
        </div>
      </div>

      <div className="space-y-4">
        {recommendations.map((rec, index) => {
          const startPlayer = getPlayerInfo(rec.start_player_id)
          const sitPlayer = rec.sit_player_id ? getPlayerInfo(rec.sit_player_id) : null
          const isExpanded = expandedRec === index

          return (
            <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
              <div 
                className="p-4 cursor-pointer hover:bg-gray-50"
                onClick={() => setExpandedRec(isExpanded ? null : index)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4">
                      {/* Slot */}
                      <div className="flex-shrink-0">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {rec.slot}
                        </span>
                      </div>

                      {/* Start Player */}
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <div>
                          <div className="font-medium text-gray-900">
                            START: {startPlayer.name}
                          </div>
                          <div className="text-sm text-gray-600">
                            {startPlayer.position} • {startPlayer.team}
                          </div>
                        </div>
                      </div>

                      {/* Arrow */}
                      {sitPlayer && (
                        <>
                          <div className="flex-shrink-0">
                            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                          </div>

                          {/* Sit Player */}
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            <div>
                              <div className="font-medium text-gray-900">
                                SIT: {sitPlayer.name}
                              </div>
                              <div className="text-sm text-gray-600">
                                {sitPlayer.position} • {sitPlayer.team}
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Metrics */}
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className={`font-semibold ${getImprovementColor(rec.projected_improvement)}`}>
                        +{rec.projected_improvement.toFixed(1)} pts
                      </div>
                      <div className="text-xs text-gray-500">improvement</div>
                    </div>

                    <div className="text-right">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(rec.confidence)}`}>
                        {(rec.confidence * 100).toFixed(0)}% confident
                      </span>
                    </div>

                    <div className="flex-shrink-0">
                      <svg
                        className={`w-5 h-5 text-gray-400 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Expanded Content */}
              {isExpanded && (
                <div className="px-4 pb-4 border-t border-gray-100 bg-gray-50">
                  <div className="pt-4">
                    <h4 className="font-medium text-gray-900 mb-2">Analysis</h4>
                    <p className="text-sm text-gray-700 mb-4">{rec.rationale}</p>

                    {/* Win Probability Impact */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-sm text-gray-600">Projected Improvement</div>
                        <div className={`text-lg font-semibold ${getImprovementColor(rec.projected_improvement)}`}>
                          +{rec.projected_improvement.toFixed(1)} points
                        </div>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-sm text-gray-600">Confidence Level</div>
                        <div className="text-lg font-semibold text-blue-600">
                          {(rec.confidence * 100).toFixed(0)}%
                        </div>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-sm text-gray-600">Win Prob Impact</div>
                        <div className="text-lg font-semibold text-green-600">
                          +{(rec.projected_improvement * 0.5).toFixed(1)}%
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <button className="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700">
                        Apply Recommendation
                      </button>
                      <button className="px-4 py-2 bg-gray-200 text-gray-800 text-sm rounded-md hover:bg-gray-300">
                        Ignore
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* Summary */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 className="font-medium text-blue-900 mb-1">Total Impact</h4>
            <p className="text-sm text-blue-800">
              Applying all recommendations could improve your projected score by{' '}
              <strong>
                +{recommendations.reduce((sum, rec) => sum + rec.projected_improvement, 0).toFixed(1)} points
              </strong>{' '}
              and increase your win probability.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}