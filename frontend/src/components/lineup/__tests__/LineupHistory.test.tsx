import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { LineupHistory } from '../LineupHistory'
import { LineupAPI } from '../../../lib/lineup-api'

// Mock the API
jest.mock('../../../lib/lineup-api')
const mockLineupAPI = LineupAPI as jest.Mocked<typeof LineupAPI>

const defaultProps = {
  franchiseId: 'franchise_1',
  season: 2024
}

describe('LineupHistory', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock the getLineupHistory method to return empty array by default
    mockLineupAPI.getLineupHistory.mockResolvedValue([])
  })

  it('renders lineup history component', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Lineup History')).toBeInTheDocument()
    })
  })

  it('displays loading state initially', () => {
    render(<LineupHistory {...defaultProps} />)
    
    expect(screen.getByRole('status')).toBeInTheDocument() // LoadingSpinner has role="status"
  })

  it('displays summary statistics', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Avg Projected')).toBeInTheDocument()
      expect(screen.getByText('Avg Actual')).toBeInTheDocument()
      expect(screen.getByText('Win Rate')).toBeInTheDocument()
      expect(screen.getByText('Avg Accuracy')).toBeInTheDocument()
    })
  })

  it('displays history table headers', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Week')).toBeInTheDocument()
      expect(screen.getByText('Projected')).toBeInTheDocument()
      expect(screen.getByText('Actual')).toBeInTheDocument()
      expect(screen.getByText('Accuracy')).toBeInTheDocument()
      expect(screen.getByText('Win Prob')).toBeInTheDocument()
      expect(screen.getByText('Result')).toBeInTheDocument()
      expect(screen.getByText('Actions')).toBeInTheDocument()
    })
  })

  it('handles sort by dropdown', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      const sortSelect = screen.getByDisplayValue('Sort by Week')
      expect(sortSelect).toBeInTheDocument()
      
      fireEvent.change(sortSelect, { target: { value: 'points' } })
      expect(sortSelect).toHaveValue('points')
    })
  })

  it('displays week details when clicked', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      // Mock data should show Week 1
      const viewDetailsButton = screen.getAllByText('View Details')[0]
      fireEvent.click(viewDetailsButton)
      
      expect(screen.getByText('Week 1 Lineup Details')).toBeInTheDocument()
    })
  })

  it('closes week details when close button clicked', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      // Open details
      const viewDetailsButton = screen.getAllByText('View Details')[0]
      fireEvent.click(viewDetailsButton)
      
      expect(screen.getByText('Week 1 Lineup Details')).toBeInTheDocument()
      
      // Close details
      const closeButton = screen.getByRole('button', { name: /close/i })
      fireEvent.click(closeButton)
      
      expect(screen.queryByText('Week 1 Lineup Details')).not.toBeInTheDocument()
    })
  })

  it('displays performance insights', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Performance Insights')).toBeInTheDocument()
      expect(screen.getByText(/Your lineups have averaged/)).toBeInTheDocument()
      expect(screen.getByText(/You're winning/)).toBeInTheDocument()
    })
  })

  it('handles API error gracefully', async () => {
    mockLineupAPI.getLineupHistory.mockRejectedValue(new Error('API Error'))
    
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('API Error')).toBeInTheDocument()
      expect(screen.getByText('Retry')).toBeInTheDocument()
    })
  })

  it('retries loading when retry button clicked', async () => {
    mockLineupAPI.getLineupHistory.mockRejectedValueOnce(new Error('API Error'))
    mockLineupAPI.getLineupHistory.mockResolvedValueOnce([])
    
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('API Error')).toBeInTheDocument()
    })
    
    const retryButton = screen.getByText('Retry')
    fireEvent.click(retryButton)
    
    await waitFor(() => {
      expect(screen.getByText('Lineup History')).toBeInTheDocument()
      expect(screen.queryByText('API Error')).not.toBeInTheDocument()
    })
  })

  it('calculates accuracy correctly', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      // Mock data should show accuracy calculations
      // The component generates mock data with projected vs actual points
      const accuracyElements = screen.getAllByText(/%$/)
      expect(accuracyElements.length).toBeGreaterThan(0)
    })
  })

  it('displays win/loss results with correct colors', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      const winElements = screen.getAllByText('WIN')
      const lossElements = screen.getAllByText('LOSS')
      
      if (winElements.length > 0) {
        expect(winElements[0]).toHaveClass('text-green-600', 'bg-green-100')
      }
      
      if (lossElements.length > 0) {
        expect(lossElements[0]).toHaveClass('text-red-600', 'bg-red-100')
      }
    })
  })

  it('shows lineup slots in detailed view', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      // Open details for first week
      const viewDetailsButton = screen.getAllByText('View Details')[0]
      fireEvent.click(viewDetailsButton)
      
      // Should show all lineup slots
      expect(screen.getByText('QB')).toBeInTheDocument()
      expect(screen.getByText('RB1')).toBeInTheDocument()
      expect(screen.getByText('RB2')).toBeInTheDocument()
      expect(screen.getByText('WR1')).toBeInTheDocument()
      expect(screen.getByText('WR2')).toBeInTheDocument()
      expect(screen.getByText('TE')).toBeInTheDocument()
      expect(screen.getByText('FLEX')).toBeInTheDocument()
      expect(screen.getByText('K')).toBeInTheDocument()
      expect(screen.getByText('DEF')).toBeInTheDocument()
    })
  })

  it('sorts history by different criteria', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      const sortSelect = screen.getByDisplayValue('Sort by Week')
      
      // Test sorting by points
      fireEvent.change(sortSelect, { target: { value: 'points' } })
      expect(sortSelect).toHaveValue('points')
      
      // Test sorting by accuracy
      fireEvent.change(sortSelect, { target: { value: 'accuracy' } })
      expect(sortSelect).toHaveValue('accuracy')
      
      // Back to week
      fireEvent.change(sortSelect, { target: { value: 'week' } })
      expect(sortSelect).toHaveValue('week')
    })
  })

  it('displays player information in detailed view', async () => {
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      // Open details
      const viewDetailsButton = screen.getAllByText('View Details')[0]
      fireEvent.click(viewDetailsButton)
      
      // Should show player names and positions
      expect(screen.getByText('Josh Allen')).toBeInTheDocument()
      expect(screen.getByText('QB • BUF')).toBeInTheDocument()
    })
  })

  it('handles empty history gracefully', async () => {
    mockLineupAPI.getLineupHistory.mockResolvedValue([])
    
    render(<LineupHistory {...defaultProps} />)
    
    await waitFor(() => {
      // Should still show the component structure
      expect(screen.getByText('Lineup History')).toBeInTheDocument()
      expect(screen.getByText('Avg Projected')).toBeInTheDocument()
    })
  })
})