import { render, screen, act } from '@testing-library/react'
import { LineupLockCountdown } from '../LineupLockCountdown'
import { LineupLockAlert } from '../../../types/lineup'

// Mock Date to control time
const mockDate = new Date('2024-09-15T14:00:00Z')
jest.useFakeTimers()
jest.setSystemTime(mockDate)

const mockAlerts: LineupLockAlert[] = [
  {
    type: 'lineup_lock',
    player_id: 'player_1',
    player_name: '<PERSON>',
    slot: 'QB',
    lock_time: '2024-09-15T15:00:00Z', // 1 hour from mock time
    time_until_lock: '1h 0m 0s',
    urgency: 'medium'
  },
  {
    type: 'lineup_lock',
    player_id: 'player_2',
    player_name: '<PERSON>',
    slot: 'RB1',
    lock_time: '2024-09-15T14:15:00Z', // 15 minutes from mock time
    time_until_lock: '0h 15m 0s',
    urgency: 'high'
  },
  {
    type: 'lineup_lock',
    player_id: 'player_3',
    player_name: '<PERSON>',
    slot: 'RB2',
    lock_time: '2024-09-15T13:30:00Z', // Already locked (30 minutes ago)
    time_until_lock: '0h 0m 0s',
    urgency: 'high'
  }
]

const defaultProps = {
  alerts: mockAlerts
}

describe('LineupLockCountdown', () => {
  beforeEach(() => {
    jest.clearAllTimers()
    jest.setSystemTime(mockDate)
  })

  afterEach(() => {
    jest.runOnlyPendingTimers()
    jest.useRealTimers()
  })

  it('renders lineup lock alerts', () => {
    render(<LineupLockCountdown {...defaultProps} />)
    
    expect(screen.getByText('Lineup Lock Alerts')).toBeInTheDocument()
    expect(screen.getByText('Josh Allen (QB)')).toBeInTheDocument()
    expect(screen.getByText('Christian McCaffrey (RB1)')).toBeInTheDocument()
    expect(screen.getByText('Derrick Henry (RB2)')).toBeInTheDocument()
  })

  it('displays countdown timers correctly', () => {
    render(<LineupLockCountdown {...defaultProps} />)
    
    expect(screen.getByText('1h 0m 0s')).toBeInTheDocument()
    expect(screen.getByText('15m 0s')).toBeInTheDocument()
  })

  it('shows locked status for expired alerts', () => {
    render(<LineupLockCountdown {...defaultProps} />)
    
    const lockedElements = screen.getAllByText('LOCKED')
    expect(lockedElements).toHaveLength(2) // One in countdown, one in status
  })

  it('applies correct urgency colors', () => {
    render(<LineupLockCountdown {...defaultProps} />)
    
    const mediumAlert = screen.getByText('Josh Allen (QB)').closest('div')
    const highAlert = screen.getByText('Christian McCaffrey (RB1)').closest('div')
    
    expect(mediumAlert).toHaveClass('text-yellow-800', 'bg-yellow-100', 'border-yellow-200')
    expect(highAlert).toHaveClass('text-red-800', 'bg-red-100', 'border-red-200')
  })

  it('shows urgent warning for high priority alerts', () => {
    render(<LineupLockCountdown {...defaultProps} />)
    
    expect(screen.getByText('Urgent: Make lineup changes now!')).toBeInTheDocument()
    expect(screen.getByText(/This player's game locks soon/)).toBeInTheDocument()
  })

  it('updates countdown every second', () => {
    render(<LineupLockCountdown {...defaultProps} />)
    
    // Initial state
    expect(screen.getByText('1h 0m 0s')).toBeInTheDocument()
    
    // Advance time by 1 second
    act(() => {
      jest.advanceTimersByTime(1000)
    })
    
    expect(screen.getByText('59m 59s')).toBeInTheDocument()
  })

  it('handles transition to locked state', () => {
    // Create alert that will lock in 2 seconds
    const shortAlert: LineupLockAlert[] = [
      {
        type: 'lineup_lock',
        player_id: 'player_4',
        player_name: 'Test Player',
        slot: 'WR1',
        lock_time: '2024-09-15T14:00:02Z',
        time_until_lock: '0h 0m 2s',
        urgency: 'high'
      }
    ]
    
    render(<LineupLockCountdown alerts={shortAlert} />)
    
    expect(screen.getByText('2s')).toBeInTheDocument()
    
    // Advance time past lock time
    act(() => {
      jest.advanceTimersByTime(3000)
    })
    
    expect(screen.getByText('LOCKED')).toBeInTheDocument()
  })

  it('displays lock times correctly', () => {
    render(<LineupLockCountdown {...defaultProps} />)
    
    // Should show formatted lock times
    expect(screen.getByText('3:00:00 PM')).toBeInTheDocument() // 15:00 UTC
    expect(screen.getByText('2:15:00 PM')).toBeInTheDocument() // 14:15 UTC
  })

  it('shows summary statistics', () => {
    render(<LineupLockCountdown {...defaultProps} />)
    
    expect(screen.getByText('2 high priority alerts •')).toBeInTheDocument()
    expect(screen.getByText('1 medium priority alerts')).toBeInTheDocument()
  })

  it('renders action buttons', () => {
    render(<LineupLockCountdown {...defaultProps} />)
    
    expect(screen.getByText('Review Lineup')).toBeInTheDocument()
    expect(screen.getByText('Set Reminders')).toBeInTheDocument()
    expect(screen.getByText('Auto-Optimize Before Lock')).toBeInTheDocument()
  })

  it('renders nothing when no alerts', () => {
    const { container } = render(<LineupLockCountdown alerts={[]} />)
    expect(container.firstChild).toBeNull()
  })

  it('applies opacity to locked alerts', () => {
    render(<LineupLockCountdown {...defaultProps} />)
    
    const lockedAlert = screen.getByText('Derrick Henry (RB2)').closest('div')
    expect(lockedAlert).toHaveClass('opacity-50')
  })

  it('shows appropriate urgency icons', () => {
    render(<LineupLockCountdown {...defaultProps} />)
    
    // Should have warning icons for high urgency and info icons for medium
    const icons = screen.getAllByRole('img', { hidden: true })
    expect(icons.length).toBeGreaterThan(0)
  })

  it('handles minutes-only countdown format', () => {
    const minutesAlert: LineupLockAlert[] = [
      {
        type: 'lineup_lock',
        player_id: 'player_5',
        player_name: 'Test Player',
        slot: 'TE',
        lock_time: '2024-09-15T14:05:00Z', // 5 minutes from mock time
        time_until_lock: '0h 5m 0s',
        urgency: 'medium'
      }
    ]
    
    render(<LineupLockCountdown alerts={minutesAlert} />)
    
    expect(screen.getByText('5m 0s')).toBeInTheDocument()
  })

  it('handles seconds-only countdown format', () => {
    const secondsAlert: LineupLockAlert[] = [
      {
        type: 'lineup_lock',
        player_id: 'player_6',
        player_name: 'Test Player',
        slot: 'K',
        lock_time: '2024-09-15T14:00:30Z', // 30 seconds from mock time
        time_until_lock: '0h 0m 30s',
        urgency: 'high'
      }
    ]
    
    render(<LineupLockCountdown alerts={secondsAlert} />)
    
    expect(screen.getByText('30s')).toBeInTheDocument()
  })
})