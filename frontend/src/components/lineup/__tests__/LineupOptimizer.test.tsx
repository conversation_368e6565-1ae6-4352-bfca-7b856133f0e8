import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { LineupOptimizer } from '../LineupOptimizer'
import { LineupAPI } from '../../../lib/lineup-api'
import { OptimalLineup } from '../../../types/lineup'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { beforeEach } from 'node:test'
import { describe } from 'node:test'

// Mock the API
jest.mock('../../../lib/lineup-api')
const mockLineupAPI = LineupAPI as jest.Mocked<typeof LineupAPI>

// Mock react-dnd
jest.mock('react-dnd', () => ({
  DndProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useDrag: () => [{ isDragging: false }, jest.fn()],
  useDrop: () => [{ isOver: false }, jest.fn()],
}))

jest.mock('react-dnd-html5-backend', () => ({
  HTML5Backend: {},
}))

const mockOptimization: OptimalLineup = {
  lineup: {
    QB: 'player_1',
    RB1: 'player_2',
    RB2: 'player_3',
    WR1: 'player_4',
    WR2: 'player_5',
    TE: 'player_6',
    FLEX: 'player_7',
    K: 'player_8',
    DEF: 'player_9'
  },
  projected_points: 125.5,
  win_probability: 0.65,
  confidence: 0.85,
  rationale: 'This lineup maximizes win probability by balancing high-floor players with upside potential.',
  alternatives: [
    {
      lineup: {
        QB: 'player_1',
        RB1: 'player_2',
        RB2: 'player_3',
        WR1: 'player_4',
        WR2: 'player_5',
        TE: 'player_6',
        FLEX: 'player_10',
        K: 'player_8',
        DEF: 'player_9'
      },
      change: 'Start player_10 instead of player_7 at FLEX',
      win_probability: 0.63,
      projected_points: 124.2
    }
  ],
  risk_level: 0.35
}

const defaultProps = {
  optimization: mockOptimization,
  franchiseId: 'franchise_1',
  week: 1,
  onUpdate: jest.fn()
}

describe('LineupOptimizer', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders lineup optimizer with all slots', () => {
    render(<LineupOptimizer {...defaultProps} />)
    
    expect(screen.getByText('Weekly Lineup Optimizer')).toBeInTheDocument()
    expect(screen.getByText('QB')).toBeInTheDocument()
    expect(screen.getByText('RB1')).toBeInTheDocument()
    expect(screen.getByText('RB2')).toBeInTheDocument()
    expect(screen.getByText('WR1')).toBeInTheDocument()
    expect(screen.getByText('WR2')).toBeInTheDocument()
    expect(screen.getByText('TE')).toBeInTheDocument()
    expect(screen.getByText('FLEX')).toBeInTheDocument()
    expect(screen.getByText('K')).toBeInTheDocument()
    expect(screen.getByText('DEF')).toBeInTheDocument()
  })

  it('displays lineup summary metrics', () => {
    render(<LineupOptimizer {...defaultProps} />)
    
    expect(screen.getByText('131.5')).toBeInTheDocument() // Calculated projected points
    expect(screen.getByText('65.0%')).toBeInTheDocument() // Win probability
    expect(screen.getByText('85%')).toBeInTheDocument() // Confidence
    expect(screen.getByText('35%')).toBeInTheDocument() // Risk level
  })

  it('displays AI rationale', () => {
    render(<LineupOptimizer {...defaultProps} />)
    
    expect(screen.getByText('AI Rationale')).toBeInTheDocument()
    expect(screen.getByText(mockOptimization.rationale)).toBeInTheDocument()
  })

  it('handles auto-optimize button click', async () => {
    mockLineupAPI.optimizeLineup.mockResolvedValue(mockOptimization)
    
    render(<LineupOptimizer {...defaultProps} />)
    
    const optimizeButton = screen.getByText('Auto-Optimize')
    fireEvent.click(optimizeButton)
    
    expect(screen.getByText('Optimizing...')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(mockLineupAPI.optimizeLineup).toHaveBeenCalledWith({
        franchise_id: 'franchise_1',
        week: 1,
        season: 2024
      })
    })
  })

  it('handles save lineup button click', async () => {
    mockLineupAPI.saveLineupRecommendation.mockResolvedValue({
      message: 'Lineup saved',
      recommendation_id: 'rec_1'
    })
    
    render(<LineupOptimizer {...defaultProps} />)
    
    const saveButton = screen.getByText('Save Lineup')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(mockLineupAPI.saveLineupRecommendation).toHaveBeenCalledWith('franchise_1', 1)
    })
  })

  it('shows alternatives when expanded', () => {
    render(<LineupOptimizer {...defaultProps} />)
    
    const alternativesButton = screen.getByText('Alternative Lineups')
    fireEvent.click(alternativesButton)
    
    expect(screen.getByText('Alternative 1')).toBeInTheDocument()
    expect(screen.getByText('124.2 pts | 63.0% win prob')).toBeInTheDocument()
    expect(screen.getByText('Start player_10 instead of player_7 at FLEX')).toBeInTheDocument()
  })

  it('allows using alternative lineup', () => {
    render(<LineupOptimizer {...defaultProps} />)
    
    // Expand alternatives
    const alternativesButton = screen.getByText('Alternative Lineups')
    fireEvent.click(alternativesButton)
    
    // Use alternative lineup
    const useButton = screen.getByText('Use This Lineup')
    fireEvent.click(useButton)
    
    // Should update the lineup display - the total will be recalculated
    // Check that the alternative lineup is now being used
    expect(screen.getByText('Unknown Player')).toBeInTheDocument() // player_10 not in mock data
  })

  it('displays drag and drop instructions', () => {
    render(<LineupOptimizer {...defaultProps} />)
    
    expect(screen.getByText(/Drag and drop players between lineup slots/)).toBeInTheDocument()
  })

  it('handles optimization error gracefully', async () => {
    mockLineupAPI.optimizeLineup.mockRejectedValue(new Error('Optimization failed'))
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
    
    render(<LineupOptimizer {...defaultProps} />)
    
    const optimizeButton = screen.getByText('Auto-Optimize')
    fireEvent.click(optimizeButton)
    
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Optimization failed:', expect.any(Error))
    })
    
    consoleSpy.mockRestore()
  })

  it('handles save error gracefully', async () => {
    mockLineupAPI.saveLineupRecommendation.mockRejectedValue(new Error('Save failed'))
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
    
    render(<LineupOptimizer {...defaultProps} />)
    
    const saveButton = screen.getByText('Save Lineup')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Save failed:', expect.any(Error))
    })
    
    consoleSpy.mockRestore()
  })
})