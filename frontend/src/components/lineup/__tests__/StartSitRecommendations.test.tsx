import { render, screen, fireEvent } from '@testing-library/react'
import { StartSitRecommendations } from '../StartSitRecommendations'
import { StartSitRecommendation } from '../../../types/lineup'

const mockRecommendations: StartSitRecommendation[] = [
  {
    type: 'start_sit',
    slot: 'FLEX',
    start_player_id: 'player_7',
    sit_player_id: 'player_10',
    projected_improvement: 3.2,
    confidence: 0.85,
    rationale: 'Player 7 has a better matchup against a weaker defense and higher projected ceiling this week.'
  },
  {
    type: 'start_sit',
    slot: 'WR2',
    start_player_id: 'player_11',
    sit_player_id: 'player_5',
    projected_improvement: 1.8,
    confidence: 0.65,
    rationale: 'Player 11 is expected to see increased target share with the team\'s WR1 questionable.'
  }
]

const defaultProps = {
  recommendations: mockRecommendations,
  franchiseId: 'franchise_1',
  week: 1
}

describe('StartSitRecommendations', () => {
  it('renders recommendations list', () => {
    render(<StartSitRecommendations {...defaultProps} />)
    
    expect(screen.getByText('Start/Sit Recommendations')).toBeInTheDocument()
    expect(screen.getByText('Week 1 • 2 recommendations')).toBeInTheDocument()
  })

  it('displays recommendation details', () => {
    render(<StartSitRecommendations {...defaultProps} />)
    
    expect(screen.getByText('FLEX')).toBeInTheDocument()
    expect(screen.getByText('WR2')).toBeInTheDocument()
    expect(screen.getByText('START: Amon-Ra St. Brown')).toBeInTheDocument()
    expect(screen.getByText('SIT: Tua Tagovailoa')).toBeInTheDocument()
    expect(screen.getByText('**** pts')).toBeInTheDocument()
    expect(screen.getByText('**** pts')).toBeInTheDocument()
  })

  it('shows confidence levels with appropriate colors', () => {
    render(<StartSitRecommendations {...defaultProps} />)
    
    const highConfidence = screen.getByText('85% confident')
    const mediumConfidence = screen.getByText('65% confident')
    
    expect(highConfidence).toHaveClass('text-green-600', 'bg-green-100')
    expect(mediumConfidence).toHaveClass('text-yellow-600', 'bg-yellow-100')
  })

  it('expands recommendation details when clicked', () => {
    render(<StartSitRecommendations {...defaultProps} />)
    
    // Initially rationale should not be visible
    expect(screen.queryByText('Player 7 has a better matchup')).not.toBeInTheDocument()
    
    // Click to expand first recommendation
    const firstRec = screen.getByText('START: Amon-Ra St. Brown').closest('.p-4')
    fireEvent.click(firstRec!)
    
    // Now rationale should be visible
    expect(screen.getByText(/Player 7 has a better matchup/)).toBeInTheDocument()
    expect(screen.getByText('Analysis')).toBeInTheDocument()
  })

  it('shows action buttons when expanded', () => {
    render(<StartSitRecommendations {...defaultProps} />)
    
    // Click to expand first recommendation
    const firstRec = screen.getByText('START: Amon-Ra St. Brown').closest('.p-4')
    fireEvent.click(firstRec!)
    
    expect(screen.getByText('Apply Recommendation')).toBeInTheDocument()
    expect(screen.getByText('Ignore')).toBeInTheDocument()
  })

  it('displays win probability impact when expanded', () => {
    render(<StartSitRecommendations {...defaultProps} />)
    
    // Click to expand first recommendation
    const firstRec = screen.getByText('START: Amon-Ra St. Brown').closest('.p-4')
    fireEvent.click(firstRec!)
    
    expect(screen.getByText('Win Prob Impact')).toBeInTheDocument()
    expect(screen.getByText('+1.6%')).toBeInTheDocument() // 3.2 * 0.5
  })

  it('shows total impact summary', () => {
    render(<StartSitRecommendations {...defaultProps} />)
    
    expect(screen.getByText('Total Impact')).toBeInTheDocument()
    expect(screen.getByText('+5.0 points')).toBeInTheDocument() // 3.2 + 1.8
  })

  it('renders empty state when no recommendations', () => {
    render(<StartSitRecommendations {...defaultProps} recommendations={[]} />)
    
    expect(screen.getByText('Your lineup is already optimal!')).toBeInTheDocument()
    expect(screen.getByText(/No start\/sit changes recommended/)).toBeInTheDocument()
  })

  it('handles recommendations without sit player', () => {
    const recWithoutSit: StartSitRecommendation[] = [
      {
        type: 'start_sit',
        slot: 'QB',
        start_player_id: 'player_1',
        projected_improvement: 2.5,
        confidence: 0.75,
        rationale: 'Start this player for better matchup.'
      }
    ]
    
    render(<StartSitRecommendations {...defaultProps} recommendations={recWithoutSit} />)
    
    expect(screen.getByText('START: Josh Allen')).toBeInTheDocument()
    expect(screen.queryByText('SIT:')).not.toBeInTheDocument()
  })

  it('colors improvement values appropriately', () => {
    render(<StartSitRecommendations {...defaultProps} />)
    
    const highImprovement = screen.getByText('**** pts')
    const lowImprovement = screen.getByText('**** pts')
    
    expect(highImprovement).toHaveClass('text-green-600')
    expect(lowImprovement).toHaveClass('text-yellow-600')
  })

  it('toggles expansion state correctly', () => {
    render(<StartSitRecommendations {...defaultProps} />)
    
    const firstRec = screen.getByText('START: Amon-Ra St. Brown').closest('.p-4')
    
    // Expand
    fireEvent.click(firstRec!)
    expect(screen.getByText('Analysis')).toBeInTheDocument()
    
    // Collapse
    fireEvent.click(firstRec!)
    expect(screen.queryByText('Analysis')).not.toBeInTheDocument()
  })

  it('shows correct arrow direction for expansion', () => {
    render(<StartSitRecommendations {...defaultProps} />)
    
    const firstRec = screen.getByText('START: Amon-Ra St. Brown').closest('.border')
    const firstArrow = firstRec!.querySelector('.flex-shrink-0:last-child svg')
    
    expect(firstArrow).not.toHaveClass('rotate-180')
    
    // Click to expand
    const clickableArea = firstRec!.querySelector('.p-4')
    fireEvent.click(clickableArea!)
    
    expect(firstArrow).toHaveClass('rotate-180')
  })
})