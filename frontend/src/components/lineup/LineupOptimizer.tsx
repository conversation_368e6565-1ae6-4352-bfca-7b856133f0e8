'use client'

import { useState, useCallback } from 'react'
import { DndProvider, useDrag, useDrop } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { OptimalLineup } from '../../types/lineup'
import { LineupAPI } from '../../lib/lineup-api'

interface LineupOptimizerProps {
  optimization: OptimalLineup
  franchiseId: string
  week: number
  onUpdate: () => void
}

interface DragItem {
  type: string
  playerId: string
  fromSlot: string
}

interface LineupSlotProps {
  slot: string
  playerId: string
  playerName: string
  projectedPoints: number
  onPlayerMove: (fromSlot: string, toSlot: string, playerId: string) => void
}

const LineupSlot: React.FC<LineupSlotProps> = ({ 
  slot, 
  playerId, 
  playerName, 
  projectedPoints, 
  onPlayerMove 
}) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'player',
    item: { type: 'player', playerId, fromSlot: slot },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }))

  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'player',
    drop: (item: DragItem) => {
      if (item.fromSlot !== slot) {
        onPlayerMove(item.fromSlot, slot, item.playerId)
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }))

  return (
    <div
      ref={(node) => drag(drop(node))}
      className={`
        p-4 border-2 border-dashed rounded-lg cursor-move transition-colors
        ${isDragging ? 'opacity-50' : 'opacity-100'}
        ${isOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-white'}
        hover:border-gray-400
      `}
    >
      <div className="flex justify-between items-center">
        <div>
          <div className="font-medium text-gray-900">{slot}</div>
          <div className="text-sm text-gray-600">{playerName}</div>
        </div>
        <div className="text-right">
          <div className="font-semibold text-green-600">{projectedPoints.toFixed(1)}</div>
          <div className="text-xs text-gray-500">pts</div>
        </div>
      </div>
    </div>
  )
}

export const LineupOptimizer: React.FC<LineupOptimizerProps> = ({
  optimization,
  franchiseId,
  week,
  onUpdate
}) => {
  const [currentLineup, setCurrentLineup] = useState(optimization.lineup)
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [showAlternatives, setShowAlternatives] = useState(false)

  // Mock player data - in real app would come from API
  const playerData = {
    player_1: { name: 'Josh Allen', position: 'QB', projected_points: 22.5 },
    player_2: { name: 'Christian McCaffrey', position: 'RB', projected_points: 18.2 },
    player_3: { name: 'Derrick Henry', position: 'RB', projected_points: 16.8 },
    player_4: { name: 'Tyreek Hill', position: 'WR', projected_points: 15.4 },
    player_5: { name: 'Davante Adams', position: 'WR', projected_points: 14.9 },
    player_6: { name: 'Travis Kelce', position: 'TE', projected_points: 13.2 },
    player_7: { name: 'Amon-Ra St. Brown', position: 'WR', projected_points: 12.8 },
    player_8: { name: 'Justin Tucker', position: 'K', projected_points: 8.5 },
    player_9: { name: 'San Francisco', position: 'DEF', projected_points: 9.2 }
  }

  const handlePlayerMove = useCallback((fromSlot: string, toSlot: string, playerId: string) => {
    const newLineup = { ...currentLineup }
    const fromPlayerId = newLineup[fromSlot]
    const toPlayerId = newLineup[toSlot]
    
    // Swap players
    newLineup[fromSlot] = toPlayerId
    newLineup[toSlot] = fromPlayerId
    
    setCurrentLineup(newLineup)
  }, [currentLineup])

  const handleOptimize = async () => {
    try {
      setIsOptimizing(true)
      const optimized = await LineupAPI.optimizeLineup({
        franchise_id: franchiseId,
        week,
        season: 2024
      })
      setCurrentLineup(optimized.lineup)
      onUpdate()
    } catch (error) {
      console.error('Optimization failed:', error)
    } finally {
      setIsOptimizing(false)
    }
  }

  const handleSaveLineup = async () => {
    try {
      await LineupAPI.saveLineupRecommendation(franchiseId, week)
      onUpdate()
    } catch (error) {
      console.error('Save failed:', error)
    }
  }

  const calculateTotalPoints = () => {
    return Object.values(currentLineup).reduce((total, playerId) => {
      const player = playerData[playerId as keyof typeof playerData]
      return total + (player?.projected_points || 0)
    }, 0)
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Weekly Lineup Optimizer</h2>
          <div className="flex space-x-3">
            <button
              onClick={handleOptimize}
              disabled={isOptimizing}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isOptimizing ? 'Optimizing...' : 'Auto-Optimize'}
            </button>
            <button
              onClick={handleSaveLineup}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Save Lineup
            </button>
          </div>
        </div>

        {/* Lineup Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          {Object.entries(currentLineup).map(([slot, playerId]) => {
            const player = playerData[playerId as keyof typeof playerData]
            return (
              <LineupSlot
                key={slot}
                slot={slot}
                playerId={playerId}
                playerName={player?.name || 'Unknown Player'}
                projectedPoints={player?.projected_points || 0}
                onPlayerMove={handlePlayerMove}
              />
            )
          })}
        </div>

        {/* Lineup Summary */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {calculateTotalPoints().toFixed(1)}
              </div>
              <div className="text-sm text-gray-600">Projected Points</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {(optimization.win_probability * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Win Probability</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {(optimization.confidence * 100).toFixed(0)}%
              </div>
              <div className="text-sm text-gray-600">Confidence</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {(optimization.risk_level * 100).toFixed(0)}%
              </div>
              <div className="text-sm text-gray-600">Risk Level</div>
            </div>
          </div>
        </div>

        {/* Rationale */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">AI Rationale</h3>
          <p className="text-gray-700 bg-blue-50 p-4 rounded-lg">
            {optimization.rationale}
          </p>
        </div>

        {/* Alternatives */}
        <div>
          <button
            onClick={() => setShowAlternatives(!showAlternatives)}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <span className="mr-2">Alternative Lineups</span>
            <svg
              className={`w-4 h-4 transform transition-transform ${showAlternatives ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {showAlternatives && (
            <div className="space-y-4">
              {optimization.alternatives.map((alt, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium text-gray-900">Alternative {index + 1}</h4>
                    <div className="text-sm text-gray-600">
                      {alt.projected_points.toFixed(1)} pts | {(alt.win_probability * 100).toFixed(1)}% win prob
                    </div>
                  </div>
                  <p className="text-sm text-gray-700">{alt.change}</p>
                  <button
                    onClick={() => setCurrentLineup(alt.lineup)}
                    className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                  >
                    Use This Lineup
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Drag and Drop Instructions */}
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>Tip:</strong> Drag and drop players between lineup slots to manually adjust your lineup. 
            The system will automatically recalculate projections and win probability.
          </p>
        </div>
      </div>
    </DndProvider>
  )
}