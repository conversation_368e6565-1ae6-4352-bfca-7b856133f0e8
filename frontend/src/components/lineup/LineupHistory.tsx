'use client'

import { useState, useEffect } from 'react'
import { LineupHistory as LineupHistoryType } from '../../types/lineup'
import { LineupAPI } from '../../lib/lineup-api'
import { LoadingSpinner } from '../ui/LoadingSpinner'
import { ErrorMessage } from '../ui/ErrorMessage'

interface LineupHistoryProps {
  franchiseId: string
  season: number
}

export const LineupHistory: React.FC<LineupHistoryProps> = ({ franchiseId, season }) => {
  const [history, setHistory] = useState<LineupHistoryType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedWeek, setSelectedWeek] = useState<number | null>(null)
  const [sortBy, setSortBy] = useState<'week' | 'points' | 'accuracy'>('week')

  // Mock player data - in real app would come from API
  const playerData = {
    player_1: { name: '<PERSON>', position: 'QB', team: 'BUF' },
    player_2: { name: '<PERSON>', position: 'RB', team: 'SF' },
    player_3: { name: '<PERSON>', position: 'RB', team: 'TEN' },
    player_4: { name: 'Tyreek Hill', position: 'WR', team: 'MIA' },
    player_5: { name: 'Davante <PERSON>', position: 'WR', team: 'LV' },
    player_6: { name: '<PERSON> Kel<PERSON>', position: 'TE', team: 'KC' },
    player_7: { name: 'Amon-Ra St. Brown', position: 'WR', team: 'DET' },
    player_8: { name: 'Justin Tucker', position: 'K', team: 'BAL' },
    player_9: { name: 'San Francisco', position: 'DEF', team: 'SF' }
  }

  useEffect(() => {
    loadHistory()
  }, [franchiseId, season])

  const loadHistory = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Generate mock history data
      const mockHistory: LineupHistoryType[] = Array.from({ length: 12 }, (_, i) => ({
        week: i + 1,
        season,
        lineup: {
          QB: 'player_1',
          RB1: 'player_2',
          RB2: 'player_3',
          WR1: 'player_4',
          WR2: 'player_5',
          TE: 'player_6',
          FLEX: 'player_7',
          K: 'player_8',
          DEF: 'player_9'
        },
        projected_points: 120 + Math.random() * 20,
        actual_points: 110 + Math.random() * 30,
        win_probability: 0.5 + Math.random() * 0.4,
        result: Math.random() > 0.5 ? 'win' as const : 'loss' as const,
        created_at: new Date(2024, 8, i + 1).toISOString()
      }))
      
      setHistory(mockHistory)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load lineup history')
    } finally {
      setLoading(false)
    }
  }

  const getPlayerInfo = (playerId: string) => {
    return playerData[playerId as keyof typeof playerData] || { 
      name: 'Unknown Player', 
      position: 'N/A', 
      team: 'N/A' 
    }
  }

  const calculateAccuracy = (projected: number, actual: number) => {
    const diff = Math.abs(projected - actual)
    const accuracy = Math.max(0, 100 - (diff / projected) * 100)
    return accuracy
  }

  const getResultColor = (result: string) => {
    switch (result) {
      case 'win':
        return 'text-green-600 bg-green-100'
      case 'loss':
        return 'text-red-600 bg-red-100'
      case 'tie':
        return 'text-yellow-600 bg-yellow-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const sortedHistory = [...history].sort((a, b) => {
    switch (sortBy) {
      case 'week':
        return b.week - a.week
      case 'points':
        return (b.actual_points || 0) - (a.actual_points || 0)
      case 'accuracy':
        const accuracyA = a.actual_points ? calculateAccuracy(a.projected_points, a.actual_points) : 0
        const accuracyB = b.actual_points ? calculateAccuracy(b.projected_points, b.actual_points) : 0
        return accuracyB - accuracyA
      default:
        return 0
    }
  })

  const averageProjected = history.reduce((sum, h) => sum + h.projected_points, 0) / history.length
  const averageActual = history.reduce((sum, h) => sum + (h.actual_points || 0), 0) / history.length
  const winRate = history.filter(h => h.result === 'win').length / history.length
  const averageAccuracy = history.reduce((sum, h) => {
    return sum + (h.actual_points ? calculateAccuracy(h.projected_points, h.actual_points) : 0)
  }, 0) / history.length

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <ErrorMessage message={error} onRetry={loadHistory} />
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Lineup History</h2>
        <div className="flex items-center space-x-4">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'week' | 'points' | 'accuracy')}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="week">Sort by Week</option>
            <option value="points">Sort by Points</option>
            <option value="accuracy">Sort by Accuracy</option>
          </select>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{averageProjected.toFixed(1)}</div>
          <div className="text-sm text-blue-800">Avg Projected</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{averageActual.toFixed(1)}</div>
          <div className="text-sm text-green-800">Avg Actual</div>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-purple-600">{(winRate * 100).toFixed(0)}%</div>
          <div className="text-sm text-purple-800">Win Rate</div>
        </div>
        <div className="bg-orange-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-orange-600">{averageAccuracy.toFixed(0)}%</div>
          <div className="text-sm text-orange-800">Avg Accuracy</div>
        </div>
      </div>

      {/* History Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Week
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Projected
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actual
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Accuracy
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Win Prob
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Result
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedHistory.map((week) => {
              const accuracy = week.actual_points ? calculateAccuracy(week.projected_points, week.actual_points) : null
              
              return (
                <tr key={week.week} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">Week {week.week}</div>
                    <div className="text-xs text-gray-500">
                      {new Date(week.created_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{week.projected_points.toFixed(1)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {week.actual_points ? week.actual_points.toFixed(1) : '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {accuracy ? `${accuracy.toFixed(0)}%` : '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {(week.win_probability * 100).toFixed(0)}%
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {week.result && (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getResultColor(week.result)}`}>
                        {week.result.toUpperCase()}
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button
                      onClick={() => setSelectedWeek(selectedWeek === week.week ? null : week.week)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>

      {/* Detailed View */}
      {selectedWeek && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Week {selectedWeek} Lineup Details
            </h3>
            <button
              onClick={() => setSelectedWeek(null)}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {(() => {
            const weekData = history.find(h => h.week === selectedWeek)
            if (!weekData) return null

            return (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(weekData.lineup).map(([slot, playerId]) => {
                  const player = getPlayerInfo(playerId)
                  return (
                    <div key={slot} className="bg-white p-3 rounded-lg">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium text-gray-900">{slot}</div>
                          <div className="text-sm text-gray-600">{player.name}</div>
                          <div className="text-xs text-gray-500">{player.position} • {player.team}</div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )
          })()}
        </div>
      )}

      {/* Performance Insights */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">Performance Insights</h4>
        <div className="text-sm text-blue-800 space-y-1">
          <p>• Your lineups have averaged {averageAccuracy.toFixed(0)}% projection accuracy</p>
          <p>• You're winning {(winRate * 100).toFixed(0)}% of your matchups</p>
          <p>• Your actual scoring is {averageActual > averageProjected ? 'exceeding' : 'below'} projections by {Math.abs(averageActual - averageProjected).toFixed(1)} points on average</p>
        </div>
      </div>
    </div>
  )
}