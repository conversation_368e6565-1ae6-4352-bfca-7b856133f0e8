'use client'

import { useState, useEffect } from 'react'
import { LineupLockAlert } from '../../types/lineup'

interface LineupLockCountdownProps {
  alerts: LineupLockAlert[]
}

interface TimeRemaining {
  hours: number
  minutes: number
  seconds: number
}

export const LineupLockCountdown: React.FC<LineupLockCountdownProps> = ({ alerts }) => {
  const [timeRemainingMap, setTimeRemainingMap] = useState<Record<string, TimeRemaining>>({})

  useEffect(() => {
    const updateCountdowns = () => {
      const newTimeMap: Record<string, TimeRemaining> = {}
      
      alerts.forEach(alert => {
        const lockTime = new Date(alert.lock_time)
        const now = new Date()
        const diff = lockTime.getTime() - now.getTime()
        
        if (diff > 0) {
          const hours = Math.floor(diff / (1000 * 60 * 60))
          const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
          const seconds = Math.floor((diff % (1000 * 60)) / 1000)
          
          newTimeMap[alert.player_id] = { hours, minutes, seconds }
        } else {
          newTimeMap[alert.player_id] = { hours: 0, minutes: 0, seconds: 0 }
        }
      })
      
      setTimeRemainingMap(newTimeMap)
    }

    updateCountdowns()
    const interval = setInterval(updateCountdowns, 1000)

    return () => clearInterval(interval)
  }, [alerts])

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high':
        return 'bg-red-100 border-red-200 text-red-800'
      case 'medium':
        return 'bg-yellow-100 border-yellow-200 text-yellow-800'
      default:
        return 'bg-blue-100 border-blue-200 text-blue-800'
    }
  }

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'high':
        return (
          <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        )
      case 'medium':
        return (
          <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      default:
        return (
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
    }
  }

  const formatCountdown = (time: TimeRemaining) => {
    if (time.hours > 0) {
      return `${time.hours}h ${time.minutes}m ${time.seconds}s`
    } else if (time.minutes > 0) {
      return `${time.minutes}m ${time.seconds}s`
    } else {
      return `${time.seconds}s`
    }
  }

  if (alerts.length === 0) {
    return null
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center space-x-2 mb-4">
        <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h2 className="text-xl font-bold text-gray-900">Lineup Lock Alerts</h2>
      </div>

      <div className="space-y-3">
        {alerts.map((alert) => {
          const timeRemaining = timeRemainingMap[alert.player_id]
          const isLocked = timeRemaining && timeRemaining.hours === 0 && timeRemaining.minutes === 0 && timeRemaining.seconds === 0

          return (
            <div
              key={alert.player_id}
              className={`p-4 rounded-lg border-2 ${getUrgencyColor(alert.urgency)} ${
                isLocked ? 'opacity-50' : ''
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getUrgencyIcon(alert.urgency)}
                  <div>
                    <div className="font-medium">
                      {alert.player_name} ({alert.slot})
                    </div>
                    <div className="text-sm opacity-75">
                      {isLocked ? 'LOCKED' : `Locks in ${formatCountdown(timeRemaining || { hours: 0, minutes: 0, seconds: 0 })}`}
                    </div>
                  </div>
                </div>

                <div className="text-right">
                  {!isLocked && timeRemaining && (
                    <div className="font-mono text-lg font-bold">
                      {formatCountdown(timeRemaining)}
                    </div>
                  )}
                  {isLocked && (
                    <div className="text-lg font-bold text-gray-500">
                      LOCKED
                    </div>
                  )}
                  <div className="text-xs opacity-75">
                    {new Date(alert.lock_time).toLocaleTimeString()}
                  </div>
                </div>
              </div>

              {alert.urgency === 'high' && !isLocked && (
                <div className="mt-3 p-3 bg-red-50 rounded-md">
                  <div className="flex items-start space-x-2">
                    <svg className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <div>
                      <div className="text-sm font-medium text-red-800">
                        Urgent: Make lineup changes now!
                      </div>
                      <div className="text-xs text-red-700 mt-1">
                        This player's game locks soon. Any lineup changes must be made immediately.
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* Quick Actions */}
      <div className="mt-6 flex flex-wrap gap-3">
        <button className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
          Review Lineup
        </button>
        <button className="px-4 py-2 bg-gray-200 text-gray-800 text-sm rounded-md hover:bg-gray-300">
          Set Reminders
        </button>
        <button className="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700">
          Auto-Optimize Before Lock
        </button>
      </div>

      {/* Summary */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <div className="text-sm text-gray-700">
          <strong>{alerts.filter(a => a.urgency === 'high').length}</strong> high priority alerts •{' '}
          <strong>{alerts.filter(a => a.urgency === 'medium').length}</strong> medium priority alerts
        </div>
      </div>
    </div>
  )
}