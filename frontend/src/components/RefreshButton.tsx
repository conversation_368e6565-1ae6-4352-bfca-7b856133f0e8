'use client'

import { useState } from 'react'
import { useMFLContext } from '@/context/MFLContext'

export default function RefreshButton({ contextKey }: { contextKey?: string }) {
  const { refreshLeague, lastRefreshed } = useMFLContext()
  const [pending, setPending] = useState(false)
  const [message, setMessage] = useState<string | null>(null)

  const onClick = async () => {
    setPending(true)
    setMessage(null)
    try {
      await refreshLeague()
      setMessage('Data refreshed')
      setTimeout(() => setMessage(null), 2500)
    } catch (e) {
      setMessage('Refresh failed')
      setTimeout(() => setMessage(null), 3000)
    } finally {
      setPending(false)
    }
  }

  return (
    <div className="flex items-center space-x-3">
      <button
        onClick={onClick}
        disabled={pending}
        className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium ${pending ? 'bg-gray-300 text-gray-600' : 'bg-blue-600 text-white hover:bg-blue-700'}`}
        aria-busy={pending}
      >
        {pending ? 'Refreshing…' : 'Refresh Data'}
      </button>
      <div className="text-xs text-gray-500">
        {lastRefreshed ? `Last refreshed: ${new Date(lastRefreshed).toLocaleTimeString()}` : 'Not refreshed yet'}
      </div>
    </div>
  )
}

