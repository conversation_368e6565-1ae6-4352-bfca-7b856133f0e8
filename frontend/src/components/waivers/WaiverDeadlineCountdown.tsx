'use client'

import { useState, useEffect } from 'react'
import { ClockIcon } from '@heroicons/react/24/outline'

interface WaiverDeadlineCountdownProps {
  deadline: Date
}

interface TimeRemaining {
  days: number
  hours: number
  minutes: number
  seconds: number
}

export default function WaiverDeadlineCountdown({ deadline }: WaiverDeadlineCountdownProps) {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({ days: 0, hours: 0, minutes: 0, seconds: 0 })
  const [isExpired, setIsExpired] = useState(false)

  useEffect(() => {
    const calculateTimeRemaining = () => {
      const now = new Date().getTime()
      const deadlineTime = deadline.getTime()
      const difference = deadlineTime - now

      if (difference <= 0) {
        setIsExpired(true)
        setTimeRemaining({ days: 0, hours: 0, minutes: 0, seconds: 0 })
        return
      }

      const days = Math.floor(difference / (1000 * 60 * 60 * 24))
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((difference % (1000 * 60)) / 1000)

      setTimeRemaining({ days, hours, minutes, seconds })
    }

    // Calculate immediately
    calculateTimeRemaining()

    // Update every second
    const interval = setInterval(calculateTimeRemaining, 1000)

    return () => clearInterval(interval)
  }, [deadline])

  const getUrgencyColor = () => {
    if (isExpired) return 'text-red-600 bg-red-100'
    
    const totalHours = timeRemaining.days * 24 + timeRemaining.hours
    if (totalHours <= 2) return 'text-red-600 bg-red-100'
    if (totalHours <= 12) return 'text-orange-600 bg-orange-100'
    if (totalHours <= 24) return 'text-yellow-600 bg-yellow-100'
    return 'text-green-600 bg-green-100'
  }

  const formatDeadline = () => {
    return deadline.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      timeZoneName: 'short'
    })
  }

  if (isExpired) {
    return (
      <div className={`inline-flex items-center px-4 py-2 rounded-lg ${getUrgencyColor()}`}>
        <ClockIcon className="w-5 h-5 mr-2" />
        <div className="text-center">
          <div className="font-semibold">Waiver Deadline Passed</div>
          <div className="text-xs opacity-75">
            {formatDeadline()}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`inline-flex items-center px-4 py-2 rounded-lg ${getUrgencyColor()}`}>
      <ClockIcon className="w-5 h-5 mr-2" />
      <div className="text-center">
        <div className="font-semibold">Waiver Deadline</div>
        <div className="text-xs opacity-75 mb-1">
          {formatDeadline()}
        </div>
        <div className="flex items-center space-x-2 text-sm font-mono">
          {timeRemaining.days > 0 && (
            <div className="text-center">
              <div className="font-bold">{timeRemaining.days}</div>
              <div className="text-xs opacity-75">day{timeRemaining.days !== 1 ? 's' : ''}</div>
            </div>
          )}
          {(timeRemaining.days > 0 || timeRemaining.hours > 0) && (
            <>
              {timeRemaining.days > 0 && <span className="opacity-50">:</span>}
              <div className="text-center">
                <div className="font-bold">{timeRemaining.hours.toString().padStart(2, '0')}</div>
                <div className="text-xs opacity-75">hr{timeRemaining.hours !== 1 ? 's' : ''}</div>
              </div>
            </>
          )}
          <span className="opacity-50">:</span>
          <div className="text-center">
            <div className="font-bold">{timeRemaining.minutes.toString().padStart(2, '0')}</div>
            <div className="text-xs opacity-75">min</div>
          </div>
          <span className="opacity-50">:</span>
          <div className="text-center">
            <div className="font-bold">{timeRemaining.seconds.toString().padStart(2, '0')}</div>
            <div className="text-xs opacity-75">sec</div>
          </div>
        </div>
      </div>
    </div>
  )
}