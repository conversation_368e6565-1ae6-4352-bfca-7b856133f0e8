'use client'

import { useState, useEffect } from 'react'
import { PlusIcon, TrashIcon, CalculatorIcon } from '@heroicons/react/24/outline'
import { WaiverTarget, WaiverStrategy, FAABOptimizationRequest } from '@/types/waiver'
import { WaiverAPI } from '@/lib/waiver-api'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface FAABCalculatorProps {
  franchiseId: string
  waiverTargets: WaiverTarget[]
}

interface SelectedTarget {
  target: WaiverTarget
  customBid?: number
}

export default function FAABCalculator({ franchiseId, waiverTargets }: FAABCalculatorProps) {
  const [selectedTargets, setSelectedTargets] = useState<SelectedTarget[]>([])
  const [budgetConstraint, setBudgetConstraint] = useState<number>(100)
  const [strategy, setStrategy] = useState<WaiverStrategy | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const addTarget = (target: WaiverTarget) => {
    if (!selectedTargets.find(st => st.target.player_id === target.player_id)) {
      setSelectedTargets([...selectedTargets, { target }])
    }
  }

  const removeTarget = (playerId: string) => {
    setSelectedTargets(selectedTargets.filter(st => st.target.player_id !== playerId))
  }

  const updateCustomBid = (playerId: string, bid: number) => {
    setSelectedTargets(selectedTargets.map(st => 
      st.target.player_id === playerId 
        ? { ...st, customBid: bid }
        : st
    ))
  }

  const calculateOptimalBids = async () => {
    if (selectedTargets.length === 0) {
      setError('Please select at least one target')
      return
    }

    try {
      setLoading(true)
      setError(null)

      const request: FAABOptimizationRequest = {
        franchise_id: franchiseId,
        target_player_ids: selectedTargets.map(st => st.target.player_id),
        budget_constraint: budgetConstraint
      }

      const optimizedStrategy = await WaiverAPI.optimizeFAAB(request)
      setStrategy(optimizedStrategy)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to optimize FAAB allocation')
    } finally {
      setLoading(false)
    }
  }

  const getTotalBid = () => {
    if (strategy) {
      return strategy.total_faab_allocation
    }
    return selectedTargets.reduce((total, st) => 
      total + (st.customBid || st.target.recommended_bid), 0
    )
  }

  const getRemainingBudget = () => {
    return budgetConstraint - getTotalBid()
  }

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel.toLowerCase()) {
      case 'low': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'high': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="space-y-6">
      {/* Budget Settings */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">FAAB Budget</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Total Budget
            </label>
            <input
              type="number"
              value={budgetConstraint}
              onChange={(e) => setBudgetConstraint(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="0"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Total Allocated
            </label>
            <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md">
              ${getTotalBid()}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Remaining Budget
            </label>
            <div className={`px-3 py-2 border border-gray-300 rounded-md ${
              getRemainingBudget() < 0 ? 'bg-red-50 text-red-600' : 'bg-green-50 text-green-600'
            }`}>
              ${getRemainingBudget()}
            </div>
          </div>
        </div>
      </div>

      {/* Target Selection */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Select Targets</h2>
          <button
            onClick={calculateOptimalBids}
            disabled={loading || selectedTargets.length === 0}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <CalculatorIcon className="w-4 h-4 mr-2" />
            {loading ? 'Optimizing...' : 'Optimize Bids'}
          </button>
        </div>

        {/* Available Targets */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Available Targets</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {waiverTargets.map((target) => (
              <button
                key={target.player_id}
                onClick={() => addTarget(target)}
                disabled={selectedTargets.find(st => st.target.player_id === target.player_id) !== undefined}
                className="flex items-center justify-between p-3 border border-gray-200 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <div className="text-left">
                  <div className="font-medium text-gray-900">{target.player_name}</div>
                  <div className="text-sm text-gray-500">{target.position} • ${target.recommended_bid}</div>
                </div>
                <PlusIcon className="w-4 h-4 text-gray-400" />
              </button>
            ))}
          </div>
        </div>

        {/* Selected Targets */}
        {selectedTargets.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">Selected Targets</h3>
            <div className="space-y-3">
              {selectedTargets.map((selectedTarget) => {
                const optimizedTarget = strategy?.targets.find(t => t.player_id === selectedTarget.target.player_id)
                const displayBid = optimizedTarget?.recommended_bid || selectedTarget.customBid || selectedTarget.target.recommended_bid

                return (
                  <div key={selectedTarget.target.player_id} className="flex items-center space-x-4 p-3 border border-gray-200 rounded-md">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{selectedTarget.target.player_name}</div>
                      <div className="text-sm text-gray-500">
                        {selectedTarget.target.position} • {selectedTarget.target.points_over_replacement.toFixed(1)} POR
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <div className="text-sm text-gray-500">Bid:</div>
                      <input
                        type="number"
                        value={selectedTarget.customBid || selectedTarget.target.recommended_bid}
                        onChange={(e) => updateCustomBid(selectedTarget.target.player_id, Number(e.target.value))}
                        className="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        min="0"
                        disabled={strategy !== null}
                      />
                      {optimizedTarget && (
                        <div className="text-sm text-green-600 font-medium">
                          → ${optimizedTarget.recommended_bid}
                        </div>
                      )}
                    </div>

                    <button
                      onClick={() => removeTarget(selectedTarget.target.player_id)}
                      className="text-red-600 hover:text-red-800 transition-colors"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="text-sm text-red-600">{error}</div>
          </div>
        )}
      </div>

      {/* Optimization Results */}
      {strategy && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Optimization Results</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">${strategy.total_faab_allocation}</div>
              <div className="text-sm text-blue-600">Total Allocation</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">${strategy.remaining_budget}</div>
              <div className="text-sm text-green-600">Remaining Budget</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{strategy.expected_value.toFixed(1)}</div>
              <div className="text-sm text-purple-600">Expected Value</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getRiskLevelColor(strategy.risk_level)}`}>
                {strategy.risk_level} Risk
              </span>
            </div>
          </div>

          {/* Trade-offs */}
          {strategy.trade_offs.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Trade-offs</h3>
              <ul className="space-y-1">
                {strategy.trade_offs.map((tradeOff, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-start">
                    <span className="text-yellow-500 mr-2">•</span>
                    {tradeOff}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Streaming Opportunities */}
          {strategy.streaming_opportunities.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Streaming Opportunities</h3>
              <div className="space-y-2">
                {strategy.streaming_opportunities.map((opportunity, index) => (
                  <div key={index} className="p-3 bg-blue-50 rounded-md">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="font-medium">{opportunity.position}</span>
                        <span className="text-sm text-gray-600 ml-2">
                          Weeks {opportunity.weeks.join(', ')}
                        </span>
                      </div>
                      <div className="text-sm">
                        <span className="text-green-600 font-medium">{opportunity.total_value.toFixed(1)} value</span>
                        <span className="text-gray-500 ml-2">({(opportunity.confidence * 100).toFixed(0)}% confidence)</span>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">{opportunity.strategy}</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {loading && (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      )}
    </div>
  )
}