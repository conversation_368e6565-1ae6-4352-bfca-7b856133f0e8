'use client'

import { useState } from 'react'
import { ArrowPathIcon, ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline'
import { StreamingOpportunity } from '@/types/waiver'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface StreamingOpportunitiesProps {
  opportunities: StreamingOpportunity[]
  onRefresh: () => void
  loading: boolean
}

export default function StreamingOpportunities({ 
  opportunities, 
  onRefresh, 
  loading 
}: StreamingOpportunitiesProps) {
  const [expandedOpportunity, setExpandedOpportunity] = useState<string | null>(null)

  const toggleExpanded = (opportunityId: string) => {
    setExpandedOpportunity(expandedOpportunity === opportunityId ? null : opportunityId)
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100'
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getPositionColor = (position: string) => {
    switch (position.toUpperCase()) {
      case 'QB': return 'text-red-600 bg-red-100'
      case 'RB': return 'text-green-600 bg-green-100'
      case 'WR': return 'text-blue-600 bg-blue-100'
      case 'TE': return 'text-purple-600 bg-purple-100'
      case 'K': return 'text-yellow-600 bg-yellow-100'
      case 'DST': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Streaming Opportunities</h2>
            <p className="text-sm text-gray-600 mt-1">
              Multi-week streaming strategies for maximum value
            </p>
          </div>
          <button
            onClick={onRefresh}
            disabled={loading}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ArrowPathIcon className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Opportunities List */}
      <div className="p-6">
        {loading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : opportunities.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No streaming opportunities found
          </div>
        ) : (
          <div className="space-y-4">
            {opportunities.map((opportunity, index) => {
              const opportunityId = `${opportunity.position}-${index}`
              const isExpanded = expandedOpportunity === opportunityId

              return (
                <div key={opportunityId} className="border border-gray-200 rounded-lg overflow-hidden">
                  <div 
                    className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                    onClick={() => toggleExpanded(opportunityId)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className={`px-3 py-1 text-sm font-medium rounded ${getPositionColor(opportunity.position)}`}>
                            {opportunity.position}
                          </span>
                          <span className="text-sm text-gray-600">
                            Weeks {opportunity.weeks.join(', ')}
                          </span>
                          <span className={`px-2 py-1 text-xs font-medium rounded ${getConfidenceColor(opportunity.confidence)}`}>
                            {(opportunity.confidence * 100).toFixed(0)}% confidence
                          </span>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Total Value:</span>
                            <div className="font-semibold text-green-600">{opportunity.total_value.toFixed(1)} pts</div>
                          </div>
                          <div>
                            <span className="text-gray-500">Strategy:</span>
                            <div className="font-semibold">{opportunity.strategy}</div>
                          </div>
                          <div>
                            <span className="text-gray-500">Targets:</span>
                            <div className="font-semibold">{opportunity.targets.length} players</div>
                          </div>
                        </div>
                      </div>

                      <div className="ml-4">
                        {isExpanded ? (
                          <ChevronUpIcon className="w-5 h-5 text-gray-400" />
                        ) : (
                          <ChevronDownIcon className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Expanded Details */}
                  {isExpanded && (
                    <div className="border-t border-gray-200 p-4 bg-gray-50">
                      <div className="space-y-4">
                        {/* Strategy Description */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Strategy</h4>
                          <p className="text-sm text-gray-700">{opportunity.strategy}</p>
                        </div>

                        {/* Week-by-Week Breakdown */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Week-by-Week Plan</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {opportunity.weeks.map((week) => (
                              <div key={week} className="p-3 bg-white rounded border">
                                <div className="font-medium text-gray-900 mb-2">Week {week}</div>
                                <div className="space-y-1">
                                  {opportunity.targets
                                    .filter(target => target.streaming_weeks?.includes(week))
                                    .map((target) => (
                                      <div key={target.player_id} className="text-sm">
                                        <div className="font-medium">{target.player_name}</div>
                                        <div className="text-gray-600">
                                          ${target.recommended_bid} • {target.points_over_replacement.toFixed(1)} POR
                                        </div>
                                      </div>
                                    ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* All Targets */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">All Streaming Targets</h4>
                          <div className="space-y-2">
                            {opportunity.targets.map((target) => (
                              <div key={target.player_id} className="flex items-center justify-between p-3 bg-white rounded border">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <span className="font-medium">{target.player_name}</span>
                                    <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded">
                                      {target.position}
                                    </span>
                                    {target.streaming_weeks && (
                                      <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                        Weeks {target.streaming_weeks.join(', ')}
                                      </span>
                                    )}
                                  </div>
                                  <div className="text-sm text-gray-600">{target.rationale}</div>
                                </div>
                                <div className="text-right ml-4">
                                  <div className="font-semibold text-green-600">${target.recommended_bid}</div>
                                  <div className="text-sm text-gray-600">{target.points_over_replacement.toFixed(1)} POR</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Value Analysis */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-3 bg-green-50 rounded">
                            <div className="text-sm font-medium text-green-800">Expected Value</div>
                            <div className="text-lg font-bold text-green-600">
                              {opportunity.total_value.toFixed(1)} points
                            </div>
                            <div className="text-xs text-green-600">
                              Over {opportunity.weeks.length} weeks
                            </div>
                          </div>
                          <div className="p-3 bg-blue-50 rounded">
                            <div className="text-sm font-medium text-blue-800">Confidence Level</div>
                            <div className="text-lg font-bold text-blue-600">
                              {(opportunity.confidence * 100).toFixed(0)}%
                            </div>
                            <div className="text-xs text-blue-600">
                              Success probability
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}