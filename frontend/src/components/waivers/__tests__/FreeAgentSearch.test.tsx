import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import FreeAgentSearch from '../FreeAgentSearch'
import { WaiverAPI } from '@/lib/waiver-api'

// Mock the WaiverAPI
jest.mock('@/lib/waiver-api')
const mockWaiverAPI = WaiverAPI as jest.Mocked<typeof WaiverAPI>

const mockFreeAgents = [
  {
    player_id: '1',
    player_name: 'Test Player 1',
    position: 'RB',
    team: 'LAR',
    projected_points: 15.5,
    points_over_replacement: 6.2,
    ownership_percentage: 45.5,
    recent_performance: [12.1, 8.5, 16.3],
    upcoming_matchups: ['vs NYG', 'vs DAL'],
    bye_week: 9,
    injury_status: 'healthy',
    target_type: 'high_priority',
    metadata: {}
  },
  {
    player_id: '2',
    player_name: 'Test Player 2',
    position: 'WR',
    team: 'KC',
    projected_points: 10.2,
    points_over_replacement: 3.1,
    recent_performance: [6.5, 14.2],
    upcoming_matchups: ['@ LV'],
    injury_status: 'questionable',
    target_type: 'streaming',
    metadata: {}
  }
]

const defaultProps = {
  freeAgents: mockFreeAgents,
  leagueId: 'league_1',
  onSearch: jest.fn(),
  loading: false
}

describe('FreeAgentSearch', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockWaiverAPI.getFreeAgents.mockResolvedValue(mockFreeAgents)
  })

  it('renders free agent search interface', () => {
    render(<FreeAgentSearch {...defaultProps} />)
    
    expect(screen.getByText('Free Agent Search')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Search players by name or team...')).toBeInTheDocument()
    expect(screen.getByText('Filters')).toBeInTheDocument()
  })

  it('displays free agents correctly', () => {
    render(<FreeAgentSearch {...defaultProps} />)
    
    expect(screen.getByText('Test Player 1')).toBeInTheDocument()
    expect(screen.getByText('Test Player 2')).toBeInTheDocument()
    
    // Check player details
    expect(screen.getByText('RB')).toBeInTheDocument()
    expect(screen.getByText('LAR')).toBeInTheDocument()
    expect(screen.getByText('15.5')).toBeInTheDocument() // Projected points
    expect(screen.getByText('6.2')).toBeInTheDocument() // Points over replacement
  })

  it('filters players by search term', () => {
    render(<FreeAgentSearch {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText('Search players by name or team...')
    fireEvent.change(searchInput, { target: { value: 'Test Player 1' } })
    
    expect(screen.getByText('Test Player 1')).toBeInTheDocument()
    expect(screen.queryByText('Test Player 2')).not.toBeInTheDocument()
  })

  it('filters players by team', () => {
    render(<FreeAgentSearch {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText('Search players by name or team...')
    fireEvent.change(searchInput, { target: { value: 'KC' } })
    
    expect(screen.queryByText('Test Player 1')).not.toBeInTheDocument()
    expect(screen.getByText('Test Player 2')).toBeInTheDocument()
  })

  it('shows and hides filters panel', () => {
    render(<FreeAgentSearch {...defaultProps} />)
    
    // Filters should be hidden initially
    expect(screen.queryByText('Position')).not.toBeInTheDocument()
    
    // Click to show filters
    fireEvent.click(screen.getByText('Filters'))
    expect(screen.getByText('Position')).toBeInTheDocument()
    expect(screen.getByText('Min Projected Points')).toBeInTheDocument()
    expect(screen.getByText('Max Results')).toBeInTheDocument()
    
    // Click to hide filters
    fireEvent.click(screen.getByText('Filters'))
    expect(screen.queryByText('Position')).not.toBeInTheDocument()
  })

  it('applies filters and searches', async () => {
    render(<FreeAgentSearch {...defaultProps} />)
    
    // Show filters
    fireEvent.click(screen.getByText('Filters'))
    
    // Set position filter
    const positionSelect = screen.getByDisplayValue('All Positions')
    fireEvent.change(positionSelect, { target: { value: 'RB' } })
    
    // Set min projected points
    const minPointsInput = screen.getByDisplayValue('5')
    fireEvent.change(minPointsInput, { target: { value: '10' } })
    
    // Apply filters
    fireEvent.click(screen.getByText('Apply Filters'))
    
    await waitFor(() => {
      expect(mockWaiverAPI.getFreeAgents).toHaveBeenCalledWith('league_1', {
        position: 'RB',
        minProjectedPoints: 10,
        maxResults: 50
      })
    })
  })

  it('shows loading state', () => {
    render(<FreeAgentSearch {...defaultProps} loading={true} />)
    
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })

  it('shows empty state when no agents found', () => {
    render(<FreeAgentSearch {...defaultProps} freeAgents={[]} />)
    
    expect(screen.getByText('No free agents found matching your criteria')).toBeInTheDocument()
  })

  it('displays injury status with correct colors', () => {
    render(<FreeAgentSearch {...defaultProps} />)
    
    const healthyStatus = screen.getByText('healthy')
    const questionableStatus = screen.getByText('questionable')
    
    expect(healthyStatus).toHaveClass('text-green-600', 'bg-green-100')
    expect(questionableStatus).toHaveClass('text-yellow-600', 'bg-yellow-100')
  })

  it('displays target type with correct colors', () => {
    render(<FreeAgentSearch {...defaultProps} />)
    
    const highPriorityType = screen.getByText('high priority')
    const streamingType = screen.getByText('streaming')
    
    expect(highPriorityType).toHaveClass('text-orange-600', 'bg-orange-100')
    expect(streamingType).toHaveClass('text-blue-600', 'bg-blue-100')
  })

  it('shows recent performance when available', () => {
    render(<FreeAgentSearch {...defaultProps} />)
    
    expect(screen.getByText('Recent Performance:')).toBeInTheDocument()
    expect(screen.getByText('12.1')).toBeInTheDocument()
    expect(screen.getByText('8.5')).toBeInTheDocument()
    expect(screen.getByText('16.3')).toBeInTheDocument()
  })

  it('shows upcoming matchups when available', () => {
    render(<FreeAgentSearch {...defaultProps} />)
    
    expect(screen.getByText('Upcoming Matchups:')).toBeInTheDocument()
    expect(screen.getByText('vs NYG')).toBeInTheDocument()
    expect(screen.getByText('vs DAL')).toBeInTheDocument()
  })

  it('handles search errors gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
    mockWaiverAPI.getFreeAgents.mockRejectedValue(new Error('Search failed'))
    
    render(<FreeAgentSearch {...defaultProps} />)
    
    // Show filters and apply
    fireEvent.click(screen.getByText('Filters'))
    fireEvent.click(screen.getByText('Apply Filters'))
    
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Search failed:', expect.any(Error))
    })
    
    consoleSpy.mockRestore()
  })
})