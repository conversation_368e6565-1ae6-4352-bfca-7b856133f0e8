import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import WaiverTargets from '../WaiverTargets'

const mockWaiverTargets = [
  {
    player_id: '1',
    player_name: 'High Priority Player',
    position: 'RB',
    target_type: 'high_priority',
    priority: 'high',
    recommended_bid: 35,
    max_bid: 50,
    points_over_replacement: 8.5,
    weekly_upside: 12.3,
    rationale: 'Strong matchup and opportunity for touches',
    drop_candidates: [
      {
        player_id: 'drop1',
        player_name: 'Drop Candidate 1',
        position: 'RB',
        projected_points: 4.2,
        drop_priority: 1,
        reason: 'Low usage and poor matchup'
      }
    ],
    streaming_weeks: [8, 9],
    confidence: 0.85,
    metadata: {}
  },
  {
    player_id: '2',
    player_name: 'Must Add Player',
    position: 'WR',
    target_type: 'must_add',
    priority: 'must_add',
    recommended_bid: 75,
    max_bid: 100,
    points_over_replacement: 15.2,
    weekly_upside: 20.1,
    rationale: 'Starter injured, immediate opportunity',
    drop_candidates: [],
    confidence: 0.95,
    metadata: {}
  }
]

const defaultProps = {
  targets: mockWaiverTargets,
  onRefresh: jest.fn(),
  loading: false
}

describe('WaiverTargets', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders waiver targets interface', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    expect(screen.getByText('Waiver Targets')).toBeInTheDocument()
    expect(screen.getByText('Refresh')).toBeInTheDocument()
    expect(screen.getByText('Sort by:')).toBeInTheDocument()
  })

  it('displays waiver targets correctly', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    expect(screen.getByText('High Priority Player')).toBeInTheDocument()
    expect(screen.getByText('Must Add Player')).toBeInTheDocument()
    
    // Check target details
    expect(screen.getByText('RB')).toBeInTheDocument()
    expect(screen.getByText('WR')).toBeInTheDocument()
    expect(screen.getByText('$35')).toBeInTheDocument() // Recommended bid
    expect(screen.getByText('$75')).toBeInTheDocument() // Another recommended bid
  })

  it('sorts targets by priority by default', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    const targetElements = screen.getAllByText(/Player/)
    expect(targetElements[0]).toHaveTextContent('Must Add Player') // must_add comes first
    expect(targetElements[1]).toHaveTextContent('High Priority Player') // high comes second
  })

  it('sorts targets by recommended bid', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    const sortSelect = screen.getByDisplayValue('Priority')
    fireEvent.change(sortSelect, { target: { value: 'bid' } })
    
    const targetElements = screen.getAllByText(/Player/)
    expect(targetElements[0]).toHaveTextContent('Must Add Player') // $75 bid comes first
    expect(targetElements[1]).toHaveTextContent('High Priority Player') // $35 bid comes second
  })

  it('sorts targets by points over replacement', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    const sortSelect = screen.getByDisplayValue('Priority')
    fireEvent.change(sortSelect, { target: { value: 'value' } })
    
    const targetElements = screen.getAllByText(/Player/)
    expect(targetElements[0]).toHaveTextContent('Must Add Player') // 15.2 POR comes first
    expect(targetElements[1]).toHaveTextContent('High Priority Player') // 8.5 POR comes second
  })

  it('expands and collapses target details', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    // Details should be hidden initially
    expect(screen.queryByText('Rationale')).not.toBeInTheDocument()
    
    // Click to expand first target
    fireEvent.click(screen.getByText('High Priority Player'))
    
    expect(screen.getByText('Rationale')).toBeInTheDocument()
    expect(screen.getByText('Strong matchup and opportunity for touches')).toBeInTheDocument()
    expect(screen.getByText('Weekly Upside')).toBeInTheDocument()
    expect(screen.getByText('12.3 points')).toBeInTheDocument()
    
    // Click to collapse
    fireEvent.click(screen.getByText('High Priority Player'))
    expect(screen.queryByText('Rationale')).not.toBeInTheDocument()
  })

  it('shows drop candidates when expanded', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    // Expand first target
    fireEvent.click(screen.getByText('High Priority Player'))
    
    expect(screen.getByText('Drop Candidates')).toBeInTheDocument()
    expect(screen.getByText('Drop Candidate 1')).toBeInTheDocument()
    expect(screen.getByText('Priority 1')).toBeInTheDocument()
    expect(screen.getByText('4.2 pts')).toBeInTheDocument()
  })

  it('shows streaming weeks when available', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    // Expand first target
    fireEvent.click(screen.getByText('High Priority Player'))
    
    expect(screen.getByText('Streaming Weeks')).toBeInTheDocument()
    expect(screen.getByText('Week 8')).toBeInTheDocument()
    expect(screen.getByText('Week 9')).toBeInTheDocument()
  })

  it('displays priority with correct colors', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    const mustAddPriority = screen.getByText('must add')
    const highPriority = screen.getByText('high')
    
    expect(mustAddPriority).toHaveClass('text-red-600', 'bg-red-100')
    expect(highPriority).toHaveClass('text-orange-600', 'bg-orange-100')
  })

  it('displays target type with correct colors', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    const mustAddType = screen.getByText('must add')
    const highPriorityType = screen.getByText('high priority')
    
    expect(mustAddType).toHaveClass('text-red-600', 'bg-red-100')
    expect(highPriorityType).toHaveClass('text-orange-600', 'bg-orange-100')
  })

  it('shows confidence percentage', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    expect(screen.getByText('85%')).toBeInTheDocument() // 0.85 * 100
    expect(screen.getByText('95%')).toBeInTheDocument() // 0.95 * 100
  })

  it('calls onRefresh when refresh button is clicked', () => {
    render(<WaiverTargets {...defaultProps} />)
    
    fireEvent.click(screen.getByText('Refresh'))
    expect(defaultProps.onRefresh).toHaveBeenCalledTimes(1)
  })

  it('shows loading state', () => {
    render(<WaiverTargets {...defaultProps} loading={true} />)
    
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })

  it('shows empty state when no targets found', () => {
    render(<WaiverTargets {...defaultProps} targets={[]} />)
    
    expect(screen.getByText('No waiver targets found')).toBeInTheDocument()
  })

  it('disables refresh button when loading', () => {
    render(<WaiverTargets {...defaultProps} loading={true} />)
    
    const refreshButton = screen.getByText('Refresh')
    expect(refreshButton).toBeDisabled()
  })

  it('shows spinning icon when loading', () => {
    render(<WaiverTargets {...defaultProps} loading={true} />)
    
    const refreshButton = screen.getByText('Refresh')
    const icon = refreshButton.querySelector('svg')
    expect(icon).toHaveClass('animate-spin')
  })
})