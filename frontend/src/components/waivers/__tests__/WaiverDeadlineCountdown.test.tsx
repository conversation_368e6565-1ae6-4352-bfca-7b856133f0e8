import { render, screen, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import WaiverDeadlineCountdown from '../WaiverDeadlineCountdown'

// Mock Date.now to control time
const mockNow = jest.spyOn(Date, 'now')

describe('WaiverDeadlineCountdown', () => {
  beforeEach(() => {
    jest.clearAllTimers()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.runOnlyPendingTimers()
    jest.useRealTimers()
    mockNow.mockRestore()
  })

  it('renders countdown with days, hours, minutes, and seconds', () => {
    const futureDate = new Date('2024-11-20T10:00:00')
    const currentDate = new Date('2024-11-18T08:30:15')
    mockNow.mockReturnValue(currentDate.getTime())

    render(<WaiverDeadlineCountdown deadline={futureDate} />)

    expect(screen.getByText('Waiver Deadline')).toBeInTheDocument()
    expect(screen.getByText('2')).toBeInTheDocument() // days
    expect(screen.getByText('01')).toBeInTheDocument() // hours
    expect(screen.getByText('29')).toBeInTheDocument() // minutes
    expect(screen.getByText('45')).toBeInTheDocument() // seconds
  })

  it('shows only hours, minutes, and seconds when less than a day', () => {
    const futureDate = new Date('2024-11-20T10:00:00')
    const currentDate = new Date('2024-11-20T08:30:15')
    mockNow.mockReturnValue(currentDate.getTime())

    render(<WaiverDeadlineCountdown deadline={futureDate} />)

    expect(screen.queryByText('day')).not.toBeInTheDocument()
    expect(screen.getByText('01')).toBeInTheDocument() // hours
    expect(screen.getByText('29')).toBeInTheDocument() // minutes
    expect(screen.getByText('45')).toBeInTheDocument() // seconds
  })

  it('shows only minutes and seconds when less than an hour', () => {
    const futureDate = new Date('2024-11-20T10:00:00')
    const currentDate = new Date('2024-11-20T09:30:15')
    mockNow.mockReturnValue(currentDate.getTime())

    render(<WaiverDeadlineCountdown deadline={futureDate} />)

    expect(screen.queryByText('day')).not.toBeInTheDocument()
    expect(screen.queryByText('hr')).not.toBeInTheDocument()
    expect(screen.getByText('29')).toBeInTheDocument() // minutes
    expect(screen.getByText('45')).toBeInTheDocument() // seconds
  })

  it('updates countdown every second', () => {
    const futureDate = new Date('2024-11-20T10:00:00')
    const currentDate = new Date('2024-11-20T09:59:58')
    mockNow.mockReturnValue(currentDate.getTime())

    render(<WaiverDeadlineCountdown deadline={futureDate} />)

    expect(screen.getByText('02')).toBeInTheDocument() // 2 seconds initially

    // Advance time by 1 second
    act(() => {
      mockNow.mockReturnValue(currentDate.getTime() + 1000)
      jest.advanceTimersByTime(1000)
    })

    expect(screen.getByText('01')).toBeInTheDocument() // 1 second remaining
  })

  it('shows expired state when deadline has passed', () => {
    const pastDate = new Date('2024-11-18T10:00:00')
    const currentDate = new Date('2024-11-20T10:00:00')
    mockNow.mockReturnValue(currentDate.getTime())

    render(<WaiverDeadlineCountdown deadline={pastDate} />)

    expect(screen.getByText('Waiver Deadline Passed')).toBeInTheDocument()
    expect(screen.queryByText('day')).not.toBeInTheDocument()
    expect(screen.queryByText('hr')).not.toBeInTheDocument()
    expect(screen.queryByText('min')).not.toBeInTheDocument()
    expect(screen.queryByText('sec')).not.toBeInTheDocument()
  })

  it('shows correct urgency colors based on time remaining', () => {
    // Test green color (more than 24 hours)
    const futureDate = new Date('2024-11-22T10:00:00')
    const currentDate = new Date('2024-11-20T08:00:00')
    mockNow.mockReturnValue(currentDate.getTime())

    const { rerender } = render(<WaiverDeadlineCountdown deadline={futureDate} />)
    
    let container = screen.getByText('Waiver Deadline').closest('div')
    expect(container).toHaveClass('text-green-600', 'bg-green-100')

    // Test yellow color (12-24 hours)
    const yellowDate = new Date('2024-11-20T20:00:00')
    mockNow.mockReturnValue(currentDate.getTime())
    rerender(<WaiverDeadlineCountdown deadline={yellowDate} />)
    
    container = screen.getByText('Waiver Deadline').closest('div')
    expect(container).toHaveClass('text-yellow-600', 'bg-yellow-100')

    // Test orange color (2-12 hours)
    const orangeDate = new Date('2024-11-20T14:00:00')
    mockNow.mockReturnValue(currentDate.getTime())
    rerender(<WaiverDeadlineCountdown deadline={orangeDate} />)
    
    container = screen.getByText('Waiver Deadline').closest('div')
    expect(container).toHaveClass('text-orange-600', 'bg-orange-100')

    // Test red color (less than 2 hours)
    const redDate = new Date('2024-11-20T09:00:00')
    mockNow.mockReturnValue(currentDate.getTime())
    rerender(<WaiverDeadlineCountdown deadline={redDate} />)
    
    container = screen.getByText('Waiver Deadline').closest('div')
    expect(container).toHaveClass('text-red-600', 'bg-red-100')
  })

  it('shows red color when deadline has passed', () => {
    const pastDate = new Date('2024-11-18T10:00:00')
    const currentDate = new Date('2024-11-20T10:00:00')
    mockNow.mockReturnValue(currentDate.getTime())

    render(<WaiverDeadlineCountdown deadline={pastDate} />)

    const container = screen.getByText('Waiver Deadline Passed').closest('div')
    expect(container).toHaveClass('text-red-600', 'bg-red-100')
  })

  it('formats deadline date correctly', () => {
    const deadline = new Date('2024-11-20T10:30:00')
    const currentDate = new Date('2024-11-19T10:00:00')
    mockNow.mockReturnValue(currentDate.getTime())

    render(<WaiverDeadlineCountdown deadline={deadline} />)

    // Should show formatted date (exact format may vary by locale)
    expect(screen.getByText(/Nov 20/)).toBeInTheDocument()
    expect(screen.getByText(/10:30/)).toBeInTheDocument()
  })

  it('handles singular vs plural time units correctly', () => {
    const futureDate = new Date('2024-11-19T09:01:01')
    const currentDate = new Date('2024-11-18T08:00:00')
    mockNow.mockReturnValue(currentDate.getTime())

    render(<WaiverDeadlineCountdown deadline={futureDate} />)

    expect(screen.getByText('day')).toBeInTheDocument() // singular
    expect(screen.getByText('hr')).toBeInTheDocument() // singular
  })

  it('pads time values with leading zeros', () => {
    const futureDate = new Date('2024-11-20T10:00:00')
    const currentDate = new Date('2024-11-20T09:05:03')
    mockNow.mockReturnValue(currentDate.getTime())

    render(<WaiverDeadlineCountdown deadline={futureDate} />)

    expect(screen.getByText('54')).toBeInTheDocument() // minutes
    expect(screen.getByText('57')).toBeInTheDocument() // seconds
  })

  it('cleans up interval on unmount', () => {
    const futureDate = new Date('2024-11-20T10:00:00')
    const currentDate = new Date('2024-11-19T10:00:00')
    mockNow.mockReturnValue(currentDate.getTime())

    const { unmount } = render(<WaiverDeadlineCountdown deadline={futureDate} />)

    // Verify interval is running
    expect(jest.getTimerCount()).toBe(1)

    unmount()

    // Verify interval is cleared
    expect(jest.getTimerCount()).toBe(0)
  })
})