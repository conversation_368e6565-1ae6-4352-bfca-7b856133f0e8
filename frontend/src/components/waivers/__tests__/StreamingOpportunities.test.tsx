import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import StreamingOpportunities from '../StreamingOpportunities'

const mockStreamingOpportunities = [
  {
    position: 'QB',
    weeks: [8, 9, 10],
    targets: [
      {
        player_id: '1',
        player_name: 'Streaming QB 1',
        position: 'QB',
        target_type: 'streaming',
        priority: 'medium',
        recommended_bid: 8,
        max_bid: 15,
        points_over_replacement: 4.2,
        weekly_upside: 6.5,
        rationale: 'Great matchup vs weak defense',
        drop_candidates: [],
        streaming_weeks: [8, 9],
        confidence: 0.75,
        metadata: {}
      },
      {
        player_id: '2',
        player_name: 'Streaming QB 2',
        position: 'QB',
        target_type: 'streaming',
        priority: 'low',
        recommended_bid: 5,
        max_bid: 10,
        points_over_replacement: 2.8,
        weekly_upside: 4.1,
        rationale: 'Decent matchup for week 10',
        drop_candidates: [],
        streaming_weeks: [10],
        confidence: 0.60,
        metadata: {}
      }
    ],
    total_value: 15.6,
    strategy: 'Stream QBs based on favorable matchups against weak pass defenses',
    confidence: 0.75
  },
  {
    position: 'DST',
    weeks: [8, 9],
    targets: [
      {
        player_id: '3',
        player_name: 'Streaming DST 1',
        position: 'DST',
        target_type: 'streaming',
        priority: 'medium',
        recommended_bid: 3,
        max_bid: 8,
        points_over_replacement: 3.5,
        weekly_upside: 8.2,
        rationale: 'Facing turnover-prone offense',
        drop_candidates: [],
        streaming_weeks: [8, 9],
        confidence: 0.80,
        metadata: {}
      }
    ],
    total_value: 8.9,
    strategy: 'Target defenses facing high-turnover offenses',
    confidence: 0.80
  }
]

const defaultProps = {
  opportunities: mockStreamingOpportunities,
  onRefresh: jest.fn(),
  loading: false
}

describe('StreamingOpportunities', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders streaming opportunities interface', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    expect(screen.getByText('Streaming Opportunities')).toBeInTheDocument()
    expect(screen.getByText('Multi-week streaming strategies for maximum value')).toBeInTheDocument()
    expect(screen.getByText('Refresh')).toBeInTheDocument()
  })

  it('displays streaming opportunities correctly', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    expect(screen.getByText('QB')).toBeInTheDocument()
    expect(screen.getByText('DST')).toBeInTheDocument()
    expect(screen.getByText('Weeks 8, 9, 10')).toBeInTheDocument()
    expect(screen.getByText('Weeks 8, 9')).toBeInTheDocument()
  })

  it('shows opportunity summary information', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    // QB opportunity
    expect(screen.getByText('15.6 pts')).toBeInTheDocument() // Total value
    expect(screen.getByText('75% confidence')).toBeInTheDocument()
    expect(screen.getByText('2 players')).toBeInTheDocument() // Number of targets
    
    // DST opportunity
    expect(screen.getByText('8.9 pts')).toBeInTheDocument() // Total value
    expect(screen.getByText('80% confidence')).toBeInTheDocument()
    expect(screen.getByText('1 players')).toBeInTheDocument() // Number of targets
  })

  it('expands and collapses opportunity details', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    // Details should be hidden initially
    expect(screen.queryByText('Strategy')).not.toBeInTheDocument()
    
    // Click to expand QB opportunity
    fireEvent.click(screen.getByText('QB'))
    
    expect(screen.getByText('Strategy')).toBeInTheDocument()
    expect(screen.getByText('Stream QBs based on favorable matchups against weak pass defenses')).toBeInTheDocument()
    expect(screen.getByText('Week-by-Week Plan')).toBeInTheDocument()
    expect(screen.getByText('All Streaming Targets')).toBeInTheDocument()
    
    // Click to collapse
    fireEvent.click(screen.getByText('QB'))
    expect(screen.queryByText('Strategy')).not.toBeInTheDocument()
  })

  it('shows week-by-week breakdown when expanded', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    // Expand QB opportunity
    fireEvent.click(screen.getByText('QB'))
    
    expect(screen.getByText('Week 8')).toBeInTheDocument()
    expect(screen.getByText('Week 9')).toBeInTheDocument()
    expect(screen.getByText('Week 10')).toBeInTheDocument()
    
    // Should show players for each week
    expect(screen.getByText('Streaming QB 1')).toBeInTheDocument()
    expect(screen.getByText('Streaming QB 2')).toBeInTheDocument()
  })

  it('shows all streaming targets when expanded', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    // Expand QB opportunity
    fireEvent.click(screen.getByText('QB'))
    
    expect(screen.getByText('All Streaming Targets')).toBeInTheDocument()
    expect(screen.getByText('Streaming QB 1')).toBeInTheDocument()
    expect(screen.getByText('Streaming QB 2')).toBeInTheDocument()
    
    // Check target details
    expect(screen.getByText('$8')).toBeInTheDocument() // Recommended bid
    expect(screen.getByText('$5')).toBeInTheDocument() // Another recommended bid
    expect(screen.getByText('4.2 POR')).toBeInTheDocument() // Points over replacement
    expect(screen.getByText('2.8 POR')).toBeInTheDocument() // Another POR
  })

  it('shows streaming weeks for each target', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    // Expand QB opportunity
    fireEvent.click(screen.getByText('QB'))
    
    expect(screen.getByText('Weeks 8, 9')).toBeInTheDocument() // QB 1 streaming weeks
    expect(screen.getByText('Weeks 10')).toBeInTheDocument() // QB 2 streaming weeks
  })

  it('shows value analysis when expanded', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    // Expand QB opportunity
    fireEvent.click(screen.getByText('QB'))
    
    expect(screen.getByText('Expected Value')).toBeInTheDocument()
    expect(screen.getByText('15.6 points')).toBeInTheDocument()
    expect(screen.getByText('Over 3 weeks')).toBeInTheDocument()
    
    expect(screen.getByText('Confidence Level')).toBeInTheDocument()
    expect(screen.getByText('75%')).toBeInTheDocument()
    expect(screen.getByText('Success probability')).toBeInTheDocument()
  })

  it('displays position colors correctly', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    const qbPosition = screen.getByText('QB')
    const dstPosition = screen.getByText('DST')
    
    expect(qbPosition).toHaveClass('text-red-600', 'bg-red-100')
    expect(dstPosition).toHaveClass('text-gray-600', 'bg-gray-100')
  })

  it('displays confidence colors correctly', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    const highConfidence = screen.getByText('80% confidence')
    const mediumConfidence = screen.getByText('75% confidence')
    
    expect(highConfidence).toHaveClass('text-green-600', 'bg-green-100')
    expect(mediumConfidence).toHaveClass('text-yellow-600', 'bg-yellow-100')
  })

  it('calls onRefresh when refresh button is clicked', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    fireEvent.click(screen.getByText('Refresh'))
    expect(defaultProps.onRefresh).toHaveBeenCalledTimes(1)
  })

  it('shows loading state', () => {
    render(<StreamingOpportunities {...defaultProps} loading={true} />)
    
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })

  it('shows empty state when no opportunities found', () => {
    render(<StreamingOpportunities {...defaultProps} opportunities={[]} />)
    
    expect(screen.getByText('No streaming opportunities found')).toBeInTheDocument()
  })

  it('disables refresh button when loading', () => {
    render(<StreamingOpportunities {...defaultProps} loading={true} />)
    
    const refreshButton = screen.getByText('Refresh')
    expect(refreshButton).toBeDisabled()
  })

  it('shows spinning icon when loading', () => {
    render(<StreamingOpportunities {...defaultProps} loading={true} />)
    
    const refreshButton = screen.getByText('Refresh')
    const icon = refreshButton.querySelector('svg')
    expect(icon).toHaveClass('animate-spin')
  })

  it('shows target rationale in expanded view', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    // Expand QB opportunity
    fireEvent.click(screen.getByText('QB'))
    
    expect(screen.getByText('Great matchup vs weak defense')).toBeInTheDocument()
    expect(screen.getByText('Decent matchup for week 10')).toBeInTheDocument()
  })

  it('handles multiple opportunities correctly', () => {
    render(<StreamingOpportunities {...defaultProps} />)
    
    // Should show both QB and DST opportunities
    expect(screen.getAllByText(/QB|DST/)).toHaveLength(2)
    
    // Expand first opportunity
    fireEvent.click(screen.getByText('QB'))
    expect(screen.getByText('Stream QBs based on favorable matchups against weak pass defenses')).toBeInTheDocument()
    
    // Expand second opportunity
    fireEvent.click(screen.getByText('DST'))
    expect(screen.getByText('Target defenses facing high-turnover offenses')).toBeInTheDocument()
  })
})