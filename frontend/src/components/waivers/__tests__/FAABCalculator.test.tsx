import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import FAABCalculator from '../FAABCalculator'
import { WaiverAPI } from '@/lib/waiver-api'

// Mock the WaiverAPI
jest.mock('@/lib/waiver-api')
const mockWaiverAPI = WaiverAPI as jest.Mocked<typeof WaiverAPI>

const mockWaiverTargets = [
  {
    player_id: '1',
    player_name: 'Target Player 1',
    position: 'RB',
    target_type: 'high_priority',
    priority: 'high',
    recommended_bid: 25,
    max_bid: 40,
    points_over_replacement: 8.5,
    weekly_upside: 12.3,
    rationale: 'Strong matchup',
    drop_candidates: [],
    confidence: 0.85,
    metadata: {}
  },
  {
    player_id: '2',
    player_name: 'Target Player 2',
    position: 'WR',
    target_type: 'streaming',
    priority: 'medium',
    recommended_bid: 15,
    max_bid: 25,
    points_over_replacement: 5.2,
    weekly_upside: 8.1,
    rationale: 'Good streaming option',
    drop_candidates: [],
    confidence: 0.70,
    metadata: {}
  }
]

const mockOptimizedStrategy = {
  strategy_name: 'Balanced Approach',
  targets: [
    {
      ...mockWaiverTargets[0],
      recommended_bid: 30 // Optimized bid
    },
    {
      ...mockWaiverTargets[1],
      recommended_bid: 12 // Optimized bid
    }
  ],
  total_faab_allocation: 42,
  remaining_budget: 58,
  expected_value: 13.7,
  risk_level: 'medium',
  streaming_opportunities: [
    {
      position: 'QB',
      weeks: [8, 9],
      targets: [],
      total_value: 8.5,
      strategy: 'Stream based on matchups',
      confidence: 0.75
    }
  ],
  trade_offs: ['May miss out on higher upside plays'],
  metadata: {}
}

const defaultProps = {
  franchiseId: 'franchise_1',
  waiverTargets: mockWaiverTargets
}

describe('FAABCalculator', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockWaiverAPI.optimizeFAAB.mockResolvedValue(mockOptimizedStrategy)
  })

  it('renders FAAB calculator interface', () => {
    render(<FAABCalculator {...defaultProps} />)
    
    expect(screen.getByText('FAAB Budget')).toBeInTheDocument()
    expect(screen.getByText('Select Targets')).toBeInTheDocument()
    expect(screen.getByText('Total Budget')).toBeInTheDocument()
    expect(screen.getByText('Optimize Bids')).toBeInTheDocument()
  })

  it('shows budget calculations correctly', () => {
    render(<FAABCalculator {...defaultProps} />)
    
    expect(screen.getByDisplayValue('100')).toBeInTheDocument() // Default budget
    expect(screen.getByText('$0')).toBeInTheDocument() // Initial total allocated
    expect(screen.getByText('$100')).toBeInTheDocument() // Initial remaining budget
  })

  it('displays available targets', () => {
    render(<FAABCalculator {...defaultProps} />)
    
    expect(screen.getByText('Available Targets')).toBeInTheDocument()
    expect(screen.getByText('Target Player 1')).toBeInTheDocument()
    expect(screen.getByText('Target Player 2')).toBeInTheDocument()
    expect(screen.getByText('RB • $25')).toBeInTheDocument()
    expect(screen.getByText('WR • $15')).toBeInTheDocument()
  })

  it('adds targets to selection', () => {
    render(<FAABCalculator {...defaultProps} />)
    
    // Add first target
    fireEvent.click(screen.getByText('Target Player 1'))
    
    expect(screen.getByText('Selected Targets')).toBeInTheDocument()
    expect(screen.getByText('$25')).toBeInTheDocument() // Total allocated
    expect(screen.getByText('$75')).toBeInTheDocument() // Remaining budget
  })

  it('removes targets from selection', () => {
    render(<FAABCalculator {...defaultProps} />)
    
    // Add target
    fireEvent.click(screen.getByText('Target Player 1'))
    
    // Remove target
    const removeButton = screen.getByRole('button', { name: '' }) // TrashIcon button
    fireEvent.click(removeButton)
    
    expect(screen.queryByText('Selected Targets')).not.toBeInTheDocument()
    expect(screen.getByText('$0')).toBeInTheDocument() // Back to 0 allocated
  })

  it('updates custom bid amounts', () => {
    render(<FAABCalculator {...defaultProps} />)
    
    // Add target
    fireEvent.click(screen.getByText('Target Player 1'))
    
    // Update bid
    const bidInput = screen.getByDisplayValue('25')
    fireEvent.change(bidInput, { target: { value: '30' } })
    
    expect(screen.getByText('$30')).toBeInTheDocument() // Updated total allocated
    expect(screen.getByText('$70')).toBeInTheDocument() // Updated remaining budget
  })

  it('shows budget warning when over budget', () => {
    render(<FAABCalculator {...defaultProps} />)
    
    // Add target and set high bid
    fireEvent.click(screen.getByText('Target Player 1'))
    const bidInput = screen.getByDisplayValue('25')
    fireEvent.change(bidInput, { target: { value: '150' } })
    
    const remainingBudget = screen.getByText('-$50')
    expect(remainingBudget).toHaveClass('text-red-600')
  })

  it('optimizes FAAB allocation', async () => {
    render(<FAABCalculator {...defaultProps} />)
    
    // Add targets
    fireEvent.click(screen.getByText('Target Player 1'))
    fireEvent.click(screen.getByText('Target Player 2'))
    
    // Optimize
    fireEvent.click(screen.getByText('Optimize Bids'))
    
    await waitFor(() => {
      expect(mockWaiverAPI.optimizeFAAB).toHaveBeenCalledWith({
        franchise_id: 'franchise_1',
        target_player_ids: ['1', '2'],
        budget_constraint: 100
      })
    })
    
    await waitFor(() => {
      expect(screen.getByText('Optimization Results')).toBeInTheDocument()
    })
  })

  it('shows optimization results', async () => {
    render(<FAABCalculator {...defaultProps} />)
    
    // Add targets and optimize
    fireEvent.click(screen.getByText('Target Player 1'))
    fireEvent.click(screen.getByText('Target Player 2'))
    fireEvent.click(screen.getByText('Optimize Bids'))
    
    await waitFor(() => {
      expect(screen.getByText('$42')).toBeInTheDocument() // Total allocation
      expect(screen.getByText('$58')).toBeInTheDocument() // Remaining budget
      expect(screen.getByText('13.7')).toBeInTheDocument() // Expected value
      expect(screen.getByText('medium Risk')).toBeInTheDocument() // Risk level
    })
  })

  it('shows optimized bid recommendations', async () => {
    render(<FAABCalculator {...defaultProps} />)
    
    // Add targets and optimize
    fireEvent.click(screen.getByText('Target Player 1'))
    fireEvent.click(screen.getByText('Target Player 2'))
    fireEvent.click(screen.getByText('Optimize Bids'))
    
    await waitFor(() => {
      expect(screen.getByText('→ $30')).toBeInTheDocument() // Optimized bid for player 1
      expect(screen.getByText('→ $12')).toBeInTheDocument() // Optimized bid for player 2
    })
  })

  it('shows trade-offs in optimization results', async () => {
    render(<FAABCalculator {...defaultProps} />)
    
    // Add targets and optimize
    fireEvent.click(screen.getByText('Target Player 1'))
    fireEvent.click(screen.getByText('Target Player 2'))
    fireEvent.click(screen.getByText('Optimize Bids'))
    
    await waitFor(() => {
      expect(screen.getByText('Trade-offs')).toBeInTheDocument()
      expect(screen.getByText('May miss out on higher upside plays')).toBeInTheDocument()
    })
  })

  it('shows streaming opportunities in results', async () => {
    render(<FAABCalculator {...defaultProps} />)
    
    // Add targets and optimize
    fireEvent.click(screen.getByText('Target Player 1'))
    fireEvent.click(screen.getByText('Target Player 2'))
    fireEvent.click(screen.getByText('Optimize Bids'))
    
    await waitFor(() => {
      expect(screen.getByText('Streaming Opportunities')).toBeInTheDocument()
      expect(screen.getByText('QB')).toBeInTheDocument()
      expect(screen.getByText('Weeks 8, 9')).toBeInTheDocument()
      expect(screen.getByText('8.5 value')).toBeInTheDocument()
    })
  })

  it('disables optimize button when no targets selected', () => {
    render(<FAABCalculator {...defaultProps} />)
    
    const optimizeButton = screen.getByText('Optimize Bids')
    expect(optimizeButton).toBeDisabled()
  })

  it('shows error when optimization fails', async () => {
    const errorMessage = 'Optimization failed'
    mockWaiverAPI.optimizeFAAB.mockRejectedValue(new Error(errorMessage))
    
    render(<FAABCalculator {...defaultProps} />)
    
    // Add target and optimize
    fireEvent.click(screen.getByText('Target Player 1'))
    fireEvent.click(screen.getByText('Optimize Bids'))
    
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })
  })

  it('shows loading state during optimization', async () => {
    render(<FAABCalculator {...defaultProps} />)
    
    // Add target and optimize
    fireEvent.click(screen.getByText('Target Player 1'))
    fireEvent.click(screen.getByText('Optimize Bids'))
    
    expect(screen.getByText('Optimizing...')).toBeInTheDocument()
  })

  it('prevents duplicate target selection', () => {
    render(<FAABCalculator {...defaultProps} />)
    
    // Add target
    fireEvent.click(screen.getByText('Target Player 1'))
    
    // Try to add same target again - button should be disabled
    const targetButton = screen.getByText('Target Player 1').closest('button')
    expect(targetButton).toBeDisabled()
  })

  it('updates budget constraint', () => {
    render(<FAABCalculator {...defaultProps} />)
    
    const budgetInput = screen.getByDisplayValue('100')
    fireEvent.change(budgetInput, { target: { value: '150' } })
    
    expect(screen.getByText('$150')).toBeInTheDocument() // Updated remaining budget
  })
})