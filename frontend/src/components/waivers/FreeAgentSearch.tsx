'use client'

import { useState, useEffect } from 'react'
import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline'
import { FreeAgent, WaiverFilters } from '@/types/waiver'
import { WaiverAPI } from '@/lib/waiver-api'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface FreeAgentSearchProps {
  freeAgents: FreeAgent[]
  leagueId: string
  onSearch: () => void
  loading: boolean
}

export default function FreeAgentSearch({ 
  freeAgents: initialFreeAgents, 
  leagueId, 
  onSearch,
  loading 
}: FreeAgentSearchProps) {
  const [freeAgents, setFreeAgents] = useState<FreeAgent[]>(initialFreeAgents)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState<WaiverFilters>({
    position: '',
    minProjectedPoints: 5,
    maxResults: 50
  })
  const [showFilters, setShowFilters] = useState(false)
  const [searching, setSearching] = useState(false)

  useEffect(() => {
    setFreeAgents(initialFreeAgents)
  }, [initialFreeAgents])

  const handleSearch = async () => {
    try {
      setSearching(true)
      const results = await WaiverAPI.getFreeAgents(leagueId, filters)
      setFreeAgents(results)
    } catch (error) {
      console.error('Search failed:', error)
    } finally {
      setSearching(false)
    }
  }

  const filteredAgents = freeAgents.filter(agent =>
    agent.player_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.team.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getInjuryStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy': return 'text-green-600 bg-green-100'
      case 'questionable': return 'text-yellow-600 bg-yellow-100'
      case 'doubtful': return 'text-orange-600 bg-orange-100'
      case 'out': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTargetTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'must_add': return 'text-red-600 bg-red-100'
      case 'high_priority': return 'text-orange-600 bg-orange-100'
      case 'streaming': return 'text-blue-600 bg-blue-100'
      case 'handcuff': return 'text-purple-600 bg-purple-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Search Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Free Agent Search</h2>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            <FunnelIcon className="w-4 h-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search players by name or team..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-md">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Position
                </label>
                <select
                  value={filters.position || ''}
                  onChange={(e) => setFilters({ ...filters, position: e.target.value || undefined })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Positions</option>
                  <option value="QB">QB</option>
                  <option value="RB">RB</option>
                  <option value="WR">WR</option>
                  <option value="TE">TE</option>
                  <option value="K">K</option>
                  <option value="DST">DST</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Min Projected Points
                </label>
                <input
                  type="number"
                  value={filters.minProjectedPoints || 0}
                  onChange={(e) => setFilters({ ...filters, minProjectedPoints: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  min="0"
                  step="0.1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Results
                </label>
                <select
                  value={filters.maxResults || 50}
                  onChange={(e) => setFilters({ ...filters, maxResults: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                  <option value={200}>200</option>
                </select>
              </div>
            </div>

            <div className="mt-4 flex justify-end">
              <button
                onClick={handleSearch}
                disabled={searching}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {searching ? 'Searching...' : 'Apply Filters'}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Results */}
      <div className="p-6">
        {loading || searching ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : filteredAgents.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No free agents found matching your criteria
          </div>
        ) : (
          <div className="space-y-4">
            {filteredAgents.map((agent) => (
              <div key={agent.player_id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {agent.player_name}
                      </h3>
                      <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">
                        {agent.position}
                      </span>
                      <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                        {agent.team}
                      </span>
                      <span className={`px-2 py-1 text-xs font-medium rounded ${getInjuryStatusColor(agent.injury_status)}`}>
                        {agent.injury_status}
                      </span>
                      <span className={`px-2 py-1 text-xs font-medium rounded ${getTargetTypeColor(agent.target_type)}`}>
                        {agent.target_type.replace('_', ' ')}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Projected Points:</span>
                        <div className="font-semibold">{agent.projected_points.toFixed(1)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Points Over Replacement:</span>
                        <div className="font-semibold">{agent.points_over_replacement.toFixed(1)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Ownership:</span>
                        <div className="font-semibold">
                          {agent.ownership_percentage ? `${agent.ownership_percentage.toFixed(1)}%` : 'N/A'}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500">Bye Week:</span>
                        <div className="font-semibold">{agent.bye_week || 'N/A'}</div>
                      </div>
                    </div>

                    {agent.recent_performance.length > 0 && (
                      <div className="mt-3">
                        <span className="text-sm text-gray-500">Recent Performance:</span>
                        <div className="flex space-x-2 mt-1">
                          {agent.recent_performance.slice(-5).map((points, index) => (
                            <span key={index} className="px-2 py-1 text-xs bg-gray-100 rounded">
                              {points.toFixed(1)}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {agent.upcoming_matchups.length > 0 && (
                      <div className="mt-3">
                        <span className="text-sm text-gray-500">Upcoming Matchups:</span>
                        <div className="flex space-x-2 mt-1">
                          {agent.upcoming_matchups.slice(0, 3).map((matchup, index) => (
                            <span key={index} className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                              {matchup}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}