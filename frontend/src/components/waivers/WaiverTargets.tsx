'use client'

import { useState } from 'react'
import { ArrowPathIcon, ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline'
import { WaiverTarget } from '@/types/waiver'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import WhyButton from '@/components/ui/WhyButton'
import ConfidenceIndicator from '@/components/ui/ConfidenceIndicator'
import { ExplanationAPI } from '@/lib/explanation-api'
import Badge from '@/components/ui/Badge'
import { RecommendationExplanation, ExplanationRequest, UncertaintyVisualization } from '@/types/explanation'

interface WaiverTargetsProps {
  targets: WaiverTarget[]
  onRefresh: () => void
  loading: boolean
  recommendationId?: string
}

export default function WaiverTargets({ targets, onRefresh, loading, recommendationId }: WaiverTargetsProps) {
  const [expandedTarget, setExpandedTarget] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState<'priority' | 'bid' | 'value'>('priority')

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'must_add': return 'text-red-600 bg-red-100'
      case 'high': return 'text-orange-600 bg-orange-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTargetTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'must_add': return 'text-red-600 bg-red-100'
      case 'high_priority': return 'text-orange-600 bg-orange-100'
      case 'streaming': return 'text-blue-600 bg-blue-100'
      case 'handcuff': return 'text-purple-600 bg-purple-100'
      case 'depth': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const sortedTargets = [...targets].sort((a, b) => {
    switch (sortBy) {
      case 'priority':
        const priorityOrder = { 'must_add': 0, 'high': 1, 'medium': 2, 'low': 3 }
        return (priorityOrder[a.priority.toLowerCase() as keyof typeof priorityOrder] || 4) - 
               (priorityOrder[b.priority.toLowerCase() as keyof typeof priorityOrder] || 4)
      case 'bid':
        return b.recommended_bid - a.recommended_bid
      case 'value':
        return b.points_over_replacement - a.points_over_replacement
      default:
        return 0
    }
  })

  const toggleExpanded = (playerId: string) => {
    setExpandedTarget(expandedTarget === playerId ? null : playerId)
  }

  const handleExplain = (targetId: string) => async (request: ExplanationRequest): Promise<RecommendationExplanation> => {
    if (!recommendationId) {
      throw new Error('No recommendation ID available')
    }
    return ExplanationAPI.getExplanation(`${recommendationId}_${targetId}`, request)
  }

  const getUncertaintyVisualization = (target: WaiverTarget): UncertaintyVisualization => {
    return {
      confidence_level: target.confidence > 0.8 ? 'high' : target.confidence > 0.6 ? 'medium' : 'low',
      confidence_score: target.confidence,
      uncertainty_range: [
        target.recommended_bid * 0.8,
        target.recommended_bid * 1.2
      ],
      risk_factors: [
        target.priority === 'low' ? 'Lower priority target' : '',
        'Other teams may bid higher',
        'Player role may change'
      ].filter(Boolean),
      confidence_indicators: [
        {
          indicator: 'Target Priority',
          status: target.priority === 'must_add' || target.priority === 'high' ? 'good' : 'warning',
          description: 'Priority level for this waiver target'
        },
        {
          indicator: 'Value Over Replacement',
          status: target.points_over_replacement > 5 ? 'good' : 'warning',
          description: 'Expected value compared to replacement level'
        },
        {
          indicator: 'Bid Confidence',
          status: target.confidence > 0.7 ? 'good' : 'poor',
          description: 'Confidence in recommended bid amount'
        }
      ]
    }
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Waiver Targets</h2>
          <button
            onClick={onRefresh}
            disabled={loading}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ArrowPathIcon className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        {/* Sort Options */}
        <div className="flex space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sort by:
            </label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as typeof sortBy)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="priority">Priority</option>
              <option value="bid">Recommended Bid</option>
              <option value="value">Points Over Replacement</option>
            </select>
          </div>
        </div>
      </div>

      {/* Targets List */}
      <div className="p-6">
        {loading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : targets.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No waiver targets found
          </div>
        ) : (
          <div className="space-y-4">
            {sortedTargets.map((target) => (
              <div key={target.player_id} className="border border-gray-200 rounded-lg overflow-hidden">
                <div 
                  className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => toggleExpanded(target.player_id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {target.player_name}
                        </h3>
                        <Badge type="position" value={target.position} />
                        <span className={`px-2 py-1 text-xs font-medium rounded ${getPriorityColor(target.priority)}`}>
                          {target.priority.replace('_', ' ')}
                        </span>
                        <span className={`px-2 py-1 text-xs font-medium rounded ${getTargetTypeColor(target.target_type)}`}>
                          {target.target_type.replace('_', ' ')}
                        </span>
                        <ConfidenceIndicator
                          confidence={target.confidence}
                          uncertainty={getUncertaintyVisualization(target)}
                          size="sm"
                        />
                        {recommendationId && (
                          <WhyButton
                            recommendationId={`${recommendationId}_${target.player_id}`}
                            onExplain={handleExplain(target.player_id)}
                            variant="icon"
                            size="sm"
                          />
                        )}
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Recommended Bid:</span>
                          <div className="font-semibold text-green-600">${target.recommended_bid}</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Max Bid:</span>
                          <div className="font-semibold">${target.max_bid}</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Points Over Replacement:</span>
                          <div className="font-semibold">{target.points_over_replacement.toFixed(1)}</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Confidence:</span>
                          <div className="font-semibold">{(target.confidence * 100).toFixed(0)}%</div>
                        </div>
                      </div>
                    </div>

                    <div className="ml-4">
                      {expandedTarget === target.player_id ? (
                        <ChevronUpIcon className="w-5 h-5 text-gray-400" />
                      ) : (
                        <ChevronDownIcon className="w-5 h-5 text-gray-400" />
                      )}
                    </div>
                  </div>
                </div>

                {/* Expanded Details */}
                {expandedTarget === target.player_id && (
                  <div className="border-t border-gray-200 p-4 bg-gray-50">
                    <div className="space-y-4">
                      {/* Rationale */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Rationale</h4>
                        <p className="text-sm text-gray-700">{target.rationale}</p>
                      </div>

                      {/* Additional Stats */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Weekly Upside</h4>
                          <p className="text-sm text-gray-700">{target.weekly_upside.toFixed(1)} points</p>
                        </div>

                        {target.streaming_weeks && target.streaming_weeks.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Streaming Weeks</h4>
                            <div className="flex space-x-2">
                              {target.streaming_weeks.map((week) => (
                                <span key={week} className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                  Week {week}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Drop Candidates */}
                      {target.drop_candidates.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Drop Candidates</h4>
                          <div className="space-y-2">
                            {target.drop_candidates.map((candidate) => (
                              <div key={candidate.player_id} className="flex items-center justify-between p-2 bg-white rounded border">
                                <div className="flex items-center space-x-2">
                                  <span className="font-medium">{candidate.player_name}</span>
                                  <Badge type="position" value={candidate.position} />
                                  <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
                                    Priority {candidate.drop_priority}
                                  </span>
                                </div>
                                <div className="text-sm text-gray-600">
                                  {candidate.projected_points.toFixed(1)} pts
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}