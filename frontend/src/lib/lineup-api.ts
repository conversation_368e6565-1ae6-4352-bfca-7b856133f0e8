import { 
  LineupOptimizationRequest, 
  LineupRecommendations, 
  OptimalLineup,
  StartSitRecommendation,
  LineupLockAlert,
  NewsAdjustment,
  LineupHistory
} from '../types/lineup'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export class LineupAPI {
  private static async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Unknown error' }))
      throw new Error(error.detail || `HTTP ${response.status}`)
    }

    return response.json()
  }

  static async getLineupRecommendations(
    franchiseId: string, 
    week: number, 
    season: number = 2025
  ): Promise<LineupRecommendations> {
    return this.request<LineupRecommendations>(
      `/lineup/recommendations/${franchiseId}?week=${week}&season=${season}`
    )
  }

  static async optimizeLineup(request: LineupOptimizationRequest): Promise<OptimalLineup> {
    return this.request<OptimalLineup>('/lineup/optimize', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  static async getStartSitRecommendations(
    franchiseId: string, 
    week: number, 
    season: number = 2025
  ): Promise<StartSitRecommendation[]> {
    return this.request<StartSitRecommendation[]>('/lineup/start-sit', {
      method: 'POST',
      body: JSON.stringify({ franchise_id: franchiseId, week, season }),
    })
  }

  static async getLineupLockAlerts(
    franchiseId: string, 
    week: number
  ): Promise<LineupLockAlert[]> {
    return this.request<LineupLockAlert[]>('/lineup/lock-alerts', {
      method: 'POST',
      body: JSON.stringify({ franchise_id: franchiseId, week }),
    })
  }

  static async applyNewsAdjustments(
    franchiseId: string,
    week: number,
    newsItems: Array<{
      player_id: string
      news_type: string
      severity: string
      description: string
      impact_on_projection?: number
    }>
  ): Promise<NewsAdjustment[]> {
    return this.request<NewsAdjustment[]>('/lineup/news-adjustments', {
      method: 'POST',
      body: JSON.stringify({
        franchise_id: franchiseId,
        week,
        news_items: newsItems,
      }),
    })
  }

  static async saveLineupRecommendation(
    franchiseId: string,
    week: number,
    season: number = 2025
  ): Promise<{ message: string; recommendation_id: string }> {
    return this.request<{ message: string; recommendation_id: string }>(
      `/lineup/save-recommendation?franchise_id=${franchiseId}&week=${week}&season=${season}`,
      { method: 'POST' }
    )
  }

  static async getLineupHistory(
    franchiseId: string,
    season: number = 2025
  ): Promise<LineupHistory[]> {
    // Mock implementation - would need backend endpoint
    return Promise.resolve([
      {
        week: 1,
        season,
        lineup: {
          QB: 'player_1',
          RB1: 'player_2',
          RB2: 'player_3',
          WR1: 'player_4',
          WR2: 'player_5',
          TE: 'player_6',
          FLEX: 'player_7',
          K: 'player_8',
          DEF: 'player_9'
        },
        projected_points: 125.5,
        actual_points: 118.2,
        win_probability: 0.65,
        result: 'win' as const,
        created_at: new Date().toISOString()
      }
    ])
  }
}