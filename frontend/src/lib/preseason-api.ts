// API client for Preseason endpoints
export type KeeperSelection = { franchise_id: string; player_id: string }
export type KeeperPayload = { league_id: string; selections: KeeperSelection[]; updated_at?: string | null }

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export async function getKeepers(leagueId: string): Promise<KeeperPayload> {
  const url = `${API_BASE}/api/v1/preseason/keepers?league_id=${encodeURIComponent(leagueId)}`
  const resp = await fetch(url)
  if (!resp.ok) {
    // Fallback to empty
    return { league_id: leagueId, selections: [], updated_at: null }
  }
  return await resp.json()
}

export async function saveKeepers(leagueId: string, selections: KeeperSelection[]): Promise<KeeperPayload> {
  const url = `${API_BASE}/api/v1/preseason/keepers?league_id=${encodeURIComponent(leagueId)}`
  const resp = await fetch(url, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ league_id: leagueId, selections, updated_at: new Date().toISOString() })
  })
  if (!resp.ok) {
    throw new Error(`Failed to save keepers: ${resp.status}`)
  }
  return await resp.json()
}

