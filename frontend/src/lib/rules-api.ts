import axios from 'axios'

const BACKEND_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
const API_BASE = `${BACKEND_BASE}/api/v1`

export type LeagueRulesResponse = {
  league_id: string
  scoring_rules: Record<string, any>
  roster_slots: Array<Record<string, any>>
  keeper_rules: Record<string, any>
  // Optional extended
  salary_cap?: Record<string, any>
  contract_rules?: Record<string, any>
  waiver_rules?: Record<string, any>
  team_size?: Record<string, any>
  lineup_lock_rules?: Record<string, any>
}

export const RulesAPI = {
  async getLeagueRules(leagueId: string): Promise<LeagueRulesResponse> {
    const url = `${API_BASE}/rules/league/${encodeURIComponent(leagueId)}/full`
    try {
      const { data } = await axios.get(url)
      return data
    } catch (err: any) {
      // Fallback: fetch league object directly and adapt
      const leagueUrl = `${API_BASE}/leagues/${encodeURIComponent(leagueId)}`
      const { data } = await axios.get(leagueUrl)
      return {
        league_id: data.id,
        scoring_rules: data.scoring_rules || {},
        roster_slots: data.roster_slots || [],
        keeper_rules: data.keeper_rules || {},
        // extended fields may be inside keeper_rules.extended but are optional
        ...(data.keeper_rules?.extended || {})
      } as LeagueRulesResponse
    }
  },

  async upsertLeagueRules(leagueId: string, payload: Partial<LeagueRulesResponse>): Promise<any> {
    const url = `${API_BASE}/rules/league/${encodeURIComponent(leagueId)}`
    const { data } = await axios.put(url, payload)
    return data
  }
}
