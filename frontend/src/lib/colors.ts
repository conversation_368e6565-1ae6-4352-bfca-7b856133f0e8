import type React from 'react'

export type TeamColor = {
  primary: string // bg color class
  text: string // text color class
  ring?: string // optional ring/border class
}

// Approximate NFL team color tokens. Keep it simple and readable in light/dark.
// You can refine per design system later.
export const NFL_TEAM_COLORS: Record<string, TeamColor> = {
  // AFC East
  BUF: { primary: 'bg-[#00338D]', text: 'text-white' },
  MIA: { primary: 'bg-[#008E97]', text: 'text-white' },
  NE: { primary: 'bg-[#002244]', text: 'text-white' },
  NYJ: { primary: 'bg-[#125740]', text: 'text-white' },
  // AFC North
  BAL: { primary: 'bg-[#241773]', text: 'text-white' },
  CIN: { primary: 'bg-[#FB4F14]', text: 'text-black' },
  CLE: { primary: 'bg-[#311D00]', text: 'text-white' },
  PIT: { primary: 'bg-[#FFB612]', text: 'text-black' },
  // AFC South
  HOU: { primary: 'bg-[#03202F]', text: 'text-white' },
  IND: { primary: 'bg-[#002C5F]', text: 'text-white' },
  JAX: { primary: 'bg-[#006778]', text: 'text-white' },
  TEN: { primary: 'bg-[#4B92DB]', text: 'text-black' },
  // AFC West
  DEN: { primary: 'bg-[#FB4F14]', text: 'text-black' },
  KC: { primary: 'bg-[#E31837]', text: 'text-white' },
  LAC: { primary: 'bg-[#0080C6]', text: 'text-white' },
  LV: { primary: 'bg-[#000000]', text: 'text-white' },
  // NFC East
  DAL: { primary: 'bg-[#041E42]', text: 'text-white' },
  NYG: { primary: 'bg-[#0B2265]', text: 'text-white' },
  PHI: { primary: 'bg-[#004C54]', text: 'text-white' },
  WAS: { primary: 'bg-[#5A1414]', text: 'text-white' },
  // NFC North
  CHI: { primary: 'bg-[#0B162A]', text: 'text-white' },
  DET: { primary: 'bg-[#0076B6]', text: 'text-white' },
  GB: { primary: 'bg-[#203731]', text: 'text-white' },
  MIN: { primary: 'bg-[#4F2683]', text: 'text-white' },
  // NFC South
  ATL: { primary: 'bg-[#A71930]', text: 'text-white' },
  CAR: { primary: 'bg-[#0085CA]', text: 'text-white' },
  NO: { primary: 'bg-[#D3BC8D]', text: 'text-black' },
  TB: { primary: 'bg-[#D50A0A]', text: 'text-white' },
  // NFC West
  ARI: { primary: 'bg-[#97233F]', text: 'text-white' },
  LAR: { primary: 'bg-[#003594]', text: 'text-white' },
  SF: { primary: 'bg-[#AA0000]', text: 'text-white' },
  SEA: { primary: 'bg-[#002244]', text: 'text-white' },
}

export function teamColorClasses(team?: string): TeamColor {
  const t = (team || '').toUpperCase()
  return NFL_TEAM_COLORS[t] || { primary: 'bg-gray-200', text: 'text-gray-800', ring: 'ring-gray-300' }
}

export type PosColor = TeamColor

export const POS_COLORS: Record<string, PosColor> = {
  QB: { primary: 'bg-amber-500', text: 'text-black' },
  RB: { primary: 'bg-sky-500', text: 'text-black' },
  WR: { primary: 'bg-emerald-500', text: 'text-black' },
  TE: { primary: 'bg-violet-500', text: 'text-white' },
}

export function positionColorClasses(pos?: string): PosColor {
  const p = (pos || '').toUpperCase()
  return POS_COLORS[p] || { primary: 'bg-gray-300', text: 'text-gray-900' }
}

// Distinct franchise colors using a fixed, high-contrast hue palette (stable across sessions)
const FRANCHISE_HUES = [0, 25, 50, 85, 115, 145, 175, 205, 235, 265, 295, 325]

function pickFranchiseHue(franchiseId: string): number {
  // Prefer numeric suffix (e.g., ..._0012) for stable mapping within a league
  const m = franchiseId.match(/(\d{1,4})$/)
  if (m) {
    const num = parseInt(m[1], 10)
    if (!Number.isNaN(num)) {
      return FRANCHISE_HUES[num % FRANCHISE_HUES.length]
    }
  }
  // Fallback: simple djb2-style hash for any id
  let hash = 5381
  for (let i = 0; i < franchiseId.length; i++) {
    hash = ((hash << 5) + hash) + franchiseId.charCodeAt(i)
  }
  const idx = Math.abs(hash) % FRANCHISE_HUES.length
  return FRANCHISE_HUES[idx]
}

// Deterministic franchise color; returns inline style
export function franchiseStyle(franchiseId?: string): React.CSSProperties | undefined {
  if (!franchiseId) return undefined
  const h = pickFranchiseHue(franchiseId)
  const sat = 70
  const light = 45
  return { backgroundColor: `hsl(${h}, ${sat}%, ${light}%)`, color: '#fff' }
}

export const NEUTRAL_FA: TeamColor = { primary: 'bg-gray-200', text: 'text-gray-800' }
export const ROOKIE_BADGE: TeamColor = { primary: 'bg-amber-300', text: 'text-black' }

