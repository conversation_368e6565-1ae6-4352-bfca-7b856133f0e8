import axios from 'axios'

const BACKEND_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
const API_BASE = `${BACKEND_BASE}/api/v1`

export type ADPConfig = {
  providers: string[]
  weights: Record<string, number>
  scoring: string
  exclude_positions: string[]
  staleness_days: number
}

export const ADPAPI = {
  async getConfig(leagueId: string): Promise<{ league_id: string, config: ADPConfig }> {
    const url = `${API_BASE}/rankings/adp/config?league_id=${encodeURIComponent(leagueId)}`
    const { data } = await axios.get(url)
    return data
  },

  async putConfig(leagueId: string, config: Partial<ADPConfig>): Promise<{ league_id: string, config: ADPConfig }> {
    const url = `${API_BASE}/rankings/adp/config?league_id=${encodeURIComponent(leagueId)}`
    const { data } = await axios.put(url, config)
    return data
  },

  async refresh(leagueId: string, season: number): Promise<any> {
    const url = `${API_BASE}/rankings/adp/refresh?league_id=${encodeURIComponent(leagueId)}&season=${season}`
    const { data } = await axios.post(url)
    return data
  }
}

