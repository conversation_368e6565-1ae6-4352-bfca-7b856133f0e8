export function exportCsv(filename: string, rows: Record<string, any>[]) {
  if (!rows || rows.length === 0) {
    // create empty file with headerless row
    const blob = new Blob([""], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.click()
    URL.revokeObjectURL(url)
    return
  }
  const headerSet = rows.reduce((set, row) => {
    Object.keys(row).forEach(k => set.add(k))
    return set
  }, new Set<string>())
  const headers = Array.from(headerSet.values())

  const esc = (val: any) => {
    if (val == null) return ''
    const s = String(val)
    if (s.includes('"') || s.includes(',') || s.includes('\n')) {
      return '"' + s.replace(/"/g, '""') + '"'
    }
    return s
  }

  const lines = [headers.join(',')]
  for (const row of rows) {
    lines.push(headers.map((h) => esc((row as Record<string, any>)[h as string])).join(','))
  }
  const csv = lines.join('\n')
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.click()
  URL.revokeObjectURL(url)
}

