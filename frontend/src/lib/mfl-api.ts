import axios from 'axios'

// MFL API Base Configuration
// Always call our backend MFL proxy so the site works in Docker and locally
const BACKEND_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
const MFL_BASE_URL = `${BACKEND_BASE}/api/v1/mfl/export`
const CURRENT_YEAR = new Date().getFullYear()

// Types for MFL API responses
export interface MFLLeague {
  id: string
  name: string
  season: string
  franchises: MFLFranchise[]
  scoring: any
  rosters: any
  settings: any
}

export interface MFLFranchise {
  id: string
  name: string
  owner_name: string
  icon: string
  logo: string
  division: string
}

export interface MFLPlayer {
  id: string
  name: string
  team: string
  position: string
  jersey: string
  height: string
  weight: string
  birthdate: string
  draft_year: string
}

export interface MFLRoster {
  franchise_id: string
  players: MFLRosterPlayer[]
}

export interface MFLRosterPlayer {
  id: string
  status: string
  salary: string
  contract_year: string
}

export interface MFLDraftResults {
  franchise: string
  round: string
  pick: string
  timestamp: string
  player: string
  comments: string
}

export interface MFLTransaction {
  type: string
  timestamp: string
  franchise: string
  franchise2?: string
  player?: string
  player2?: string
  comments: string
}

export interface MFLLiveScoring {
  franchise: string
  score: string
  players: Array<{
    id: string
    score: string
    gameSecondsRemaining: string
    playingStatus: string
  }>
}

export interface MFLSchedule {
  week: string
  matchups: Array<{
    franchise: string
    opponent: string
    isHome: boolean
  }>
}

export interface MFLStandings {
  franchise: string
  h2hw: string // head-to-head wins
  h2hl: string // head-to-head losses  
  h2ht: string // head-to-head ties
  pf: string   // points for
  pa: string   // points against
  pp: string   // potential points
}

class MFLApiClient {
  private baseUrl: string
  private year: number
  private leagueId: string | null = null
  private username: string | null = null
  private password: string | null = null

  constructor(year: number = CURRENT_YEAR) {
    this.baseUrl = MFL_BASE_URL
    this.year = year
  }

  // Set season year for subsequent requests
  setYear(year: number) {
    if (Number.isFinite(year)) {
      this.year = year
    }
  }

  // Set authentication credentials
  setCredentials(username: string, password: string) {
    this.username = username
    this.password = password
  }

  // Set league ID for subsequent requests
  setLeague(leagueId: string) {
    this.leagueId = leagueId
  }

  // Build API URL with common parameters
  private buildUrl(endpoint: string, params: Record<string, any> = {}): string {
    const baseParams: Record<string, any> = {
      TYPE: endpoint,
      L: this.leagueId,
      YEAR: this.year,
      JSON: 1,
      ...params
    }

    // Remove null/undefined values
    Object.keys(baseParams).forEach((key: string) => {
      if (baseParams[key] === null || baseParams[key] === undefined) {
        delete baseParams[key]
      }
    })

    const queryString = new URLSearchParams(baseParams as any).toString()
    return `${this.baseUrl}?${queryString}`
  }

  // Generic API request method
  private async request<T>(endpoint: string, params: Record<string, any> = {}): Promise<T> {
    try {
      const url = this.buildUrl(endpoint, params)
      console.log('MFL API Request:', url)
      
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'AI Fantasy Assistant'
        }
      })

      return response.data
    } catch (error) {
      console.error('MFL API Error:', error)
      throw new Error(`MFL API request failed: ${error}`)
    }
  }

  // League Information
  async getLeague(): Promise<MFLLeague> {
    if (!this.leagueId) {
      throw new Error('League ID not set. Call setLeague() first.')
    }
    const raw: any = await this.request<any>('league')
    const league = raw?.league ?? raw ?? {}

    // Normalize franchises array (MFL often nests as franchises.franchise)
    let franchises: MFLFranchise[] = []
    if (league.franchises) {
      const f = (league.franchises as any).franchise ?? league.franchises
      if (Array.isArray(f)) franchises = f
      else if (f) franchises = [f]
    }

    // Best-effort normalization for name/season
    const normalized: MFLLeague = {
      id: String(league.id ?? this.leagueId),
      name: String(league.name ?? league.leagueName ?? 'League'),
      season: String(league.season ?? this.year),
      franchises,
      scoring: league.scoring ?? league.scoringRules ?? {},
      rosters: league.roster ?? league.rosters ?? {},
      settings: league.settings ?? {}
    }

    return normalized
  }

  // Get raw league payload (verbatim MFL)
  async getLeagueRaw(): Promise<any> {
    const raw: any = await this.request<any>('league')
    return raw?.league ?? raw ?? {}
  }

  // Get all players
  async getPlayers(): Promise<{ players: { player: MFLPlayer[] } }> {
    return this.request('players')
  }

  // Get league rosters
  async getRosters(): Promise<{ rosters: { franchise: MFLRoster[] } }> {
    return this.request('rosters')
  }

  // Get specific franchise roster
  async getFranchiseRoster(franchiseId: string): Promise<{ rosters: { franchise: MFLRoster } }> {
    return this.request('rosters', { FRANCHISE: franchiseId })
  }

  // Get raw MFL rules
  async getRules(): Promise<any> {
    return this.request('rules')
  }

  // Get league schedule
  async getSchedule(week?: number): Promise<{ schedule: { matchup: MFLSchedule[] } }> {
    const params = week ? { W: week } : {}
    return this.request('schedule', params)
  }

  // Get league standings
  async getStandings(): Promise<{ leagueStandings: { franchise: MFLStandings[] } }> {
    return this.request('leagueStandings')
  }

  // Get live scoring for current week
  async getLiveScoring(week?: number): Promise<{ liveScoring: { franchise: MFLLiveScoring[] } }> {
    const params = week ? { W: week } : {}
    return this.request('liveScoring', params)
  }

  // Get draft results
  async getDraftResults(): Promise<{ draftResults: { draftUnit: { draftPick: MFLDraftResults[] } } }> {
    return this.request('draftResults')
  }

  // Get transactions (trades, waivers, etc.)
  async getTransactions(days?: number): Promise<{ transactions: { transaction: MFLTransaction[] } }> {
    const params = days ? { DAYS: days } : {}
    return this.request('transactions', params)
  }

  // Get free agents
  async getFreeAgents(): Promise<{ freeAgents: { leagueUnit: { player: string[] } } }> {
    return this.request('freeAgents')
  }

  // Get player scores for a specific week
  async getPlayerScores(week: number, position?: string): Promise<any> {
    const params: any = { W: week }
    if (position) params.POSITION = position
    return this.request('playerScores', params)
  }

  // Get projected scores
  async getProjectedScores(week: number): Promise<any> {
    return this.request('projectedScores', { W: week })
  }

  // Get injury reports
  async getInjuries(): Promise<any> {
    return this.request('injuries')
  }

  // Get league rules and scoring
  async getLeagueRules(): Promise<any> {
    return this.request('rules')
  }

  // Get salary information (for salary cap leagues)
  async getSalaries(): Promise<any> {
    return this.request('salaries')
  }

  // Get auction results (for auction leagues)
  async getAuctionResults(): Promise<any> {
    return this.request('auctionResults')
  }

  // Search for leagues (useful for finding league IDs)
  async searchLeagues(searchTerm: string): Promise<any> {
    return this.request('leagueSearch', { SEARCH: searchTerm })
  }

  // Get weekly results/matchup results
  async getWeeklyResults(week: number): Promise<any> {
    return this.request('weeklyResults', { W: week })
  }

  // Get playoff bracket
  async getPlayoffBracket(): Promise<any> {
    return this.request('playoffBracket')
  }

  // Utility method to get current NFL week
  async getCurrentWeek(): Promise<number> {
    try {
      const response = await this.request('nflSchedule')
      // Parse the current week from NFL schedule
      // This is a simplified version - you might need to implement more logic
      return 12 // Default to week 12 for now
    } catch (error) {
      return 12 // Fallback
    }
  }

  // Helper method to get player details by ID
  async getPlayerDetails(playerId: string): Promise<MFLPlayer | null> {
    try {
      const playersResponse = await this.getPlayers()
      const players = playersResponse.players.player
      return players.find(p => p.id === playerId) || null
    } catch (error) {
      console.error('Error fetching player details:', error)
      return null
    }
  }

  // Helper method to get franchise details
  async getFranchiseDetails(franchiseId: string): Promise<MFLFranchise | null> {
    try {
      const league = await this.getLeague()
      return league.franchises.find(f => f.id === franchiseId) || null
    } catch (error) {
      console.error('Error fetching franchise details:', error)
      return null
    }
  }
}

// Create and export a singleton instance
export const mflApi = new MFLApiClient()

// Export utility functions
export const MFLUtils = {
  // Convert MFL player position codes to readable format
  formatPosition: (position: string): string => {
    const positionMap: Record<string, string> = {
      'QB': 'QB',
      'RB': 'RB', 
      'WR': 'WR',
      'TE': 'TE',
      'K': 'K',
      'Def': 'DEF',
      'DT': 'DT',
      'DE': 'DE',
      'LB': 'LB',
      'DB': 'DB'
    }
    return positionMap[position] || position
  },

  // Format MFL timestamp to readable date
  formatTimestamp: (timestamp: string): string => {
    const date = new Date(parseInt(timestamp) * 1000)
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
  },

  // Calculate fantasy points based on stats (basic PPR example)
  calculateFantasyPoints: (stats: any, scoring: any = {}): number => {
    // This is a basic example - you'd customize based on your league's scoring
    const {
      passingYards = 0,
      passingTDs = 0,
      interceptions = 0,
      rushingYards = 0,
      rushingTDs = 0,
      receptions = 0,
      receivingYards = 0,
      receivingTDs = 0,
      fumbles = 0
    } = stats

    let points = 0
    points += passingYards * 0.04 // 1 point per 25 yards
    points += passingTDs * 4
    points -= interceptions * 2
    points += rushingYards * 0.1 // 1 point per 10 yards
    points += rushingTDs * 6
    points += receptions * 1 // PPR
    points += receivingYards * 0.1
    points += receivingTDs * 6
    points -= fumbles * 2

    return Math.round(points * 10) / 10 // Round to 1 decimal
  }
}

export default mflApi