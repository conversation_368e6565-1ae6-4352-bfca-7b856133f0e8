"use client"

import { useEffect, useMemo, useState } from 'react'
import Layout from '@/components/Layout'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import axios from 'axios'
import { ROOKIE_BADGE } from '@/lib/colors'
import Badge from '@/components/ui/Badge'
import { MasterPlayersProvider, useMasterPlayers } from '@/context/MasterPlayersContext'

interface ADPItem {
  player_id: string
  name: string
  position: string
  nfl_team: string
  fantasy_team: { id: string | null, name: string }
  is_rookie?: boolean
  adp_index: { adp: number | null, overall_rank: number | null }
  sources: {
    MFL: { adp: number | null, stdev: number | null, times_drafted: number | null }
    FFC: { adp: number | null, stdev: number | null, times_drafted: number | null }
  }
}

export default function AllPlayersPage() {
  return (
    <MasterPlayersProvider>
      <AllPlayersInner />
    </MasterPlayersProvider>
  )
}

function AllPlayersInner() {
  const [items, setItems] = useState<ADPItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const [page, setPage] = useState(1)
  const [size, setSize] = useState(100)
  const [sort, setSort] = useState('adp_index.asc')
  const [position, setPosition] = useState<string>('')
  const [team, setTeam] = useState<string>('')
  const [q, setQ] = useState<string>('')
  const [total, setTotal] = useState(0)
  const [season] = useState<number>(new Date().getFullYear())

  // Master list freshness & manual refresh
  const { lastUpdated, refresh, loading: masterLoading } = useMasterPlayers()
  const [refreshing, setRefreshing] = useState(false)
  const [staleThreshold, setStaleThreshold] = useState<number>(60)
  // Live ticker so freshness counts up
  const [nowTs, setNowTs] = useState<number>(() => Date.now())
  useEffect(() => {
    const id = setInterval(() => setNowTs(Date.now()), 60_000)
    return () => clearInterval(id)
  }, [])
  // Parse RFC3339 (with possible Z) and compute age upwards (count up)
  const updatedAt = useMemo(() => (lastUpdated ? new Date(lastUpdated) : null), [lastUpdated])
  const minutesAgo = useMemo(() => (updatedAt ? Math.max(0, Math.floor((nowTs - updatedAt.getTime()) / 60000)) : null), [updatedAt, nowTs])
  const hoursAgo = useMemo(() => (minutesAgo !== null ? Math.floor(minutesAgo / 60) : null), [minutesAgo])
  const remainingMinutes = useMemo(() => (minutesAgo !== null && hoursAgo !== null ? minutesAgo - hoursAgo * 60 : null), [minutesAgo, hoursAgo])
  // Consider stale if older than 24h by default
  const defaultStaleHours = 24
  const isStale = useMemo(() => (hoursAgo !== null ? hoursAgo >= defaultStaleHours : true), [hoursAgo])

  // Load/save stale threshold from localStorage
  useEffect(() => {
    if (typeof window === 'undefined') return
    const saved = localStorage.getItem('master_stale_threshold_mins')
    if (saved) {
      const n = parseInt(saved, 10)
      if (!Number.isNaN(n) && n > 0) setStaleThreshold(n)
    }
  }, [])
  useEffect(() => {
    if (typeof window === 'undefined') return
    localStorage.setItem('master_stale_threshold_mins', String(staleThreshold))
  }, [staleThreshold])

  const base = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)
      const params = new URLSearchParams()
      params.set('season', String(season))
      params.set('page', String(page))
      params.set('size', String(size))
      params.set('sort', sort)
      if (position) params.set('position', position)
      if (team) params.set('team', team)
      if (q) params.set('q', q)
      // Exclude IDP by default
      params.set('include_idp', 'false')
      const url = `${base}/api/v1/rankings/adp/all?${params.toString()}`
      const { data } = await axios.get(url)
      setItems(data?.items || [])
      setTotal(data?.total || 0)
    } catch (e: any) {
      setError(e?.message || 'Failed to load players')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, size, sort, position, team, lastUpdated])

  const onRefreshMaster = async () => {
    try {
      setRefreshing(true)
      await refresh()
      // re-fetch table so rookie flags and counts reflect latest data immediately
      await fetchData()
    } finally {
      setRefreshing(false)
    }
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="bg-white shadow rounded-lg p-4">
          <div className="flex flex-wrap items-end gap-3">
            <div>
              <label className="block text-xs text-gray-600">Search</label>
              <input value={q} onChange={(e) => setQ(e.target.value)} onKeyDown={(e) => { if (e.key === 'Enter') { setPage(1); fetchData() } }} className="px-2 py-1 border rounded" placeholder="Player name" />
            </div>
            <div>
              <label className="block text-xs text-gray-600">Position</label>
              <select value={position} onChange={(e) => { setPosition(e.target.value); setPage(1) }} className="px-2 py-1 border rounded">
                <option value="">All (offense only)</option>
                <option value="QB">QB</option>
                <option value="RB">RB</option>
                <option value="WR">WR</option>
                <option value="TE">TE</option>
                {/* IDP positions (DL/LB/DB) intentionally excluded */}
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-600">NFL Team</label>
              <input value={team} onChange={(e) => { setTeam(e.target.value.toUpperCase()); setPage(1) }} className="px-2 py-1 border rounded" placeholder="e.g. SF" />
            </div>
            <div>
              <label className="block text-xs text-gray-600">Sort</label>
              <select value={sort} onChange={(e) => { setSort(e.target.value); setPage(1) }} className="px-2 py-1 border rounded">
                <option value="adp_index.asc">ADP Index ↑</option>
                <option value="adp_index.desc">ADP Index ↓</option>
                <option value="mfl.asc">MFL ADP ↑</option>
                <option value="mfl.desc">MFL ADP ↓</option>
                <option value="ffc.asc">FFC ADP ↑</option>
                <option value="ffc.desc">FFC ADP ↓</option>
                <option value="espn.asc">ESPN ADP ↑</option>
                <option value="espn.desc">ESPN ADP ↓</option>
                <option value="sleeper.asc">Sleeper ADP ↑</option>
                <option value="sleeper.desc">Sleeper ADP ↓</option>
                <option value="cbs.asc">CBS ADP ↑</option>
                <option value="cbs.desc">CBS ADP ↓</option>
                <option value="nfl.asc">NFL ADP ↑</option>
                <option value="nfl.desc">NFL ADP ↓</option>
                <option value="rtsports.asc">RTSports ADP ↑</option>
                <option value="rtsports.desc">RTSports ADP ↓</option>
                <option value="fantrax.asc">Fantrax ADP ↑</option>
                <option value="fantrax.desc">Fantrax ADP ↓</option>
                <option value="name.asc">Name A→Z</option>
                <option value="name.desc">Name Z→A</option>
                <option value="position.asc">Pos ↑</option>
                <option value="position.desc">Pos ↓</option>
                <option value="team.asc">NFL ↑</option>
                <option value="team.desc">NFL ↓</option>
              </select>
            </div>
            <div className="ml-auto flex items-center gap-3">
              <div className="flex items-end gap-2">
                <button
                  onClick={onRefreshMaster}
                  disabled={masterLoading || refreshing}
                  className={`px-3 py-2 rounded text-sm font-medium ${masterLoading || refreshing ? 'bg-gray-300 text-gray-600' : 'bg-blue-600 text-white hover:bg-blue-700'}`}
                  aria-busy={masterLoading || refreshing}
                >
                  {masterLoading || refreshing ? 'Refreshing…' : 'Refresh Master'}
                </button>
              </div>
              <div className="text-xs text-gray-600">
                {updatedAt ? (
                  <span
                    className={isStale ? 'text-orange-600' : 'text-green-600'}
                    title={`Fresh/Stale is based on the lastUpdated timestamp of the master player list. Stale if older than ${defaultStaleHours} hours. Sources: MFL ADP and Fantasy Football Calculator.`}
                  >
                    {isStale ? 'Stale' : 'Fresh'} • Updated {hoursAgo !== null && remainingMinutes !== null ? `${hoursAgo}h ${remainingMinutes}m ago` : updatedAt.toLocaleString()}
                  </span>
                ) : (
                  <span className="text-gray-500">No update info</span>
                )}
              </div>
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">ADP includes data from composite and configured providers (MFL, FFC, ESPN, Sleeper, CBS, NFL, RTSports, Fantrax)</div>
        </div>

        <div className="bg-white shadow rounded-lg overflow-hidden">
          {loading ? (
            <div className="p-8 flex justify-center"><LoadingSpinner /></div>
          ) : error ? (
            <div className="p-4"><ErrorMessage message={error} onDismiss={() => setError(null)} /></div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Player</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Pos</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">NFL</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Fantasy Team</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">ADP Index</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Rank</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Providers</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {items.length === 0 ? (
                    <tr><td colSpan={8} className="px-4 py-6 text-center text-sm text-gray-600">No players found.</td></tr>
                  ) : items.map((it) => {
                    const rostered = !!it.fantasy_team?.id
                    return (
                      <tr key={it.player_id}>
                        <td className="px-4 py-2 text-sm text-gray-900">
                          <div className="flex items-center gap-2">
                            <span>{it.name}</span>
                            {it.is_rookie ? (
                              <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-[10px] font-medium ${ROOKIE_BADGE.primary} ${ROOKIE_BADGE.text}`}>R</span>
                            ) : null}
                          </div>
                        </td>
                        <td className="px-4 py-2 text-sm">
                          <Badge type="position" value={it.position} />
                        </td>
                        <td className="px-4 py-2 text-sm">
                          <Badge type="team" value={it.nfl_team || 'FA'} />
                        </td>
                        <td className="px-4 py-2 text-sm">
                          {rostered ? (
                            <Badge type="franchise" franchiseId={it.fantasy_team?.id} label={it.fantasy_team?.name} />
                          ) : (
                            <Badge type="neutral" label="FA" />
                          )}
                        </td>
                        <td className="px-4 py-2 text-sm font-medium">{it.adp_index.adp ?? '—'}</td>
                        <td className="px-4 py-2 text-sm">{it.adp_index.overall_rank ?? '—'}</td>
                        <td className="px-4 py-2 text-[11px] text-gray-700">
                          {(() => {
                            const provs = [
                              ["MFL", it.sources?.MFL?.adp],
                              ["FFC", it.sources?.FFC?.adp],
                              ["ESPN", it.sources?.ESPN?.adp],
                              ["SLP", it.sources?.SLEEPER?.adp],
                              ["CBS", it.sources?.CBS?.adp],
                              ["NFL", it.sources?.NFL?.adp],
                              ["RTS", it.sources?.RTSPORTS?.adp],
                              ["FNT", it.sources?.FANTRAX?.adp],
                            ].filter(([_, v]) => v != null)
                            if (provs.length === 0) return '—'
                            return provs.map(([k, v]) => `${k} ${Number(v).toFixed(1)}`).join(' | ')
                          })()}
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">Total: {total}</div>
          <div className="flex items-center gap-2">
            <button className="px-3 py-1 border rounded" disabled={page === 1} onClick={() => setPage((p) => Math.max(1, p - 1))}>Prev</button>
            <span className="text-sm">Page {page}</span>
            <button className="px-3 py-1 border rounded" disabled={page * size >= total} onClick={() => setPage((p) => p + 1)}>Next</button>
          </div>
        </div>
      </div>
    </Layout>
  )
}

