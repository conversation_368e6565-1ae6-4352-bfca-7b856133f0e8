'use client'

import { useState, useEffect } from 'react'
import Layout from '@/components/Layout'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'

export default function AlertsPage() {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'active' | 'history' | 'settings'>('active')

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Mock alerts data
  const mockActiveAlerts = [
    {
      id: 1,
      type: 'injury',
      severity: 'high',
      player: '<PERSON>',
      team: 'SF',
      message: 'Listed as questionable with ankle injury',
      timestamp: '2 hours ago',
      action: 'Consider backup options'
    },
    {
      id: 2,
      type: 'waiver',
      severity: 'medium',
      player: '<PERSON><PERSON>',
      team: 'PIT',
      message: 'High-value waiver target available',
      timestamp: '4 hours ago',
      action: 'Submit waiver claim'
    },
    {
      id: 3,
      type: 'trade_deadline',
      severity: 'low',
      message: 'Trade deadline approaching in 5 days',
      timestamp: '1 day ago',
      action: 'Review trade opportunities'
    }
  ]

  const mockAlertHistory = [
    {
      id: 4,
      type: 'lineup',
      severity: 'medium',
      player: 'Josh Allen',
      team: 'BUF',
      message: 'Optimal lineup updated for Week 12',
      timestamp: '1 day ago',
      resolved: true
    },
    {
      id: 5,
      type: 'injury',
      severity: 'high',
      player: 'Derrick Henry',
      team: 'TEN',
      message: 'Cleared from injury report',
      timestamp: '2 days ago',
      resolved: true
    }
  ]

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'injury':
        return '🏥'
      case 'waiver':
        return '📝'
      case 'trade_deadline':
        return '⏰'
      case 'lineup':
        return '📋'
      default:
        return '🔔'
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <LoadingSpinner />
        </div>
      </Layout>
    )
  }

  if (error) {
    return (
      <Layout>
        <ErrorMessage 
          message="There was an error loading alerts. Please try again."
          onDismiss={() => setError(null)}
        />
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Alerts & Notifications</h1>
              <p className="mt-1 text-sm text-gray-500">
                Stay updated with important fantasy football alerts and notifications
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                {mockActiveAlerts.length} Active
              </span>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('active')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'active'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Active Alerts ({mockActiveAlerts.length})
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'history'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Alert History
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'settings'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Alert Settings
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'active' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Active Alerts
                </h3>
                {mockActiveAlerts.length > 0 ? (
                  <div className="space-y-4">
                    {mockActiveAlerts.map((alert) => (
                      <div key={alert.id} className={`border rounded-lg p-4 ${getSeverityColor(alert.severity)}`}>
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3">
                            <span className="text-lg">{getTypeIcon(alert.type)}</span>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                {alert.player && (
                                  <span className="font-medium text-gray-900">
                                    {alert.player} ({alert.team})
                                  </span>
                                )}
                                <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                                  {alert.severity.toUpperCase()}
                                </span>
                              </div>
                              <p className="text-sm text-gray-700 mb-2">{alert.message}</p>
                              <div className="flex items-center justify-between">
                                <span className="text-xs text-gray-500">{alert.timestamp}</span>
                                {alert.action && (
                                  <span className="text-xs font-medium text-gray-700">
                                    Action: {alert.action}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          <button className="text-gray-400 hover:text-gray-600">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-400 text-4xl mb-2">🔔</div>
                    <p className="text-gray-500">No active alerts</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'history' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Alert History
                </h3>
                <div className="space-y-4">
                  {mockAlertHistory.map((alert) => (
                    <div key={alert.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                      <div className="flex items-start space-x-3">
                        <span className="text-lg opacity-60">{getTypeIcon(alert.type)}</span>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            {alert.player && (
                              <span className="font-medium text-gray-700">
                                {alert.player} ({alert.team})
                              </span>
                            )}
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                              RESOLVED
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{alert.message}</p>
                          <span className="text-xs text-gray-500">{alert.timestamp}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Alert Settings
                </h3>
                <div className="space-y-6">
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">Notification Types</h4>
                    <div className="space-y-3">
                      {[
                        { id: 'injury', label: 'Injury Reports', description: 'Get notified about player injuries and status changes' },
                        { id: 'waiver', label: 'Waiver Opportunities', description: 'Alerts for high-value waiver wire pickups' },
                        { id: 'lineup', label: 'Lineup Optimization', description: 'Notifications when optimal lineup changes' },
                        { id: 'trade', label: 'Trade Opportunities', description: 'Alerts for potential beneficial trades' },
                        { id: 'deadlines', label: 'Important Deadlines', description: 'Reminders for trade deadlines, waiver periods, etc.' }
                      ].map((setting) => (
                        <div key={setting.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">{setting.label}</div>
                            <div className="text-sm text-gray-500">{setting.description}</div>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" defaultChecked className="sr-only peer" />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">Delivery Methods</h4>
                    <div className="space-y-3">
                      {[
                        { id: 'browser', label: 'Browser Notifications', enabled: true },
                        { id: 'email', label: 'Email Notifications', enabled: false },
                        { id: 'sms', label: 'SMS Notifications', enabled: false }
                      ].map((method) => (
                        <div key={method.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                          <div className="font-medium text-gray-900">{method.label}</div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" defaultChecked={method.enabled} className="sr-only peer" />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}