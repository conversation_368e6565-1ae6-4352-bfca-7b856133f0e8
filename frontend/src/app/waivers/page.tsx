'use client'

import { useState, useEffect } from 'react'
import Layout from '@/components/Layout'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import Badge from '@/components/ui/Badge'

export default function WaiversPage() {
  const [activeTab, setActiveTab] = useState<'targets' | 'search' | 'calculator' | 'streaming'>('targets')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [faabBudget, setFaabBudget] = useState(100)
  const [selectedPlayers, setSelectedPlayers] = useState<string[]>([])

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Mock waiver wire data
  const mockWaiverTargets = [
    { id: 'player_1', name: '<PERSON><PERSON>', position: 'RB', team: 'PIT', ownership: '45%', priority: 'High', faabSuggestion: '$15-20' },
    { id: 'player_2', name: 'Deon Jackson', position: 'RB', team: 'IND', ownership: '12%', priority: 'Medium', faabSuggestion: '$8-12' },
    { id: 'player_3', name: 'Romeo Doubs', position: 'WR', team: 'GB', ownership: '38%', priority: 'High', faabSuggestion: '$12-18' },
    { id: 'player_4', name: 'Tyler Higbee', position: 'TE', team: 'LAR', ownership: '25%', priority: 'Low', faabSuggestion: '$3-6' },
  ]

  const mockFreeAgents = [
    { id: 'fa_1', name: 'Jerick McKinnon', position: 'RB', team: 'KC', ownership: '8%', trend: 'up' },
    { id: 'fa_2', name: 'Kendrick Bourne', position: 'WR', team: 'NE', ownership: '15%', trend: 'down' },
    { id: 'fa_3', name: 'Logan Thomas', position: 'TE', team: 'WAS', ownership: '22%', trend: 'up' },
    { id: 'fa_4', name: 'Jamaal Williams', position: 'RB', team: 'NO', ownership: '35%', trend: 'up' },
  ]

  const mockStreamingOptions = [
    { id: 'stream_1', name: 'Carolina Panthers', position: 'DEF', opponent: 'vs ATL', matchupRating: 'A+', projectedPoints: 12 },
    { id: 'stream_2', name: 'New York Jets', position: 'DEF', opponent: '@ DEN', matchupRating: 'A', projectedPoints: 10 },
    { id: 'stream_3', name: 'Daniel Carlson', position: 'K', opponent: 'vs IND', matchupRating: 'A-', projectedPoints: 9 },
  ]

  const tabs = [
    { id: 'targets', name: 'Waiver Targets', count: mockWaiverTargets.length },
    { id: 'search', name: 'Free Agent Search', count: mockFreeAgents.length },
    { id: 'calculator', name: 'FAAB Calculator', count: null },
    { id: 'streaming', name: 'Streaming', count: mockStreamingOptions.length },
  ] as const

  const handlePlayerSelect = (playerId: string) => {
    setSelectedPlayers(prev => 
      prev.includes(playerId) 
        ? prev.filter(id => id !== playerId)
        : [...prev, playerId]
    )
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <LoadingSpinner />
        </div>
      </Layout>
    )
  }

  if (error) {
    return (
      <Layout>
        <ErrorMessage 
          message="There was an error loading waiver wire data. Please try again."
          onDismiss={() => setError(null)}
        />
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Waiver Wire</h1>
              <p className="mt-1 text-sm text-gray-500">
                Optimize your waiver claims and FAAB spending
              </p>
            </div>
            <div className="text-sm text-gray-600">
              Waiver Deadline: Wednesday 10:00 AM
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors`}
                >
                  {tab.name}
                  {tab.count !== null && (
                    <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                      activeTab === tab.id 
                        ? 'bg-blue-100 text-blue-600' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {tab.count}
                    </span>
                  )}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'targets' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Recommended Waiver Targets
                </h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Player
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Position
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Team
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Ownership
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Priority
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          FAAB Suggestion
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {mockWaiverTargets.map((player) => (
                        <tr key={player.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{player.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge type="position" value={player.position} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <Badge type="team" value={player.team} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {player.ownership}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              player.priority === 'High' ? 'bg-red-100 text-red-800' :
                              player.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {player.priority}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {player.faabSuggestion}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {activeTab === 'search' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Free Agent Search
                </h3>
                <div className="mb-4">
                  <input
                    type="text"
                    placeholder="Search players..."
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Player
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Position
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Team
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Ownership
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Trend
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {mockFreeAgents.map((player) => (
                        <tr key={player.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{player.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge type="position" value={player.position} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <Badge type="team" value={player.team} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {player.ownership}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center ${
                              player.trend === 'up' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {player.trend === 'up' ? '↗' : '↘'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {activeTab === 'calculator' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  FAAB Calculator
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Current FAAB Budget
                    </label>
                    <input
                      type="number"
                      value={faabBudget}
                      onChange={(e) => setFaabBudget(Number(e.target.value))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Weeks Remaining
                    </label>
                    <input
                      type="number"
                      defaultValue={6}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">FAAB Recommendations</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <p>• Suggested weekly budget: ${Math.round(faabBudget / 6)}</p>
                    <p>• High-priority targets: ${Math.round(faabBudget * 0.15)}-${Math.round(faabBudget * 0.25)}</p>
                    <p>• Medium-priority targets: ${Math.round(faabBudget * 0.08)}-${Math.round(faabBudget * 0.15)}</p>
                    <p>• Low-priority targets: $1-${Math.round(faabBudget * 0.08)}</p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'streaming' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Streaming Opportunities
                </h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Player/Team
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Position
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Matchup
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Rating
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Projected Points
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {mockStreamingOptions.map((option) => (
                        <tr key={option.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{option.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge type="position" value={option.position} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {option.opponent}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              option.matchupRating.startsWith('A') ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {option.matchupRating}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {option.projectedPoints}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}