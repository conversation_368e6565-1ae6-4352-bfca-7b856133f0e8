'use client'

import { useState, useEffect } from 'react'
import { 
  FreeAgentSearch,
  WaiverTargets,
  FAABCalculator,
  WaiverDeadlineCountdown,
  StreamingOpportunities
} from '@/components/waivers'
import { WaiverAPI } from '@/lib/waiver-api'
import { FreeAgent, WaiverTarget, StreamingOpportunity } from '@/types/waiver'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import ErrorMessage from '@/components/ui/ErrorMessage'

export default function WaiversPage() {
  const [activeTab, setActiveTab] = useState<'targets' | 'search' | 'calculator' | 'streaming'>('targets')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Mock data - in real app, these would come from context or props
  const franchiseId = 'franchise_1'
  const leagueId = 'league_1'
  const currentWeek = 8
  const waiverDeadline = new Date('2024-11-20T10:00:00')

  const [waiverTargets, setWaiverTargets] = useState<WaiverTarget[]>([])
  const [freeAgents, setFreeAgents] = useState<FreeAgent[]>([])
  const [streamingOpportunities, setStreamingOpportunities] = useState<StreamingOpportunity[]>([])

  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load waiver targets by default
      const targets = await WaiverAPI.getWaiverTargets(franchiseId, currentWeek)
      setWaiverTargets(targets)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load waiver data')
    } finally {
      setLoading(false)
    }
  }

  const loadFreeAgents = async () => {
    try {
      setLoading(true)
      const agents = await WaiverAPI.getFreeAgents(leagueId)
      setFreeAgents(agents)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load free agents')
    } finally {
      setLoading(false)
    }
  }

  const loadStreamingOpportunities = async () => {
    try {
      setLoading(true)
      const opportunities = await WaiverAPI.getStreamingOpportunities(franchiseId, currentWeek)
      setStreamingOpportunities(opportunities)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load streaming opportunities')
    } finally {
      setLoading(false)
    }
  }

  const handleTabChange = async (tab: typeof activeTab) => {
    setActiveTab(tab)
    
    // Load data for the selected tab if not already loaded
    if (tab === 'search' && freeAgents.length === 0) {
      await loadFreeAgents()
    } else if (tab === 'streaming' && streamingOpportunities.length === 0) {
      await loadStreamingOpportunities()
    }
  }

  const tabs = [
    { id: 'targets', name: 'Waiver Targets', count: waiverTargets.length },
    { id: 'search', name: 'Free Agent Search', count: freeAgents.length },
    { id: 'calculator', name: 'FAAB Calculator', count: null },
    { id: 'streaming', name: 'Streaming', count: streamingOpportunities.length },
  ] as const

  if (loading && activeTab === 'targets') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Waiver Wire</h1>
              <p className="mt-2 text-gray-600">
                Optimize your waiver claims and FAAB spending
              </p>
            </div>
            <WaiverDeadlineCountdown deadline={waiverDeadline} />
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6">
            <ErrorMessage message={error} onDismiss={() => setError(null)} />
          </div>
        )}

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors`}
                >
                  {tab.name}
                  {tab.count !== null && (
                    <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                      activeTab === tab.id 
                        ? 'bg-blue-100 text-blue-600' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {tab.count}
                    </span>
                  )}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'targets' && (
            <WaiverTargets 
              targets={waiverTargets}
              onRefresh={loadInitialData}
              loading={loading}
            />
          )}

          {activeTab === 'search' && (
            <FreeAgentSearch 
              freeAgents={freeAgents}
              leagueId={leagueId}
              onSearch={loadFreeAgents}
              loading={loading}
            />
          )}

          {activeTab === 'calculator' && (
            <FAABCalculator 
              franchiseId={franchiseId}
              waiverTargets={waiverTargets}
            />
          )}

          {activeTab === 'streaming' && (
            <StreamingOpportunities 
              opportunities={streamingOpportunities}
              onRefresh={loadStreamingOpportunities}
              loading={loading}
            />
          )}
        </div>
      </div>
    </div>
  )
}