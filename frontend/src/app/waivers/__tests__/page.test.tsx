import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import WaiversPage from '../page'
import { WaiverAPI } from '@/lib/waiver-api'

// Mock the WaiverAPI
jest.mock('@/lib/waiver-api')
const mockWaiverAPI = WaiverAPI as jest.Mocked<typeof WaiverAPI>

// Mock the waiver components
jest.mock('@/components/waivers', () => ({
  FreeAgentSearch: ({ freeAgents, loading }: any) => (
    <div data-testid="free-agent-search">
      {loading ? 'Loading...' : `${freeAgents.length} free agents`}
    </div>
  ),
  WaiverTargets: ({ targets, loading }: any) => (
    <div data-testid="waiver-targets">
      {loading ? 'Loading...' : `${targets.length} targets`}
    </div>
  ),
  FAABCalculator: ({ waiverTargets }: any) => (
    <div data-testid="faab-calculator">
      Calculator with {waiverTargets.length} targets
    </div>
  ),
  WaiverDeadlineCountdown: ({ deadline }: any) => (
    <div data-testid="deadline-countdown">
      Deadline: {deadline.toISOString()}
    </div>
  ),
  StreamingOpportunities: ({ opportunities, loading }: any) => (
    <div data-testid="streaming-opportunities">
      {loading ? 'Loading...' : `${opportunities.length} opportunities`}
    </div>
  ),
}))

const mockWaiverTargets = [
  {
    player_id: '1',
    player_name: 'Test Player 1',
    position: 'RB',
    target_type: 'high_priority',
    priority: 'high',
    recommended_bid: 25,
    max_bid: 40,
    points_over_replacement: 8.5,
    weekly_upside: 12.3,
    rationale: 'Strong matchup this week',
    drop_candidates: [],
    confidence: 0.85,
    metadata: {}
  }
]

const mockFreeAgents = [
  {
    player_id: '2',
    player_name: 'Free Agent 1',
    position: 'WR',
    team: 'LAR',
    projected_points: 12.5,
    points_over_replacement: 4.2,
    recent_performance: [8.5, 12.1, 6.3],
    upcoming_matchups: ['vs NYG', 'vs DAL'],
    injury_status: 'healthy',
    target_type: 'streaming',
    metadata: {}
  }
]

const mockStreamingOpportunities = [
  {
    position: 'QB',
    weeks: [8, 9, 10],
    targets: [],
    total_value: 15.6,
    strategy: 'Stream based on matchups',
    confidence: 0.75
  }
]

describe('WaiversPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockWaiverAPI.getWaiverTargets.mockResolvedValue(mockWaiverTargets)
    mockWaiverAPI.getFreeAgents.mockResolvedValue(mockFreeAgents)
    mockWaiverAPI.getStreamingOpportunities.mockResolvedValue(mockStreamingOpportunities)
  })

  it('renders the waiver page with header and tabs', async () => {
    render(<WaiversPage />)
    
    expect(screen.getByText('Waiver Wire')).toBeInTheDocument()
    expect(screen.getByText('Optimize your waiver claims and FAAB spending')).toBeInTheDocument()
    
    // Check tabs
    expect(screen.getByText('Waiver Targets')).toBeInTheDocument()
    expect(screen.getByText('Free Agent Search')).toBeInTheDocument()
    expect(screen.getByText('FAAB Calculator')).toBeInTheDocument()
    expect(screen.getByText('Streaming')).toBeInTheDocument()
  })

  it('loads waiver targets by default', async () => {
    render(<WaiversPage />)
    
    await waitFor(() => {
      expect(mockWaiverAPI.getWaiverTargets).toHaveBeenCalledWith('franchise_1', 8)
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('waiver-targets')).toHaveTextContent('1 targets')
    })
  })

  it('displays deadline countdown', () => {
    render(<WaiversPage />)
    
    expect(screen.getByTestId('deadline-countdown')).toBeInTheDocument()
  })

  it('switches between tabs correctly', async () => {
    render(<WaiversPage />)
    
    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByTestId('waiver-targets')).toBeInTheDocument()
    })
    
    // Switch to Free Agent Search
    fireEvent.click(screen.getByText('Free Agent Search'))
    
    await waitFor(() => {
      expect(mockWaiverAPI.getFreeAgents).toHaveBeenCalledWith('league_1')
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('free-agent-search')).toHaveTextContent('1 free agents')
    })
    
    // Switch to FAAB Calculator
    fireEvent.click(screen.getByText('FAAB Calculator'))
    expect(screen.getByTestId('faab-calculator')).toHaveTextContent('Calculator with 1 targets')
    
    // Switch to Streaming
    fireEvent.click(screen.getByText('Streaming'))
    
    await waitFor(() => {
      expect(mockWaiverAPI.getStreamingOpportunities).toHaveBeenCalledWith('franchise_1', 8)
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('streaming-opportunities')).toHaveTextContent('1 opportunities')
    })
  })

  it('shows loading state initially', () => {
    render(<WaiversPage />)
    
    // Should show loading spinner initially
    expect(screen.getByTestId('waiver-targets')).toHaveTextContent('Loading...')
  })

  it('handles API errors gracefully', async () => {
    const errorMessage = 'Failed to load waiver data'
    mockWaiverAPI.getWaiverTargets.mockRejectedValue(new Error(errorMessage))
    
    render(<WaiversPage />)
    
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })
  })

  it('shows tab counts correctly', async () => {
    render(<WaiversPage />)
    
    await waitFor(() => {
      expect(screen.getByText('1')).toBeInTheDocument() // Waiver targets count
    })
    
    // Switch to free agents to load data
    fireEvent.click(screen.getByText('Free Agent Search'))
    
    await waitFor(() => {
      const freeAgentTab = screen.getByText('Free Agent Search').closest('button')
      expect(freeAgentTab).toHaveTextContent('1') // Free agents count
    })
  })

  it('allows refreshing data', async () => {
    render(<WaiversPage />)
    
    await waitFor(() => {
      expect(mockWaiverAPI.getWaiverTargets).toHaveBeenCalledTimes(1)
    })
    
    // The refresh functionality would be tested through the child components
    // This test verifies the initial load works correctly
  })
})