'use client'

import { useState, useEffect } from 'react'

export default function DebugPage() {
  const [storageData, setStorageData] = useState<any>({})

  useEffect(() => {
    const data = {
      mfl_league_id: localStorage.getItem('mfl_league_id'),
      mfl_credentials: localStorage.getItem('mfl_credentials'),
      allKeys: Object.keys(localStorage)
    }
    setStorageData(data)
  }, [])

  const clearStorage = () => {
    localStorage.clear()
    window.location.href = '/'
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-4">Debug Information</h1>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-medium">LocalStorage Contents:</h3>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
              {JSON.stringify(storageData, null, 2)}
            </pre>
          </div>

          <div className="space-x-4">
            <button
              onClick={clearStorage}
              className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Clear Storage & Go Home
            </button>
            <a
              href="/"
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 inline-block"
            >
              Go to Home
            </a>
            <a
              href="/mfl-test"
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 inline-block"
            >
              Test MFL API
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}