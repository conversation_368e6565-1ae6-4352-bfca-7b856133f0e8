'use client'

import { useState, useEffect } from 'react'
import { useMFL } from '@/hooks/useMFL'

export default function MFLDebugPage() {
  const [leagueId, setLeagueId] = useState<string | null>(null)
  const [credentials, setCredentials] = useState<any>(null)
  const [rawLocal, setRawLocal] = useState<{ mfl_league_id: string | null, mfl_credentials: string | null } | null>(null)

  const {
    loading,
    error,
    league,
    standings,
    players,
    rosters,
    initialize
  } = useMFL(leagueId || undefined)

  useEffect(() => {
    if (typeof window === 'undefined') return
    const savedLeague = localStorage.getItem('mfl_league_id')
    const savedCredentials = localStorage.getItem('mfl_credentials')
    
    setLeagueId(savedLeague)
    if (savedCredentials) {
      setCredentials(JSON.parse(savedCredentials))
    }
    setRawLocal({
      mfl_league_id: savedLeague,
      mfl_credentials: savedCredentials,
    })
  }, [])

  const clearAndReset = () => {
    if (typeof window === 'undefined') return
    localStorage.removeItem('mfl_league_id')
    localStorage.removeItem('mfl_credentials')
    window.location.href = '/'
  }

  const reloadData = () => {
    if (leagueId) {
      initialize()
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold mb-4">MFL Debug Information</h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Configuration */}
            <div>
              <h3 className="font-medium text-gray-900 mb-3">Current Configuration</h3>
              <div className="bg-gray-50 p-3 rounded text-sm space-y-2">
                <p><strong>League ID:</strong> {leagueId || 'Not set'}</p>
                <p><strong>Username:</strong> {credentials?.username || 'Not set'}</p>
                <p><strong>Password:</strong> {credentials?.password ? '***' : 'Not set'}</p>
              </div>
            </div>

            {/* Status */}
            <div>
              <h3 className="font-medium text-gray-900 mb-3">Connection Status</h3>
              <div className="bg-gray-50 p-3 rounded text-sm space-y-2">
                <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
                <p><strong>Error:</strong> {error || 'None'}</p>
                <p><strong>League Loaded:</strong> {league ? 'Yes' : 'No'}</p>
                <p><strong>Standings Loaded:</strong> {standings ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </div>

          <div className="mt-6 space-x-4">
            <button
              onClick={reloadData}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Reload Data
            </button>
            <button
              onClick={clearAndReset}
              className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Clear & Reset
            </button>
            <a
              href="/"
              className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 inline-block"
            >
              Go to Dashboard
            </a>
          </div>
        </div>

        {/* League Data */}
        {league && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="font-medium text-gray-900 mb-3">League Information</h3>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
              {JSON.stringify(league, null, 2)}
            </pre>
          </div>
        )}

        {/* Standings Data */}
        {standings && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="font-medium text-gray-900 mb-3">Standings Data</h3>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
              {JSON.stringify(standings, null, 2)}
            </pre>
          </div>
        )}

        {/* Raw Data for Debugging */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="font-medium text-gray-900 mb-3">Raw LocalStorage</h3>
          <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
            {rawLocal ? JSON.stringify(rawLocal, null, 2) : 'Loading…'}
          </pre>
        </div>
      </div>
    </div>
  )
}
