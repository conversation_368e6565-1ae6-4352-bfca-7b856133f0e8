import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  
  // Get all query parameters
  const params = new URLSearchParams()
  searchParams.forEach((value, key) => {
    params.append(key, value)
  })

  // Get the year from params or use current year
  const year = searchParams.get('YEAR') || new Date().getFullYear().toString()
  
  try {
    // Build MFL API URL
    const mflUrl = `https://api.myfantasyleague.com/${year}/export?${params.toString()}`
    
    console.log('Proxying MFL request:', mflUrl)
    
    // Make request to MFL API
    const response = await fetch(mflUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'AI Fantasy Assistant/1.0',
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
      },
    })

    console.log('MFL API Response Status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('MFL API Error Response:', errorText)
      throw new Error(`MFL API responded with status: ${response.status} - ${errorText}`)
    }

    const contentType = response.headers.get('content-type')
    console.log('MFL API Content-Type:', contentType)

    let data
    if (contentType?.includes('application/json')) {
      data = await response.json()
    } else {
      // MFL sometimes returns XML, try to parse as text first
      const text = await response.text()
      console.log('MFL API Response (first 200 chars):', text.substring(0, 200))
      
      // Try to parse as JSON
      try {
        data = JSON.parse(text)
      } catch {
        // If not JSON, return the raw text with an error
        throw new Error(`MFL API returned non-JSON response: ${text.substring(0, 100)}...`)
      }
    }
    
    console.log('MFL API Success, returning data')
    
    // Return the data with CORS headers
    return NextResponse.json(data, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    })
  } catch (error) {
    console.error('MFL API proxy error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch from MFL API', 
        details: error instanceof Error ? error.message : 'Unknown error',
        url: `https://api.myfantasyleague.com/${year}/export?${params.toString()}`
      },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}