import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import LineupPage from '../page'
import { LineupAPI } from '../../../lib/lineup-api'
import { LineupRecommendations } from '../../../types/lineup'

// Mock the API
jest.mock('../../../lib/lineup-api')
const mockLineupAPI = LineupAPI as jest.Mocked<typeof LineupAPI>

// Mock the child components
jest.mock('../../../components/lineup/LineupOptimizer', () => ({
  LineupOptimizer: ({ onUpdate }: { onUpdate: () => void }) => (
    <div data-testid="lineup-optimizer">
      <button onClick={onUpdate}>Update</button>
    </div>
  )
}))

jest.mock('../../../components/lineup/StartSitRecommendations', () => ({
  StartSitRecommendations: () => <div data-testid="start-sit-recommendations" />
}))

jest.mock('../../../components/lineup/LineupLockCountdown', () => ({
  LineupLockCountdown: () => <div data-testid="lineup-lock-countdown" />
}))

jest.mock('../../../components/lineup/LineupHistory', () => ({
  LineupHistory: () => <div data-testid="lineup-history" />
}))

const mockRecommendations: LineupRecommendations = {
  optimization: {
    lineup: {
      QB: 'player_1',
      RB1: 'player_2',
      RB2: 'player_3',
      WR1: 'player_4',
      WR2: 'player_5',
      TE: 'player_6',
      FLEX: 'player_7',
      K: 'player_8',
      DEF: 'player_9'
    },
    projected_points: 125.5,
    win_probability: 0.65,
    confidence: 0.85,
    rationale: 'Test rationale',
    alternatives: [],
    risk_level: 0.35
  },
  start_sit_recommendations: [
    {
      type: 'start_sit',
      slot: 'FLEX',
      start_player_id: 'player_7',
      sit_player_id: 'player_10',
      projected_improvement: 3.2,
      confidence: 0.85,
      rationale: 'Test rationale'
    }
  ],
  lock_alerts: [
    {
      type: 'lineup_lock',
      player_id: 'player_1',
      player_name: 'Josh Allen',
      slot: 'QB',
      lock_time: '2024-09-15T15:00:00Z',
      time_until_lock: '1h 0m 0s',
      urgency: 'medium'
    }
  ],
  generated_at: new Date().toISOString()
}

describe('LineupPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockLineupAPI.getLineupRecommendations.mockResolvedValue(mockRecommendations)
  })

  it('renders lineup page with header', async () => {
    render(<LineupPage />)
    
    expect(screen.getByText('Lineup Management')).toBeInTheDocument()
    expect(screen.getByText('Optimize your weekly lineup with AI-powered recommendations')).toBeInTheDocument()
  })

  it('displays loading state initially', () => {
    render(<LineupPage />)
    
    expect(screen.getByRole('status')).toBeInTheDocument() // LoadingSpinner
  })

  it('loads recommendations on mount', async () => {
    render(<LineupPage />)
    
    await waitFor(() => {
      expect(mockLineupAPI.getLineupRecommendations).toHaveBeenCalledWith('franchise_1', 1)
    })
  })

  it('displays week selector', async () => {
    render(<LineupPage />)
    
    await waitFor(() => {
      expect(screen.getByLabelText('Select Week')).toBeInTheDocument()
      expect(screen.getByDisplayValue('1')).toBeInTheDocument()
    })
  })

  it('changes week when selector is updated', async () => {
    render(<LineupPage />)
    
    await waitFor(() => {
      const weekSelect = screen.getByLabelText('Select Week')
      fireEvent.change(weekSelect, { target: { value: '5' } })
      
      expect(weekSelect).toHaveValue('5')
    })
    
    await waitFor(() => {
      expect(mockLineupAPI.getLineupRecommendations).toHaveBeenCalledWith('franchise_1', 5)
    })
  })

  it('displays tab navigation', async () => {
    render(<LineupPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Lineup Optimizer')).toBeInTheDocument()
      expect(screen.getByText('Start/Sit Recommendations')).toBeInTheDocument()
      expect(screen.getByText('Lineup History')).toBeInTheDocument()
    })
  })

  it('switches tabs correctly', async () => {
    render(<LineupPage />)
    
    await waitFor(() => {
      // Initially on optimizer tab
      expect(screen.getByTestId('lineup-optimizer')).toBeInTheDocument()
      expect(screen.queryByTestId('start-sit-recommendations')).not.toBeInTheDocument()
      
      // Switch to recommendations tab
      fireEvent.click(screen.getByText('Start/Sit Recommendations'))
      expect(screen.getByTestId('start-sit-recommendations')).toBeInTheDocument()
      expect(screen.queryByTestId('lineup-optimizer')).not.toBeInTheDocument()
      
      // Switch to history tab
      fireEvent.click(screen.getByText('Lineup History'))
      expect(screen.getByTestId('lineup-history')).toBeInTheDocument()
      expect(screen.queryByTestId('start-sit-recommendations')).not.toBeInTheDocument()
    })
  })

  it('displays lock alerts when present', async () => {
    render(<LineupPage />)
    
    await waitFor(() => {
      expect(screen.getByTestId('lineup-lock-countdown')).toBeInTheDocument()
    })
  })

  it('hides lock alerts when none present', async () => {
    const recommendationsWithoutAlerts = {
      ...mockRecommendations,
      lock_alerts: []
    }
    mockLineupAPI.getLineupRecommendations.mockResolvedValue(recommendationsWithoutAlerts)
    
    render(<LineupPage />)
    
    await waitFor(() => {
      expect(screen.queryByTestId('lineup-lock-countdown')).not.toBeInTheDocument()
    })
  })

  it('handles API error gracefully', async () => {
    mockLineupAPI.getLineupRecommendations.mockRejectedValue(new Error('API Error'))
    
    render(<LineupPage />)
    
    await waitFor(() => {
      expect(screen.getByText('API Error')).toBeInTheDocument()
      expect(screen.getByText('Retry')).toBeInTheDocument()
    })
  })

  it('retries loading when retry button clicked', async () => {
    mockLineupAPI.getLineupRecommendations.mockRejectedValueOnce(new Error('API Error'))
    mockLineupAPI.getLineupRecommendations.mockResolvedValueOnce(mockRecommendations)
    
    render(<LineupPage />)
    
    await waitFor(() => {
      expect(screen.getByText('API Error')).toBeInTheDocument()
    })
    
    const retryButton = screen.getByText('Retry')
    fireEvent.click(retryButton)
    
    await waitFor(() => {
      expect(screen.getByText('Lineup Management')).toBeInTheDocument()
      expect(screen.queryByText('API Error')).not.toBeInTheDocument()
    })
  })

  it('handles optimization update callback', async () => {
    render(<LineupPage />)
    
    await waitFor(() => {
      const updateButton = screen.getByText('Update')
      fireEvent.click(updateButton)
    })
    
    await waitFor(() => {
      // Should call API again after update
      expect(mockLineupAPI.getLineupRecommendations).toHaveBeenCalledTimes(2)
    })
  })

  it('shows active tab styling', async () => {
    render(<LineupPage />)
    
    await waitFor(() => {
      const optimizerTab = screen.getByText('Lineup Optimizer')
      const recommendationsTab = screen.getByText('Start/Sit Recommendations')
      
      expect(optimizerTab).toHaveClass('border-blue-500', 'text-blue-600')
      expect(recommendationsTab).toHaveClass('border-transparent', 'text-gray-500')
    })
  })

  it('updates tab styling when switching', async () => {
    render(<LineupPage />)
    
    await waitFor(() => {
      const recommendationsTab = screen.getByText('Start/Sit Recommendations')
      fireEvent.click(recommendationsTab)
      
      expect(recommendationsTab).toHaveClass('border-blue-500', 'text-blue-600')
    })
  })

  it('displays all week options in selector', async () => {
    render(<LineupPage />)
    
    await waitFor(() => {
      const weekSelect = screen.getByLabelText('Select Week')
      const options = weekSelect.querySelectorAll('option')
      
      expect(options).toHaveLength(18) // Weeks 1-18
      expect(options[0]).toHaveTextContent('Week 1')
      expect(options[17]).toHaveTextContent('Week 18')
    })
  })

  it('passes correct props to child components', async () => {
    render(<LineupPage />)
    
    await waitFor(() => {
      // LineupOptimizer should receive optimization data
      expect(screen.getByTestId('lineup-optimizer')).toBeInTheDocument()
      
      // StartSitRecommendations should be rendered when tab is active
      fireEvent.click(screen.getByText('Start/Sit Recommendations'))
      expect(screen.getByTestId('start-sit-recommendations')).toBeInTheDocument()
      
      // LineupHistory should be rendered when tab is active
      fireEvent.click(screen.getByText('Lineup History'))
      expect(screen.getByTestId('lineup-history')).toBeInTheDocument()
    })
  })
})