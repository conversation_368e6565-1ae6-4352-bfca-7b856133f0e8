'use client'

import { useState, useEffect } from 'react'
import { LineupOptimizer } from '../../components/lineup/LineupOptimizer'
import { StartSitRecommendations } from '../../components/lineup/StartSitRecommendations'
import { LineupLockCountdown } from '../../components/lineup/LineupLockCountdown'
import { LineupHistory } from '../../components/lineup/LineupHistory'
import { LineupAPI } from '../../lib/lineup-api'
import { LineupRecommendations } from '../../types/lineup'
import { LoadingSpinner } from '../../components/ui/LoadingSpinner'
import { ErrorMessage } from '../../components/ui/ErrorMessage'

export default function LineupPage() {
  const [recommendations, setRecommendations] = useState<LineupRecommendations | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedWeek, setSelectedWeek] = useState(1)
  const [selectedFranchise] = useState('franchise_1') // Would come from user context
  const [activeTab, setActiveTab] = useState<'optimizer' | 'recommendations' | 'history'>('optimizer')

  useEffect(() => {
    loadRecommendations()
  }, [selectedWeek, selectedFranchise])

  const loadRecommendations = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await LineupAPI.getLineupRecommendations(selectedFranchise, selectedWeek)
      setRecommendations(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load lineup recommendations')
    } finally {
      setLoading(false)
    }
  }

  const handleWeekChange = (week: number) => {
    setSelectedWeek(week)
  }

  const handleOptimizationUpdate = () => {
    loadRecommendations()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <ErrorMessage message={error} onRetry={loadRecommendations} />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Lineup Management</h1>
          <p className="mt-2 text-gray-600">
            Optimize your weekly lineup with AI-powered recommendations
          </p>
        </div>

        {/* Week Selector */}
        <div className="mb-6">
          <label htmlFor="week-select" className="block text-sm font-medium text-gray-700 mb-2">
            Select Week
          </label>
          <select
            id="week-select"
            value={selectedWeek}
            onChange={(e) => handleWeekChange(Number(e.target.value))}
            className="block w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            {Array.from({ length: 18 }, (_, i) => i + 1).map((week) => (
              <option key={week} value={week}>
                Week {week}
              </option>
            ))}
          </select>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('optimizer')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'optimizer'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Lineup Optimizer
            </button>
            <button
              onClick={() => setActiveTab('recommendations')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'recommendations'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Start/Sit Recommendations
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'history'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Lineup History
            </button>
          </nav>
        </div>

        {/* Lock Alerts */}
        {recommendations?.lock_alerts && recommendations.lock_alerts.length > 0 && (
          <div className="mb-6">
            <LineupLockCountdown alerts={recommendations.lock_alerts} />
          </div>
        )}

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'optimizer' && recommendations && (
            <LineupOptimizer
              optimization={recommendations.optimization}
              franchiseId={selectedFranchise}
              week={selectedWeek}
              onUpdate={handleOptimizationUpdate}
            />
          )}

          {activeTab === 'recommendations' && recommendations && (
            <StartSitRecommendations
              recommendations={recommendations.start_sit_recommendations}
              franchiseId={selectedFranchise}
              week={selectedWeek}
            />
          )}

          {activeTab === 'history' && (
            <LineupHistory
              franchiseId={selectedFranchise}
              season={2024}
            />
          )}
        </div>
      </div>
    </div>
  )
}