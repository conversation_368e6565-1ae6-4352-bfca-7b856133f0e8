'use client'

import { useMemo, useState, useEffect } from 'react'
import Layout from '@/components/Layout'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { useMFLContext } from '@/context/MFLContext'
import RefreshButton from '@/components/RefreshButton'
import Badge from '@/components/ui/Badge'

export default function LineupPage() {
  const [selectedWeek, setSelectedWeek] = useState(12)
  const [activeTab, setActiveTab] = useState<'optimizer' | 'recommendations' | 'history'>('optimizer')

  const { leagueId, loading, error, league, rosters, players } = useMFLContext() as any

  useEffect(() => {
    // nothing extra; provider initializes on app mount if leagueId exists
  }, [leagueId])

  // Prepare data and hooks unconditionally to keep hooks order stable
  const myRoster = rosters && rosters.length > 0 ? rosters[0] : null
  const rosterPlayers = myRoster?.players || []

  const playerMap = useMemo(() => {
    const map: Record<string, any> = {}
    ;(players || []).forEach((p: any) => { map[p.id] = p })
    return map
  }, [players])

  const normalizePos = (pos?: string) => {
    if (!pos) return 'UNK'
    if (pos.toUpperCase() === 'DEF' || pos === 'Def') return 'DEF'
    return pos.toUpperCase()
  }

  const lineup = useMemo(() => {
    const detailed = (rosterPlayers || [])
      .map((rp: any) => ({ ...rp, meta: playerMap[rp.id] }))
      .filter((x: any) => !!x.meta)

    const byPos: Record<string, any[]> = {}
    detailed.forEach((x: any) => {
      const pos = normalizePos(x.meta.position)
      if (!byPos[pos]) byPos[pos] = []
      byPos[pos].push(x)
    })

    const take = (pos: string) => (byPos[pos] && byPos[pos].length > 0 ? byPos[pos].shift() : null)

    const starters: { label: string, item: any | null }[] = []
    starters.push({ label: 'QB', item: take('QB') })
    starters.push({ label: 'RB1', item: take('RB') })
    starters.push({ label: 'RB2', item: take('RB') })
    starters.push({ label: 'WR1', item: take('WR') })
    starters.push({ label: 'WR2', item: take('WR') })
    starters.push({ label: 'TE', item: take('TE') })
    const flex = (byPos['RB'] && byPos['RB'][0]) || (byPos['WR'] && byPos['WR'][0]) || (byPos['TE'] && byPos['TE'][0]) || null
    if (flex) {
      const pos = normalizePos(flex.meta.position)
      byPos[pos].shift()
    }
    starters.push({ label: 'FLEX', item: flex })
    starters.push({ label: 'K', item: take('K') })
    starters.push({ label: 'DEF', item: take('DEF') })

    const bench: any[] = []
    Object.values(byPos).forEach(arr => arr.forEach(x => bench.push(x)))

    return { starters, bench }
  }, [rosterPlayers, playerMap])

  // Check if MFL is configured
  if (!leagueId) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <h2 className="text-xl font-bold text-gray-900 mb-4">MFL Setup Required</h2>
            <p className="text-gray-600 mb-4">
              Please configure your MyFantasyLeague connection to view lineup data.
            </p>
            <a
              href="/"
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Go to Setup
            </a>
          </div>
        </div>
      </Layout>
    )
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <LoadingSpinner />
            <p className="mt-4 text-gray-600">Loading lineup data from MFL...</p>
          </div>
        </div>
      </Layout>
    )
  }

  if (error) {
    return (
      <Layout>
        <ErrorMessage 
          message={error || "There was an error loading lineup data."}
          onDismiss={() => window.location.reload()}
        />
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Lineup Management</h1>
              <p className="mt-1 text-sm text-gray-500">
                {league?.name || 'Your League'} - Week {selectedWeek} • Connected to MFL
              </p>
              {rosterPlayers.length > 0 && (
                <p className="text-xs text-green-600 mt-1">
                  ✓ {rosterPlayers.length} players loaded from your MFL roster
                </p>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <RefreshButton contextKey="lineup" />
              <select
                value={selectedWeek}
                onChange={(e) => setSelectedWeek(Number(e.target.value))}
                className="block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                {Array.from({ length: 18 }, (_, i) => i + 1).map((week) => (
                  <option key={week} value={week}>
                    Week {week}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('optimizer')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'optimizer'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Lineup Optimizer
              </button>
              <button
                onClick={() => setActiveTab('recommendations')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'recommendations'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Start/Sit Recommendations
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'history'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Lineup History
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'optimizer' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Current Lineup */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Suggested Lineup - Week {selectedWeek}
                  </h3>
                  <div className="space-y-3">
                    {lineup.starters.map((slot, idx) => (
                      <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Badge type="neutral" label={slot.label} />
                          <div>
                            {slot.item ? (
                              <>
                                <div className="font-medium text-gray-900">{slot.item.meta.name}</div>
                                <div className="flex items-center gap-2 text-sm text-gray-700">
                                  <Badge type="position" value={normalizePos(slot.item.meta.position)} />
                                  <Badge type="team" value={slot.item.meta.team || 'FA'} />
                                </div>
                              </>
                            ) : (
                              <div className="text-sm text-gray-500">No player available</div>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-xs text-gray-500">Proj: —</div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-4 p-3 bg-blue-50 rounded-lg text-sm text-blue-800">
                    Roster size: <span className="font-semibold">{rosterPlayers.length}</span>
                  </div>
                </div>

                {/* Bench */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Bench</h3>
                  {lineup.bench.length === 0 ? (
                    <div className="text-sm text-gray-600">No bench players.</div>
                  ) : (
                    <div className="space-y-3">
                      {lineup.bench.map((p: any, idx: number) => (
                        <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">{p.meta.name}</div>
                            <div className="flex items-center gap-2 text-sm text-gray-700">
                              <Badge type="position" value={normalizePos(p.meta.position)} />
                              <Badge type="team" value={p.meta.team || 'FA'} />
                            </div>
                          </div>
                          <div className="text-right text-xs text-gray-500">Proj: —</div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'recommendations' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Start/Sit Recommendations</h3>
                <div className="text-sm text-gray-600">
                  Recommendations will be powered by projections soon. For now, review the suggested lineup and bench above.
                </div>
              </div>
            )}

            {activeTab === 'history' && (
              <div className="text-sm text-gray-600">
                Lineup history view coming soon.
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}
