'use client'

import Layout from '@/components/Layout'
import { useMFLContext } from '@/context/MFLContext'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import RefreshButton from '@/components/RefreshButton'
import { useEffect, useMemo, useState } from 'react'
import axios from 'axios'
import mflApi from '@/lib/mfl-api'
import Badge from '@/components/ui/Badge'

// Contract helpers
function roundToIncrement(value: number, increment: number = 25): number {
  if (!Number.isFinite(value)) return value
  return Math.round(value / increment) * increment
}

function formatDollars(value: number): string {
  if (!Number.isFinite(value)) return '—'
  return `$${Math.round(value).toLocaleString()}`
}

// Computes next contract in DOLLARS, assuming incoming MFL salary values are in "thousands".
function computeNextContract(currentSalaryRaw: any, currentYearRaw: any): { nextYear?: number, nextSalary?: number, mustFranchise?: boolean } {
  const currentSalaryThousands = currentSalaryRaw != null && currentSalaryRaw !== '' ? Number(currentSalaryRaw) : undefined
  if (!currentSalaryThousands) return {}
  let currentYearNum = currentYearRaw != null && currentYearRaw !== '' ? Number(currentYearRaw) : undefined
  if (!Number.isFinite(currentYearNum) || !currentYearNum || currentYearNum <= 0) currentYearNum = 1

  // Minimum franchise salary is $3,000,000 -> 3000 in "thousands" units
  const franchiseMinThousands = 3000
  const nextYear = currentYearNum + 1
  let nextSalaryThousands: number | undefined

  if (currentYearNum === 1) {
    nextSalaryThousands = currentSalaryThousands * 1.10
  } else if (currentYearNum === 2) {
    // Year 2 -> Year 3: per league rule, simply 1.2x the Year 1 salary shown in MFL (pre-season)
    nextSalaryThousands = currentSalaryThousands * 1.20
  } else {
    // Year 3 or later: franchise escalation 30% per year, minimum $3M
    nextSalaryThousands = Math.max(currentSalaryThousands * 1.30, franchiseMinThousands)
  }

  // Convert to dollars and round to nearest $25
  const nextSalaryDollars = roundToIncrement((nextSalaryThousands || 0) * 1000, 25)
  const mustFranchise = nextYear >= 4
  return { nextYear, nextSalary: nextSalaryDollars, mustFranchise }
}

function extractRosterPlayers(roster: any): any[] {
  if (!roster) return []
  // Common MFL shapes
  // 1) roster.players.player -> array|object
  if (roster.players && roster.players.player) {
    const list = roster.players.player
    return Array.isArray(list) ? list : (list ? [list] : [])
  }
  // 2) roster.player -> array|object
  if (roster.player) {
    const list = roster.player
    return Array.isArray(list) ? list : (list ? [list] : [])
  }
  // 3) roster.players -> already array
  if (Array.isArray(roster.players)) return roster.players
  return []
}

export default function RosterPage() {
  const { leagueId, loading, error, league, rosters, players, myFranchiseId, setMyFranchiseId, seasonYear } = useMFLContext() as any
  const [pendingFranchise, setPendingFranchise] = useState<string>('')
  const [isClient, setIsClient] = useState(false)
  const [salaryMap, setSalaryMap] = useState<Record<string, { salary?: number, year?: number }>>({})
  const [ecrMap, setEcrMap] = useState<Record<string, { consensus_rank?: number }>>({})
  const [adpMap, setAdpMap] = useState<Record<string, { adp_index?: number; overall_rank?: number; metadata?: any }>>({})
  const [adpRefreshing, setAdpRefreshing] = useState(false)

  useEffect(() => { setIsClient(true) }, [])

  const myRoster = useMemo(() => {
    if (!rosters || rosters.length === 0) return null
    if (myFranchiseId) {
      return rosters.find((r: any) =>
        String(r.franchise_id ?? r.id ?? r.franchise)?.toLowerCase() === String(myFranchiseId).toLowerCase()
      ) || rosters[0]
    }
    return rosters[0]
  }, [rosters, myFranchiseId])

  const rosterPlayers: any[] = useMemo(() => extractRosterPlayers(myRoster), [myRoster])

  // Load MFL salaries export as fallback for missing contract salary/year
  useEffect(() => {
    const loadSalaries = async () => {
      try {
        if (!leagueId) return
        mflApi.setLeague(leagueId)
        const resp: any = await mflApi.getSalaries()
        // Try multiple shapes
        const list = resp?.salaries?.salary || resp?.salaries?.player || resp?.salary || []
        const arr = Array.isArray(list) ? list : (list ? [list] : [])
        const map: Record<string, { salary?: number, year?: number }> = {}
        arr.forEach((row: any) => {
          const id = String(row.id || row.player || row.player_id || '').trim()
          if (!id) return
          const sal = Number(row.salary || row.amount || row.value)
          const yr = Number(row.contractYear || row.year || row.contract_year)
          map[id] = { salary: Number.isFinite(sal) ? sal : undefined, year: Number.isFinite(yr) ? yr : undefined }
        })
        setSalaryMap(map)
      } catch {
        setSalaryMap({})
      }
    }
    loadSalaries()
  }, [leagueId])

// Fetch ECR for players on the roster from backend rankings endpoint
  useEffect(() => {
    const fetchECR = async () => {
      try {
        const ids = (rosterPlayers || [])
          .map((rp: any) => String(rp.id))
          .filter(Boolean)
          .map((id: string) => (id.startsWith('mfl_') ? id : `mfl_${id}`))
        if (ids.length === 0) return
        const base = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
        const params = new URLSearchParams()
        ids.forEach(id => params.append('player_id', id))
        params.append('season', String(seasonYear || new Date().getFullYear()))
        const url = `${base}/api/v1/rankings/ecr?${params.toString()}`
        const { data } = await axios.get(url)
        const incoming = (data?.ecr || {})
        setEcrMap(incoming)
      } catch (e) {
        setEcrMap({})
      }
    }
    fetchECR()
  }, [rosterPlayers, seasonYear])

  // Fetch ADP Index for players on the roster
  useEffect(() => {
    const fetchADP = async () => {
      try {
        const ids = (rosterPlayers || [])
          .map((rp: any) => String(rp.id))
          .filter(Boolean)
          .map((id: string) => (id.startsWith('mfl_') ? id : `mfl_${id}`))
        if (ids.length === 0) return
        const base = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
        const params = new URLSearchParams()
        ids.forEach(id => params.append('player_id', id))
        params.append('season', String(seasonYear || new Date().getFullYear()))
        const url = `${base}/api/v1/rankings/adp/index?${params.toString()}`
        const { data } = await axios.get(url)
        const incoming = (data?.adp_index || {})
        setAdpMap(incoming)
      } catch (e) {
        setAdpMap({})
      }
    }
    fetchADP()
  }, [rosterPlayers, seasonYear])

  const playerMap = useMemo(() => {
    const map: Record<string, any> = {}
    ;(players || []).forEach((p: any) => { map[String(p.id)] = p })
    return map
  }, [players])

  if (!isClient) {
    return (
      <Layout>
        <div className="p-6 text-sm text-gray-600">Loading roster…</div>
      </Layout>
    )
  }

  if (!leagueId) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <h2 className="text-xl font-bold text-gray-900 mb-4">MFL Setup Required</h2>
            <p className="text-gray-600 mb-4">Set your MFL League ID in Settings to view your roster.</p>
            <a href="/settings" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Go to Settings</a>
          </div>
        </div>
      </Layout>
    )
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <LoadingSpinner />
        </div>
      </Layout>
    )
  }

  if (error) {
    return (
      <Layout>
        <ErrorMessage message={`There was an error loading your roster: ${error}`} onDismiss={() => window.location.reload()} />
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">My Roster</h1>
              <p className="mt-1 text-sm text-gray-500">{league?.name || 'Your League'}</p>
              <p className="text-xs text-gray-600 mt-1">Franchise: {myFranchiseId || 'Auto-detecting…'}</p>
              {rosterPlayers.length > 0 && (
                <p className="text-xs text-green-600 mt-1">✓ {rosterPlayers.length} players on roster</p>
              )}
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={async () => {
                  try {
                    setAdpRefreshing(true)
                    const base = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
                    const leagueCanonical = leagueId ? (String(leagueId).startsWith('mfl_') ? String(leagueId) : `mfl_${String(leagueId)}`) : ''
                    const url = `${base}/api/v1/rankings/adp/refresh?season=${encodeURIComponent(String(seasonYear || new Date().getFullYear()))}&league_id=${encodeURIComponent(leagueCanonical)}`
                    await axios.post(url)
                    // refetch ADP index afterwards
                    const ids = (rosterPlayers || [])
                      .map((rp: any) => String(rp.id))
                      .filter(Boolean)
                      .map((id: string) => (id.startsWith('mfl_') ? id : `mfl_${id}`))
                    if (ids.length) {
                      const params = new URLSearchParams()
                      ids.forEach(id => params.append('player_id', id))
                      params.append('season', String(seasonYear || new Date().getFullYear()))
                      const getUrl = `${base}/api/v1/rankings/adp/index?${params.toString()}`
                      const { data } = await axios.get(getUrl)
                      setAdpMap(data?.adp_index || {})
                    }
                  } finally {
                    setAdpRefreshing(false)
                  }
                }}
                className={`px-3 py-2 rounded text-white ${adpRefreshing ? 'bg-gray-400' : 'bg-indigo-600 hover:bg-indigo-700'}`}
                disabled={adpRefreshing}
              >
                {adpRefreshing ? 'Refreshing ADP…' : 'Refresh ADP Now'}
              </button>
              <RefreshButton contextKey="roster" />
            </div>
          </div>
        </div>

        {/* Franchise Selector (if not detected) */}
        {!myFranchiseId && league && Array.isArray((league as any).franchises) && (
          <div className="bg-white shadow rounded-lg p-4">
            <div className="flex items-end gap-3">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">Select Your Franchise</label>
                <select
                  value={pendingFranchise}
                  onChange={(e) => setPendingFranchise(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
                >
                  <option value="">Choose…</option>
                  {((league as any).franchises || []).map((f: any) => (
                    <option key={f.id} value={f.id}>{f.name || f.id}</option>
                  ))}
                </select>
              </div>
              <button
                onClick={() => { if (pendingFranchise) setMyFranchiseId(pendingFranchise) }}
                disabled={!pendingFranchise}
                className={`px-4 py-2 rounded text-white ${pendingFranchise ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-300'}`}
              >
                Set Franchise
              </button>
            </div>
          </div>
        )}

        {/* Roster Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Player</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pos</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Team</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {rosterPlayers.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="px-6 py-4 text-sm text-gray-600">No players on roster.</td>
                  </tr>
                ) : (
                  rosterPlayers.map((rp: any, idx: number) => {
                    const meta = playerMap[String(rp.id)]
                    return (
                      <tr key={idx}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{meta?.name || rp.id}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {(() => {
                            const pos = (meta?.position || '').toUpperCase()
                            return (
                              <Badge type="position" value={pos || '—'} />
                            )
                          })()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {(() => {
                            const label = meta?.team || 'FA'
                            return (
                              <Badge type="team" value={label} />
                            )
                          })()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <div className="flex flex-col gap-1">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              {rp.status || 'Active'}
                            </span>
{/* Current year (pre-season adjusted from last MFL season) */}
                            <div className="text-[11px] text-gray-600">
                              {(() => {
                                const rawId = String(rp.id)
                                const pid = rawId
                                const rawSalary = rp.salary ?? rp.contract_salary ?? rp.contractSalary ?? salaryMap[pid]?.salary
                                const rawYear = rp.contract_year ?? rp.contractYear ?? (rp.contract ? rp.contract.year : undefined) ?? salaryMap[pid]?.year
                                const curr = computeNextContract(rawSalary, rawYear)
                                if (!curr.nextYear || !curr.nextSalary) {
                                  // Fallback: show raw if available (convert thousands -> dollars)
                                  const salaryStr = rawSalary != null && rawSalary !== '' ? formatDollars(Number(rawSalary) * 1000) : '—'
                                  const yearStr = rawYear != null && rawYear !== '' ? String(rawYear) : '—'
                                  return <>This Yr: {yearStr} · Salary: {salaryStr}</>
                                }
                                return (
                                  <>
                                    This Yr: {curr.nextYear} · Salary: {formatDollars(curr.nextSalary)} {curr.mustFranchise && (<span className="ml-1 text-red-600 font-medium">(Must be franchised)</span>)}
                                  </>
                                )
                              })()}
                            </div>
                            {/* ADP Index (PPR) */}
                            <div className="text-[11px] text-gray-700">
                              {(() => {
                                const key = String(rp.id).startsWith('mfl_') ? String(rp.id) : `mfl_${String(rp.id)}`
                                const info = adpMap[key]
                                const rank = info?.overall_rank
                                return <>ADP Index (PPR): <span>{rank ? `#${rank}` : '—'}</span></>
                              })()}
                            </div>
                            {/* ECR display using React state (kept for later in-season use) */}
                            <div className="text-[11px] text-gray-600" data-ecr-player-id={(String(rp.id).startsWith('mfl_') ? String(rp.id) : `mfl_${String(rp.id)}`)}>
                              {(() => {
                                const key = String(rp.id).startsWith('mfl_') ? String(rp.id) : `mfl_${String(rp.id)}`
                                const info = ecrMap[key]
                                const val = info && Number.isFinite(info.consensus_rank as any) ? Math.round(Number(info.consensus_rank)) : null
                                return <>ECR: <span className="ecr-val">{val ? `#${val}` : '—'}</span></>
                              })()}
                            </div>
                          </div>
                        </td>
                      </tr>
                    )
                  })
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Layout>
  )
}

