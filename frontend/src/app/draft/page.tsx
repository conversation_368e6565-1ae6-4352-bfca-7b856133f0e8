'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import DraftBoard from '@/components/draft/DraftBoard'
import DraftTimer from '@/components/draft/DraftTimer'
import DraftRecommendation from '@/components/draft/DraftRecommendation'
import DraftSimulation from '@/components/draft/DraftSimulation'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import ErrorMessage from '@/components/ui/ErrorMessage'
import { api } from '@/lib/api'
import { DraftBoardData, DraftRecommendationData, DraftStrategy } from '@/types/draft'

export default function DraftPage() {
  const searchParams = useSearchParams()
  const leagueId = searchParams.get('league') || 'default-league'
  const franchiseId = searchParams.get('franchise') || 'default-franchise'

  const [draftBoard, setDraftBoard] = useState<DraftBoardData | null>(null)
  const [currentRecommendation, setCurrentRecommendation] = useState<DraftRecommendationData | null>(null)
  const [availablePlayers, setAvailablePlayers] = useState<string[]>([])
  const [currentPick, setCurrentPick] = useState(1)
  const [strategy, setStrategy] = useState<DraftStrategy>('VALUE_BASED')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDraftActive, setIsDraftActive] = useState(false)
  const [pickDeadline, setPickDeadline] = useState<Date | null>(null)
  const [showSimulation, setShowSimulation] = useState(false)

  // Load initial draft board
  const loadDraftBoard = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await api.post('/draft/board', {
        league_id: leagueId,
        season: 2024,
        strategy: strategy,
        force_refresh: false
      })
      
      setDraftBoard(response.data)
      setAvailablePlayers(response.data.overall_rankings)
      setError(null)
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load draft board')
    } finally {
      setIsLoading(false)
    }
  }, [leagueId, strategy])

  // Get current pick recommendation
  const getCurrentRecommendation = useCallback(async () => {
    if (!availablePlayers.length) return

    try {
      const response = await api.post('/draft/recommendation', {
        league_id: leagueId,
        franchise_id: franchiseId,
        available_players: availablePlayers,
        current_pick: currentPick,
        season: 2024,
        strategy: strategy
      })
      
      setCurrentRecommendation(response.data)
    } catch (err: any) {
      console.error('Failed to get recommendation:', err)
    }
  }, [leagueId, franchiseId, availablePlayers, currentPick, strategy])

  // Handle player being picked
  const handlePlayerPicked = async (playerId: string) => {
    try {
      // Update draft board with the pick
      const response = await api.put('/draft/board/update', {
        league_id: leagueId,
        picked_player_id: playerId,
        season: 2024
      })
      
      setDraftBoard(response.data)
      setAvailablePlayers(response.data.overall_rankings)
      setCurrentPick(prev => prev + 1)
      
      // Clear current recommendation to force refresh
      setCurrentRecommendation(null)
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to update draft board')
    }
  }

  // Handle strategy change
  const handleStrategyChange = (newStrategy: DraftStrategy) => {
    setStrategy(newStrategy)
    setDraftBoard(null)
    setCurrentRecommendation(null)
  }

  // Initialize draft room
  useEffect(() => {
    loadDraftBoard()
  }, [loadDraftBoard])

  // Get recommendation when board or pick changes
  useEffect(() => {
    if (draftBoard && availablePlayers && availablePlayers.length > 0) {
      getCurrentRecommendation()
    }
  }, [draftBoard, getCurrentRecommendation])

  // Simulate draft timer (in real implementation, this would come from draft service)
  useEffect(() => {
    if (isDraftActive) {
      const deadline = new Date()
      deadline.setMinutes(deadline.getMinutes() + 2) // 2 minute pick timer
      setPickDeadline(deadline)
    }
  }, [currentPick, isDraftActive])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Draft Room</h1>
              <p className="mt-2 text-gray-600">
                Pick {currentPick} • {strategy.replace('_', ' ')} Strategy
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Strategy Selector */}
              <select
                value={strategy}
                onChange={(e) => handleStrategyChange(e.target.value as DraftStrategy)}
                className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="VALUE_BASED">Value Based</option>
                <option value="POSITIONAL_NEED">Positional Need</option>
                <option value="BEST_AVAILABLE">Best Available</option>
                <option value="ZERO_RB">Zero RB</option>
                <option value="ROBUST_RB">Robust RB</option>
              </select>

              {/* Draft Controls */}
              <button
                onClick={() => setIsDraftActive(!isDraftActive)}
                className={`px-4 py-2 rounded-md font-medium ${
                  isDraftActive
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                {isDraftActive ? 'Pause Draft' : 'Start Draft'}
              </button>

              <button
                onClick={() => setShowSimulation(!showSimulation)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium"
              >
                {showSimulation ? 'Hide Simulation' : 'Run Simulation'}
              </button>
            </div>
          </div>

          {/* Draft Timer */}
          {isDraftActive && pickDeadline && (
            <div className="mt-4">
              <DraftTimer 
                deadline={pickDeadline}
                onTimeExpired={() => {
                  // Auto-pick logic would go here
                  console.log('Time expired for pick', currentPick)
                }}
              />
            </div>
          )}
        </div>

        {error && (
          <div className="mb-6">
            <ErrorMessage message={error} />
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Draft Board - Left Column */}
          <div className="lg:col-span-2">
            {draftBoard && (
              <DraftBoard
                draftBoard={draftBoard}
                onPlayerSelect={handlePlayerPicked}
                currentPick={currentPick}
                isDraftActive={isDraftActive}
              />
            )}
          </div>

          {/* Recommendations - Right Column */}
          <div className="space-y-6">
            {currentRecommendation && (
              <DraftRecommendation
                recommendation={currentRecommendation}
                onSelectPlayer={() => handlePlayerPicked(currentRecommendation.player_id)}
                isDraftActive={isDraftActive}
              />
            )}

            {/* Draft Simulation */}
            {showSimulation && (
              <DraftSimulation
                leagueId={leagueId}
                franchiseId={franchiseId}
                strategy={strategy}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}