import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { useSearchParams } from 'next/navigation'
import DraftPage from '../page'
import { api } from '@/lib/api'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { it } from 'node:test'
import { beforeEach } from 'node:test'
import { describe } from 'node:test'

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn()
}))

// Mock API
jest.mock('@/lib/api')
const mockedApi = api as jest.Mocked<typeof api>

// Mock child components
jest.mock('@/components/draft/DraftBoard', () => {
  return function MockDraftBoard({ draftBoard, onPlayerSelect, isDraftActive }: any) {
    if (!draftBoard) return null
    return (
      <div data-testid="draft-board">
        <button 
          onClick={() => onPlayerSelect('player1')}
          disabled={!isDraftActive}
        >
          Mock Draft Player
        </button>
      </div>
    )
  }
})

jest.mock('@/components/draft/DraftTimer', () => {
  return function MockDraftTimer({ onTimeExpired }: any) {
    return (
      <div data-testid="draft-timer">
        <button onClick={onTimeExpired}>Mock Time Expired</button>
      </div>
    )
  }
})

jest.mock('@/components/draft/DraftRecommendation', () => {
  return function MockDraftRecommendation({ recommendation, onSelectPlayer, isDraftActive }: any) {
    if (!recommendation) return null
    return (
      <div data-testid="draft-recommendation">
        <button 
          onClick={onSelectPlayer}
          disabled={!isDraftActive}
        >
          Mock Select Recommended Player
        </button>
      </div>
    )
  }
})

jest.mock('@/components/draft/DraftSimulation', () => {
  return function MockDraftSimulation() {
    return <div data-testid="draft-simulation">Mock Simulation</div>
  }
})

const mockDraftBoard = {
  tiers: [
    {
      tier_number: 1,
      position: 'RB',
      players: ['player1', 'player2'],
      min_value: 15.0,
      max_value: 18.5,
      avg_value: 16.75,
      tier_break_threshold: 2.0
    }
  ],
  overall_rankings: ['player1', 'player2', 'player3'],
  position_rankings: { RB: ['player1', 'player2'] },
  value_over_replacement: { player1: 18.5, player2: 15.0 },
  last_updated: '2024-01-01T12:00:00Z',
  strategy: 'VALUE_BASED'
}

const mockRecommendation = {
  player_id: 'player1',
  player_name: 'Test Player',
  position: 'RB',
  team: 'SF',
  projected_points: 285.5,
  value_over_replacement: 45.2,
  tier: 1,
  confidence: 0.85,
  rationale: 'Great player',
  alternatives: ['player2'],
  positional_need_score: 2.5,
  opportunity_cost: 8.3
}

describe('DraftPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(useSearchParams as jest.Mock).mockReturnValue({
      get: jest.fn((key: string) => {
        if (key === 'league') return 'test-league'
        if (key === 'franchise') return 'test-franchise'
        return null
      })
    })
  })

  it('renders draft room interface', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })

    render(<DraftPage />)

    await waitFor(() => {
      expect(screen.getByText('Draft Room')).toBeInTheDocument()
      expect(screen.getByText('Pick 1 • VALUE BASED Strategy')).toBeInTheDocument()
    })
  })

  it('loads draft board on mount', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })

    render(<DraftPage />)

    await waitFor(() => {
      expect(mockedApi.post).toHaveBeenCalledWith('/draft/board', {
        league_id: 'test-league',
        season: 2024,
        strategy: 'VALUE_BASED',
        force_refresh: false
      })
    })
  })

  it('gets recommendation after loading board', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })

    render(<DraftPage />)

    await waitFor(() => {
      expect(mockedApi.post).toHaveBeenCalledWith('/draft/recommendation', {
        league_id: 'test-league',
        franchise_id: 'test-franchise',
        available_players: ['player1', 'player2', 'player3'],
        current_pick: 1,
        season: 2024,
        strategy: 'VALUE_BASED'
      })
    })
  })

  it('handles strategy change', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })
    mockedApi.post.mockResolvedValueOnce({ data: { ...mockDraftBoard, strategy: 'ZERO_RB' } })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })

    render(<DraftPage />)

    await waitFor(() => {
      expect(screen.getByDisplayValue('Value Based')).toBeInTheDocument()
    })

    const strategySelect = screen.getByDisplayValue('Value Based')
    fireEvent.change(strategySelect, { target: { value: 'ZERO_RB' } })

    await waitFor(() => {
      expect(mockedApi.post).toHaveBeenCalledWith('/draft/board', {
        league_id: 'test-league',
        season: 2024,
        strategy: 'ZERO_RB',
        force_refresh: false
      })
    })
  })

  it('starts and pauses draft', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })

    render(<DraftPage />)

    await waitFor(() => {
      expect(screen.getByText('Start Draft')).toBeInTheDocument()
    })

    const draftButton = screen.getByText('Start Draft')
    fireEvent.click(draftButton)

    expect(screen.getByText('Pause Draft')).toBeInTheDocument()
    expect(screen.getByTestId('draft-timer')).toBeInTheDocument()
  })

  it('handles player selection and updates board', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })
    mockedApi.put.mockResolvedValueOnce({ 
      data: { 
        ...mockDraftBoard, 
        overall_rankings: ['player2', 'player3'] // player1 removed
      } 
    })

    render(<DraftPage />)

    await waitFor(() => {
      expect(screen.getByTestId('draft-board')).toBeInTheDocument()
    })

    const draftButton = screen.getByText('Mock Draft Player')
    fireEvent.click(draftButton)

    await waitFor(() => {
      expect(mockedApi.put).toHaveBeenCalledWith('/draft/board/update', {
        league_id: 'test-league',
        picked_player_id: 'player1',
        season: 2024
      })
    })
  })

  it('increments pick number after player selection', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })
    mockedApi.put.mockResolvedValueOnce({ data: mockDraftBoard })

    render(<DraftPage />)

    await waitFor(() => {
      expect(screen.getByText('Pick 1 • VALUE BASED Strategy')).toBeInTheDocument()
    })

    const draftButton = screen.getByText('Mock Draft Player')
    fireEvent.click(draftButton)

    await waitFor(() => {
      expect(screen.getByText('Pick 2 • VALUE BASED Strategy')).toBeInTheDocument()
    })
  })

  it('shows and hides simulation panel', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })

    render(<DraftPage />)

    await waitFor(() => {
      expect(screen.getByText('Run Simulation')).toBeInTheDocument()
    })

    // Simulation should be hidden initially
    expect(screen.queryByTestId('draft-simulation')).not.toBeInTheDocument()

    const simulationButton = screen.getByText('Run Simulation')
    fireEvent.click(simulationButton)

    expect(screen.getByTestId('draft-simulation')).toBeInTheDocument()
    expect(screen.getByText('Hide Simulation')).toBeInTheDocument()
  })

  it('handles API errors gracefully', async () => {
    mockedApi.post.mockRejectedValueOnce({
      response: { data: { detail: 'Failed to load draft board' } }
    })

    render(<DraftPage />)

    await waitFor(() => {
      expect(screen.getByText('Failed to load draft board')).toBeInTheDocument()
    }, { timeout: 3000 })
  })

  it('shows loading state initially', () => {
    mockedApi.post.mockImplementation(() => new Promise(() => {})) // Never resolves

    render(<DraftPage />)

    expect(screen.getByRole('status')).toBeInTheDocument() // Loading spinner
  })

  it('handles time expiration', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })

    render(<DraftPage />)

    // Start draft to show timer
    await waitFor(() => {
      const startButton = screen.getByText('Start Draft')
      fireEvent.click(startButton)
    })

    const timeExpiredButton = screen.getByText('Mock Time Expired')
    fireEvent.click(timeExpiredButton)

    // Should log time expiration (in real implementation would auto-pick)
    expect(console.log).toHaveBeenCalledWith('Time expired for pick', 1)
  })

  it('disables draft actions when draft is inactive', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })

    render(<DraftPage />)

    await waitFor(() => {
      const draftButton = screen.getByText('Mock Draft Player')
      const recommendButton = screen.getByText('Mock Select Recommended Player')
      
      expect(draftButton).toBeDisabled()
      expect(recommendButton).toBeDisabled()
    })
  })

  it('enables draft actions when draft is active', async () => {
    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })

    render(<DraftPage />)

    await waitFor(() => {
      const startButton = screen.getByText('Start Draft')
      fireEvent.click(startButton)
    })

    const draftButton = screen.getByText('Mock Draft Player')
    const recommendButton = screen.getByText('Mock Select Recommended Player')
    
    expect(draftButton).not.toBeDisabled()
    expect(recommendButton).not.toBeDisabled()
  })

  it('uses URL parameters for league and franchise', async () => {
    ;(useSearchParams as jest.Mock).mockReturnValue({
      get: jest.fn((key: string) => {
        if (key === 'league') return 'custom-league'
        if (key === 'franchise') return 'custom-franchise'
        return null
      })
    })

    mockedApi.post.mockResolvedValueOnce({ data: mockDraftBoard })
    mockedApi.post.mockResolvedValueOnce({ data: mockRecommendation })

    render(<DraftPage />)

    await waitFor(() => {
      expect(mockedApi.post).toHaveBeenCalledWith('/draft/board', {
        league_id: 'custom-league',
        season: 2024,
        strategy: 'VALUE_BASED',
        force_refresh: false
      })
    })
  })
})