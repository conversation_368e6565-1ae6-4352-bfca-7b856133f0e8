import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import TradesPage from '../page'

// Mock the trade components
jest.mock('@/components/trades/TradeAnalysis', () => {
  return function MockTradeAnalysis({ franchiseId, leagueId }: any) {
    return <div data-testid="trade-analysis">Trade Analysis - {franchiseId} - {leagueId}</div>
  }
})

jest.mock('@/components/trades/TradeProposal', () => {
  return function MockTradeProposal({ franchiseId, leagueId }: any) {
    return <div data-testid="trade-proposal">Trade Proposal - {franchiseId} - {leagueId}</div>
  }
})

jest.mock('@/components/trades/TeamNeedAnalysis', () => {
  return function MockTeamNeedAnalysis({ franchiseId, leagueId }: any) {
    return <div data-testid="team-need-analysis">Team Need Analysis - {franchiseId} - {leagueId}</div>
  }
})

jest.mock('@/components/trades/TradeHistory', () => {
  return function MockTradeHistory({ franchiseId, leagueId }: any) {
    return <div data-testid="trade-history">Trade History - {franchiseId} - {leagueId}</div>
  }
})

jest.mock('@/components/ui/LoadingSpinner', () => {
  return function MockLoadingSpinner() {
    return <div data-testid="loading-spinner">Loading...</div>
  }
})

jest.mock('@/components/ui/ErrorMessage', () => {
  return function MockErrorMessage({ message }: { message: string }) {
    return <div data-testid="error-message">{message}</div>
  }
})

describe('TradesPage', () => {
  it('renders loading state initially', () => {
    render(<TradesPage />)
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('renders trade analysis page with navigation tabs', async () => {
    render(<TradesPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Analysis')).toBeInTheDocument()
    })

    // Check all tabs are present
    expect(screen.getByText('Trade Suggestions')).toBeInTheDocument()
    expect(screen.getByText('Evaluate Trade')).toBeInTheDocument()
    expect(screen.getByText('Team Needs')).toBeInTheDocument()
    expect(screen.getByText('Trade History')).toBeInTheDocument()

    // Check default tab is active (Trade Suggestions)
    expect(screen.getByTestId('trade-analysis')).toBeInTheDocument()
  })

  it('switches between tabs correctly', async () => {
    render(<TradesPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Analysis')).toBeInTheDocument()
    })

    // Click on Evaluate Trade tab
    fireEvent.click(screen.getByText('Evaluate Trade'))
    expect(screen.getByTestId('trade-proposal')).toBeInTheDocument()
    expect(screen.queryByTestId('trade-analysis')).not.toBeInTheDocument()

    // Click on Team Needs tab
    fireEvent.click(screen.getByText('Team Needs'))
    expect(screen.getByTestId('team-need-analysis')).toBeInTheDocument()
    expect(screen.queryByTestId('trade-proposal')).not.toBeInTheDocument()

    // Click on Trade History tab
    fireEvent.click(screen.getByText('Trade History'))
    expect(screen.getByTestId('trade-history')).toBeInTheDocument()
    expect(screen.queryByTestId('team-need-analysis')).not.toBeInTheDocument()

    // Click back to Trade Suggestions
    fireEvent.click(screen.getByText('Trade Suggestions'))
    expect(screen.getByTestId('trade-analysis')).toBeInTheDocument()
    expect(screen.queryByTestId('trade-history')).not.toBeInTheDocument()
  })

  it('passes correct props to components', async () => {
    render(<TradesPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Analysis')).toBeInTheDocument()
    })

    // Check that components receive correct franchise and league IDs
    expect(screen.getByText('Trade Analysis - franchise_1 - league_1')).toBeInTheDocument()

    // Switch to other tabs and verify props
    fireEvent.click(screen.getByText('Evaluate Trade'))
    expect(screen.getByText('Trade Proposal - franchise_1 - league_1')).toBeInTheDocument()

    fireEvent.click(screen.getByText('Team Needs'))
    expect(screen.getByText('Team Need Analysis - franchise_1 - league_1')).toBeInTheDocument()

    fireEvent.click(screen.getByText('Trade History'))
    expect(screen.getByText('Trade History - franchise_1 - league_1')).toBeInTheDocument()
  })

  it('displays correct tab icons', async () => {
    render(<TradesPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Analysis')).toBeInTheDocument()
    })

    // Check that tab icons are displayed
    const tabs = screen.getAllByRole('button')
    expect(tabs.some(tab => tab.textContent?.includes('🔄'))).toBe(true) // Trade Suggestions
    expect(tabs.some(tab => tab.textContent?.includes('⚖️'))).toBe(true) // Evaluate Trade
    expect(tabs.some(tab => tab.textContent?.includes('📊'))).toBe(true) // Team Needs
    expect(tabs.some(tab => tab.textContent?.includes('📋'))).toBe(true) // Trade History
  })

  it('applies correct CSS classes for active and inactive tabs', async () => {
    render(<TradesPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Trade Analysis')).toBeInTheDocument()
    })

    const tradeSuggestionsTab = screen.getByText('Trade Suggestions').closest('button')
    const evaluateTradeTab = screen.getByText('Evaluate Trade').closest('button')

    // Initially, Trade Suggestions should be active
    expect(tradeSuggestionsTab).toHaveClass('border-blue-500', 'text-blue-600')
    expect(evaluateTradeTab).toHaveClass('border-transparent', 'text-gray-500')

    // Click on Evaluate Trade tab
    fireEvent.click(screen.getByText('Evaluate Trade'))

    // Now Evaluate Trade should be active
    expect(evaluateTradeTab).toHaveClass('border-blue-500', 'text-blue-600')
    expect(tradeSuggestionsTab).toHaveClass('border-transparent', 'text-gray-500')
  })
})