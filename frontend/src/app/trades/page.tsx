'use client'

import { useEffect, useMemo, useState } from 'react'
import Layout from '@/components/Layout'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { useMFLContext } from '@/context/MFLContext'

export default function TradesPage() {
  const [activeTab, setActiveTab] = useState<'analysis' | 'proposal' | 'needs' | 'history'>('analysis')
  const { loading, error, league, standings, transactions, loadTransactions } = useMFLContext()

  useEffect(() => {
    // Load recent transactions (last 14 days) once mounted
    loadTransactions(14)
  }, [loadTransactions])

  const recentTrades = useMemo(() => {
    // Filter transactions for type 'TRADE' if present
    return (transactions || []).filter(t => (t.type || '').toLowerCase().includes('trade')).slice(0, 10)
  }, [transactions])

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <LoadingSpinner />
        </div>
      </Layout>
    )
  }

  if (error) {
    return (
      <Layout>
        <ErrorMessage 
          message={`There was an error loading trade data: ${error}`}
          onDismiss={() => window.location.reload()}
        />
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900">Trade Analysis</h1>
          <p className="mt-1 text-sm text-gray-500">
            {league ? `${league.name} • Recent league transactions and analysis` : 'Analyze trade opportunities and history'}
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {[
                { id: 'analysis', label: 'Recent Trades', icon: '🔄' },
                { id: 'proposal', label: 'Evaluate Trade', icon: '⚖️' },
                { id: 'needs', label: 'Team Needs', icon: '📊' },
                { id: 'history', label: 'Trade History', icon: '📋' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'analysis' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Recent Trades
                </h3>
                {recentTrades.length === 0 ? (
                  <div className="text-sm text-gray-600">No recent trades found in the last 14 days.</div>
                ) : (
                <div className="space-y-4">
                  {recentTrades.map((t, idx) => (
                    <div key={idx} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-medium text-gray-900">Trade</div>
                        <div className="text-xs text-gray-500">{t.timestamp ? new Date(parseInt(t.timestamp) * 1000).toLocaleString() : 'Unknown time'}</div>
                      </div>
                      <div className="text-sm text-gray-700">
                        {t.comments || 'Trade details not provided'}
                      </div>
                    </div>
                  ))}
                </div>
                )}
              </div>
            )}

            {activeTab === 'proposal' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Evaluate Trade Proposal
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Players You Give
                    </label>
                    <textarea
                      placeholder="Enter player names, one per line"
                      rows={4}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Players You Receive
                    </label>
                    <textarea
                      placeholder="Enter player names, one per line"
                      rows={4}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="mt-4">
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Analyze Trade
                  </button>
                </div>
                
                <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Trade Analysis Results</h4>
                  <p className="text-sm text-gray-600">
                    Enter players above and click "Analyze Trade" to see detailed analysis including fairness score, 
                    positional impact, and long-term value assessment.
                  </p>
                </div>
              </div>
            )}

            {activeTab === 'needs' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Team Needs Analysis
                </h3>
                <div className="text-sm text-gray-600">
                  Team needs analysis will be powered by league rosters and projections in a follow-up pass.
                </div>
              </div>
            )}

            {activeTab === 'history' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Trade History
                </h3>
                <div className="space-y-4">
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">Week 8 Trade</span>
                      <span className="text-sm text-gray-500">Nov 15, 2024</span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Traded Away:</span>
                        <div className="text-gray-900">Stefon Diggs, Tony Pollard</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Received:</span>
                        <div className="text-gray-900">Davante Adams</div>
                      </div>
                    </div>
                    <div className="mt-2 text-sm text-gray-600">
                      Trade Partner: Team Gamma
                    </div>
                  </div>
                  
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">Week 5 Trade</span>
                      <span className="text-sm text-gray-500">Oct 25, 2024</span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Traded Away:</span>
                        <div className="text-gray-900">Lamar Jackson</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Received:</span>
                        <div className="text-gray-900">Josh Allen, 2024 2nd Round Pick</div>
                      </div>
                    </div>
                    <div className="mt-2 text-sm text-gray-600">
                      Trade Partner: Team Delta
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}
