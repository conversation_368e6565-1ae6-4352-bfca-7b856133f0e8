'use client'

import { useState, useEffect } from 'react'
import { TradeAnalysis } from '@/components/trades/TradeAnalysis'
import { TradeProposal } from '@/components/trades/TradeProposal'
import { TeamNeedAnalysis } from '@/components/trades/TeamNeedAnalysis'
import { TradeHistory } from '@/components/trades/TradeHistory'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'

export default function TradesPage() {
  const [activeTab, setActiveTab] = useState<'analysis' | 'proposal' | 'needs' | 'history'>('analysis')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [franchiseId, setFranchiseId] = useState<string>('')
  const [leagueId, setLeagueId] = useState<string>('')

  useEffect(() => {
    // In a real app, get these from context or URL params
    setFranchiseId('franchise_1')
    setLeagueId('league_1')
    setLoading(false)
  }, [])

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return <ErrorMessage message={error} />
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Trade Analysis</h1>
        <p className="text-gray-600">
          Analyze trade opportunities, evaluate proposals, and track your trading history
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'analysis', label: 'Trade Suggestions', icon: '🔄' },
            { id: 'proposal', label: 'Evaluate Trade', icon: '⚖️' },
            { id: 'needs', label: 'Team Needs', icon: '📊' },
            { id: 'history', label: 'Trade History', icon: '📋' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'analysis' && (
          <TradeAnalysis franchiseId={franchiseId} leagueId={leagueId} />
        )}
        {activeTab === 'proposal' && (
          <TradeProposal franchiseId={franchiseId} leagueId={leagueId} />
        )}
        {activeTab === 'needs' && (
          <TeamNeedAnalysis franchiseId={franchiseId} leagueId={leagueId} />
        )}
        {activeTab === 'history' && (
          <TradeHistory franchiseId={franchiseId} leagueId={leagueId} />
        )}
      </div>
    </div>
  )
}