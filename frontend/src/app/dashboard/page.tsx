'use client'

import { useState, useEffect } from 'react'
import { useQuery } from 'react-query'
import Layout from '@/components/Layout'
import DashboardHeader from '@/components/dashboard/DashboardHeader'
import LeagueOverview from '@/components/dashboard/LeagueOverview'
import UpcomingDeadlines from '@/components/dashboard/UpcomingDeadlines'
import RecentRecommendations from '@/components/dashboard/RecentRecommendations'
import AlertsSummary from '@/components/dashboard/AlertsSummary'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import ErrorMessage from '@/components/ui/ErrorMessage'
import { api } from '@/lib/api'
import { League, Franchise, Recommendation } from '@/types'

interface DashboardData {
  leagues: League[]
  franchises: Franchise[]
  recommendations: Recommendation[]
  alertsSummary: {
    urgent: number
    high: number
    medium: number
    low: number
    total: number
  }
}

export default function DashboardPage() {
  const [selectedLeague, setSelectedLeague] = useState<string | null>(null)
  const [selectedFranchise, setSelectedFranchise] = useState<string | null>(null)

  // Fetch dashboard data
  const { data: dashboardData, isLoading, error, refetch } = useQuery<DashboardData>(
    ['dashboard', selectedLeague, selectedFranchise],
    async () => {
      const [leaguesRes, franchisesRes, recommendationsRes, alertsRes] = await Promise.all([
        api.get('/leagues/'),
        api.get('/franchises/', { 
          params: selectedLeague ? { league_id: selectedLeague } : {} 
        }),
        api.get('/recommendations/', { 
          params: {
            ...(selectedLeague && { league_id: selectedLeague }),
            ...(selectedFranchise && { franchise_id: selectedFranchise }),
            limit: 10
          }
        }),
        api.get('/alerts/summary', { 
          params: {
            ...(selectedLeague && { league_id: selectedLeague }),
            ...(selectedFranchise && { franchise_id: selectedFranchise })
          }
        })
      ])

      return {
        leagues: leaguesRes.data,
        franchises: franchisesRes.data,
        recommendations: recommendationsRes.data,
        alertsSummary: alertsRes.data
      }
    },
    {
      refetchInterval: 30000, // Refetch every 30 seconds
      staleTime: 10000 // Consider data stale after 10 seconds
    }
  )

  // Set default selections when data loads
  useEffect(() => {
    if (dashboardData?.leagues.length && !selectedLeague) {
      setSelectedLeague(dashboardData.leagues[0].id)
    }
  }, [dashboardData?.leagues, selectedLeague])

  useEffect(() => {
    if (dashboardData?.franchises.length && !selectedFranchise && selectedLeague) {
      const leagueFranchises = dashboardData.franchises.filter(f => f.league_id === selectedLeague)
      if (leagueFranchises.length > 0) {
        setSelectedFranchise(leagueFranchises[0].id)
      }
    }
  }, [dashboardData?.franchises, selectedFranchise, selectedLeague])

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <LoadingSpinner size="large" />
        </div>
      </Layout>
    )
  }

  if (error) {
    return (
      <Layout>
        <ErrorMessage 
          title="Failed to load dashboard"
          message="There was an error loading your dashboard data. Please try again."
          onRetry={() => refetch()}
        />
      </Layout>
    )
  }

  const currentLeague = dashboardData?.leagues.find(l => l.id === selectedLeague)
  const currentFranchise = dashboardData?.franchises.find(f => f.id === selectedFranchise)

  return (
    <Layout>
      <div className="space-y-6">
        <DashboardHeader 
          leagues={dashboardData?.leagues || []}
          franchises={dashboardData?.franchises || []}
          selectedLeague={selectedLeague}
          selectedFranchise={selectedFranchise}
          onLeagueChange={setSelectedLeague}
          onFranchiseChange={setSelectedFranchise}
        />

        {currentLeague && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main content area */}
            <div className="lg:col-span-2 space-y-6">
              <LeagueOverview 
                league={currentLeague}
                franchise={currentFranchise}
              />
              
              <RecentRecommendations 
                recommendations={dashboardData?.recommendations || []}
                isLoading={isLoading}
              />
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <AlertsSummary 
                alertsSummary={dashboardData?.alertsSummary}
                isLoading={isLoading}
              />
              
              <UpcomingDeadlines 
                leagueId={selectedLeague}
                franchiseId={selectedFranchise}
              />
            </div>
          </div>
        )}

        {!currentLeague && dashboardData?.leagues.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No leagues found
            </h3>
            <p className="text-gray-600 mb-4">
              Get started by creating your first league or importing from MFL.
            </p>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
              Create League
            </button>
          </div>
        )}
      </div>
    </Layout>
  )
}