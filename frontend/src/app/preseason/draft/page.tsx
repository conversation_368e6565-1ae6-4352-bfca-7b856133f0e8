'use client'

import { useEffect, useMemo, useRef, useState } from 'react'
import Layout from '@/components/Layout'
import { useMFLContext } from '@/context/MFLContext'
import { exportCsv } from '@/lib/csv'
import { getKeepers } from '@/lib/preseason-api'
import Badge from '@/components/ui/Badge'
import { MasterPlayersProvider, useMasterPlayers } from '@/context/MasterPlayersContext'

interface ADPRow {
  player_id: string
  name: string
  position: string
  nfl_team: string
  adp_index: { adp: number | null }
  sources: { MFL: { adp: number | null }, FFC: { adp: number | null } }
}

export default function PreseasonDraftPage() {
  return (
    <MasterPlayersProvider>
      <DraftInner />
    </MasterPlayersProvider>
  )
}

function DraftInner() {
  const { leagueId, initialize, refreshLeague } = useMFLContext() as any
  const { items: masterItems, lastUpdated, loading: masterLoading, keptIds } = useMasterPlayers()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [available, setAvailable] = useState<ADPRow[]>([])
  const [isClient, setIsClient] = useState(false)
  const [sort, setSort] = useState<'adp_index' | 'mfl' | 'ffc'>('adp_index')
  const [debugInfo, setDebugInfo] = useState<{loaded:number; kept:number} | null>(null)
  const [position, setPosition] = useState<string>('')
  const [team, setTeam] = useState<string>('')
  const [q, setQ] = useState<string>('')

  useEffect(() => { setIsClient(true) }, [])

  const didInit = useRef(false)
  useEffect(() => {
    const load = async () => {
      try {
        if (!leagueId || masterLoading) return
        setLoading(true)
        if (!didInit.current) {
          try { await initialize() } catch {}
          try { await refreshLeague() } catch {}
          didInit.current = true
        }
        // Use keptIds from master context (preloaded from preseason store)
        const itemsSource: ADPRow[] = (masterItems || []) as any
        const items: ADPRow[] = itemsSource.filter((row) => !keptIds.has(row.player_id))
        setAvailable(items)
        setDebugInfo({ loaded: itemsSource.length, kept: keptIds.size })
      } catch (e: any) {
        setError(e?.message || 'Failed to load draft pool')
      } finally {
        setLoading(false)
      }
    }
    load()
  }, [leagueId, sort, initialize, refreshLeague, masterItems, masterLoading])

  const sortedAndFiltered = useMemo(() => {
    const getKey = (row: ADPRow) => {
      if (sort === 'mfl') return row.sources?.MFL?.adp ?? 1e9
      if (sort === 'ffc') return row.sources?.FFC?.adp ?? 1e9
      return row.adp_index?.adp ?? 1e9
    }
    return available
      .slice()
      .sort((a, b) => (getKey(a) as number) - (getKey(b) as number))
      .filter(row => {
        if (position && String(row.position).toUpperCase() !== position.toUpperCase()) return false
        if (team && String(row.nfl_team || '').toUpperCase() !== team.toUpperCase()) return false
        if (q) {
          const s = q.toLowerCase()
          const name = (row.name || '').toLowerCase()
          if (!name.includes(s)) return false
        }
        return true
      })
  }, [available, position, team, q, sort])

  const handleExport = () => {
    const rows = sortedAndFiltered.map(row => ({
      player_id: row.player_id,
      name: row.name,
      position: row.position,
      team: row.nfl_team,
      adp_index: row.adp_index?.adp ?? '',
      mfl_adp: row.sources?.MFL?.adp ?? '',
      ffc_adp: row.sources?.FFC?.adp ?? '',
    }))
    exportCsv('draft_available.csv', rows)
  }

  if (!isClient) {
    return (
      <Layout>
        <div className="p-6 text-sm text-gray-600">Loading…</div>
      </Layout>
    )
  }

  if (!leagueId) {
    return (
      <Layout>
        <div className="p-6 text-sm text-gray-600">Set your MFL League ID in Settings to build your draft pool.</div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Draft Prep – Available Players</h1>
              <p className="mt-1 text-sm text-gray-500">Excludes players marked as kept on the Keeper Selections page.</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-xs text-gray-500 mr-2">{lastUpdated ? `Master updated ${new Date(lastUpdated).toLocaleString()}` : ''}</div>
              <button className="px-3 py-2 rounded bg-green-600 text-white" onClick={handleExport}>Export CSV</button>
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">{debugInfo ? `Loaded ${debugInfo.loaded} ADP rows • Excluded ${debugInfo.kept} kept` : ''}</div>
          <div className="mt-4 flex flex-wrap gap-3 text-sm">
            <div>
              <label className="block text-xs text-gray-600">Sort</label>
              <select value={sort} onChange={(e) => setSort(e.target.value as any)} className="px-2 py-1 border rounded">
                <option value="adp_index">ADP Index</option>
                <option value="mfl">MFL ADP</option>
                <option value="ffc">FFC ADP</option>
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-600">Position</label>
              <select value={position} onChange={(e) => setPosition(e.target.value)} className="px-2 py-1 border rounded">
                <option value="">All</option>
                <option value="QB">QB</option>
                <option value="RB">RB</option>
                <option value="WR">WR</option>
                <option value="TE">TE</option>
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-600">NFL Team</label>
              <input value={team} onChange={(e) => setTeam(e.target.value.toUpperCase())} className="px-2 py-1 border rounded" placeholder="e.g. SF" />
            </div>
            <div>
              <label className="block text-xs text-gray-600">Search</label>
              <input value={q} onChange={(e) => setQ(e.target.value)} className="px-2 py-1 border rounded" placeholder="Player name" />
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg overflow-hidden">
          {(loading || masterLoading) ? (
            <div className="p-6 text-sm text-gray-600">Building draft pool…</div>
          ) : error ? (
            <div className="p-6 text-sm text-red-600">{error}</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Player</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Pos</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">NFL</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">ADP Index</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">MFL ADP</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">FFC ADP</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sortedAndFiltered.length === 0 ? (
                    <tr><td colSpan={6} className="px-4 py-6 text-center text-sm text-gray-600">No players match filters.</td></tr>
                  ) : sortedAndFiltered.map(row => (
                    <tr key={row.player_id}>
                      <td className="px-4 py-2 text-sm text-gray-900">{row.name}</td>
                      <td className="px-4 py-2 text-sm"><Badge type="position" value={row.position} /></td>
                      <td className="px-4 py-2 text-sm"><Badge type="team" value={row.nfl_team || 'FA'} /></td>
                      <td className="px-4 py-2 text-sm">{row.adp_index?.adp ?? '—'}</td>
                      <td className="px-4 py-2 text-sm">{row.sources?.MFL?.adp ?? '—'}</td>
                      <td className="px-4 py-2 text-sm">{row.sources?.FFC?.adp ?? '—'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </Layout>
  )
}

