"use client"

import { useEffect, useMemo, useRef, useState } from 'react'
import Layout from '@/components/Layout'
import { useMFLContext } from '@/context/MFLContext'
import { exportCsv } from '@/lib/csv'
import { getKeepers, saveKeepers, KeeperSelection } from '@/lib/preseason-api'
import Badge from '@/components/ui/Badge'
import RefreshButton from '@/components/RefreshButton'

export default function PreseasonKeepersPage() {
const { leagueId, league, rosters, players, initialize, refreshLeague } = useMFLContext() as any
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selections, setSelections] = useState<Record<string, Set<string>>>({}) // franchise_id -> Set<player_id>
  const [isClient, setIsClient] = useState(false)

  const playerMap = useMemo(() => {
    const m: Record<string, any> = {}
    ;(players || []).forEach((p: any) => { m[String(p.id)] = p })
    return m
  }, [players])

  const franchises: any[] = useMemo(() => {
    return (league?.franchises || [])
  }, [league])

  const POS_ORDER = useMemo(() => {
    const order: Record<string, number> = { QB: 1, RB: 2, WR: 3, TE: 4, K: 5, DEF: 6, DL: 7, LB: 8, DB: 9 }
    return (pos?: string) => order[(pos || '').toUpperCase()] ?? 99
  }, [])

useEffect(() => { setIsClient(true) }, [])

// Load selections once when leagueId is present; avoid repeated refreshes in dev strict mode
const loadedRef = useRef<string | null>(null)
useEffect(() => {
  const run = async () => {
    if (!leagueId) return
    if (loadedRef.current === leagueId) return
    loadedRef.current = leagueId
    try {
      setLoading(true)
      const resp = await getKeepers(leagueId.startsWith('mfl_') ? leagueId : `mfl_${leagueId}`)
      const map: Record<string, Set<string>> = {}
      for (const s of resp.selections || []) {
        const fr = String(s.franchise_id)
        const pid = String(s.player_id)
        if (!map[fr]) map[fr] = new Set<string>()
        map[fr].add(pid)
      }
      setSelections(map)
    } catch (e: any) {
      setError(e?.message || 'Failed to load selections')
    } finally {
      setLoading(false)
    }
  }
  run()
}, [leagueId])

  const rosterByFranchise: Record<string, any[]> = useMemo(() => {
    const out: Record<string, any[]> = {}
    ;(rosters || []).forEach((r: any) => {
      const fr = String(r.franchise_id || r.id || r.franchise)
      let list: any[] = []
      if (r.players && r.players.player) {
        list = Array.isArray(r.players.player) ? r.players.player : [r.players.player]
      } else if (Array.isArray(r.players)) {
        list = r.players
      } else if (r.player) {
        list = Array.isArray(r.player) ? r.player : [r.player]
      }
      out[fr] = list
    })
    return out
  }, [rosters])

  const toggle = (franchiseId: string, playerId: string) => {
    setSelections(prev => {
      const next = { ...prev }
      const set = new Set<string>(prev[franchiseId] || [])
      if (set.has(playerId)) set.delete(playerId)
      else set.add(playerId)
      next[franchiseId] = set
      return next
    })
  }

  const handleSave = async () => {
    try {
      setLoading(true)
      if (!leagueId) return
      const selectionsList: KeeperSelection[] = []
      for (const [fr, set] of Object.entries(selections)) {
        set.forEach(pid => selectionsList.push({ franchise_id: fr, player_id: pid }))
      }
      await saveKeepers(leagueId.startsWith('mfl_') ? leagueId : `mfl_${leagueId}`, selectionsList)
    } catch (e: any) {
      setError(e?.message || 'Failed to save selections')
    } finally {
      setLoading(false)
    }
  }

  const handleClearAll = () => {
    setSelections({})
  }

  const handleExport = () => {
    const rows: any[] = []
    for (const [fr, set] of Object.entries(selections)) {
      const franchise = (franchises || []).find((f: any) => String(f.id) === String(fr))
      set.forEach(pid => {
        const p = playerMap[pid]
        rows.push({
          franchise_id: fr,
          franchise_name: franchise?.name || fr,
          player_id: pid,
          player_name: p?.name,
          position: p?.position,
          team: p?.team,
        })
      })
    }
    exportCsv('keepers.csv', rows)
  }

  if (!isClient) {
    return (
      <Layout>
        <div className="p-6 text-sm text-gray-600">Loading…</div>
      </Layout>
    )
  }

  if (!leagueId) {
    return (
      <Layout>
        <div className="p-6 text-sm text-gray-600">Set your MFL League ID in Settings to manage preseason keepers.</div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Keeper Selections</h1>
              <p className="mt-1 text-sm text-gray-500">{league?.name || leagueId}</p>
            </div>
<div className="flex items-center gap-2">
              <RefreshButton contextKey="preseason-keepers" />
              <button className="px-3 py-2 rounded bg-gray-200 text-gray-800" onClick={handleClearAll}>Clear All</button>
              <button className="px-3 py-2 rounded bg-blue-600 text-white" onClick={handleSave} disabled={loading}>{loading ? 'Saving…' : 'Save'}</button>
              <button className="px-3 py-2 rounded bg-green-600 text-white" onClick={handleExport}>Export CSV</button>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {(franchises || []).map((f: any) => {
            const frId = String(f.id)
            const roster = rosterByFranchise[frId] || []
            const kept = selections[frId] || new Set<string>()
            // Count kept by position for warnings
            const posCounts: Record<string, number> = {}
            kept.forEach((pid) => {
              const meta = playerMap[pid]
              const p = String(meta?.position || '').toUpperCase()
              posCounts[p] = (posCounts[p] || 0) + 1
            })
            const overLimit = Object.entries(posCounts).filter(([, c]) => c > 2)
            // Sort roster by position order, then name
            const sortedRoster = roster.slice().sort((a: any, b: any) => {
              const ma = playerMap[String(a.id)]
              const mb = playerMap[String(b.id)]
              const pa = POS_ORDER(ma?.position)
              const pb = POS_ORDER(mb?.position)
              if (pa !== pb) return pa - pb
              const na = String(ma?.name || '')
              const nb = String(mb?.name || '')
              return na.localeCompare(nb)
            })
            return (
              <div key={frId} className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b flex items-center justify-between">
                  <div>
                    <div className="text-lg font-medium text-gray-900">{f.name || frId}</div>
                    <div className="text-xs text-gray-500">Auto-sorted by position • Limit 2 keepers per position</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-600">Kept: {kept.size}</div>
                    {overLimit.length > 0 && (
                      <div className="text-xs text-red-600">Over limit: {overLimit.map(([p,c]) => `${p}(${c})`).join(', ')}</div>
                    )}
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Keep</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Player</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pos</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">NFL</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {roster.length === 0 ? (
                        <tr><td colSpan={4} className="px-6 py-4 text-sm text-gray-600">No players</td></tr>
                      ) : sortedRoster.map((rp: any, idx: number) => {
                        const pid = String(rp.id)
                        const meta = playerMap[pid]
                        const checked = kept.has(pid)
                        const pos = String(meta?.position || '').toUpperCase()
                        const posAtLimit = (posCounts[pos] || 0) >= 2 && !checked
                        return (
                          <tr key={idx}>
                            <td className="px-6 py-3">
                              <input type="checkbox" checked={checked} onChange={() => toggle(frId, pid)} />
                              {posAtLimit && (
                                <span className="ml-2 text-xs text-orange-600">limit reached</span>
                              )}
                            </td>
                            <td className="px-6 py-3 text-sm font-medium text-gray-900">{meta?.name || pid}</td>
                            <td className="px-6 py-3 text-sm"><Badge type="position" value={pos} /></td>
                            <td className="px-6 py-3 text-sm"><Badge type="team" value={meta?.team || 'FA'} /></td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </Layout>
  )
}

