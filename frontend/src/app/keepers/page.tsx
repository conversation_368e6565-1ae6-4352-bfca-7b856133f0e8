'use client'

import { useState, useEffect } from 'react'
import { useQuery } from 'react-query'
import Layout from '@/components/Layout'
import KeeperSelection from '@/components/keepers/KeeperSelection'
import ValueAnalysis from '@/components/keepers/ValueAnalysis'
import ScenarioComparison from '@/components/keepers/ScenarioComparison'
import DeadlineCountdown from '@/components/keepers/DeadlineCountdown'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import ErrorMessage from '@/components/ui/ErrorMessage'
import { keeperApi } from '@/lib/keeper-api'

export default function KeepersPage() {
  const [selectedFranchiseId, setSelectedFranchiseId] = useState<string>('')
  const [selectedScenario, setSelectedScenario] = useState<string>('Optimal')
  const [keeperDeadline] = useState<Date>(new Date('2024-08-30T23:59:59'))

  // Get keeper candidates
  const {
    data: candidates,
    isLoading: candidatesLoading,
    error: candidatesError,
    refetch: refetchCandidates
  } = useQuery(
    ['keeper-candidates', selectedFranchiseId],
    () => keeperApi.getKeeperCandidates(selectedFranchiseId),
    {
      enabled: !!selectedFranchiseId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  )

  // Get keeper scenarios
  const {
    data: scenarios,
    isLoading: scenariosLoading,
    error: scenariosError,
    refetch: refetchScenarios
  } = useQuery(
    ['keeper-scenarios', selectedFranchiseId],
    () => keeperApi.optimizeKeepers(selectedFranchiseId),
    {
      enabled: !!selectedFranchiseId,
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  )

  // Set default franchise ID (in a real app, this would come from user context)
  useEffect(() => {
    if (!selectedFranchiseId) {
      setSelectedFranchiseId('franchise_1') // Default franchise
    }
  }, [selectedFranchiseId])

  const handleRefreshData = () => {
    refetchCandidates()
    refetchScenarios()
  }

  const isLoading = candidatesLoading || scenariosLoading
  const error = candidatesError || scenariosError

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Keeper Management</h1>
              <p className="mt-1 text-sm text-gray-500">
                Optimize your keeper selections with AI-powered value analysis
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <DeadlineCountdown deadline={keeperDeadline} />
              <button
                onClick={handleRefreshData}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Refresh Data
              </button>
            </div>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <ErrorMessage 
            message="Failed to load keeper data. Please try refreshing the page." 
          />
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center py-12">
            <LoadingSpinner />
          </div>
        )}

        {/* Main Content */}
        {!isLoading && !error && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Keeper Selection */}
            <div className="space-y-6">
              <KeeperSelection
                candidates={candidates || []}
                onSelectionChange={() => {
                  // Trigger scenario recalculation if needed
                  refetchScenarios()
                }}
              />
              
              <ValueAnalysis
                candidates={candidates || []}
                selectedScenario={scenarios?.find(s => s.scenario_name === selectedScenario)}
              />
            </div>

            {/* Right Column - Scenario Comparison */}
            <div>
              <ScenarioComparison
                scenarios={scenarios || []}
                selectedScenario={selectedScenario}
                onScenarioSelect={setSelectedScenario}
              />
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}