import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { QueryClient, QueryClientProvider } from 'react-query'
import KeepersPage from '../page'
import { keeperApi } from '@/lib/keeper-api'

// Mock the keeper API
jest.mock('@/lib/keeper-api', () => ({
  keeperApi: {
    getKeeperCandidates: jest.fn(),
    optimizeKeepers: jest.fn(),
  }
}))

// Mock the Layout component
jest.mock('@/components/Layout', () => {
  return function MockLayout({ children }: { children: React.ReactNode }) {
    return <div data-testid="layout">{children}</div>
  }
})

// Mock the child components
jest.mock('@/components/keepers/KeeperSelection', () => {
  return function MockKeeperSelection({ candidates, onSelectionChange }: any) {
    return (
      <div data-testid="keeper-selection">
        <div>Keeper Selection Component</div>
        <div>Candidates: {candidates.length}</div>
        <button onClick={() => onSelectionChange([])}>Mock Selection Change</button>
      </div>
    )
  }
})

jest.mock('@/components/keepers/ValueAnalysis', () => {
  return function MockValueAnalysis({ candidates, selectedScenario }: any) {
    return (
      <div data-testid="value-analysis">
        <div>Value Analysis Component</div>
        <div>Candidates: {candidates.length}</div>
        <div>Scenario: {selectedScenario?.scenario_name || 'None'}</div>
      </div>
    )
  }
})

jest.mock('@/components/keepers/ScenarioComparison', () => {
  return function MockScenarioComparison({ scenarios, selectedScenario, onScenarioSelect }: any) {
    return (
      <div data-testid="scenario-comparison">
        <div>Scenario Comparison Component</div>
        <div>Scenarios: {scenarios.length}</div>
        <div>Selected: {selectedScenario}</div>
        <button onClick={() => onScenarioSelect('Value-Focused')}>Select Value-Focused</button>
      </div>
    )
  }
})

jest.mock('@/components/keepers/DeadlineCountdown', () => {
  return function MockDeadlineCountdown({ deadline }: any) {
    return (
      <div data-testid="deadline-countdown">
        <div>Deadline Countdown Component</div>
        <div>Deadline: {deadline.toISOString()}</div>
      </div>
    )
  }
})

jest.mock('@/components/ui/LoadingSpinner', () => {
  return function MockLoadingSpinner() {
    return <div data-testid="loading-spinner">Loading...</div>
  }
})

jest.mock('@/components/ui/ErrorMessage', () => {
  return function MockErrorMessage({ message }: { message: string }) {
    return <div data-testid="error-message">{message}</div>
  }
})

const mockCandidates = [
  {
    player_id: '1',
    player_name: 'Josh Allen',
    position: 'QB',
    current_cost: 8,
    projected_points: 320.5,
    replacement_level: 250.0,
    value_over_replacement: 70.5,
    keeper_cost: 7,
    is_eligible: true,
    constraints_violated: [],
    metadata: {}
  }
]

const mockScenarios = [
  {
    scenario_name: 'Optimal',
    selected_keepers: [],
    total_value: 70.5,
    constraints_satisfied: true,
    trade_offs: [],
    metadata: {}
  }
]

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      cacheTime: 0,
    },
  },
})

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient()
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  )
}

describe('KeepersPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders keeper management interface', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockResolvedValue(mockCandidates)
    ;(keeperApi.optimizeKeepers as jest.Mock).mockResolvedValue(mockScenarios)

    renderWithQueryClient(<KeepersPage />)

    expect(screen.getByText('Keeper Management')).toBeInTheDocument()
    expect(screen.getByText('Optimize your keeper selections with AI-powered value analysis')).toBeInTheDocument()
  })

  it('displays deadline countdown', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockResolvedValue(mockCandidates)
    ;(keeperApi.optimizeKeepers as jest.Mock).mockResolvedValue(mockScenarios)

    renderWithQueryClient(<KeepersPage />)

    expect(screen.getByTestId('deadline-countdown')).toBeInTheDocument()
    expect(screen.getByText('Deadline Countdown Component')).toBeInTheDocument()
  })

  it('shows loading state while fetching data', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockImplementation(() => new Promise(() => {}))
    ;(keeperApi.optimizeKeepers as jest.Mock).mockImplementation(() => new Promise(() => {}))

    renderWithQueryClient(<KeepersPage />)

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })

  it('shows error state when API calls fail', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockRejectedValue(new Error('API Error'))
    ;(keeperApi.optimizeKeepers as jest.Mock).mockResolvedValue(mockScenarios)

    renderWithQueryClient(<KeepersPage />)

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument()
      expect(screen.getByText('Failed to load keeper data. Please try refreshing the page.')).toBeInTheDocument()
    })
  })

  it('renders child components when data is loaded', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockResolvedValue(mockCandidates)
    ;(keeperApi.optimizeKeepers as jest.Mock).mockResolvedValue(mockScenarios)

    renderWithQueryClient(<KeepersPage />)

    await waitFor(() => {
      expect(screen.getByTestId('keeper-selection')).toBeInTheDocument()
      expect(screen.getByTestId('value-analysis')).toBeInTheDocument()
      expect(screen.getByTestId('scenario-comparison')).toBeInTheDocument()
    })

    expect(screen.getByText('Keeper Selection Component')).toBeInTheDocument()
    expect(screen.getByText('Value Analysis Component')).toBeInTheDocument()
    expect(screen.getByText('Scenario Comparison Component')).toBeInTheDocument()
  })

  it('passes correct data to child components', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockResolvedValue(mockCandidates)
    ;(keeperApi.optimizeKeepers as jest.Mock).mockResolvedValue(mockScenarios)

    renderWithQueryClient(<KeepersPage />)

    await waitFor(() => {
      expect(screen.getByText('Candidates: 1')).toBeInTheDocument()
      expect(screen.getByText('Scenarios: 1')).toBeInTheDocument()
      expect(screen.getByText('Selected: Optimal')).toBeInTheDocument()
    })
  })

  it('handles scenario selection changes', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockResolvedValue(mockCandidates)
    ;(keeperApi.optimizeKeepers as jest.Mock).mockResolvedValue(mockScenarios)

    renderWithQueryClient(<KeepersPage />)

    await waitFor(() => {
      expect(screen.getByText('Selected: Optimal')).toBeInTheDocument()
    })

    // Click to change scenario
    const selectButton = screen.getByText('Select Value-Focused')
    fireEvent.click(selectButton)

    expect(screen.getByText('Selected: Value-Focused')).toBeInTheDocument()
  })

  it('handles refresh data button click', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockResolvedValue(mockCandidates)
    ;(keeperApi.optimizeKeepers as jest.Mock).mockResolvedValue(mockScenarios)

    renderWithQueryClient(<KeepersPage />)

    await waitFor(() => {
      expect(screen.getByText('Refresh Data')).toBeInTheDocument()
    })

    const refreshButton = screen.getByText('Refresh Data')
    fireEvent.click(refreshButton)

    // Should call the API functions again
    expect(keeperApi.getKeeperCandidates).toHaveBeenCalledTimes(2)
    expect(keeperApi.optimizeKeepers).toHaveBeenCalledTimes(2)
  })

  it('handles keeper selection changes', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockResolvedValue(mockCandidates)
    ;(keeperApi.optimizeKeepers as jest.Mock).mockResolvedValue(mockScenarios)

    renderWithQueryClient(<KeepersPage />)

    await waitFor(() => {
      expect(screen.getByTestId('keeper-selection')).toBeInTheDocument()
    })

    // Trigger selection change
    const selectionButton = screen.getByText('Mock Selection Change')
    fireEvent.click(selectionButton)

    // Should trigger scenarios refetch
    expect(keeperApi.optimizeKeepers).toHaveBeenCalledTimes(2)
  })

  it('uses default franchise ID', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockResolvedValue(mockCandidates)
    ;(keeperApi.optimizeKeepers as jest.Mock).mockResolvedValue(mockScenarios)

    renderWithQueryClient(<KeepersPage />)

    await waitFor(() => {
      expect(keeperApi.getKeeperCandidates).toHaveBeenCalledWith('franchise_1')
      expect(keeperApi.optimizeKeepers).toHaveBeenCalledWith('franchise_1')
    })
  })

  it('handles empty candidates array', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockResolvedValue([])
    ;(keeperApi.optimizeKeepers as jest.Mock).mockResolvedValue([])

    renderWithQueryClient(<KeepersPage />)

    await waitFor(() => {
      expect(screen.getByText('Candidates: 0')).toBeInTheDocument()
      expect(screen.getByText('Scenarios: 0')).toBeInTheDocument()
    })
  })

  it('handles scenarios API error while candidates succeed', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockResolvedValue(mockCandidates)
    ;(keeperApi.optimizeKeepers as jest.Mock).mockRejectedValue(new Error('Scenarios API Error'))

    renderWithQueryClient(<KeepersPage />)

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument()
    })
  })

  it('sets correct deadline date', async () => {
    ;(keeperApi.getKeeperCandidates as jest.Mock).mockResolvedValue(mockCandidates)
    ;(keeperApi.optimizeKeepers as jest.Mock).mockResolvedValue(mockScenarios)

    renderWithQueryClient(<KeepersPage />)

    await waitFor(() => {
      expect(screen.getByText(/2024-08-30T23:59:59/)).toBeInTheDocument()
    })
  })
})