'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function Home() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to dashboard
    router.push('/dashboard')
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          AI Fantasy Assistant
        </h1>
        <LoadingSpinner size="large" />
        <p className="mt-4 text-gray-600">
          Redirecting to dashboard...
        </p>
      </div>
    </div>
  )
}