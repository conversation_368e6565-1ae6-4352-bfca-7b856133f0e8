'use client'

import { useEffect, useMemo, useState } from 'react'
import Layout from '@/components/Layout'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { useMFLContext } from '@/context/MFLContext'
import RefreshButton from '@/components/RefreshButton'
import mflApi from '@/lib/mfl-api'
import { RulesAPI, type LeagueRulesResponse } from '@/lib/rules-api'
import { ADPAPI, type ADPConfig } from '@/lib/adp-api'

type SettingsTab = 'general' | 'leagues' | 'rules' | 'notifications' | 'api'

function LeagueFacts() {
  const { leagueId } = useMFLContext()
  const [facts, setFacts] = useState<any | null>(null)
  const [err, setErr] = useState<string | null>(null)
  useEffect(() => {
    const run = async () => {
      try {
        if (!leagueId) return
        mflApi.setLeague(leagueId)
        const raw = await mflApi.getLeagueRaw()
        setFacts(raw)
        setErr(null)
      } catch (e: any) {
        setFacts(null)
        setErr(e?.message || 'Failed to fetch MFL league payload')
      }
    }
    run()
  }, [leagueId])

  const starters = (() => {
    const s = (facts as any)?.starters
    const list = Array.isArray(s?.position) ? s.position : (s?.position ? [s.position] : [])
    return { count: s?.count, positions: list }
  })()

  const cap = (facts as any)?.salaryCapAmount
  const bbid = {
    type: (facts as any)?.currentWaiverType,
    minimum: (facts as any)?.bbidMinimum,
    increment: (facts as any)?.bbidIncrement,
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <div className="font-medium text-gray-900 mb-2">MFL Lineup, Waivers, Cap</div>
      {err && <div className="text-xs text-red-600 mb-2">{err}</div>}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <div className="text-gray-700 font-medium mb-1">Starting Lineup</div>
          <div className="text-gray-600 mb-1">Starters: {starters.count || '—'}</div>
          <ul className="list-disc list-inside text-gray-800">
            {starters.positions.map((p: any, idx: number) => (
              <li key={idx}>{p.name} {p.limit ? `(${p.limit})` : ''}</li>
            ))}
            {starters.positions.length === 0 && <li>—</li>}
          </ul>
        </div>
        <div>
          <div className="text-gray-700 font-medium mb-1">Waivers (BBID)</div>
          <div>Type: {bbid.type || '—'}</div>
          <div>Minimum: {bbid.minimum || '—'}</div>
          <div>Increment: {bbid.increment || '—'}</div>
          <div className="mt-3 text-gray-700 font-medium">Salary Cap</div>
          <div>Cap Amount: {cap ? `${cap} (MFL units)` : '—'}</div>
        </div>
      </div>
    </div>
  )
}

function MFLRulesPanel({ leagueId }: { leagueId: string }) {
  const [data, setData] = useState<any | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const load = async () => {
      try {
        setLoading(true)
        setError(null)
        mflApi.setLeague(leagueId)
        const raw = await mflApi.getRules()
        setData(raw?.rules || raw || null)
      } catch (e: any) {
        setError(e?.message || 'Failed to fetch MFL rules')
        setData(null)
      } finally {
        setLoading(false)
      }
    }
    if (leagueId) load()
  }, [leagueId])

  const EVENT_TITLES: Record<string, string> = {
    'PY': 'Passing Yards',
    'IN': 'Interceptions Thrown',
    'P2': 'Passing 2-pt Conversions',
    'RY': 'Rushing Yards',
    'R2': 'Rushing 2-pt Conversions',
    'CY': 'Receiving Yards',
    'CC': 'Receptions',
    'C2': 'Receiving 2-pt Conversions',
    '#TD': 'Any Touchdowns',
    'FG': 'Field Goals',
    'EP': 'Extra Points',
    'UY': 'Punt Return Yards',
    'KY': 'Kick Return Yards',
    'FL': 'Fumbles Lost',
    'FC': 'Fumbles Recovered (DEF)',
    'IC': 'Interceptions (DEF)',
    'BLF': 'Blocked Field Goals',
    'BLP': 'Blocked Punts',
    'BLE': 'Blocked Extra Points',
    'SK': 'Sacks',
    'SF': 'Safeties',
    'TYA': 'Team Yards Allowed (DEF)',
    '#T': 'Def/Sp Teams Touchdowns'
  }

  const formatPts = (s: string) => s.startsWith('*.') || s.startsWith('*-') ? `${s.replace('*','')} per unit` : s

  return (
    <div className="mt-8">
      <h3 className="text-lg font-medium text-gray-900 mb-3">Raw MFL Rules</h3>
      {loading && <div className="text-sm text-gray-600">Loading MFL rules…</div>}
      {error && <div className="text-sm text-red-600">{error}</div>}
      {!loading && !error && !data && (
        <div className="text-sm text-gray-600">No rules returned from MFL.</div>
      )}
      {data && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Scoring Rules */}
          {(data.positionRules || []).map((block: any, i: number) => (
            <div key={`pr-${i}`} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-900 mb-2">Positions: {block.positions || '—'}</div>
              <table className="min-w-full text-sm">
                <thead className="text-gray-500">
                  <tr>
                    <th className="text-left pr-3">Event</th>
                    <th className="text-left pr-3">Range</th>
                    <th className="text-left">Points</th>
                  </tr>
                </thead>
                <tbody className="text-gray-800">
                  {(block.rule || []).map((r: any, idx: number) => (
                    <tr key={idx} className="border-t border-gray-100">
                      <td className="py-1 pr-3">{EVENT_TITLES[r.event?.$t] || r.event?.$t || '—'}</td>
                      <td className="py-1 pr-3">{r.range?.$t || '—'}</td>
                      <td className="py-1">{r.points?.$t ? formatPts(r.points.$t) : '—'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ))}

        </div>
      )}
    </div>
  )
}

function ADPUploadForm({ defaultSeason }: { defaultSeason: number }) {
  const [file, setFile] = useState<File | null>(null)
  const [source, setSource] = useState<string>('AGG_ADP')
  const [season, setSeason] = useState<number>(defaultSeason)
  const [busy, setBusy] = useState(false)
  const [msg, setMsg] = useState<string | null>(null)
  const [err, setErr] = useState<string | null>(null)

  const base = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setMsg(null)
    setErr(null)
    if (!file) {
      setErr('Please choose a CSV file to upload.')
      return
    }
    try {
      setBusy(true)
      const form = new FormData()
      form.set('file', file)
      form.set('source', source)
      form.set('season', String(season))
      const res = await fetch(`${base}/api/v1/upload/adp`, {
        method: 'POST',
        body: form,
      })
      if (!res.ok) {
        const detail = await res.json().catch(() => ({}))
        throw new Error(detail?.detail || `Upload failed (${res.status})`)
      }
      const data = await res.json()
      const saved = data?.data?.saved_count ?? data?.saved_count
      const total = data?.data?.total_rows ?? data?.total_rows
      setMsg(`Uploaded successfully: saved ${saved} of ${total} rows.`)
    } catch (e: any) {
      setErr(e?.message || 'Upload failed')
    } finally {
      setBusy(false)
    }
  }

  return (
    <form onSubmit={onSubmit} className="space-y-3">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Source</label>
          <input
            type="text"
            value={source}
            onChange={e => setSource(e.target.value.toUpperCase())}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            placeholder="AGG_ADP"
          />
          <p className="text-xs text-gray-500 mt-1">Must match your configured aggregator source name.</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Season</label>
          <input
            type="number"
            value={season}
            onChange={e => setSeason(parseInt(e.target.value || '0', 10))}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            min={2000}
            max={2100}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">CSV File</label>
          <input
            type="file"
            accept=".csv, text/csv, application/vnd.ms-excel"
            onChange={e => setFile(e.target.files?.[0] || null)}
            className="block w-full text-sm"
          />
        </div>
      </div>
      <div className="flex items-center gap-3">
        <button
          type="submit"
          disabled={busy || !file}
          className={`px-4 py-2 rounded text-white ${busy || !file ? 'bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}
        >
          {busy ? 'Uploading…' : 'Upload CSV'}
        </button>
        {msg && <span className="text-sm text-green-600">{msg}</span>}
        {err && <span className="text-sm text-red-600">{err}</span>}
      </div>
    </form>
  )
}

const ALL_ADP_PROVIDERS = ["MFL","FFC","ESPN","SLEEPER","CBS","NFL","RTSPORTS","FANTRAX"] as const

type Provider = typeof ALL_ADP_PROVIDERS[number]

type ADPWeightsEditorProps = {
  config: ADPConfig
  onChange: (cfg: ADPConfig) => void
  onSave: (cfg: ADPConfig) => Promise<void>
  onRefresh: () => Promise<void>
  saving: boolean
  refreshing: boolean
}

function ADPWeightsEditor({ config, onChange, onSave, onRefresh, saving, refreshing }: ADPWeightsEditorProps) {
  const [local, setLocal] = useState<ADPConfig>(config)

  useEffect(() => {
    setLocal(config)
  }, [config])

  const selectedProviders = useMemo(() => new Set((local.providers || []).map(p => p.toUpperCase())), [local.providers])

  const handleToggleProvider = (prov: Provider) => {
    const sel = new Set(selectedProviders)
    if (sel.has(prov)) sel.delete(prov); else sel.add(prov)
    const nextProviders = ALL_ADP_PROVIDERS.filter(p => sel.has(p))
    setLocal(prev => ({ ...prev, providers: nextProviders }))
  }

  const handleWeightChange = (prov: Provider, value: string) => {
    const num = value === '' ? NaN : Number(value)
    setLocal(prev => ({
      ...prev,
      weights: {
        ...(prev.weights || {}),
        [prov]: isNaN(num) ? 0 : Math.max(0, num)
      }
    }))
  }

  const sumWeights = useMemo(() => {
    const provs = local.providers || []
    return provs.reduce((acc, p) => acc + (Number(local.weights?.[p]) || 0), 0)
  }, [local])

  const normalize = () => {
    const provs = local.providers || []
    if (provs.length === 0) return
    const total = provs.reduce((acc, p) => acc + (Number(local.weights?.[p]) || 0), 0)
    let nextWeights: Record<string, number> = { ...(local.weights || {}) }
    if (total > 0) {
      for (const p of provs) {
        nextWeights[p] = (Number(nextWeights[p]) || 0) / total
      }
    } else {
      const equal = 1 / provs.length
      for (const p of provs) {
        nextWeights[p] = equal
      }
    }
    setLocal(prev => ({ ...prev, weights: nextWeights }))
  }

  const valid = (local.providers || []).length > 0 && sumWeights > 0

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <div className="lg:col-span-1">
          <div className="text-sm text-gray-700 font-medium mb-2">Enabled Providers</div>
          <div className="grid grid-cols-2 gap-2 text-sm">
            {ALL_ADP_PROVIDERS.map((prov) => (
              <label key={prov} className="inline-flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={selectedProviders.has(prov)}
                  onChange={() => handleToggleProvider(prov)}
                />
                <span>{prov}</span>
              </label>
            ))}
          </div>
        </div>
        <div className="lg:col-span-2">
          <div className="text-sm text-gray-700 font-medium mb-2">Weights</div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {ALL_ADP_PROVIDERS.map((prov) => (
              <div key={`w-${prov}`} className={`p-3 border rounded ${selectedProviders.has(prov) ? 'bg-white' : 'bg-gray-50 opacity-60'}`}>
                <div className="text-xs text-gray-500 mb-1">{prov}</div>
                <input
                  type="number"
                  step="0.01"
                  min={0}
                  value={Number(local.weights?.[prov] ?? 0).toString()}
                  onChange={(e) => handleWeightChange(prov, e.target.value)}
                  disabled={!selectedProviders.has(prov)}
                  className="block w-full px-2 py-1 border border-gray-300 rounded"
                />
              </div>
            ))}
          </div>
          <div className="mt-2 flex items-center gap-3 text-sm">
            <div className="text-gray-600">Sum: <span className="font-medium">{sumWeights.toFixed(3)}</span></div>
            <button type="button" onClick={normalize} className="px-2 py-1 text-xs bg-gray-100 rounded border">Normalize</button>
            {!valid && (
              <span className="text-xs text-red-600">Select at least one provider and set total weight &gt; 0</span>
            )}
          </div>
        </div>
      </div>

      <div className="flex flex-wrap gap-3">
        <button
          type="button"
          onClick={async () => { if (valid) await onSave(local); onChange(local) }}
          disabled={!valid || saving}
          className={`px-4 py-2 rounded text-white ${(!valid || saving) ? 'bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}
        >
          {saving ? 'Saving…' : 'Save Weights'}
        </button>
        <button
          type="button"
          onClick={onRefresh}
          disabled={refreshing}
          className={`px-4 py-2 rounded text-white ${refreshing ? 'bg-gray-400' : 'bg-indigo-600 hover:bg-indigo-700'}`}
        >
          {refreshing ? 'Rebuilding…' : 'Rebuild Composite Index'}
        </button>
      </div>
    </div>
  )
}

export default function SettingsPage() {
  const { loading, error, leagueId, setLeagueId, league, standings, refreshLeague, seasonYear, setSeasonYear, setCredentials } = useMFLContext()
  const [activeTab, setActiveTab] = useState<SettingsTab>('general')
  const [editLeagueId, setEditLeagueId] = useState<string>('')
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [connStatus, setConnStatus] = useState<'idle' | 'testing' | 'success' | 'failed'>('idle')
  const [isClient, setIsClient] = useState(false)

  // ADP config state
  const [adpConfig, setAdpConfig] = useState<ADPConfig | null>(null)
  const [adpLoading, setAdpLoading] = useState(false)
  const [adpError, setAdpError] = useState<string | null>(null)
  const [adpSaving, setAdpSaving] = useState(false)
  const [adpRefreshing, setAdpRefreshing] = useState(false)

  // Backend rules state (from our API, not MFL)
  const [rules, setRules] = useState<LeagueRulesResponse | null>(null)
  const [rulesLoading, setRulesLoading] = useState(false)
  const [rulesError, setRulesError] = useState<string | null>(null)

  // Raw MFL league facts for fallback display
  const [mflFacts, setMflFacts] = useState<any | null>(null)
  const [mflFactsLoading, setMflFactsLoading] = useState(false)
  const [mflFactsError, setMflFactsError] = useState<string | null>(null)

  useEffect(() => {
    setIsClient(true)
    const stored = typeof window !== 'undefined' ? (localStorage.getItem('mfl_league_id') || '') : ''
    setEditLeagueId(leagueId || stored || '')
    const creds = typeof window !== 'undefined' ? localStorage.getItem('mfl_credentials') : null
    if (creds) {
      try {
        const parsed = JSON.parse(creds)
        setUsername(parsed.username || '')
        setPassword(parsed.password || '')
      } catch {}
    }
  }, [leagueId])

  // Load backend rules when leagueId is available
  useEffect(() => {
    const load = async () => {
      if (!isClient || !leagueId) return
      try {
        setRulesLoading(true)
        setRulesError(null)
        const data = await RulesAPI.getLeagueRules(leagueId)
        setRules(data)
      } catch (e: any) {
        setRules(null)
        setRulesError(e?.message || 'Failed to load rules')
      } finally {
        setRulesLoading(false)
      }
    }
    load()
  }, [isClient, leagueId])

  // Load ADP config when leagueId is available
  useEffect(() => {
    const load = async () => {
      if (!isClient || !leagueId) return
      try {
        setAdpLoading(true)
        setAdpError(null)
        const data = await ADPAPI.getConfig(String(leagueId))
        setAdpConfig(data.config)
      } catch (e: any) {
        setAdpConfig(null)
        setAdpError(e?.message || 'Failed to load ADP config')
      } finally {
        setAdpLoading(false)
      }
    }
    load()
  }, [isClient, leagueId])

  // Load raw MFL league facts for lineup/waivers/cap fallbacks
  useEffect(() => {
    const loadMfl = async () => {
      if (!isClient || !leagueId) return
      try {
        setMflFactsLoading(true)
        setMflFactsError(null)
        mflApi.setLeague(leagueId)
        const raw = await mflApi.getLeagueRaw()
        setMflFacts(raw)
      } catch (e: any) {
        setMflFacts(null)
        setMflFactsError(e?.message || 'Failed to load MFL league details')
      } finally {
        setMflFactsLoading(false)
      }
    }
    loadMfl()
  }, [isClient, leagueId])

  if (!isClient) {
    return (
      <Layout>
        <div className="p-6 text-sm text-gray-600">Loading settings…</div>
      </Layout>
    )
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <LoadingSpinner />
        </div>
      </Layout>
    )
  }

  if (error) {
    return (
      <Layout>
        <ErrorMessage 
          message={`There was an error loading settings: ${error}`}
          onDismiss={() => window.location.reload()}
        />
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="mt-1 text-sm text-gray-500">Manage your AI Fantasy Assistant preferences and configuration</p>
        </div>

        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {(['general','leagues','rules','notifications','api'] as SettingsTab[]).map(tab => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${activeTab === tab ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                >
                  {tab[0].toUpperCase() + tab.slice(1)}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'general' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">General Preferences</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Default Season</label>
                      <select
                        value={seasonYear}
                        onChange={(e) => setSeasonYear(parseInt(e.target.value))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
                      >
                        {[2025].map(y => (<option key={y} value={y}>{y}</option>))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Time Zone</label>
                      <select className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm">
                        <option value="EST">Eastern Time (EST)</option>
                        <option value="CST">Central Time (CST)</option>
                        <option value="MST">Mountain Time (MST)</option>
                        <option value="PST">Pacific Time (PST)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Default Strategy</label>
                      <select className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm">
                        <option value="balanced">Balanced</option>
                        <option value="aggressive">Aggressive</option>
                        <option value="conservative">Conservative</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Risk Tolerance</label>
                      <select className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm">
                        <option value="low">Low Risk</option>
                        <option value="medium">Medium Risk</option>
                        <option value="high">High Risk</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">Display Preferences</h4>
                  <div className="space-y-3">
                    {[
                      { id: 'dark_mode', label: 'Dark Mode', description: 'Use dark theme for the interface' },
                      { id: 'compact_view', label: 'Compact View', description: 'Show more information in less space' },
                      { id: 'auto_refresh', label: 'Auto Refresh', description: 'Automatically refresh data every 5 minutes' }
                    ].map((setting) => (
                      <div key={setting.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div>
                          <div className="font-medium text-gray-900">{setting.label}</div>
                          <div className="text-sm text-gray-500">{setting.description}</div>
                        </div>
                        <input type="checkbox" />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'leagues' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">League Management</h3>
                  <div className="space-y-4">
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900">{league?.name || 'League'}</h4>
                          <p className="text-sm text-gray-500">Season: {league?.season || '-'}</p>
                        </div>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">MFL League ID:</span>
                          <div className="font-medium">
                            <input
                              value={editLeagueId}
                              onChange={e => setEditLeagueId(e.target.value)}
                              placeholder="Enter MFL league ID"
                              className="mt-1 block w-full px-2 py-1 border border-gray-300 rounded"
                            />
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-500">Teams:</span>
                          <div className="font-medium">{standings?.length || 0}</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Season:</span>
                          <div className="font-medium">{league?.season || '-'}</div>
                        </div>
                        <div>
                          <span className="text-gray-500">League Name:</span>
                          <div className="font-medium">{league?.name || '-'}</div>
                        </div>
                      </div>
                      <div className="mt-3 flex flex-wrap gap-2 items-center">
                        <button
                          onClick={async () => {
                            const newId = editLeagueId?.trim() || ''
                            if (newId) {
                              mflApi.setLeague(newId)
                              setLeagueId(newId)
                              setTimeout(() => { refreshLeague() }, 0)
                            } else {
                              setLeagueId(null)
                            }
                          }}
                          className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                        >
                          Apply League ID
                        </button>
                        <RefreshButton contextKey="settings-league" />
                      </div>
                    </div>

                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <div className="text-gray-400 text-2xl mb-2">+</div>
                      <h4 className="font-medium text-gray-900 mb-1">Add New League</h4>
                      <p className="text-sm text-gray-500 mb-3">Connect another MFL league to get AI recommendations</p>
                      <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Add League</button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'rules' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">League Rules</h3>
                  {!league && (<div className="text-sm text-gray-600">Load your league first to view rules.</div>)}
                  {league && (
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-gray-900 mb-2">Keeper Rules</h4>
                          {(rulesLoading || mflFactsLoading) && <span className="text-xs text-gray-500">Loading…</span>}
                        </div>
                        {(rulesError || mflFactsError) && <div className="text-xs text-red-600">{rulesError || mflFactsError}</div>}
                        <div className="text-sm text-gray-700 space-y-1">
                          <div><span className="text-gray-500">Max Keepers:</span> <span className="ml-2 font-medium">{rules?.keeper_rules?.max_keepers ?? '—'}</span></div>
                          <div><span className="text-gray-500">Optional Exemptions:</span> <span className="ml-2 font-medium">{rules?.keeper_rules?.exemption?.max_exemption_players ?? 0}</span></div>
                          <div><span className="text-gray-500">Per-Position Max:</span> <span className="ml-2 font-medium">{rules?.keeper_rules?.max_per_position ?? '—'}</span></div>
                          <div><span className="text-gray-500">Exemptions:</span> <span className="ml-2 font-medium">{(rules?.keeper_rules?.exemption?.types || []).join(', ') || '—'}</span></div>
                          <div className="pt-2 text-gray-500">Contracts Enabled: <span className="ml-2 font-medium">{mflFacts?.usesContractYear === '1' ? 'Yes' : (mflFacts ? 'No' : '—')}</span></div>
                          <div className="text-gray-500">IR Slots: <span className="ml-2 font-medium">{mflFacts?.injuredReserve ?? '—'}</span></div>
                        </div>
                      </div>

                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-2">Waiver / FAAB</h4>
                        <div className="text-sm text-gray-700 space-y-1">
                          <div><span className="text-gray-500">Type:</span> <span className="ml-2 font-medium">{rules?.waiver_rules?.type ?? rules?.keeper_rules?.extended?.waiver_rules?.type ?? mflFacts?.currentWaiverType ?? '—'}</span></div>
                          <div><span className="text-gray-500">Budget:</span> <span className="ml-2 font-medium">{rules?.waiver_rules?.budget ?? rules?.keeper_rules?.extended?.waiver_rules?.budget ?? '—'}</span></div>
                          <div><span className="text-gray-500">Tiebreaker:</span> <span className="ml-2 font-medium">{rules?.waiver_rules?.tie_breaker ?? rules?.waiver_rules?.tiebreaker ?? rules?.keeper_rules?.extended?.waiver_rules?.tiebreaker ?? '—'}</span></div>
                          <div className="text-gray-500"><span>Minimum Bid:</span> <span className="ml-2 font-medium">{mflFacts?.bbidMinimum ?? '—'}</span></div>
                          <div className="text-gray-500"><span>Increment:</span> <span className="ml-2 font-medium">{mflFacts?.bbidIncrement ?? '—'}</span></div>
                          <div>
                            <span className="text-gray-500">Windows:</span>
                            <div className="ml-2">
                              {(() => {
                                const windows = (rules as any)?.waiver_rules?.windows || (rules as any)?.waiver_rules?.periods || (rules as any)?.keeper_rules?.extended?.waiver_rules?.windows || []
                                if (!windows || windows.length === 0) return <span className="font-medium">—</span>
                                return (
                                  <ul className="list-disc list-inside">
                                    {windows.map((w: any, idx: number) => (
                                      <li key={idx}>{w.day} {w.time || w.time_local} {w.tz ? `(${w.tz})` : (w.timezone ? `(${w.timezone})` : '')}</li>
                                    ))}
                                  </ul>
                                )
                              })()}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-2">Salary Cap</h4>
                        <div className="text-sm text-gray-700 space-y-1">
                          {(() => {
                            const sc = (rules as any)?.salary_cap || (rules as any)?.keeper_rules?.extended?.salary_cap || {}
                            const fallbackAmount = mflFacts?.salaryCapAmount
                            const enabled = Boolean(sc.cap || sc.amount || fallbackAmount)
                            const amount = (sc.amount ?? sc.cap ?? fallbackAmount)
                            const minSal = sc.min_salary ?? sc.minimum_salary
                            return (
                              <>
                                <div><span className="text-gray-500">Enabled:</span> <span className="ml-2 font-medium">{String(enabled)}</span></div>
                                <div><span className="text-gray-500">Cap Amount:</span> <span className="ml-2 font-medium">{amount ?? '—'}</span></div>
                                <div><span className="text-gray-500">Min Salary:</span> <span className="ml-2 font-medium">{minSal ?? '—'}</span></div>
                              </>
                            )
                          })()}
                        </div>
                      </div>
                    </div>
                  )}

                  {league && (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-2">Team Size & Lineup</h4>
                        <div className="text-sm text-gray-700 space-y-1">
                          <div>
                            <span className="text-gray-500">Team Size:</span>
                            <span className="ml-2 font-medium">
                              {rules?.team_size ? `${rules.team_size.total} (Starters: ${rules.team_size.starters}, Bench: ${rules.team_size.bench})` : (mflFacts?.rosterSize ? `${mflFacts.rosterSize}` : '—')}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">Starting Slots:</span>
                            <ul className="list-disc list-inside ml-2">
                              {(rules?.roster_slots || []).filter((s: any) => s.type === 'starting').map((s: any, idx: number) => (
                                <li key={idx}>{s.position} × {s.count}{s.eligible_positions ? ` (Flex: ${s.eligible_positions.join('/')})` : ''}</li>
                              ))}
                              {(!rules?.roster_slots || rules?.roster_slots.length === 0) && (() => {
                                const s = (mflFacts as any)?.starters
                                const list = Array.isArray(s?.position) ? s.position : (s?.position ? [s.position] : [])
                                return list.map((p: any, idx: number) => (
                                  <li key={`mfl-${idx}`}>{p.name} {p.limit ? `(${p.limit})` : ''}</li>
                                ))
                              })()}
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-2">Defense Yards Allowed</h4>
                        <div className="text-sm text-gray-700">
                          {(() => {
                            const tiers = (rules as any)?.defense_yards_allowed_tiers || (rules as any)?.keeper_rules?.extended?.defense_yards_allowed_tiers || (rules as any)?.keeper_rules?.extended?.scoring_defense_yards_allowed_buckets || []
                            if (!tiers || tiers.length === 0) return <div className="text-gray-500">—</div>
                            return (
                              <ul className="list-disc list-inside">
                                {tiers.map((b: any, idx: number) => (
                                  <li key={idx}>
                                    {b.min_yards != null ? `${b.min_yards}+` : (b.min != null ? `${b.min}+` : (b.max_yards != null ? `≤${b.max_yards}` : (b.max != null ? `≤${b.max}` : '')))} yards: {b.points} pts
                                  </li>
                                ))}
                              </ul>
                            )
                          })()}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Raw MFL Rules (verbatim) */}
                  {league && (
                    <MFLRulesPanel leagueId={String(leagueId)} />
                  )}
                </div>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Settings</h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">Email Notifications</h4>
                      <div className="space-y-3">
                        {[
                          { id: 'weekly_report', label: 'Weekly Report', description: 'Get a summary of your team performance and recommendations' },
                          { id: 'injury_alerts', label: 'Injury Alerts', description: 'Immediate notifications for player injuries' },
                          { id: 'waiver_recommendations', label: 'Waiver Recommendations', description: 'Weekly waiver wire suggestions' },
                          { id: 'trade_opportunities', label: 'Trade Opportunities', description: 'Notifications for potential beneficial trades' }
                        ].map((setting) => (
                          <div key={setting.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <div className="font-medium text-gray-900">{setting.label}</div>
                              <div className="text-sm text-gray-500">{setting.description}</div>
                            </div>
                            <input type="checkbox" defaultChecked />
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">Notification Timing</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Weekly Report Day</label>
                          <select className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm">
                            <option value="tuesday">Tuesday</option>
                            <option value="wednesday">Wednesday</option>
                            <option value="thursday">Thursday</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Report Time</label>
                          <select className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm">
                            <option value="08:00">8:00 AM</option>
                            <option value="12:00">12:00 PM</option>
                            <option value="18:00">6:00 PM</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'api' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">API Configuration</h3>
                  <div className="space-y-6">
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">MFL API Settings</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">MFL Username</label>
                          <input
                            type="text"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            placeholder="Enter your MFL username"
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">MFL Password</label>
                          <input
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder="Enter your MFL password"
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
                          />
                        </div>
                      </div>
                      <div className="mt-3 flex items-center space-x-3">
                        <button
                          onClick={async () => {
                            if (!username || !password) return
                            setConnStatus('testing')
                            try {
                              mflApi.setCredentials(username, password)
                              await mflApi.getPlayers()
                              setCredentials(username, password)
                              setConnStatus('success')
                              setTimeout(() => setConnStatus('idle'), 2000)
                            } catch (e) {
                              setConnStatus('failed')
                              setTimeout(() => setConnStatus('idle'), 3000)
                            }
                          }}
                          disabled={connStatus === 'testing'}
                          className={`px-4 py-2 rounded text-white ${connStatus === 'testing' ? 'bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}
                        >
                          {connStatus === 'testing' ? 'Testing…' : 'Test Connection'}
                        </button>
                        {connStatus === 'success' && (<span className="text-sm text-green-600">✓ Connection successful</span>)}
                        {connStatus === 'failed' && (<span className="text-sm text-red-600">Connection failed</span>)}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">Data Refresh Settings</h4>
                      <div className="space-y-3">
                        {[
                          { id: 'auto_sync', label: 'Auto Sync', description: 'Automatically sync league data every hour' },
                          { id: 'real_time_scores', label: 'Real-time Scores', description: 'Update player scores during games' },
                          { id: 'injury_updates', label: 'Injury Updates', description: 'Check for injury report updates every 15 minutes' }
                        ].map((setting) => (
                          <div key={setting.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <div className="font-medium text-gray-900">{setting.label}</div>
                              <div className="text-sm text-gray-500">{setting.description}</div>
                            </div>
                            <input type="checkbox" defaultChecked />
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M4.062 19h15.876c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L2.33 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                        </div>
                        <div className="ml-3 text-sm text-yellow-700">
                          MFL API has rate limits. Excessive requests may result in temporary blocks.
                        </div>
                      </div>
                    </div>

                    {/* Upload multi-provider ADP CSV */}
                    <div className="bg-white border border-gray-200 rounded-lg p-4">
                      <h4 className="text-md font-medium text-gray-900 mb-3">Upload ADP CSV (Aggregator)</h4>
                      <p className="text-sm text-gray-600 mb-3">
                        Upload a CSV with headers: Name, Team, POS, ESPN, Sleeper, CBS, NFL, RTSports, Fantrax. The file will be exploded into per-provider ADP rows.
                      </p>
                      <ADPUploadForm defaultSeason={seasonYear} />
                    </div>

                    {/* ADP Provider Weights */}
                    <div className="bg-white border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-md font-medium text-gray-900">ADP Provider Weights</h4>
                        {!leagueId && (
                          <span className="text-xs text-gray-500">Set a League ID to configure ADP</span>
                        )}
                      </div>

                      {adpLoading && (
                        <div className="text-sm text-gray-600">Loading ADP config…</div>
                      )}
                      {adpError && (
                        <div className="text-sm text-red-600 mb-2">{adpError}</div>
                      )}

                      {leagueId && adpConfig && (
                        <ADPWeightsEditor
                          config={adpConfig}
                          onChange={setAdpConfig}
                          onSave={async (cfg) => {
                            setAdpSaving(true)
                            try {
                              await ADPAPI.putConfig(String(leagueId), cfg)
                            } catch (e: any) {
                              setAdpError(e?.message || 'Failed to save ADP config')
                            } finally {
                              setAdpSaving(false)
                            }
                          }}
                          onRefresh={async () => {
                            setAdpRefreshing(true)
                            setAdpError(null)
                            try {
                              await ADPAPI.refresh(String(leagueId), seasonYear)
                            } catch (e: any) {
                              setAdpError(e?.message || 'Failed to rebuild composite index')
                            } finally {
                              setAdpRefreshing(false)
                            }
                          }}
                          saving={adpSaving}
                          refreshing={adpRefreshing}
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div className="flex justify-end space-x-3">
              <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">Save Changes</button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
