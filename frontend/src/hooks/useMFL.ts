import { useState, useEffect, useCallback } from 'react'
import { mflApi, MFLLeague, MFLPlayer, MFLRoster, MFLStandings, MFLFranchise } from '../lib/mfl-api'

// Custom hook for MFL API integration
export const useMFL = (leagueId?: string, year?: number) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [league, setLeague] = useState<MFLLeague | null>(null)
  const [players, setPlayers] = useState<MFLPlayer[]>([])
  const [rosters, setRosters] = useState<MFLRoster[]>([])
  const [standings, setStandings] = useState<MFLStandings[]>([])

  // Initialize MFL API
  useEffect(() => {
    if (leagueId) {
      mflApi.setLeague(leagueId)
    }
  }, [leagueId])

  // Apply season year if provided
  useEffect(() => {
    if (year) {
      // @ts-ignore setYear exists on client
      (mflApi as any).setYear(year)
    }
  }, [year])

  // Generic API call wrapper
  const apiCall = useCallback(async <T>(
    apiFunction: () => Promise<T>,
    onSuccess?: (data: T) => void,
    onError?: (error: string) => void
  ): Promise<T | null> => {
    try {
      setLoading(true)
      setError(null)
      const result = await apiFunction()
      onSuccess?.(result)
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      onError?.(errorMessage)
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  // Load league information
  const loadLeague = useCallback(async () => {
    return apiCall(
      () => mflApi.getLeague(),
      (data) => setLeague(data)
    )
  }, [apiCall])

  // Load all players
  const loadPlayers = useCallback(async () => {
    return apiCall(
      async () => {
        const response = await mflApi.getPlayers()
        return response.players.player
      },
      (data) => setPlayers(data)
    )
  }, [apiCall])

  // Load league rosters
  const loadRosters = useCallback(async () => {
    return apiCall(
      async () => {
        const response = await mflApi.getRosters()
        return response.rosters.franchise
      },
      (data) => setRosters(data)
    )
  }, [apiCall])

  // Load league standings
  const loadStandings = useCallback(async () => {
    return apiCall(
      async () => {
        const response = await mflApi.getStandings()
        return response.leagueStandings.franchise
      },
      (data) => setStandings(data)
    )
  }, [apiCall])

  // Load specific franchise roster
  const loadFranchiseRoster = useCallback(async (franchiseId: string) => {
    return apiCall(() => mflApi.getFranchiseRoster(franchiseId))
  }, [apiCall])

  // Load live scoring
  const loadLiveScoring = useCallback(async (week?: number) => {
    return apiCall(() => mflApi.getLiveScoring(week))
  }, [apiCall])

  // Load schedule
  const loadSchedule = useCallback(async (week?: number) => {
    return apiCall(() => mflApi.getSchedule(week))
  }, [apiCall])

  // Load transactions
  const loadTransactions = useCallback(async (days?: number) => {
    return apiCall(() => mflApi.getTransactions(days))
  }, [apiCall])

  // Load free agents
  const loadFreeAgents = useCallback(async () => {
    return apiCall(() => mflApi.getFreeAgents())
  }, [apiCall])

  // Load player scores
  const loadPlayerScores = useCallback(async (week: number, position?: string) => {
    return apiCall(() => mflApi.getPlayerScores(week, position))
  }, [apiCall])

  // Load projected scores
  const loadProjectedScores = useCallback(async (week: number) => {
    return apiCall(() => mflApi.getProjectedScores(week))
  }, [apiCall])

  // Load draft results
  const loadDraftResults = useCallback(async () => {
    return apiCall(() => mflApi.getDraftResults())
  }, [apiCall])

  // Load injuries
  const loadInjuries = useCallback(async () => {
    return apiCall(() => mflApi.getInjuries())
  }, [apiCall])

  // Initialize with basic data
  const initialize = useCallback(async () => {
    if (!leagueId) return

    setLoading(true)
    try {
      // Load data sequentially to better handle individual failures
      console.log('Loading league info...')
      await loadLeague().catch(err => console.warn('League info failed:', err))
      
      console.log('Loading standings...')
      await loadStandings().catch(err => console.warn('Standings failed:', err))
      
      console.log('Loading players...')
      await loadPlayers().catch(err => console.warn('Players failed:', err))
      
      console.log('Loading rosters...')
      await loadRosters().catch(err => console.warn('Rosters failed:', err))
      
      console.log('MFL initialization complete')
    } catch (err) {
      console.error('Failed to initialize MFL data:', err)
    } finally {
      setLoading(false)
    }
  }, [leagueId, loadLeague, loadPlayers, loadRosters, loadStandings])

  return {
    // State
    loading,
    error,
    league,
    players,
    rosters,
    standings,

    // Actions
    initialize,
    loadLeague,
    loadPlayers,
    loadRosters,
    loadStandings,
    loadFranchiseRoster,
    loadLiveScoring,
    loadSchedule,
    loadTransactions,
    loadFreeAgents,
    loadPlayerScores,
    loadProjectedScores,
    loadDraftResults,
    loadInjuries,

    // Utilities
    setCredentials: mflApi.setCredentials.bind(mflApi),
    setLeague: mflApi.setLeague.bind(mflApi)
  }
}

// Hook for player data with caching
export const useMFLPlayer = (playerId: string) => {
  const [player, setPlayer] = useState<MFLPlayer | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!playerId) return

    const loadPlayer = async () => {
      setLoading(true)
      try {
        const playerData = await mflApi.getPlayerDetails(playerId)
        setPlayer(playerData)
        setError(null)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load player')
      } finally {
        setLoading(false)
      }
    }

    loadPlayer()
  }, [playerId])

  return { player, loading, error }
}

// Hook for franchise data
export const useMFLFranchise = (franchiseId: string) => {
  const [franchise, setFranchise] = useState<MFLFranchise | null>(null)
  const [roster, setRoster] = useState<MFLRoster | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadFranchiseData = useCallback(async () => {
    if (!franchiseId) return

    setLoading(true)
    try {
      const [franchiseData, rosterResp] = await Promise.all([
        mflApi.getFranchiseDetails(franchiseId),
        mflApi.getFranchiseRoster(franchiseId)
      ])
      
      setFranchise(franchiseData)
      setRoster((rosterResp as any)?.rosters?.franchise ?? null)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load franchise data')
    } finally {
      setLoading(false)
    }
  }, [franchiseId])

  useEffect(() => {
    loadFranchiseData()
  }, [loadFranchiseData])

  return { 
    franchise, 
    roster, 
    loading, 
    error, 
    reload: loadFranchiseData 
  }
}