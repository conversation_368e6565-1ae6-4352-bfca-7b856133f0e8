import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('Starting global teardown for E2E tests...');
  
  try {
    // Clean up any global test data
    console.log('Cleaning up test data...');
    
    // Reset any global state
    delete process.env.E2E_SETUP_COMPLETE;
    
    console.log('Global teardown completed successfully');
  } catch (error) {
    console.error('Global teardown failed:', error);
    // Don't throw here as it might mask test failures
  }
}

export default globalTeardown;