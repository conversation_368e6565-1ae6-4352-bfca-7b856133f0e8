import { test, expect } from '@playwright/test';

test.describe('Keeper Workflow E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the keepers page
    await page.goto('/keepers');
  });

  test('should display keeper selection interface', async ({ page }) => {
    // Check that the main keeper interface elements are present
    await expect(page.locator('h1')).toContainText('Keeper Selection');
    
    // Should have a list of eligible players
    await expect(page.locator('[data-testid="keeper-candidates"]')).toBeVisible();
    
    // Should have value analysis section
    await expect(page.locator('[data-testid="value-analysis"]')).toBeVisible();
    
    // Should have scenario comparison
    await expect(page.locator('[data-testid="scenario-comparison"]')).toBeVisible();
  });

  test('should show player value analysis', async ({ page }) => {
    // Wait for data to load
    await page.waitForSelector('[data-testid="keeper-candidates"]');
    
    // Click on a player to see detailed analysis
    const firstPlayer = page.locator('[data-testid="keeper-candidate"]').first();
    await firstPlayer.click();
    
    // Should show value over replacement
    await expect(page.locator('[data-testid="value-over-replacement"]')).toBeVisible();
    
    // Should show keeper cost
    await expect(page.locator('[data-testid="keeper-cost"]')).toBeVisible();
    
    // Should show projected points
    await expect(page.locator('[data-testid="projected-points"]')).toBeVisible();
  });

  test('should allow keeper selection and show scenarios', async ({ page }) => {
    // Wait for candidates to load
    await page.waitForSelector('[data-testid="keeper-candidate"]');
    
    // Select a few keepers
    const candidates = page.locator('[data-testid="keeper-candidate"]');
    const count = await candidates.count();
    
    if (count > 0) {
      // Select first keeper
      await candidates.nth(0).locator('[data-testid="select-keeper"]').click();
      
      // Verify selection
      await expect(candidates.nth(0)).toHaveClass(/selected/);
      
      if (count > 1) {
        // Select second keeper
        await candidates.nth(1).locator('[data-testid="select-keeper"]').click();
        
        // Should update scenario comparison
        await expect(page.locator('[data-testid="scenario-total-value"]')).toBeVisible();
      }
    }
  });

  test('should show keeper deadline countdown', async ({ page }) => {
    // Should display deadline information
    await expect(page.locator('[data-testid="deadline-countdown"]')).toBeVisible();
    
    // Should show time remaining or deadline passed message
    const deadlineElement = page.locator('[data-testid="deadline-countdown"]');
    const text = await deadlineElement.textContent();
    
    expect(text).toMatch(/(days?|hours?|minutes?|deadline|expired)/i);
  });

  test('should provide explanations for recommendations', async ({ page }) => {
    // Wait for recommendations to load
    await page.waitForSelector('[data-testid="keeper-recommendation"]');
    
    // Click on "Why?" button for explanation
    const whyButton = page.locator('[data-testid="why-button"]').first();
    if (await whyButton.isVisible()) {
      await whyButton.click();
      
      // Should show explanation panel
      await expect(page.locator('[data-testid="explanation-panel"]')).toBeVisible();
      
      // Should contain rationale text
      await expect(page.locator('[data-testid="explanation-text"]')).toContainText(/value|points|replacement/i);
    }
  });

  test('should handle no eligible keepers scenario', async ({ page }) => {
    // This test would need specific test data setup
    // For now, we'll check that the interface handles empty states gracefully
    
    const candidatesList = page.locator('[data-testid="keeper-candidates"]');
    
    // If no candidates, should show appropriate message
    const noCandidatesMessage = page.locator('[data-testid="no-candidates-message"]');
    
    // Either candidates exist or no-candidates message is shown
    const hasCandidates = await page.locator('[data-testid="keeper-candidate"]').count() > 0;
    const hasNoMessage = await noCandidatesMessage.isVisible();
    
    expect(hasCandidates || hasNoMessage).toBeTruthy();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Should still show main elements
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('[data-testid="keeper-candidates"]')).toBeVisible();
    
    // Mobile-specific elements should be visible
    const mobileMenu = page.locator('[data-testid="mobile-menu"]');
    if (await mobileMenu.isVisible()) {
      await mobileMenu.click();
      // Should show navigation options
      await expect(page.locator('[data-testid="mobile-nav"]')).toBeVisible();
    }
  });

  test('should handle loading states', async ({ page }) => {
    // Intercept API calls to simulate slow loading
    await page.route('**/api/keepers/**', async route => {
      // Delay the response
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.continue();
    });
    
    // Navigate to page
    await page.goto('/keepers');
    
    // Should show loading indicator
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
    
    // Wait for loading to complete
    await page.waitForSelector('[data-testid="keeper-candidates"]', { timeout: 10000 });
    
    // Loading indicator should be gone
    await expect(page.locator('[data-testid="loading-spinner"]')).not.toBeVisible();
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Intercept API calls to simulate errors
    await page.route('**/api/keepers/**', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      });
    });
    
    // Navigate to page
    await page.goto('/keepers');
    
    // Should show error message
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    
    // Error message should be user-friendly
    const errorText = await page.locator('[data-testid="error-message"]').textContent();
    expect(errorText).toMatch(/(error|failed|try again)/i);
  });
});