import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('Starting global setup for E2E tests...');
  
  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Wait for the application to be ready
    const baseURL = config.projects[0].use.baseURL || 'http://localhost:3000';
    console.log(`Checking if application is ready at ${baseURL}`);
    
    // Try to access the health endpoint or main page
    await page.goto(`${baseURL}/api/health`, { waitUntil: 'networkidle' });
    
    // Check if backend is responding
    const backendURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    try {
      await page.goto(`${backendURL}/health`, { waitUntil: 'networkidle' });
      console.log('Backend health check passed');
    } catch (error) {
      console.warn('Backend health check failed, tests may fail:', error);
    }
    
    // Set up test data if needed
    // This could include creating test users, leagues, etc.
    console.log('Setting up test data...');
    
    // Store any global state that tests might need
    process.env.E2E_SETUP_COMPLETE = 'true';
    
    console.log('Global setup completed successfully');
  } catch (error) {
    console.error('Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export default globalSetup;