#!/usr/bin/env python3
"""
Script to start Celery worker with proper configuration.
"""
import os
import sys
import subprocess
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

def start_worker():
    """Start Celery worker with appropriate settings."""
    
    # Set environment variables
    os.environ.setdefault('PYTHONPATH', str(backend_dir))
    
    # Celery worker command
    cmd = [
        'celery',
        '-A', 'app.core.celery:celery_app',
        'worker',
        '--loglevel=info',
        '--concurrency=4',
        '--queues=default,data_refresh,alerts,file_processing,failed',
        '--hostname=worker@%h',
        '--without-gossip',
        '--without-mingle',
        '--without-heartbeat'
    ]
    
    print("Starting Celery worker...")
    print(f"Command: {' '.join(cmd)}")
    print(f"Working directory: {backend_dir}")
    
    try:
        # Start the worker
        subprocess.run(cmd, cwd=backend_dir, check=True)
    except KeyboardInterrupt:
        print("\nShutting down worker...")
    except subprocess.CalledProcessError as e:
        print(f"Worker failed with exit code {e.returncode}")
        sys.exit(e.returncode)

if __name__ == "__main__":
    start_worker()