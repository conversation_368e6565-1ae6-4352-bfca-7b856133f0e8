#!/usr/bin/env python3
"""
Simple test runner for database models.

This script tests the basic functionality of our database models.
"""
import sys
from pathlib import Path
from decimal import Decimal
from datetime import datetime, timedelta

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.models.base import Base
from app.models import (
    League, Player, PlayerPosition, InjuryStatus, Franchise, Roster, 
    RosterPlayer, Projection, Recommendation, RecommendationType, 
    RecommendationPriority
)


def test_models():
    """Test basic model functionality."""
    print("Testing database models...")
    
    # Create in-memory SQLite database for testing
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    Base.metadata.create_all(engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    try:
        # Test League creation
        print("✓ Testing League model...")
        league = League(
            id="test_league",
            name="Test League",
            season=2024,
            scoring_rules={
                "passing_yards": 0.04,
                "rushing_yards": 0.1,
                "touchdowns": 6
            },
            roster_slots=[
                {"position": "QB", "count": 1},
                {"position": "RB", "count": 2}
            ]
        )
        session.add(league)
        session.commit()
        
        assert league.validate_scoring_rules() is False  # Missing required fields
        league.scoring_rules.update({
            "receiving_yards": 0.1,
            "passing_touchdowns": 4,
            "rushing_touchdowns": 6,
            "receiving_touchdowns": 6
        })
        assert league.validate_scoring_rules() is True
        assert league.get_roster_slot_count("QB") == 1
        assert league.get_roster_slot_count("RB") == 2
        
        # Test Player creation
        print("✓ Testing Player model...")
        player = Player(
            id="test_player",
            name="Test Player",
            position=PlayerPosition.QB,
            team="KC",
            bye_week=10
        )
        session.add(player)
        session.commit()
        
        assert player.position == PlayerPosition.QB
        assert player.injury_status == InjuryStatus.HEALTHY
        assert player.is_available() is True
        
        # Test Franchise creation
        print("✓ Testing Franchise model...")
        franchise = Franchise(
            id="test_franchise",
            name="Test Team",
            owner_name="Test Owner",
            league_id=league.id,
            faab_budget=Decimal("100.00"),
            faab_spent=Decimal("25.50")
        )
        session.add(franchise)
        session.commit()
        
        assert franchise.get_remaining_faab() == Decimal("74.50")
        
        # Test Roster and RosterPlayer
        print("✓ Testing Roster models...")
        roster = Roster(
            id="test_roster",
            franchise_id=franchise.id
        )
        session.add(roster)
        
        roster_player = RosterPlayer(
            id="test_rp",
            roster_id=roster.id,
            player_id=player.id,
            roster_slot="QB",
            is_keeper=True,
            keeper_cost=3
        )
        session.add(roster_player)
        session.commit()
        
        assert roster_player.is_starting() is True
        
        # Test Projection
        print("✓ Testing Projection model...")
        projection = Projection(
            id="test_projection",
            player_id=player.id,
            week=1,
            season=2024,
            source="TestSource",
            projected_points=Decimal("18.5"),
            floor=Decimal("12.0"),
            ceiling=Decimal("25.0"),
            confidence_level=Decimal("0.85")
        )
        session.add(projection)
        session.commit()
        
        assert projection.is_season_long() is False
        assert projection.get_confidence_interval() == (Decimal("12.0"), Decimal("25.0"))
        
        # Test season-long projection
        season_projection = Projection(
            id="test_season_projection",
            player_id=player.id,
            week=None,
            season=2024,
            source="TestSource",
            projected_points=Decimal("285.6")
        )
        session.add(season_projection)
        session.commit()
        
        assert season_projection.is_season_long() is True
        
        # Test Recommendation
        print("✓ Testing Recommendation model...")
        recommendation = Recommendation(
            id="test_recommendation",
            league_id=league.id,
            franchise_id=franchise.id,
            type=RecommendationType.KEEPER,
            title="Test Recommendation",
            description="Test description",
            rationale="Test rationale",
            confidence=Decimal("0.85"),
            expires_at=datetime.now() + timedelta(hours=1)
        )
        session.add(recommendation)
        session.commit()
        
        assert recommendation.is_expired() is False
        assert recommendation.status.value == "ACTIVE"
        
        # Test alternatives
        alternative = {
            "title": "Alternative option",
            "rationale": "Alternative rationale",
            "confidence": 0.65
        }
        recommendation.add_alternative(alternative)
        assert len(recommendation.alternatives) == 1
        assert recommendation.get_primary_alternative() == alternative
        
        # Test status updates
        recommendation.mark_accepted()
        assert recommendation.status.value == "ACCEPTED"
        
        print("✅ All model tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        session.close()
    
    return True


def test_relationships():
    """Test model relationships."""
    print("Testing model relationships...")
    
    # Create in-memory SQLite database for testing
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    Base.metadata.create_all(engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    try:
        # Create related objects
        league = League(
            id="test_league",
            name="Test League",
            season=2024,
            scoring_rules={"passing_yards": 0.04},
            roster_slots=[{"position": "QB", "count": 1}]
        )
        session.add(league)
        
        franchise = Franchise(
            id="test_franchise",
            name="Test Team",
            owner_name="Test Owner",
            league_id=league.id
        )
        session.add(franchise)
        
        player = Player(
            id="test_player",
            name="Test Player",
            position=PlayerPosition.QB,
            team="KC"
        )
        session.add(player)
        
        roster = Roster(
            id="test_roster",
            franchise_id=franchise.id
        )
        session.add(roster)
        
        roster_player = RosterPlayer(
            id="test_rp",
            roster_id=roster.id,
            player_id=player.id,
            roster_slot="QB"
        )
        session.add(roster_player)
        
        projection = Projection(
            id="test_projection",
            player_id=player.id,
            season=2024,
            source="TestSource",
            projected_points=Decimal("18.5")
        )
        session.add(projection)
        
        recommendation = Recommendation(
            id="test_recommendation",
            league_id=league.id,
            franchise_id=franchise.id,
            type=RecommendationType.KEEPER,
            title="Test Recommendation",
            description="Test description",
            rationale="Test rationale",
            confidence=Decimal("0.85")
        )
        session.add(recommendation)
        
        session.commit()
        
        # Test relationships
        assert len(league.franchises) == 1
        assert league.franchises[0] == franchise
        
        assert franchise.league == league
        assert franchise.roster == roster
        
        assert len(player.projections) == 1
        assert player.projections[0] == projection
        
        assert len(player.roster_players) == 1
        assert player.roster_players[0] == roster_player
        
        assert roster_player.player == player
        assert roster_player.roster == roster
        
        assert len(roster.roster_players) == 1
        assert roster.get_active_players()[0] == roster_player
        
        print("✅ All relationship tests passed!")
        
    except Exception as e:
        print(f"❌ Relationship test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        session.close()
    
    return True


def main():
    """Main test function."""
    print("Running database model tests...\n")
    
    success = True
    
    if not test_models():
        success = False
    
    print()
    
    if not test_relationships():
        success = False
    
    print()
    
    if success:
        print("🎉 All tests passed successfully!")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())