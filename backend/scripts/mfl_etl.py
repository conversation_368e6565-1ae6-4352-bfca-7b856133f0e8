#!/usr/bin/env python3
"""
MFL ETL Pipeline Script

Command-line script for running MFL data ingestion and ETL processes.
Supports both one-time ingestion and scheduled updates.
"""
import argparse
import logging
import sys
import json
from datetime import datetime
from pathlib import Path

# Add the backend app to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from app.core.database import get_db
from app.services.mfl_ingestion import create_mfl_ingestion_service, MFLIngestionError


def setup_logging(log_level: str = "INFO") -> None:
    """Set up logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('mfl_etl.log')
        ]
    )


def ingest_league_data(league_id: str, api_key: str, validate: bool = True) -> dict:
    """
    Ingest data for a single league.
    
    Args:
        league_id: MFL league identifier
        api_key: MFL API key
        validate: Whether to validate ingested data
        
    Returns:
        Dictionary with ingestion results
    """
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"Starting MFL data ingestion for league {league_id}")
        
        # Create ingestion service
        service = create_mfl_ingestion_service()
        
        # Run ingestion
        results = service.ingest_league_data(league_id, api_key)
        
        # Validate data if requested
        if validate:
            logger.info("Validating ingested data...")
            validation_results = service.validate_ingested_data(f"mfl_{league_id}")
            results["validation"] = validation_results
            
            if not validation_results["is_valid"]:
                logger.warning(f"Data validation found issues: {validation_results['issues']}")
            else:
                logger.info("Data validation passed")
        
        logger.info(f"MFL ingestion completed successfully for league {league_id}")
        return results
        
    except MFLIngestionError as e:
        logger.error(f"MFL ingestion failed: {e}")
        return {"error": str(e), "league_id": league_id}
    except Exception as e:
        logger.error(f"Unexpected error during ingestion: {e}")
        return {"error": f"Unexpected error: {e}", "league_id": league_id}


def ingest_multiple_leagues(config_file: str) -> list:
    """
    Ingest data for multiple leagues from configuration file.
    
    Args:
        config_file: Path to JSON configuration file
        
    Returns:
        List of ingestion results for each league
    """
    logger = logging.getLogger(__name__)
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        results = []
        for league_config in config.get("leagues", []):
            league_id = league_config["league_id"]
            api_key = league_config["api_key"]
            validate = league_config.get("validate", True)
            
            logger.info(f"Processing league {league_id}")
            result = ingest_league_data(league_id, api_key, validate)
            results.append(result)
            
            # Add delay between leagues if specified
            import time
            delay = config.get("delay_between_leagues", 0)
            if delay > 0:
                logger.info(f"Waiting {delay} seconds before next league...")
                time.sleep(delay)
        
        return results
        
    except FileNotFoundError:
        logger.error(f"Configuration file not found: {config_file}")
        return [{"error": f"Configuration file not found: {config_file}"}]
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in configuration file: {e}")
        return [{"error": f"Invalid JSON in configuration file: {e}"}]
    except Exception as e:
        logger.error(f"Error processing configuration file: {e}")
        return [{"error": f"Error processing configuration file: {e}"}]


def print_results(results: dict or list) -> None:
    """Print ingestion results in a formatted way."""
    if isinstance(results, list):
        # Multiple league results
        print("\n" + "="*60)
        print("MFL ETL PIPELINE RESULTS")
        print("="*60)
        
        total_leagues = len(results)
        successful_leagues = sum(1 for r in results if "error" not in r)
        
        print(f"Total leagues processed: {total_leagues}")
        print(f"Successful ingestions: {successful_leagues}")
        print(f"Failed ingestions: {total_leagues - successful_leagues}")
        
        for i, result in enumerate(results, 1):
            print(f"\n--- League {i} ---")
            if "error" in result:
                print(f"❌ ERROR: {result['error']}")
                if "league_id" in result:
                    print(f"League ID: {result['league_id']}")
            else:
                print(f"✅ SUCCESS: League {result['league_id']}")
                print(f"   Leagues: {result['leagues_processed']}")
                print(f"   Players: {result['players_processed']}")
                print(f"   Franchises: {result['franchises_processed']}")
                print(f"   Rosters: {result['rosters_processed']}")
                
                if result.get("errors"):
                    print(f"   Warnings: {len(result['errors'])}")
                    for error in result["errors"]:
                        print(f"     - {error}")
                
                if "validation" in result:
                    validation = result["validation"]
                    status = "✅ PASSED" if validation["is_valid"] else "⚠️  ISSUES"
                    print(f"   Validation: {status}")
                    if validation["issues"]:
                        for issue in validation["issues"]:
                            print(f"     - {issue}")
    else:
        # Single league result
        print("\n" + "="*60)
        print("MFL ETL PIPELINE RESULT")
        print("="*60)
        
        if "error" in results:
            print(f"❌ ERROR: {results['error']}")
            if "league_id" in results:
                print(f"League ID: {results['league_id']}")
        else:
            print(f"✅ SUCCESS: League {results['league_id']}")
            print(f"Ingestion completed at: {results['ingestion_timestamp']}")
            print(f"Leagues processed: {results['leagues_processed']}")
            print(f"Players processed: {results['players_processed']}")
            print(f"Franchises processed: {results['franchises_processed']}")
            print(f"Rosters processed: {results['rosters_processed']}")
            
            if results.get("errors"):
                print(f"Warnings ({len(results['errors'])}):")
                for error in results["errors"]:
                    print(f"  - {error}")
            
            if "validation" in results:
                validation = results["validation"]
                status = "PASSED" if validation["is_valid"] else "FAILED"
                print(f"Data validation: {status}")
                if validation["issues"]:
                    print("Validation issues:")
                    for issue in validation["issues"]:
                        print(f"  - {issue}")


def create_sample_config(output_file: str) -> None:
    """Create a sample configuration file."""
    sample_config = {
        "leagues": [
            {
                "league_id": "12345",
                "api_key": "your_mfl_api_key_here",
                "validate": True
            }
        ],
        "delay_between_leagues": 5,
        "settings": {
            "log_level": "INFO",
            "backup_before_ingestion": False
        }
    }
    
    with open(output_file, 'w') as f:
        json.dump(sample_config, f, indent=2)
    
    print(f"Sample configuration file created: {output_file}")
    print("Please edit the file with your actual league IDs and API keys.")


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="MFL ETL Pipeline - Ingest fantasy football data from MyFantasyLeague",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Ingest single league
  python mfl_etl.py --league-id 12345 --api-key YOUR_API_KEY
  
  # Ingest multiple leagues from config file
  python mfl_etl.py --config leagues.json
  
  # Create sample configuration file
  python mfl_etl.py --create-config sample_config.json
  
  # Ingest with validation disabled
  python mfl_etl.py --league-id 12345 --api-key YOUR_API_KEY --no-validate
        """
    )
    
    # Main operation modes
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument(
        "--league-id",
        help="MFL league ID for single league ingestion"
    )
    group.add_argument(
        "--config",
        help="JSON configuration file for multiple league ingestion"
    )
    group.add_argument(
        "--create-config",
        help="Create a sample configuration file"
    )
    
    # Single league options
    parser.add_argument(
        "--api-key",
        help="MFL API key (required for single league ingestion)"
    )
    
    # General options
    parser.add_argument(
        "--no-validate",
        action="store_true",
        help="Skip data validation after ingestion"
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Set logging level (default: INFO)"
    )
    parser.add_argument(
        "--output",
        help="Save results to JSON file"
    )
    
    args = parser.parse_args()
    
    # Set up logging
    setup_logging(args.log_level)
    
    # Handle create-config mode
    if args.create_config:
        create_sample_config(args.create_config)
        return
    
    # Handle single league mode
    if args.league_id:
        if not args.api_key:
            print("Error: --api-key is required for single league ingestion")
            sys.exit(1)
        
        validate = not args.no_validate
        results = ingest_league_data(args.league_id, args.api_key, validate)
    
    # Handle config file mode
    elif args.config:
        results = ingest_multiple_leagues(args.config)
    
    # Print results
    print_results(results)
    
    # Save results to file if requested
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\nResults saved to: {args.output}")
    
    # Exit with error code if any ingestion failed
    if isinstance(results, list):
        failed_count = sum(1 for r in results if "error" in r)
        if failed_count > 0:
            sys.exit(1)
    elif "error" in results:
        sys.exit(1)


if __name__ == "__main__":
    main()