#!/usr/bin/env python3
"""
Script to start Celery beat scheduler with proper configuration.
"""
import os
import sys
import subprocess
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

def start_beat():
    """Start Celery beat scheduler with appropriate settings."""
    
    # Set environment variables
    os.environ.setdefault('PYTHONPATH', str(backend_dir))
    
    # Celery beat command
    cmd = [
        'celery',
        '-A', 'app.core.celery:celery_app',
        'beat',
        '--loglevel=info',
        '--schedule=/tmp/celerybeat-schedule',
        '--pidfile=/tmp/celerybeat.pid'
    ]
    
    print("Starting Celery beat scheduler...")
    print(f"Command: {' '.join(cmd)}")
    print(f"Working directory: {backend_dir}")
    
    try:
        # Start the scheduler
        subprocess.run(cmd, cwd=backend_dir, check=True)
    except KeyboardInterrupt:
        print("\nShutting down beat scheduler...")
    except subprocess.CalledProcessError as e:
        print(f"Beat scheduler failed with exit code {e.returncode}")
        sys.exit(e.returncode)

if __name__ == "__main__":
    start_beat()