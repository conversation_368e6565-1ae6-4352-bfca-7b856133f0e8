#!/usr/bin/env python3
"""
Validate database model structure without running them.

This script performs static analysis of the model files to ensure they're properly structured.
"""
import sys
import ast
import importlib.util
from pathlib import Path


def validate_model_file(file_path):
    """Validate a single model file."""
    print(f"Validating {file_path.name}...")
    
    try:
        # Parse the file as AST
        with open(file_path, 'r') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        # Check for required imports
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module)
        
        # Check for class definitions
        classes = []
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes.append(node.name)
        
        print(f"  ✓ Found classes: {', '.join(classes)}")
        print(f"  ✓ Imports look good")
        
        return True
        
    except SyntaxError as e:
        print(f"  ❌ Syntax error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False


def validate_all_models():
    """Validate all model files."""
    print("Validating database models...\n")
    
    backend_dir = Path(__file__).parent.parent
    models_dir = backend_dir / "app" / "models"
    
    if not models_dir.exists():
        print("❌ Models directory not found!")
        return False
    
    model_files = [
        "base.py",
        "league.py", 
        "player.py",
        "roster.py",
        "projection.py",
        "recommendation.py"
    ]
    
    success = True
    
    for model_file in model_files:
        file_path = models_dir / model_file
        if file_path.exists():
            if not validate_model_file(file_path):
                success = False
        else:
            print(f"❌ Missing model file: {model_file}")
            success = False
        print()
    
    # Validate __init__.py
    init_file = models_dir / "__init__.py"
    if init_file.exists():
        print("Validating __init__.py...")
        if validate_model_file(init_file):
            print("  ✓ __init__.py looks good")
        else:
            success = False
    else:
        print("❌ Missing __init__.py file")
        success = False
    
    return success


def validate_database_config():
    """Validate database configuration files."""
    print("\nValidating database configuration...")
    
    backend_dir = Path(__file__).parent.parent
    
    # Check database.py
    db_file = backend_dir / "app" / "core" / "database.py"
    if db_file.exists():
        print("✓ database.py exists")
        if validate_model_file(db_file):
            print("  ✓ database.py structure looks good")
        else:
            return False
    else:
        print("❌ database.py missing")
        return False
    
    # Check sample_data.py
    sample_file = backend_dir / "app" / "core" / "sample_data.py"
    if sample_file.exists():
        print("✓ sample_data.py exists")
        if validate_model_file(sample_file):
            print("  ✓ sample_data.py structure looks good")
        else:
            return False
    else:
        print("❌ sample_data.py missing")
        return False
    
    # Check Alembic configuration
    alembic_ini = backend_dir / "alembic.ini"
    if alembic_ini.exists():
        print("✓ alembic.ini exists")
    else:
        print("❌ alembic.ini missing")
        return False
    
    alembic_env = backend_dir / "alembic" / "env.py"
    if alembic_env.exists():
        print("✓ alembic/env.py exists")
    else:
        print("❌ alembic/env.py missing")
        return False
    
    return True


def validate_test_files():
    """Validate test files."""
    print("\nValidating test files...")
    
    backend_dir = Path(__file__).parent.parent
    
    # Check test_models.py
    test_file = backend_dir / "tests" / "test_models.py"
    if test_file.exists():
        print("✓ test_models.py exists")
        if validate_model_file(test_file):
            print("  ✓ test_models.py structure looks good")
        else:
            return False
    else:
        print("❌ test_models.py missing")
        return False
    
    return True


def main():
    """Main validation function."""
    print("🔍 Validating AI Fantasy Assistant Database Models\n")
    
    success = True
    
    if not validate_all_models():
        success = False
    
    if not validate_database_config():
        success = False
    
    if not validate_test_files():
        success = False
    
    print("\n" + "="*50)
    
    if success:
        print("🎉 All validations passed!")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Initialize database: python scripts/init_db.py --sample-data")
        print("3. Run tests: python -m pytest tests/test_models.py -v")
        return 0
    else:
        print("💥 Some validations failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())