#!/usr/bin/env python3
"""
Database initialization script.

This script creates the database tables and optionally loads sample data.
"""
import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.database import init_db, create_tables
from app.core.sample_data import create_sample_data, clear_sample_data
from app.core.database import SessionLocal


def main():
    """Main initialization function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Initialize the fantasy assistant database")
    parser.add_argument(
        "--sample-data", 
        action="store_true", 
        help="Load sample data after creating tables"
    )
    parser.add_argument(
        "--clear-data", 
        action="store_true", 
        help="Clear all existing data before initialization"
    )
    parser.add_argument(
        "--tables-only", 
        action="store_true", 
        help="Only create tables, don't load any data"
    )
    
    args = parser.parse_args()
    
    print("Initializing Fantasy Assistant database...")
    
    try:
        if args.clear_data:
            print("Clearing existing data...")
            db = SessionLocal()
            try:
                clear_sample_data(db)
            finally:
                db.close()
        
        if args.tables_only:
            print("Creating database tables...")
            create_tables()
            print("Database tables created successfully!")
        elif args.sample_data:
            print("Creating tables and loading sample data...")
            init_db()
            print("Database initialized with sample data successfully!")
        else:
            print("Creating database tables...")
            create_tables()
            print("Database tables created successfully!")
            print("Use --sample-data flag to load sample data.")
            
    except Exception as e:
        print(f"Error initializing database: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()