#!/usr/bin/env python3
"""
Create a new Alembic migration.

This script helps create database migrations using Alembic.
"""
import sys
import os
import subprocess
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))


def run_alembic_command(command_args):
    """Run an Alembic command."""
    try:
        # Change to the backend directory
        backend_dir = Path(__file__).parent.parent
        os.chdir(backend_dir)
        
        # Run the alembic command
        cmd = [sys.executable, "-m", "alembic"] + command_args
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
            return True
        else:
            print(f"Error running alembic: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Create database migrations")
    parser.add_argument(
        "--message", "-m",
        required=True,
        help="Migration message"
    )
    parser.add_argument(
        "--autogenerate",
        action="store_true",
        help="Auto-generate migration from model changes"
    )
    
    args = parser.parse_args()
    
    print(f"Creating migration: {args.message}")
    
    command_args = ["revision"]
    if args.autogenerate:
        command_args.append("--autogenerate")
    command_args.extend(["-m", args.message])
    
    if run_alembic_command(command_args):
        print("Migration created successfully!")
    else:
        print("Failed to create migration.")
        sys.exit(1)


if __name__ == "__main__":
    main()