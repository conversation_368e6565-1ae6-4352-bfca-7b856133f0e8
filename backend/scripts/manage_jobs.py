#!/usr/bin/env python3
"""
Management script for background jobs.
"""
import sys
import argparse
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.tasks.monitoring import JobMonitor
from app.tasks.data_refresh import refresh_mfl_data, refresh_projection_aggregates
from app.tasks.alert_processing import check_upcoming_deadlines, process_player_news
from app.core.celery import celery_app


def list_active_tasks():
    """List all currently active tasks."""
    monitor = JobMonitor()
    tasks = monitor.get_active_tasks()
    
    if not tasks:
        print("No active tasks found.")
        return
    
    print(f"Found {len(tasks)} active tasks:")
    for task in tasks:
        print(f"  - {task['id']}: {task['name']} (worker: {task['worker']})")


def list_scheduled_tasks():
    """List all scheduled tasks."""
    monitor = JobMonitor()
    tasks = monitor.get_scheduled_tasks()
    
    if not tasks:
        print("No scheduled tasks found.")
        return
    
    print(f"Found {len(tasks)} scheduled tasks:")
    for task in tasks:
        print(f"  - {task['id']}: {task['name']} (eta: {task['eta']})")


def get_worker_stats():
    """Get worker statistics."""
    monitor = JobMonitor()
    stats = monitor.get_worker_stats()
    
    print(f"Workers: {stats['workers']}")
    for worker, details in stats['details'].items():
        print(f"  {worker}:")
        print(f"    Status: {details['status']}")
        print(f"    Active tasks: {details['active_tasks']}")
        print(f"    Processed tasks: {details['processed_tasks']}")


def get_queue_lengths():
    """Get queue lengths."""
    monitor = JobMonitor()
    queues = monitor.get_queue_lengths()
    
    if not queues:
        print("No queues found or all queues are empty.")
        return
    
    print("Queue lengths:")
    for queue, length in queues.items():
        print(f"  {queue}: {length}")


def trigger_mfl_refresh():
    """Trigger MFL data refresh."""
    print("Triggering MFL data refresh...")
    task = refresh_mfl_data.delay()
    print(f"Task started with ID: {task.id}")
    return task.id


def trigger_projection_refresh():
    """Trigger projection aggregation refresh."""
    print("Triggering projection aggregation refresh...")
    task = refresh_projection_aggregates.delay()
    print(f"Task started with ID: {task.id}")
    return task.id


def trigger_deadline_check():
    """Trigger deadline check."""
    print("Triggering deadline check...")
    task = check_upcoming_deadlines.delay()
    print(f"Task started with ID: {task.id}")
    return task.id


def trigger_news_processing():
    """Trigger player news processing."""
    print("Triggering player news processing...")
    task = process_player_news.delay()
    print(f"Task started with ID: {task.id}")
    return task.id


def get_task_status(task_id):
    """Get status of a specific task."""
    monitor = JobMonitor()
    status = monitor.get_task_status(task_id)
    
    print(f"Task {task_id}:")
    print(f"  Status: {status['status']}")
    print(f"  Task name: {status.get('task_name', 'Unknown')}")
    if status['result']:
        print(f"  Result: {status['result']}")
    if status['traceback']:
        print(f"  Error: {status['traceback']}")


def cancel_task(task_id):
    """Cancel a running task."""
    monitor = JobMonitor()
    result = monitor.cancel_task(task_id)
    
    if result['success']:
        print(f"Task {task_id} cancelled successfully.")
    else:
        print(f"Failed to cancel task {task_id}: {result['error']}")


def retry_task(task_id):
    """Retry a failed task."""
    monitor = JobMonitor()
    result = monitor.retry_failed_task(task_id)
    
    if result['success']:
        print(f"Task {task_id} retried successfully. New task ID: {result['new_task_id']}")
    else:
        print(f"Failed to retry task {task_id}: {result['error']}")


def purge_queues():
    """Purge all queues."""
    print("Purging all queues...")
    celery_app.control.purge()
    print("All queues purged.")


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="Manage background jobs")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List commands
    subparsers.add_parser('list-active', help='List active tasks')
    subparsers.add_parser('list-scheduled', help='List scheduled tasks')
    subparsers.add_parser('worker-stats', help='Get worker statistics')
    subparsers.add_parser('queue-lengths', help='Get queue lengths')
    
    # Trigger commands
    subparsers.add_parser('trigger-mfl', help='Trigger MFL data refresh')
    subparsers.add_parser('trigger-projections', help='Trigger projection refresh')
    subparsers.add_parser('trigger-deadlines', help='Trigger deadline check')
    subparsers.add_parser('trigger-news', help='Trigger news processing')
    
    # Task management commands
    status_parser = subparsers.add_parser('status', help='Get task status')
    status_parser.add_argument('task_id', help='Task ID')
    
    cancel_parser = subparsers.add_parser('cancel', help='Cancel task')
    cancel_parser.add_argument('task_id', help='Task ID')
    
    retry_parser = subparsers.add_parser('retry', help='Retry failed task')
    retry_parser.add_argument('task_id', help='Task ID')
    
    # Maintenance commands
    subparsers.add_parser('purge', help='Purge all queues')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'list-active':
            list_active_tasks()
        elif args.command == 'list-scheduled':
            list_scheduled_tasks()
        elif args.command == 'worker-stats':
            get_worker_stats()
        elif args.command == 'queue-lengths':
            get_queue_lengths()
        elif args.command == 'trigger-mfl':
            trigger_mfl_refresh()
        elif args.command == 'trigger-projections':
            trigger_projection_refresh()
        elif args.command == 'trigger-deadlines':
            trigger_deadline_check()
        elif args.command == 'trigger-news':
            trigger_news_processing()
        elif args.command == 'status':
            get_task_status(args.task_id)
        elif args.command == 'cancel':
            cancel_task(args.task_id)
        elif args.command == 'retry':
            retry_task(args.task_id)
        elif args.command == 'purge':
            purge_queues()
        else:
            print(f"Unknown command: {args.command}")
            parser.print_help()
    
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()