#!/usr/bin/env python3
"""
Create alert tables in the database.
"""
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from app.models.base import Base
from app.models.alert import Alert, AlertSchedule, PlayerNewsAlert
from app.core.config import settings


def create_alert_tables():
    """Create alert tables in the database."""
    # Use development database URL
    engine = create_engine(settings.database_url_dev)
    
    # Create all tables
    Base.metadata.create_all(engine)
    
    print("Alert tables created successfully!")


if __name__ == "__main__":
    create_alert_tables()