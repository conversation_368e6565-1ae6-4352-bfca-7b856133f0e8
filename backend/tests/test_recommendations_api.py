"""
Integration tests for recommendations API endpoints.
"""
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.core.database import get_db
from app.models.league import League
from app.models.roster import Franchise
from app.models.recommendation import Recommendation, RecommendationType


client = TestClient(app)


class TestRecommendationsAPI:
    """Test cases for recommendations API endpoints."""
    
    @pytest.fixture
    def test_league(self, db_session: Session):
        """Create a test league for recommendation tests."""
        league = League(
            id="rec_test_league",
            name="Recommendation Test League",
            season=2024,
            scoring_rules={},
            roster_slots=[]
        )
        db_session.add(league)
        db_session.commit()
        return league
    
    @pytest.fixture
    def test_franchise(self, db_session: Session, test_league: League):
        """Create a test franchise for recommendation tests."""
        franchise = Franchise(
            id="rec_test_franchise",
            name="Recommendation Test Team",
            owner_name="Recommendation Test Owner",
            league_id=test_league.id
        )
        db_session.add(franchise)
        db_session.commit()
        return franchise
    
    @pytest.fixture
    def test_recommendations(self, db_session: Session, test_franchise: Franchise):
        """Create test recommendations."""
        recommendations = [
            Recommendation(
                id="keeper_rec_001",
                type=RecommendationType.KEEPER,
                franchise_id=test_franchise.id,
                title="Keep Christian McCaffrey",
                description="McCaffrey provides excellent value as a 3rd round keeper",
                rationale="High projected points with low keeper cost",
                confidence=0.85,
                alternatives=[
                    {
                        "title": "Keep Tyreek Hill",
                        "description": "Alternative keeper option",
                        "confidence": 0.75
                    }
                ],
                metadata={
                    "risk_factors": ["Injury history"],
                    "expected_impact": {"points_gained": 50},
                    "supporting_data": {"projected_points": 280}
                },
                expires_at=datetime.utcnow() + timedelta(days=30)
            ),
            Recommendation(
                id="draft_rec_001",
                type=RecommendationType.DRAFT,
                franchise_id=test_franchise.id,
                title="Draft Josh Allen",
                description="Allen is the best available QB at this pick",
                rationale="Tier 1 QB with high upside",
                confidence=0.92,
                alternatives=[],
                metadata={
                    "risk_factors": [],
                    "expected_impact": {"win_probability_change": 0.05},
                    "supporting_data": {"tier": 1, "value_over_replacement": 45}
                }
            ),
            Recommendation(
                id="trade_rec_001",
                type=RecommendationType.TRADE,
                franchise_id=test_franchise.id,
                title="Trade for Travis Kelce",
                description="Upgrade at TE position",
                rationale="Addresses positional need with fair value exchange",
                confidence=0.68,
                alternatives=[],
                metadata={
                    "risk_factors": ["Age concerns"],
                    "expected_impact": {"win_probability_change": 0.03},
                    "supporting_data": {"fairness_score": 0.85}
                },
                is_active=False  # Inactive recommendation
            )
        ]
        
        for rec in recommendations:
            db_session.add(rec)
        db_session.commit()
        return recommendations
    
    def test_list_recommendations_all(self, db_session: Session, test_recommendations: list):
        """Test listing all recommendations."""
        response = client.get("/api/v1/recommendations/")
        
        assert response.status_code == 200
        data = response.json()
        # Should only return active recommendations by default
        assert len(data) == 2
        
        rec_ids = [rec["id"] for rec in data]
        assert "keeper_rec_001" in rec_ids
        assert "draft_rec_001" in rec_ids
        assert "trade_rec_001" not in rec_ids  # Inactive
    
    def test_list_recommendations_by_franchise(self, db_session: Session, test_recommendations: list, test_franchise: Franchise):
        """Test listing recommendations filtered by franchise."""
        response = client.get(f"/api/v1/recommendations/?franchise_id={test_franchise.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert all(rec["id"] in ["keeper_rec_001", "draft_rec_001"] for rec in data)
    
    def test_list_recommendations_by_type(self, db_session: Session, test_recommendations: list):
        """Test listing recommendations filtered by type."""
        response = client.get("/api/v1/recommendations/?recommendation_type=keeper")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["id"] == "keeper_rec_001"
        assert data[0]["type"] == "keeper"
    
    def test_list_recommendations_by_confidence(self, db_session: Session, test_recommendations: list):
        """Test listing recommendations filtered by minimum confidence."""
        response = client.get("/api/v1/recommendations/?min_confidence=0.9")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["id"] == "draft_rec_001"
        assert data[0]["confidence"] >= 0.9
    
    def test_list_recommendations_include_inactive(self, db_session: Session, test_recommendations: list):
        """Test listing recommendations including inactive ones."""
        response = client.get("/api/v1/recommendations/?active_only=false")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3
        
        rec_ids = [rec["id"] for rec in data]
        assert "trade_rec_001" in rec_ids
    
    def test_get_recommendation_success(self, db_session: Session, test_recommendations: list):
        """Test getting a specific recommendation."""
        response = client.get("/api/v1/recommendations/keeper_rec_001")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "keeper_rec_001"
        assert data["type"] == "keeper"
        assert data["title"] == "Keep Christian McCaffrey"
        assert data["description"] == "McCaffrey provides excellent value as a 3rd round keeper"
        assert data["rationale"] == "High projected points with low keeper cost"
        assert data["confidence"] == 0.85
        assert data["confidence_level"] == "high"
        assert len(data["alternatives"]) == 1
        assert data["alternatives"][0]["title"] == "Keep Tyreek Hill"
        assert data["risk_factors"] == ["Injury history"]
        assert data["expected_impact"]["points_gained"] == 50
        assert data["supporting_data"]["projected_points"] == 280
        assert data["is_active"] == True
        assert data["expires_at"] is not None
    
    def test_get_recommendation_not_found(self, db_session: Session):
        """Test getting a non-existent recommendation."""
        response = client.get("/api/v1/recommendations/nonexistent_rec")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_explain_recommendation_success(self, db_session: Session, test_recommendations: list):
        """Test getting detailed explanation for a recommendation."""
        explanation_request = {
            "include_alternatives": True,
            "include_supporting_data": True,
            "include_risk_analysis": True,
            "detail_level": "standard"
        }
        
        response = client.post("/api/v1/recommendations/keeper_rec_001/explain", json=explanation_request)
        
        assert response.status_code == 200
        data = response.json()
        assert data["recommendation_id"] == "keeper_rec_001"
        assert "explanation" in data
        assert "methodology" in data
        assert "key_factors" in data
        assert "assumptions" in data
        assert "limitations" in data
        assert "confidence_breakdown" in data
        assert "alternative_scenarios" in data
        assert "data_sources" in data
        assert "last_updated" in data
        
        # Check that explanation is specific to keeper recommendations
        assert "value over replacement" in data["explanation"].lower()
        assert "Integer Linear Programming" in data["methodology"]
        assert len(data["key_factors"]) > 0
        assert len(data["assumptions"]) > 0
        assert len(data["data_sources"]) > 0
    
    def test_explain_recommendation_detailed(self, db_session: Session, test_recommendations: list):
        """Test getting detailed explanation with sensitivity analysis."""
        explanation_request = {
            "include_alternatives": True,
            "include_supporting_data": True,
            "include_risk_analysis": True,
            "detail_level": "detailed"
        }
        
        response = client.post("/api/v1/recommendations/draft_rec_001/explain", json=explanation_request)
        
        assert response.status_code == 200
        data = response.json()
        assert data["recommendation_id"] == "draft_rec_001"
        assert "sensitivity_analysis" in data
        assert data["sensitivity_analysis"] is not None
    
    def test_get_active_recommendations_for_franchise(self, db_session: Session, test_recommendations: list, test_franchise: Franchise):
        """Test getting all active recommendations for a franchise."""
        response = client.get(f"/api/v1/recommendations/franchise/{test_franchise.id}/active")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2  # Only active recommendations
        
        rec_ids = [rec["id"] for rec in data]
        assert "keeper_rec_001" in rec_ids
        assert "draft_rec_001" in rec_ids
        assert "trade_rec_001" not in rec_ids  # Inactive
        
        # Check that all recommendations belong to the franchise
        assert all(rec["franchise_id"] == test_franchise.id for rec in data)
        
        # Check that detailed information is included
        keeper_rec = next(rec for rec in data if rec["id"] == "keeper_rec_001")
        assert keeper_rec["confidence_level"] == "high"
        assert len(keeper_rec["alternatives"]) == 1
        assert keeper_rec["risk_factors"] == ["Injury history"]
    
    def test_get_active_recommendations_by_type(self, db_session: Session, test_recommendations: list, test_franchise: Franchise):
        """Test getting active recommendations filtered by type."""
        response = client.get(f"/api/v1/recommendations/franchise/{test_franchise.id}/active?recommendation_type=draft")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["id"] == "draft_rec_001"
        assert data[0]["type"] == "draft"
    
    def test_get_active_recommendations_franchise_not_found(self, db_session: Session):
        """Test getting recommendations for non-existent franchise."""
        response = client.get("/api/v1/recommendations/franchise/nonexistent_franchise/active")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_dismiss_recommendation_success(self, db_session: Session, test_recommendations: list):
        """Test dismissing a recommendation."""
        response = client.put("/api/v1/recommendations/keeper_rec_001/dismiss")
        
        assert response.status_code == 200
        data = response.json()
        assert "dismissed successfully" in data["message"]
        assert data["recommendation_id"] == "keeper_rec_001"
        assert "dismissed_at" in data
        
        # Verify recommendation is marked inactive
        rec = db_session.query(Recommendation).filter(Recommendation.id == "keeper_rec_001").first()
        assert rec.is_active == False
    
    def test_dismiss_recommendation_not_found(self, db_session: Session):
        """Test dismissing a non-existent recommendation."""
        response = client.put("/api/v1/recommendations/nonexistent_rec/dismiss")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_confidence_level_categorization(self, db_session: Session, test_recommendations: list):
        """Test that confidence levels are correctly categorized."""
        # Test high confidence (0.85)
        response = client.get("/api/v1/recommendations/keeper_rec_001")
        assert response.status_code == 200
        assert response.json()["confidence_level"] == "high"
        
        # Test very high confidence (0.92)
        response = client.get("/api/v1/recommendations/draft_rec_001")
        assert response.status_code == 200
        assert response.json()["confidence_level"] == "very_high"
    
    def test_health_check(self, db_session: Session):
        """Test recommendations service health check."""
        response = client.get("/api/v1/recommendations/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "recommendations"
        assert "timestamp" in data