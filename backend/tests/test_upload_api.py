"""
Unit tests for file upload API endpoints.
"""
import io
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, Mock

from app.main import app
from app.models import Player, Projection, Ranking
from tests.conftest import TestingSessionLocal


client = TestClient(app)


class TestUploadAPI:
    """Test cases for upload API endpoints."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.db = TestingSessionLocal()
    
    def teardown_method(self):
        """Clean up after tests."""
        self.db.close()
    
    def create_test_csv_file(self, content: str, filename: str = "test.csv"):
        """Create test CSV file for upload."""
        return ("file", (filename, io.BytesIO(content.encode()), "text/csv"))
    
    def test_upload_projections_success(self):
        """Test successful projection upload."""
        csv_content = "player_name,projected_points,position\nJosh Allen,25.5,QB"
        files = {"file": ("projections.csv", io.BytesIO(csv_content.encode()), "text/csv")}
        data = {"source": "test_source", "season": 2024}
        
        response = client.post("/api/v1/upload/projections", files=files, data=data)
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "Successfully processed" in result["message"]
        assert result["data"]["data_type"] == "projections"
    
    def test_upload_projections_invalid_file(self):
        """Test projection upload with invalid file."""
        files = {"file": ("test.txt", io.BytesIO(b"invalid content"), "text/plain")}
        data = {"source": "test_source", "season": 2024}
        
        response = client.post("/api/v1/upload/projections", files=files, data=data)
        
        assert response.status_code == 400
        assert "Unsupported file format" in response.json()["detail"]
    
    def test_upload_projections_missing_columns(self):
        """Test projection upload with missing required columns."""
        csv_content = "player_name\nJosh Allen"  # Missing projected_points
        files = {"file": ("projections.csv", io.BytesIO(csv_content.encode()), "text/csv")}
        data = {"source": "test_source", "season": 2024}
        
        response = client.post("/api/v1/upload/projections", files=files, data=data)
        
        assert response.status_code == 422
        assert "Missing required columns" in response.json()["detail"]
    
    def test_upload_rankings_success(self):
        """Test successful ranking upload."""
        csv_content = "player_name,overall_rank,position\nJosh Allen,1,QB"
        files = {"file": ("rankings.csv", io.BytesIO(csv_content.encode()), "text/csv")}
        data = {"source": "test_source", "season": 2024, "ranking_type": "expert"}
        
        response = client.post("/api/v1/upload/rankings", files=files, data=data)
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert result["data"]["data_type"] == "rankings"
    
    def test_upload_rankings_invalid_type(self):
        """Test ranking upload with invalid ranking type."""
        csv_content = "player_name,overall_rank\nJosh Allen,1"
        files = {"file": ("rankings.csv", io.BytesIO(csv_content.encode()), "text/csv")}
        data = {"source": "test_source", "season": 2024, "ranking_type": "invalid"}
        
        response = client.post("/api/v1/upload/rankings", files=files, data=data)
        
        assert response.status_code == 400
        assert "Invalid ranking_type" in response.json()["detail"]
    
    def test_upload_adp_success(self):
        """Test successful ADP upload."""
        csv_content = "player_name,adp,position\nJosh Allen,1.5,QB"
        files = {"file": ("adp.csv", io.BytesIO(csv_content.encode()), "text/csv")}
        data = {"source": "test_source", "season": 2024}
        
        response = client.post("/api/v1/upload/adp", files=files, data=data)
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert result["data"]["data_type"] == "adp"
    
    def test_get_supported_formats(self):
        """Test getting supported formats information."""
        response = client.get("/api/v1/upload/supported-formats")
        
        assert response.status_code == 200
        result = response.json()
        assert "supported_extensions" in result
        assert ".csv" in result["supported_extensions"]
        assert "data_types" in result
        assert "projections" in result["data_types"]
    
    def test_delete_uploaded_data_projections(self):
        """Test deleting uploaded projection data."""
        # First create some test data
        player = Player(id="test_1", name="Test Player", position="QB", team="TEST")
        projection = Projection(
            id="proj_1",
            player_id="test_1",
            season=2024,
            source="test_source",
            projected_points=20.0
        )
        
        self.db.add(player)
        self.db.add(projection)
        self.db.commit()
        
        response = client.delete(
            "/api/v1/upload/data/projections",
            params={"source": "test_source", "season": 2024}
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert result["deleted_count"] == 1
    
    def test_delete_uploaded_data_invalid_type(self):
        """Test deleting data with invalid data type."""
        response = client.delete(
            "/api/v1/upload/data/invalid",
            params={"source": "test_source", "season": 2024}
        )
        
        assert response.status_code == 400
        assert "Invalid data_type" in response.json()["detail"]


class TestAsyncUploadAPI:
    """Test cases for async upload API endpoints."""
    
    @patch('app.tasks.file_processing.process_uploaded_file.delay')
    def test_upload_projections_async_success(self, mock_task):
        """Test successful async projection upload."""
        # Mock the Celery task
        mock_task.return_value.id = "test_task_id"
        
        csv_content = "player_name,projected_points\nJosh Allen,25.5"
        files = {"file": ("projections.csv", io.BytesIO(csv_content.encode()), "text/csv")}
        data = {"source": "test_source", "season": 2024}
        
        response = client.post("/api/v1/async-upload/projections", files=files, data=data)
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert result["task_id"] == "test_task_id"
        assert "status_url" in result
        
        # Verify task was called
        mock_task.assert_called_once()
    
    @patch('app.tasks.file_processing.cleanup_old_data.delay')
    @patch('app.tasks.file_processing.process_uploaded_file.delay')
    def test_upload_projections_async_with_cleanup(self, mock_process, mock_cleanup):
        """Test async projection upload with cleanup."""
        mock_process.return_value.id = "process_task_id"
        mock_cleanup.return_value.id = "cleanup_task_id"
        
        csv_content = "player_name,projected_points\nJosh Allen,25.5"
        files = {"file": ("projections.csv", io.BytesIO(csv_content.encode()), "text/csv")}
        data = {"source": "test_source", "season": 2024, "cleanup_old": True}
        
        response = client.post("/api/v1/async-upload/projections", files=files, data=data)
        
        assert response.status_code == 200
        
        # Verify both tasks were called
        mock_cleanup.assert_called_once_with('projections', 'test_source', 2024)
        mock_process.assert_called_once()
    
    def test_upload_async_file_too_large(self):
        """Test async upload with file too large."""
        large_content = "x" * (11 * 1024 * 1024)  # 11MB
        files = {"file": ("large.csv", io.BytesIO(large_content.encode()), "text/csv")}
        data = {"source": "test_source", "season": 2024}
        
        response = client.post("/api/v1/async-upload/projections", files=files, data=data)
        
        assert response.status_code == 413
        assert "File too large" in response.json()["detail"]
    
    @patch('app.core.celery.celery_app.AsyncResult')
    def test_get_task_status_success(self, mock_result):
        """Test getting task status."""
        # Mock successful task
        mock_task = Mock()
        mock_task.status = "SUCCESS"
        mock_task.result = {"status": "SUCCESS", "result": {"saved_count": 5}}
        mock_task.info = None
        mock_task.successful.return_value = True
        mock_task.failed.return_value = False
        mock_result.return_value = mock_task
        
        response = client.get("/api/v1/async-upload/status/test_task_id")
        
        assert response.status_code == 200
        result = response.json()
        assert result["task_id"] == "test_task_id"
        assert result["status"] == "SUCCESS"
        assert result["result"]["result"]["saved_count"] == 5
    
    @patch('app.core.celery.celery_app.AsyncResult')
    def test_get_task_status_failed(self, mock_result):
        """Test getting status of failed task."""
        # Mock failed task
        mock_task = Mock()
        mock_task.status = "FAILURE"
        mock_task.info = "Task failed due to error"
        mock_task.successful.return_value = False
        mock_task.failed.return_value = True
        mock_result.return_value = mock_task
        
        response = client.get("/api/v1/async-upload/status/test_task_id")
        
        assert response.status_code == 200
        result = response.json()
        assert result["status"] == "FAILURE"
        assert result["error"] == "Task failed due to error"
    
    @patch('app.core.celery.celery_app.control.revoke')
    def test_cancel_task(self, mock_revoke):
        """Test cancelling a task."""
        response = client.delete("/api/v1/async-upload/cancel/test_task_id")
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "cancellation requested" in result["message"]
        
        # Verify revoke was called
        mock_revoke.assert_called_once_with("test_task_id", terminate=True)