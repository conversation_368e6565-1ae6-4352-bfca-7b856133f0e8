"""
Tests for the monitoring and logging system.
"""
import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.core.health import (
    HealthChecker, HealthStatus, HealthCheckResult,
    DatabaseHealthChecker, RedisHealthChecker, HealthCheckManager
)
from app.core.metrics import (
    Counter, Gauge, Histogram, Timer, MetricsRegistry,
    increment_counter, set_gauge, observe_histogram
)
from app.core.error_tracking import <PERSON><PERSON>r<PERSON><PERSON>, ErrorSeverity
from app.core.logging import get_logger, log_with_context


class TestHealthCheckers:
    """Test health check functionality."""
    
    @pytest.mark.asyncio
    async def test_health_checker_success(self):
        """Test successful health check."""
        class MockHealthChecker(HealthChecker):
            def __init__(self):
                super().__init__("test_service", timeout=1.0)
            
            async def _perform_check(self):
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": "Service is healthy",
                    "details": {"connections": 5}
                }
        
        checker = MockHealthChecker()
        result = await checker.check()
        
        assert result.name == "test_service"
        assert result.status == HealthStatus.HEALTHY
        assert result.message == "Service is healthy"
        assert result.details == {"connections": 5}
        assert result.duration_ms > 0
    
    @pytest.mark.asyncio
    async def test_health_checker_timeout(self):
        """Test health check timeout."""
        class SlowHealthChecker(HealthChecker):
            def __init__(self):
                super().__init__("slow_service", timeout=0.1)
            
            async def _perform_check(self):
                await asyncio.sleep(0.2)  # Longer than timeout
                return {"status": HealthStatus.HEALTHY, "message": "Done"}
        
        checker = SlowHealthChecker()
        result = await checker.check()
        
        assert result.status == HealthStatus.UNHEALTHY
        assert "timed out" in result.message.lower()
    
    @pytest.mark.asyncio
    async def test_health_checker_exception(self):
        """Test health check with exception."""
        class FailingHealthChecker(HealthChecker):
            def __init__(self):
                super().__init__("failing_service", timeout=1.0)
            
            async def _perform_check(self):
                raise Exception("Service unavailable")
        
        checker = FailingHealthChecker()
        result = await checker.check()
        
        assert result.status == HealthStatus.UNHEALTHY
        assert "Service unavailable" in result.message
    
    @pytest.mark.asyncio
    async def test_health_check_manager(self):
        """Test health check manager."""
        manager = HealthCheckManager()
        
        # Clear default checkers for testing
        manager.checkers = []
        
        # Add mock checker
        class MockChecker(HealthChecker):
            def __init__(self, name, status):
                super().__init__(name, timeout=1.0)
                self.test_status = status
            
            async def _perform_check(self):
                return {
                    "status": self.test_status,
                    "message": f"{self.name} status"
                }
        
        manager.checkers = [
            MockChecker("service1", HealthStatus.HEALTHY),
            MockChecker("service2", HealthStatus.DEGRADED),
        ]
        
        result = await manager.run_all_checks()
        
        assert result["status"] == HealthStatus.DEGRADED.value
        assert len(result["checks"]) == 2
        assert result["checks"]["service1"]["status"] == HealthStatus.HEALTHY.value
        assert result["checks"]["service2"]["status"] == HealthStatus.DEGRADED.value


class TestMetrics:
    """Test metrics collection functionality."""
    
    def test_counter(self):
        """Test counter metric."""
        counter = Counter("test_counter", "Test counter")
        
        assert counter.get_current_value() == 0
        
        counter.increment()
        assert counter.get_current_value() == 1
        
        counter.increment(5)
        assert counter.get_current_value() == 6
        
        summary = counter.get_summary()
        assert summary.current_value == 6
        assert summary.total_samples == 2
    
    def test_gauge(self):
        """Test gauge metric."""
        gauge = Gauge("test_gauge", "Test gauge")
        
        assert gauge.get_current_value() == 0
        
        gauge.set(10)
        assert gauge.get_current_value() == 10
        
        gauge.increment(5)
        assert gauge.get_current_value() == 15
        
        gauge.decrement(3)
        assert gauge.get_current_value() == 12
        
        summary = gauge.get_summary()
        assert summary.current_value == 12
        assert summary.total_samples == 3
    
    def test_histogram(self):
        """Test histogram metric."""
        histogram = Histogram("test_histogram", "Test histogram")
        
        # Add some values
        values = [0.1, 0.5, 1.2, 2.8, 5.5]
        for value in values:
            histogram.observe(value)
        
        summary = histogram.get_summary()
        assert summary.total_samples == 5
        assert summary.min_value == 0.1
        assert summary.max_value == 5.5
        
        # Test percentiles
        p50 = histogram.get_percentile(50)
        assert p50 is not None
        assert 0.1 <= p50 <= 5.5
    
    def test_timer(self):
        """Test timer metric."""
        timer = Timer("test_timer", "Test timer")
        
        # Test context manager
        with timer.time():
            pass  # Quick operation
        
        summary = timer.get_summary()
        assert summary.total_samples == 1
        assert summary.current_value >= 0
    
    def test_metrics_registry(self):
        """Test metrics registry."""
        registry = MetricsRegistry()
        
        # Test getting default metrics
        counter = registry.get_counter("http_requests_total")
        assert counter is not None
        assert isinstance(counter, Counter)
        
        gauge = registry.get_gauge("celery_active_tasks")
        assert gauge is not None
        assert isinstance(gauge, Gauge)
        
        # Test metrics snapshot
        snapshot = registry.get_metrics_snapshot()
        assert "timestamp" in snapshot
        assert "metrics" in snapshot
        assert len(snapshot["metrics"]) > 0
    
    def test_convenience_functions(self):
        """Test convenience functions for metrics."""
        # These should not raise exceptions even if metrics don't exist
        increment_counter("nonexistent_counter")
        set_gauge("nonexistent_gauge", 42)
        observe_histogram("nonexistent_histogram", 1.5)
        
        # Test with existing metrics
        increment_counter("http_requests_total", 1, {"method": "GET"})
        set_gauge("celery_active_tasks", 5)


class TestErrorTracking:
    """Test error tracking functionality."""
    
    def test_error_tracker_basic(self):
        """Test basic error tracking."""
        tracker = ErrorTracker(max_events=100)
        
        # Track an error
        error = ValueError("Test error")
        error_id = tracker.track_error(error, {"user_id": "123"}, ErrorSeverity.MEDIUM)
        
        assert error_id is not None
        
        # Check error summary
        summary = tracker.get_error_summary(hours=1)
        assert summary["total_errors"] == 1
        assert summary["unique_error_types"] == 1
        assert "ValueError" in summary["error_types"]
        
        # Check recent errors
        recent = tracker.get_recent_errors(limit=10)
        assert len(recent) == 1
        assert recent[0]["error_type"] == "ValueError"
        assert recent[0]["message"] == "Test error"
        assert recent[0]["severity"] == "medium"
    
    def test_error_deduplication(self):
        """Test error deduplication."""
        tracker = ErrorTracker(max_events=100)
        
        # Track the same error multiple times
        error = ValueError("Duplicate error")
        
        error_id1 = tracker.track_error(error)
        error_id2 = tracker.track_error(error)
        
        # Should be deduplicated
        recent = tracker.get_recent_errors(limit=10)
        assert len(recent) == 1
        assert recent[0]["count"] > 1
    
    def test_alert_conditions(self):
        """Test alert triggering conditions."""
        tracker = ErrorTracker(max_events=100)
        alerts_triggered = []
        
        def test_alert_handler(error_event):
            alerts_triggered.append(error_event)
        
        tracker.add_alert_handler(test_alert_handler)
        
        # Test critical error alert
        critical_error = Exception("Critical failure")
        tracker.track_error(critical_error, severity=ErrorSeverity.CRITICAL)
        
        assert len(alerts_triggered) == 1
        assert alerts_triggered[0].severity == ErrorSeverity.CRITICAL


class TestLogging:
    """Test logging functionality."""
    
    def test_get_logger(self):
        """Test logger creation."""
        logger = get_logger("test_module")
        assert logger.name == "test_module"
    
    def test_log_with_context(self):
        """Test contextual logging."""
        logger = get_logger("test_context")
        
        # This should not raise an exception
        log_with_context(
            logger,
            20,  # INFO level
            "Test message with context",
            user_id="123",
            operation="test"
        )


@pytest.mark.asyncio
async def test_integration_monitoring_endpoints():
    """Test integration of monitoring components."""
    from app.core.health import health_manager
    from app.core.metrics import metrics_registry
    
    # Test health checks
    health_result = await health_manager.run_all_checks()
    assert "status" in health_result
    assert "checks" in health_result
    assert "timestamp" in health_result
    
    # Test metrics
    metrics_snapshot = metrics_registry.get_metrics_snapshot()
    assert "timestamp" in metrics_snapshot
    assert "metrics" in metrics_snapshot
    
    # Test that we can get specific metrics
    counter = metrics_registry.get_counter("http_requests_total")
    if counter:
        counter.increment()
        assert counter.get_current_value() > 0