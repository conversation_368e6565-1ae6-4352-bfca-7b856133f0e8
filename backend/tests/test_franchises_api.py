"""
Integration tests for franchise management API endpoints.
"""
import pytest
from decimal import Decimal
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.core.database import get_db
from app.models.league import League
from app.models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>er, RosterPlayer
from app.models.player import Player, PlayerPosition


client = TestClient(app)


class TestFranchisesAPI:
    """Test cases for franchise management API endpoints."""
    
    @pytest.fixture
    def test_league(self, db_session: Session):
        """Create a test league for franchise tests."""
        league = League(
            id="test_league",
            name="Test League",
            season=2024,
            scoring_rules={},
            roster_slots=[]
        )
        db_session.add(league)
        db_session.commit()
        return league
    
    @pytest.fixture
    def test_player(self, db_session: Session):
        """Create a test player for roster tests."""
        player = Player(
            id="test_player_001",
            name="Test Player",
            position=PlayerPosition.RB,
            team="TEST",
            bye_week=10
        )
        db_session.add(player)
        db_session.commit()
        return player
    
    def test_create_franchise_success(self, db_session: Session, test_league: League):
        """Test successful franchise creation."""
        franchise_data = {
            "id": "test_franchise_001",
            "name": "Test Team",
            "owner_name": "Test Owner",
            "league_id": test_league.id,
            "salary_cap": "200.00",
            "faab_budget": "100.00",
            "franchise_metadata": {"division": "North"}
        }
        
        response = client.post("/api/v1/franchises/", json=franchise_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == franchise_data["id"]
        assert data["name"] == franchise_data["name"]
        assert data["owner_name"] == franchise_data["owner_name"]
        assert data["league_id"] == test_league.id
        assert data["league_name"] == test_league.name
        assert float(data["salary_cap"]) == 200.00
        assert float(data["faab_budget"]) == 100.00
        assert float(data["faab_spent"]) == 0.00
        assert float(data["remaining_faab"]) == 100.00
        assert data["is_active"] == True
        assert data["roster_size"] == 0
        assert "created_at" in data
    
    def test_create_franchise_duplicate_id(self, db_session: Session, test_league: League):
        """Test franchise creation with duplicate ID fails."""
        # Create first franchise
        franchise = Franchise(
            id="duplicate_franchise",
            name="First Team",
            owner_name="First Owner",
            league_id=test_league.id
        )
        db_session.add(franchise)
        db_session.commit()
        
        # Try to create second franchise with same ID
        franchise_data = {
            "id": "duplicate_franchise",
            "name": "Second Team",
            "owner_name": "Second Owner",
            "league_id": test_league.id
        }
        
        response = client.post("/api/v1/franchises/", json=franchise_data)
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]
    
    def test_create_franchise_invalid_league(self, db_session: Session):
        """Test franchise creation with invalid league ID fails."""
        franchise_data = {
            "id": "test_franchise_002",
            "name": "Test Team",
            "owner_name": "Test Owner",
            "league_id": "nonexistent_league"
        }
        
        response = client.post("/api/v1/franchises/", json=franchise_data)
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_list_franchises_empty(self, db_session: Session):
        """Test listing franchises when none exist."""
        response = client.get("/api/v1/franchises/")
        
        assert response.status_code == 200
        assert response.json() == []
    
    def test_list_franchises_with_data(self, db_session: Session, test_league: League):
        """Test listing franchises with existing data."""
        # Create test franchises
        franchises = [
            Franchise(id="franchise1", name="Team 1", owner_name="Owner 1", league_id=test_league.id),
            Franchise(id="franchise2", name="Team 2", owner_name="Owner 2", league_id=test_league.id),
            Franchise(id="franchise3", name="Team 3", owner_name="Owner 3", league_id=test_league.id, is_active=False)
        ]
        
        for franchise in franchises:
            db_session.add(franchise)
        db_session.commit()
        
        # Test default filtering (active only)
        response = client.get("/api/v1/franchises/")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert all(franchise["is_active"] for franchise in data)
        
        # Test league filtering
        response = client.get(f"/api/v1/franchises/?league_id={test_league.id}")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert all(franchise["league_id"] == test_league.id for franchise in data)
        
        # Test including inactive franchises
        response = client.get("/api/v1/franchises/?active_only=false")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3
    
    def test_get_franchise_success(self, db_session: Session, test_league: League):
        """Test getting a specific franchise."""
        franchise = Franchise(
            id="get_test_franchise",
            name="Get Test Team",
            owner_name="Get Test Owner",
            league_id=test_league.id,
            salary_cap=Decimal("200.00"),
            faab_budget=Decimal("100.00"),
            faab_spent=Decimal("25.00"),
            franchise_metadata={"division": "South"}
        )
        db_session.add(franchise)
        db_session.commit()
        
        response = client.get("/api/v1/franchises/get_test_franchise")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "get_test_franchise"
        assert data["name"] == "Get Test Team"
        assert data["owner_name"] == "Get Test Owner"
        assert data["league_id"] == test_league.id
        assert data["league_name"] == test_league.name
        assert float(data["salary_cap"]) == 200.00
        assert float(data["faab_budget"]) == 100.00
        assert float(data["faab_spent"]) == 25.00
        assert float(data["remaining_faab"]) == 75.00
        assert data["franchise_metadata"] == {"division": "South"}
        assert data["roster_size"] == 0
    
    def test_get_franchise_not_found(self, db_session: Session):
        """Test getting a non-existent franchise."""
        response = client.get("/api/v1/franchises/nonexistent_franchise")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_update_franchise_success(self, db_session: Session, test_league: League):
        """Test successful franchise update."""
        franchise = Franchise(
            id="update_test_franchise",
            name="Original Name",
            owner_name="Original Owner",
            league_id=test_league.id,
            faab_budget=Decimal("100.00")
        )
        db_session.add(franchise)
        db_session.commit()
        
        update_data = {
            "name": "Updated Name",
            "owner_name": "Updated Owner",
            "faab_spent": "30.00",
            "franchise_metadata": {"updated": True}
        }
        
        response = client.put("/api/v1/franchises/update_test_franchise", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Name"
        assert data["owner_name"] == "Updated Owner"
        assert float(data["faab_spent"]) == 30.00
        assert float(data["remaining_faab"]) == 70.00
        assert data["franchise_metadata"] == {"updated": True}
    
    def test_update_franchise_not_found(self, db_session: Session):
        """Test updating a non-existent franchise."""
        update_data = {"name": "New Name"}
        
        response = client.put("/api/v1/franchises/nonexistent_franchise", json=update_data)
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_delete_franchise_success(self, db_session: Session, test_league: League):
        """Test successful franchise deletion."""
        franchise = Franchise(
            id="delete_test_franchise",
            name="Delete Test Team",
            owner_name="Delete Test Owner",
            league_id=test_league.id
        )
        db_session.add(franchise)
        db_session.commit()
        
        response = client.delete("/api/v1/franchises/delete_test_franchise")
        
        assert response.status_code == 200
        assert "deleted successfully" in response.json()["message"]
        
        # Verify franchise is deleted
        deleted_franchise = db_session.query(Franchise).filter(Franchise.id == "delete_test_franchise").first()
        assert deleted_franchise is None
    
    def test_delete_franchise_not_found(self, db_session: Session):
        """Test deleting a non-existent franchise."""
        response = client.delete("/api/v1/franchises/nonexistent_franchise")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_get_franchise_roster_success(self, db_session: Session, test_league: League, test_player: Player):
        """Test getting franchise roster."""
        # Create franchise with roster
        franchise = Franchise(
            id="roster_test_franchise",
            name="Roster Test Team",
            owner_name="Roster Test Owner",
            league_id=test_league.id
        )
        db_session.add(franchise)
        db_session.commit()
        
        roster = Roster(
            id="roster_test_roster",
            franchise_id=franchise.id,
            total_salary=Decimal("150.00")
        )
        db_session.add(roster)
        db_session.commit()
        
        roster_player = RosterPlayer(
            id="roster_player_001",
            roster_id=roster.id,
            player_id=test_player.id,
            roster_slot="RB1",
            salary=Decimal("25.00"),
            is_keeper=True,
            keeper_cost=3
        )
        db_session.add(roster_player)
        db_session.commit()
        
        response = client.get("/api/v1/franchises/roster_test_franchise/roster")
        
        assert response.status_code == 200
        data = response.json()
        assert data["franchise_id"] == franchise.id
        assert data["franchise_name"] == franchise.name
        assert float(data["total_salary"]) == 150.00
        assert data["is_valid"] == True
        assert len(data["players"]) == 1
        
        player_data = data["players"][0]
        assert player_data["player_id"] == test_player.id
        assert player_data["player_name"] == test_player.name
        assert player_data["position"] == test_player.position.value
        assert player_data["roster_slot"] == "RB1"
        assert float(player_data["salary"]) == 25.00
        assert player_data["is_keeper"] == True
        assert player_data["keeper_cost"] == 3
        
        assert data["position_counts"]["RB"] == 1
        assert len(data["starting_lineup"]) == 1
        assert len(data["bench_players"]) == 0
    
    def test_get_franchise_roster_not_found(self, db_session: Session):
        """Test getting roster for non-existent franchise."""
        response = client.get("/api/v1/franchises/nonexistent_franchise/roster")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_add_player_to_roster_success(self, db_session: Session, test_league: League, test_player: Player):
        """Test adding player to franchise roster."""
        # Create franchise with roster
        franchise = Franchise(
            id="add_player_franchise",
            name="Add Player Team",
            owner_name="Add Player Owner",
            league_id=test_league.id
        )
        db_session.add(franchise)
        db_session.commit()
        
        roster = Roster(
            id="add_player_roster",
            franchise_id=franchise.id
        )
        db_session.add(roster)
        db_session.commit()
        
        response = client.post(
            f"/api/v1/franchises/add_player_franchise/roster/add-player"
            f"?player_id={test_player.id}&roster_slot=RB1&salary=30.00&is_keeper=true&keeper_cost=4"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "added to franchise" in data["message"]
        assert data["player_id"] == test_player.id
        assert data["franchise_id"] == franchise.id
        assert data["roster_slot"] == "RB1"
        
        # Verify player was added to database
        roster_player = db_session.query(RosterPlayer).filter(
            RosterPlayer.roster_id == roster.id,
            RosterPlayer.player_id == test_player.id
        ).first()
        assert roster_player is not None
        assert roster_player.roster_slot == "RB1"
        assert roster_player.salary == Decimal("30.00")
        assert roster_player.is_keeper == True
        assert roster_player.keeper_cost == 4
    
    def test_add_player_to_roster_duplicate(self, db_session: Session, test_league: League, test_player: Player):
        """Test adding duplicate player to roster fails."""
        # Create franchise with roster and existing player
        franchise = Franchise(
            id="duplicate_player_franchise",
            name="Duplicate Player Team",
            owner_name="Duplicate Player Owner",
            league_id=test_league.id
        )
        db_session.add(franchise)
        db_session.commit()
        
        roster = Roster(
            id="duplicate_player_roster",
            franchise_id=franchise.id
        )
        db_session.add(roster)
        db_session.commit()
        
        # Add player first time
        roster_player = RosterPlayer(
            id="existing_roster_player",
            roster_id=roster.id,
            player_id=test_player.id
        )
        db_session.add(roster_player)
        db_session.commit()
        
        # Try to add same player again
        response = client.post(
            f"/api/v1/franchises/duplicate_player_franchise/roster/add-player"
            f"?player_id={test_player.id}"
        )
        
        assert response.status_code == 400
        assert "already on the roster" in response.json()["detail"]
    
    def test_remove_player_from_roster_success(self, db_session: Session, test_league: League, test_player: Player):
        """Test removing player from franchise roster."""
        # Create franchise with roster and player
        franchise = Franchise(
            id="remove_player_franchise",
            name="Remove Player Team",
            owner_name="Remove Player Owner",
            league_id=test_league.id
        )
        db_session.add(franchise)
        db_session.commit()
        
        roster = Roster(
            id="remove_player_roster",
            franchise_id=franchise.id
        )
        db_session.add(roster)
        db_session.commit()
        
        roster_player = RosterPlayer(
            id="remove_roster_player",
            roster_id=roster.id,
            player_id=test_player.id
        )
        db_session.add(roster_player)
        db_session.commit()
        
        response = client.delete(
            f"/api/v1/franchises/remove_player_franchise/roster/remove-player"
            f"?player_id={test_player.id}"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "removed from franchise" in data["message"]
        assert data["player_id"] == test_player.id
        assert data["franchise_id"] == franchise.id
        
        # Verify player was marked inactive
        db_session.refresh(roster_player)
        assert roster_player.is_active == False
    
    def test_remove_player_from_roster_not_found(self, db_session: Session, test_league: League, test_player: Player):
        """Test removing non-existent player from roster fails."""
        # Create franchise with empty roster
        franchise = Franchise(
            id="empty_roster_franchise",
            name="Empty Roster Team",
            owner_name="Empty Roster Owner",
            league_id=test_league.id
        )
        db_session.add(franchise)
        db_session.commit()
        
        roster = Roster(
            id="empty_roster",
            franchise_id=franchise.id
        )
        db_session.add(roster)
        db_session.commit()
        
        response = client.delete(
            f"/api/v1/franchises/empty_roster_franchise/roster/remove-player"
            f"?player_id={test_player.id}"
        )
        
        assert response.status_code == 404
        assert "not found on roster" in response.json()["detail"]
    
    def test_health_check(self, db_session: Session):
        """Test franchise service health check."""
        response = client.get("/api/v1/franchises/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "franchise_management"
        assert "timestamp" in data