"""
Tests for the league rules engine.
"""
import pytest
from decimal import Decimal
from typing import Dict, Any, List

from backend.app.services.rules_engine import (
    RulesEngine, 
    LeagueRulesSchema, 
    KeeperRulesSchema,
    ScoringType
)
from backend.app.models.player import Player, PlayerPosition, InjuryStatus
from backend.app.models.roster import <PERSON><PERSON><PERSON>, RosterPlayer, <PERSON>an<PERSON><PERSON>
from backend.app.models.league import League


class TestLeagueRulesSchema:
    """Test the league rules schema validation."""
    
    def test_default_rules_are_valid(self):
        """Test that default rules create a valid schema."""
        schema = LeagueRulesSchema()
        assert schema.league_type == "redraft"
        assert schema.scoring_type == ScoringType.STANDARD
        assert "passing_yards" in schema.scoring_rules
        assert len(schema.roster_slots) > 0
    
    def test_custom_scoring_rules_validation(self):
        """Test validation of custom scoring rules."""
        # Valid custom rules
        valid_rules = {
            "scoring_rules": {
                "passing_yards": 0.04,
                "passing_touchdowns": 6.0,
                "rushing_yards": 0.1,
                "rushing_touchdowns": 6.0,
                "receiving_yards": 0.1,
                "receiving_touchdowns": 6.0,
                "receptions": 1.0,  # PPR
            }
        }
        schema = LeagueRulesSchema(**valid_rules)
        assert schema.scoring_rules["receptions"] == 1.0
        
        # Invalid rules - missing required fields
        invalid_rules = {
            "scoring_rules": {
                "passing_yards": 0.04,
                # Missing required fields
            }
        }
        with pytest.raises(ValueError, match="Missing required scoring fields"):
            LeagueRulesSchema(**invalid_rules)
    
    def test_roster_slots_validation(self):
        """Test validation of roster slot configuration."""
        # Valid roster configuration
        valid_roster = {
            "roster_slots": [
                {"position": "QB", "count": 1, "type": "starting"},
                {"position": "RB", "count": 2, "type": "starting"},
                {"position": "WR", "count": 2, "type": "starting"},
                {"position": "TE", "count": 1, "type": "starting"},
                {"position": "BENCH", "count": 6, "type": "bench"},
            ]
        }
        schema = LeagueRulesSchema(**valid_roster)
        assert len(schema.roster_slots) == 5
        
        # Invalid roster - no QB
        invalid_roster = {
            "roster_slots": [
                {"position": "RB", "count": 2, "type": "starting"},
                {"position": "WR", "count": 2, "type": "starting"},
            ]
        }
        with pytest.raises(ValueError, match="Must have at least one QB starting slot"):
            LeagueRulesSchema(**invalid_roster)
        
        # Invalid roster - empty slots
        with pytest.raises(ValueError, match="Roster slots cannot be empty"):
            LeagueRulesSchema(roster_slots=[])


class TestKeeperRulesSchema:
    """Test the keeper rules schema validation."""
    
    def test_valid_keeper_rules(self):
        """Test valid keeper rules configuration."""
        keeper_rules = {
            "max_keepers": 3,
            "round_escalation": True,
            "escalation_rounds": 2,
            "franchise_tags": 1,
            "max_years_kept": 3,
        }
        schema = KeeperRulesSchema(**keeper_rules)
        assert schema.max_keepers == 3
        assert schema.round_escalation is True
        assert schema.escalation_rounds == 2
    
    def test_keeper_rules_constraints(self):
        """Test keeper rules validation constraints."""
        # Invalid - too many keepers
        with pytest.raises(ValueError):
            KeeperRulesSchema(max_keepers=25)
        
        # Invalid - negative keepers
        with pytest.raises(ValueError):
            KeeperRulesSchema(max_keepers=-1)


class TestRulesEngine:
    """Test the core rules engine functionality."""
    
    @pytest.fixture
    def rules_engine(self):
        """Create a rules engine instance."""
        return RulesEngine()
    
    @pytest.fixture
    def sample_league_rules(self):
        """Sample league rules for testing."""
        return {
            "league_type": "redraft",
            "scoring_type": "ppr",
            "scoring_rules": {
                "passing_yards": 0.04,
                "passing_touchdowns": 4.0,
                "passing_interceptions": -2.0,
                "rushing_yards": 0.1,
                "rushing_touchdowns": 6.0,
                "receiving_yards": 0.1,
                "receiving_touchdowns": 6.0,
                "receptions": 1.0,
                "fumbles_lost": -2.0,
            },
            "roster_slots": [
                {"position": "QB", "count": 1, "type": "starting"},
                {"position": "RB", "count": 2, "type": "starting"},
                {"position": "WR", "count": 2, "type": "starting"},
                {"position": "TE", "count": 1, "type": "starting"},
                {"position": "FLEX", "count": 1, "type": "starting", "eligible_positions": ["RB", "WR", "TE"]},
                {"position": "K", "count": 1, "type": "starting"},
                {"position": "DEF", "count": 1, "type": "starting"},
                {"position": "BENCH", "count": 6, "type": "bench"},
            ]
        }
    
    @pytest.fixture
    def sample_keeper_rules(self):
        """Sample keeper rules for testing."""
        return {
            "max_keepers": 3,
            "keeper_deadline": None,
            "round_escalation": True,
            "escalation_rounds": 1,
            "franchise_tags": 1,
            "max_years_kept": None,
            "cost_rules": {
                "minimum_cost": 1,
                "waiver_pickup_cost": "last_round",
            }
        }
    
    def test_validate_league_rules_valid(self, rules_engine, sample_league_rules):
        """Test validation of valid league rules."""
        is_valid, errors = rules_engine.validate_league_rules(sample_league_rules)
        assert is_valid is True
        assert len(errors) == 0
    
    def test_validate_league_rules_invalid(self, rules_engine):
        """Test validation of invalid league rules."""
        invalid_rules = {
            "scoring_rules": {
                "passing_yards": 0.04,
                # Missing required fields
            }
        }
        is_valid, errors = rules_engine.validate_league_rules(invalid_rules)
        assert is_valid is False
        assert len(errors) > 0
    
    def test_parse_league_rules(self, rules_engine, sample_league_rules):
        """Test parsing of league rules."""
        schema = rules_engine.parse_league_rules(sample_league_rules)
        assert isinstance(schema, LeagueRulesSchema)
        assert schema.scoring_type == ScoringType.PPR
        assert schema.scoring_rules["receptions"] == 1.0
    
    def test_calculate_player_score(self, rules_engine):
        """Test player scoring calculation."""
        player_stats = {
            "passing_yards": 300,
            "passing_touchdowns": 2,
            "rushing_yards": 50,
            "receiving_yards": 80,
            "receiving_touchdowns": 1,
            "receptions": 8,
        }
        
        scoring_rules = {
            "passing_yards": 0.04,
            "passing_touchdowns": 4.0,
            "rushing_yards": 0.1,
            "receiving_yards": 0.1,
            "receiving_touchdowns": 6.0,
            "receptions": 1.0,
        }
        
        score = rules_engine.calculate_player_score(player_stats, scoring_rules)
        expected = (300 * 0.04) + (2 * 4.0) + (50 * 0.1) + (80 * 0.1) + (1 * 6.0) + (8 * 1.0)
        assert score == expected
    
    def test_calculate_player_score_missing_stats(self, rules_engine):
        """Test scoring with missing stats (should be ignored)."""
        player_stats = {
            "passing_yards": 250,
            "unknown_stat": 100,  # Should be ignored
        }
        
        scoring_rules = {
            "passing_yards": 0.04,
            "passing_touchdowns": 4.0,
        }
        
        score = rules_engine.calculate_player_score(player_stats, scoring_rules)
        assert score == 250 * 0.04
    
    def test_get_roster_slot_requirements(self, rules_engine, sample_league_rules):
        """Test getting roster slot requirements."""
        requirements = rules_engine.get_roster_slot_requirements(sample_league_rules["roster_slots"])
        
        assert requirements["QB"]["starting"] == 1
        assert requirements["RB"]["starting"] == 2
        assert requirements["WR"]["starting"] == 2
        assert requirements["TE"]["starting"] == 1
        assert requirements["K"]["starting"] == 1
        assert requirements["DEF"]["starting"] == 1
        assert requirements["BENCH"]["total"] == 6
    
    def test_validate_keeper_selection_valid(self, rules_engine, sample_keeper_rules):
        """Test validation of valid keeper selection."""
        # Create mock keeper players
        keeper_players = []
        for i in range(2):  # 2 keepers, under the limit of 3
            player = Player(
                id=f"player_{i}",
                name=f"Player {i}",
                position=PlayerPosition.RB,
                team="TEST"
            )
            roster_player = RosterPlayer(
                id=f"rp_{i}",
                roster_id="test_roster",
                player_id=f"player_{i}",
                keeper_cost=5,
                is_keeper=True
            )
            roster_player.player = player
            keeper_players.append(roster_player)
        
        is_valid, violations = rules_engine.validate_keeper_selection(keeper_players, sample_keeper_rules)
        assert is_valid is True
        assert len(violations) == 0
    
    def test_validate_keeper_selection_too_many(self, rules_engine, sample_keeper_rules):
        """Test validation with too many keepers."""
        # Create 4 keeper players (over the limit of 3)
        keeper_players = []
        for i in range(4):
            player = Player(
                id=f"player_{i}",
                name=f"Player {i}",
                position=PlayerPosition.RB,
                team="TEST"
            )
            roster_player = RosterPlayer(
                id=f"rp_{i}",
                roster_id="test_roster",
                player_id=f"player_{i}",
                keeper_cost=5,
                is_keeper=True
            )
            roster_player.player = player
            keeper_players.append(roster_player)
        
        is_valid, violations = rules_engine.validate_keeper_selection(keeper_players, sample_keeper_rules)
        assert is_valid is False
        assert "Too many keepers selected" in violations[0]
    
    def test_validate_keeper_selection_no_rules(self, rules_engine):
        """Test keeper validation when no keeper rules exist."""
        # Create a keeper player
        player = Player(
            id="player_1",
            name="Player 1",
            position=PlayerPosition.RB,
            team="TEST"
        )
        roster_player = RosterPlayer(
            id="rp_1",
            roster_id="test_roster",
            player_id="player_1",
            keeper_cost=5,
            is_keeper=True
        )
        roster_player.player = player
        
        is_valid, violations = rules_engine.validate_keeper_selection([roster_player], None)
        assert is_valid is False
        assert "No keepers allowed" in violations[0]
    
    def test_calculate_keeper_cost(self, rules_engine, sample_keeper_rules):
        """Test keeper cost calculation."""
        player = Player(
            id="player_1",
            name="Player 1",
            position=PlayerPosition.RB,
            team="TEST"
        )
        roster_player = RosterPlayer(
            id="rp_1",
            roster_id="test_roster",
            player_id="player_1",
            keeper_cost=8,
            is_keeper=True
        )
        roster_player.player = player
        
        cost = rules_engine.calculate_keeper_cost(roster_player, sample_keeper_rules)
        # With round escalation, cost should be 8 - 1 = 7
        assert cost == 7
    
    def test_calculate_keeper_cost_minimum(self, rules_engine, sample_keeper_rules):
        """Test keeper cost with minimum cost enforcement."""
        player = Player(
            id="player_1",
            name="Player 1",
            position=PlayerPosition.RB,
            team="TEST"
        )
        roster_player = RosterPlayer(
            id="rp_1",
            roster_id="test_roster",
            player_id="player_1",
            keeper_cost=1,  # Very low cost
            is_keeper=True
        )
        roster_player.player = player
        
        cost = rules_engine.calculate_keeper_cost(roster_player, sample_keeper_rules)
        # Should enforce minimum cost of 1
        assert cost == 1
    
    def test_get_league_rules_json_schema(self, rules_engine):
        """Test getting JSON schema for league rules."""
        schema = rules_engine.get_league_rules_json_schema()
        assert "properties" in schema
        assert "scoring_rules" in schema["properties"]
        assert "roster_slots" in schema["properties"]
    
    def test_normalize_mfl_rules(self, rules_engine):
        """Test normalization of MFL rules."""
        mfl_rules = {
            "scoring": {
                "passYds": "0.04",
                "passTD": "4",
                "rushYds": "0.1",
                "rushTD": "6",
                "recYds": "0.1",
                "recTD": "6",
                "rec": "1",
            },
            "roster": {
                "QB": 1,
                "RB": 2,
                "WR": 2,
            }
        }
        
        normalized = rules_engine.normalize_mfl_rules(mfl_rules)
        assert normalized["scoring_rules"]["passing_yards"] == 0.04
        assert normalized["scoring_rules"]["receptions"] == 1.0
        assert len(normalized["roster_slots"]) > 0


class TestRosterValidation:
    """Test roster constraint validation."""
    
    @pytest.fixture
    def rules_engine(self):
        return RulesEngine()
    
    @pytest.fixture
    def sample_roster_slots(self):
        return [
            {"position": "QB", "count": 1, "type": "starting"},
            {"position": "RB", "count": 2, "type": "starting"},
            {"position": "WR", "count": 2, "type": "starting"},
            {"position": "TE", "count": 1, "type": "starting"},
            {"position": "FLEX", "count": 1, "type": "starting", "eligible_positions": ["RB", "WR", "TE"]},
            {"position": "K", "count": 1, "type": "starting"},
            {"position": "DEF", "count": 1, "type": "starting"},
            {"position": "BENCH", "count": 6, "type": "bench"},
        ]
    
    def create_test_roster(self, starting_lineup: Dict[str, int]) -> Roster:
        """Create a test roster with specified starting lineup."""
        roster = Roster(id="test_roster", franchise_id="test_franchise")
        roster.roster_players = []
        
        player_id = 1
        for position, count in starting_lineup.items():
            for i in range(count):
                player = Player(
                    id=f"player_{player_id}",
                    name=f"Player {player_id}",
                    position=PlayerPosition(position),
                    team="TEST"
                )
                
                slot = position if position != "FLEX" else "FLEX"
                roster_player = RosterPlayer(
                    id=f"rp_{player_id}",
                    roster_id="test_roster",
                    player_id=f"player_{player_id}",
                    roster_slot=slot,
                    is_active=True
                )
                roster_player.player = player
                roster.roster_players.append(roster_player)
                player_id += 1
        
        return roster
    
    def test_valid_roster_constraints(self, rules_engine, sample_roster_slots):
        """Test validation of a valid roster."""
        starting_lineup = {
            "QB": 1,
            "RB": 2,
            "WR": 2,
            "TE": 1,
            "K": 1,
            "DEF": 1,
        }
        
        roster = self.create_test_roster(starting_lineup)
        
        # Add a FLEX player (RB)
        flex_player = Player(
            id="flex_player",
            name="Flex Player",
            position=PlayerPosition.RB,
            team="TEST"
        )
        flex_roster_player = RosterPlayer(
            id="flex_rp",
            roster_id="test_roster",
            player_id="flex_player",
            roster_slot="FLEX",
            is_active=True
        )
        flex_roster_player.player = flex_player
        roster.roster_players.append(flex_roster_player)
        
        is_valid, violations = rules_engine.validate_roster_constraints(roster, sample_roster_slots)
        assert is_valid is True
        assert len(violations) == 0
    
    def test_missing_required_positions(self, rules_engine, sample_roster_slots):
        """Test validation with missing required positions."""
        starting_lineup = {
            "QB": 1,
            "RB": 1,  # Missing 1 RB
            "WR": 2,
            "TE": 1,
            "K": 1,
            "DEF": 1,
        }
        
        roster = self.create_test_roster(starting_lineup)
        
        is_valid, violations = rules_engine.validate_roster_constraints(roster, sample_roster_slots)
        assert is_valid is False
        assert any("Not enough starting RB players" in v for v in violations)
    
    def test_too_many_players_at_position(self, rules_engine, sample_roster_slots):
        """Test validation with too many players at a position."""
        starting_lineup = {
            "QB": 2,  # Too many QBs
            "RB": 2,
            "WR": 2,
            "TE": 1,
            "K": 1,
            "DEF": 1,
        }
        
        roster = self.create_test_roster(starting_lineup)
        
        is_valid, violations = rules_engine.validate_roster_constraints(roster, sample_roster_slots)
        assert is_valid is False
        assert any("Too many starting QB players" in v for v in violations)
    
    def test_missing_flex_player(self, rules_engine, sample_roster_slots):
        """Test validation with missing FLEX player."""
        starting_lineup = {
            "QB": 1,
            "RB": 2,
            "WR": 2,
            "TE": 1,
            "K": 1,
            "DEF": 1,
            # Missing FLEX player
        }
        
        roster = self.create_test_roster(starting_lineup)
        
        is_valid, violations = rules_engine.validate_roster_constraints(roster, sample_roster_slots)
        assert is_valid is False
        assert any("Not enough FLEX players" in v for v in violations)


class TestScoringCalculations:
    """Test various scoring calculation scenarios."""
    
    @pytest.fixture
    def rules_engine(self):
        return RulesEngine()
    
    def test_standard_scoring(self, rules_engine):
        """Test standard scoring calculation."""
        stats = {
            "passing_yards": 250,
            "passing_touchdowns": 2,
            "rushing_yards": 100,
            "rushing_touchdowns": 1,
            "receiving_yards": 50,
            "receptions": 5,
        }
        
        standard_rules = {
            "passing_yards": 0.04,
            "passing_touchdowns": 4.0,
            "rushing_yards": 0.1,
            "rushing_touchdowns": 6.0,
            "receiving_yards": 0.1,
            "receptions": 0.0,  # No PPR
        }
        
        score = rules_engine.calculate_player_score(stats, standard_rules)
        expected = (250 * 0.04) + (2 * 4.0) + (100 * 0.1) + (1 * 6.0) + (50 * 0.1)
        assert score == expected
    
    def test_ppr_scoring(self, rules_engine):
        """Test PPR scoring calculation."""
        stats = {
            "receiving_yards": 100,
            "receptions": 8,
            "receiving_touchdowns": 1,
        }
        
        ppr_rules = {
            "receiving_yards": 0.1,
            "receptions": 1.0,  # Full PPR
            "receiving_touchdowns": 6.0,
        }
        
        score = rules_engine.calculate_player_score(stats, ppr_rules)
        expected = (100 * 0.1) + (8 * 1.0) + (1 * 6.0)
        assert score == expected
    
    def test_negative_scoring(self, rules_engine):
        """Test scoring with negative points."""
        stats = {
            "passing_interceptions": 2,
            "fumbles_lost": 1,
            "field_goals_missed": 1,
        }
        
        rules = {
            "passing_interceptions": -2.0,
            "fumbles_lost": -2.0,
            "field_goals_missed": -1.0,
        }
        
        score = rules_engine.calculate_player_score(stats, rules)
        expected = (2 * -2.0) + (1 * -2.0) + (1 * -1.0)
        assert score == expected
    
    def test_defense_scoring(self, rules_engine):
        """Test defensive scoring calculation."""
        stats = {
            "defense_touchdowns": 1,
            "defense_interceptions": 2,
            "defense_sacks": 3,
            "defense_points_allowed_7_13": 1,  # Allowed 7-13 points
        }
        
        defense_rules = {
            "defense_touchdowns": 6.0,
            "defense_interceptions": 2.0,
            "defense_sacks": 1.0,
            "defense_points_allowed_7_13": 4.0,
        }
        
        score = rules_engine.calculate_player_score(stats, defense_rules)
        expected = (1 * 6.0) + (2 * 2.0) + (3 * 1.0) + (1 * 4.0)
        assert score == expected