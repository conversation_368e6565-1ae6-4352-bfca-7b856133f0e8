"""
Unit tests for file processing background tasks.
"""
import pytest
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
from decimal import Decimal

from app.tasks.file_processing import (
    process_uploaded_file,
    cleanup_old_data,
    validate_uploaded_data
)
from app.models import Player, Projection, Ranking
from tests.conftest import TestingSessionLocal


class TestFileProcessingTasks:
    """Test cases for file processing tasks."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.db = TestingSessionLocal()
    
    def teardown_method(self):
        """Clean up after tests."""
        self.db.close()
    
    @patch('app.tasks.file_processing.SessionLocal')
    @patch('tempfile.NamedTemporaryFile')
    def test_process_uploaded_file_success(self, mock_tempfile, mock_session):
        """Test successful file processing task."""
        # Mock database session
        mock_session.return_value = self.db
        
        # Mock temporary file
        mock_temp = Mock()
        mock_temp.name = "/tmp/test_file.csv"
        mock_temp.__enter__ = Mock(return_value=mock_temp)
        mock_temp.__exit__ = Mock(return_value=None)
        mock_tempfile.return_value = mock_temp
        
        # Create test data
        csv_content = b"player_name,projected_points\nJosh <PERSON>,25.5"
        
        # Mock the task's update_state method
        with patch.object(process_uploaded_file, 'update_state') as mock_update:
            with patch('builtins.open', create=True) as mock_open:
                mock_file = Mock()
                mock_file.read.return_value = csv_content
                mock_open.return_value.__enter__.return_value = mock_file
                
                with patch('os.path.exists', return_value=True):
                    with patch('os.unlink'):
                        # Execute task
                        result = process_uploaded_file(
                            csv_content,
                            "test.csv",
                            "projections",
                            "test_source",
                            2024
                        )
        
        assert result["status"] == "SUCCESS"
        assert "result" in result
    
    @patch('app.tasks.file_processing.SessionLocal')
    def test_cleanup_old_data_projections(self, mock_session):
        """Test cleanup task for projections."""
        mock_session.return_value = self.db
        
        # Create test data
        player = Player(id="test_1", name="Test Player", position="QB", team="TEST")
        projection = Projection(
            id="proj_1",
            player_id="test_1",
            season=2024,
            source="test_source",
            projected_points=Decimal("20.0")
        )
        
        self.db.add(player)
        self.db.add(projection)
        self.db.commit()
        
        # Mock the task's update_state method
        with patch.object(cleanup_old_data, 'update_state'):
            result = cleanup_old_data("projections", "test_source", 2024)
        
        assert result["status"] == "SUCCESS"
        assert result["deleted_count"] == 1
        
        # Verify data was deleted
        remaining = self.db.query(Projection).filter(
            Projection.source == "test_source",
            Projection.season == 2024
        ).count()
        assert remaining == 0
    
    @patch('app.tasks.file_processing.SessionLocal')
    def test_cleanup_old_data_rankings(self, mock_session):
        """Test cleanup task for rankings."""
        mock_session.return_value = self.db
        
        # Create test data
        player = Player(id="test_1", name="Test Player", position="QB", team="TEST")
        ranking = Ranking(
            id="rank_1",
            player_id="test_1",
            season=2024,
            source="test_source",
            ranking_type="expert",
            overall_rank=1
        )
        
        self.db.add(player)
        self.db.add(ranking)
        self.db.commit()
        
        # Mock the task's update_state method
        with patch.object(cleanup_old_data, 'update_state'):
            result = cleanup_old_data("rankings", "test_source", 2024)
        
        assert result["status"] == "SUCCESS"
        assert result["deleted_count"] == 1
    
    @patch('app.tasks.file_processing.SessionLocal')
    def test_validate_uploaded_data_projections(self, mock_session):
        """Test validation task for projections."""
        mock_session.return_value = self.db
        
        # Create test data with valid and invalid projections
        player = Player(id="test_1", name="Test Player", position="QB", team="TEST")
        
        valid_projection = Projection(
            id="proj_1",
            player_id="test_1",
            season=2024,
            source="test_source",
            projected_points=Decimal("20.0"),
            floor=Decimal("15.0"),
            ceiling=Decimal("25.0")
        )
        
        invalid_projection = Projection(
            id="proj_2",
            player_id="test_1",
            season=2024,
            source="test_source",
            projected_points=Decimal("-5.0"),  # Invalid: negative points
            floor=Decimal("20.0"),
            ceiling=Decimal("10.0")  # Invalid: floor > ceiling
        )
        
        self.db.add(player)
        self.db.add(valid_projection)
        self.db.add(invalid_projection)
        self.db.commit()
        
        # Mock the task's update_state method
        with patch.object(validate_uploaded_data, 'update_state'):
            result = validate_uploaded_data("projections", "test_source", 2024)
        
        assert result["status"] == "SUCCESS"
        validation_results = result["validation_results"]
        assert validation_results["total_records"] == 2
        assert validation_results["valid_records"] == 1
        assert validation_results["invalid_records"] == 1
        assert len(validation_results["issues"]) > 0
    
    @patch('app.tasks.file_processing.SessionLocal')
    def test_validate_uploaded_data_rankings(self, mock_session):
        """Test validation task for rankings."""
        mock_session.return_value = self.db
        
        # Create test data
        player = Player(id="test_1", name="Test Player", position="QB", team="TEST")
        
        valid_ranking = Ranking(
            id="rank_1",
            player_id="test_1",
            season=2024,
            source="test_source",
            ranking_type="expert",
            overall_rank=1,
            adp=Decimal("1.5")
        )
        
        invalid_ranking = Ranking(
            id="rank_2",
            player_id="test_1",
            season=2024,
            source="test_source",
            ranking_type="expert",
            overall_rank=-1,  # Invalid: negative rank
            adp=Decimal("-2.0")  # Invalid: negative ADP
        )
        
        self.db.add(player)
        self.db.add(valid_ranking)
        self.db.add(invalid_ranking)
        self.db.commit()
        
        # Mock the task's update_state method
        with patch.object(validate_uploaded_data, 'update_state'):
            result = validate_uploaded_data("rankings", "test_source", 2024)
        
        assert result["status"] == "SUCCESS"
        validation_results = result["validation_results"]
        assert validation_results["total_records"] == 2
        assert validation_results["valid_records"] == 1
        assert validation_results["invalid_records"] == 1
    
    @patch('app.tasks.file_processing.SessionLocal')
    def test_task_error_handling(self, mock_session):
        """Test task error handling."""
        # Mock database session to raise an exception
        mock_session.side_effect = Exception("Database connection failed")
        
        # Mock the task's update_state method
        with patch.object(cleanup_old_data, 'update_state') as mock_update:
            with pytest.raises(Exception, match="Database connection failed"):
                cleanup_old_data("projections", "test_source", 2024)
            
            # Verify error state was set
            mock_update.assert_called_with(
                state='FAILURE',
                meta={'status': 'Cleanup error', 'error': 'Database connection failed'}
            )