"""
Unit tests for the alert service.
"""
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.services.alert_service import AlertService
from app.models.alert import (
    Alert, AlertSchedule, PlayerNewsAlert,
    AlertType, AlertPriority, AlertStatus, DeliveryMethod
)
from app.models.player import Player, PlayerPosition, InjuryStatus
from app.models.league import League
from app.models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RosterPlayer


class TestAlertService:
    """Test cases for AlertService."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_db = Mock(spec=Session)
        self.alert_service = AlertService(self.mock_db)
    
    def test_create_alert_basic(self):
        """Test creating a basic alert."""
        # Arrange
        alert_type = AlertType.KEEPER_DEADLINE
        title = "Test Alert"
        message = "Test message"
        priority = AlertPriority.HIGH
        
        # Act
        with patch('app.services.alert_service.Alert') as mock_alert_class:
            mock_alert = Mock()
            mock_alert.alert_type = alert_type
            mock_alert.title = title
            mock_alert.message = message
            mock_alert.priority = priority
            mock_alert.status = AlertStatus.PENDING
            mock_alert.delivery_methods = [DeliveryMethod.IN_APP.value]
            mock_alert_class.return_value = mock_alert
            
            alert = self.alert_service.create_alert(
                alert_type=alert_type,
                title=title,
                message=message,
                priority=priority
            )
            
            # Assert
            assert alert.alert_type == alert_type
            assert alert.title == title
            assert alert.message == message
            assert alert.priority == priority
            assert alert.status == AlertStatus.PENDING
            assert alert.delivery_methods == [DeliveryMethod.IN_APP.value]
            self.mock_db.add.assert_called_once()
            self.mock_db.commit.assert_called_once()
            self.mock_db.refresh.assert_called_once()
    
    def test_create_alert_with_all_parameters(self):
        """Test creating an alert with all parameters."""
        # Arrange
        alert_type = AlertType.PLAYER_NEWS
        title = "Player News"
        message = "Important news"
        priority = AlertPriority.URGENT
        league_id = "league123"
        franchise_id = "franchise456"
        player_id = "player789"
        scheduled_for = datetime.now() + timedelta(hours=1)
        expires_at = datetime.now() + timedelta(days=1)
        delivery_methods = [DeliveryMethod.EMAIL.value, DeliveryMethod.IN_APP.value]
        alert_data = {"test": "data"}
        
        # Act
        alert = self.alert_service.create_alert(
            alert_type=alert_type,
            title=title,
            message=message,
            priority=priority,
            league_id=league_id,
            franchise_id=franchise_id,
            player_id=player_id,
            scheduled_for=scheduled_for,
            expires_at=expires_at,
            delivery_methods=delivery_methods,
            alert_data=alert_data
        )
        
        # Assert
        assert alert.league_id == league_id
        assert alert.franchise_id == franchise_id
        assert alert.player_id == player_id
        assert alert.scheduled_for == scheduled_for
        assert alert.expires_at == expires_at
        assert alert.delivery_methods == delivery_methods
        assert alert.alert_data == alert_data
    
    def test_create_keeper_deadline_alert(self):
        """Test creating a keeper deadline alert."""
        # Arrange
        schedule = Mock(spec=AlertSchedule)
        schedule.id = "schedule123"
        schedule.league_id = "league123"
        schedule.franchise_id = "franchise456"
        schedule.target_datetime = datetime.now() + timedelta(hours=2)
        
        advance_notice = 2.0
        time_until_deadline = timedelta(hours=2)
        
        # Act
        alert = self.alert_service._create_keeper_deadline_alert(
            schedule, advance_notice, time_until_deadline
        )
        
        # Assert
        assert alert.alert_type == AlertType.KEEPER_DEADLINE
        assert alert.priority == AlertPriority.HIGH
        assert "Keeper Deadline Soon" in alert.title
        assert "2 hours" in alert.message
        assert alert.league_id == schedule.league_id
        assert alert.franchise_id == schedule.franchise_id
    
    def test_create_waiver_deadline_alert(self):
        """Test creating a waiver deadline alert."""
        # Arrange
        schedule = Mock(spec=AlertSchedule)
        schedule.id = "schedule123"
        schedule.league_id = "league123"
        schedule.franchise_id = "franchise456"
        schedule.target_datetime = datetime.now() + timedelta(hours=24)
        
        advance_notice = 24.0
        time_until_deadline = timedelta(hours=24)
        
        # Act
        alert = self.alert_service._create_waiver_deadline_alert(
            schedule, advance_notice, time_until_deadline
        )
        
        # Assert
        assert alert.alert_type == AlertType.WAIVER_DEADLINE
        assert alert.priority == AlertPriority.MEDIUM
        assert "Waiver Period Ending Soon" in alert.title
        assert "24 hours" in alert.message
    
    def test_create_lineup_lock_alert(self):
        """Test creating a lineup lock alert."""
        # Arrange
        schedule = Mock(spec=AlertSchedule)
        schedule.id = "schedule123"
        schedule.league_id = "league123"
        schedule.franchise_id = "franchise456"
        schedule.target_datetime = datetime.now() + timedelta(minutes=30)
        
        advance_notice = 0.5
        time_until_deadline = timedelta(minutes=30)
        
        # Act
        alert = self.alert_service._create_lineup_lock_alert(
            schedule, advance_notice, time_until_deadline
        )
        
        # Assert
        assert alert.alert_type == AlertType.LINEUP_LOCK
        assert alert.priority == AlertPriority.URGENT
        assert "Lineup Lock Imminent" in alert.title
        assert "30 minutes" in alert.message
    
    def test_create_trade_deadline_alert(self):
        """Test creating a trade deadline alert."""
        # Arrange
        schedule = Mock(spec=AlertSchedule)
        schedule.id = "schedule123"
        schedule.league_id = "league123"
        schedule.franchise_id = "franchise456"
        schedule.target_datetime = datetime.now() + timedelta(hours=48)
        
        advance_notice = 48.0
        time_until_deadline = timedelta(hours=48)
        
        # Act
        alert = self.alert_service._create_trade_deadline_alert(
            schedule, advance_notice, time_until_deadline
        )
        
        # Assert
        assert alert.alert_type == AlertType.TRADE_DEADLINE
        assert alert.priority == AlertPriority.MEDIUM
        assert "Trade Deadline Approaching" in alert.title
        assert "48 hours" in alert.message
    
    def test_process_deadline_monitoring(self):
        """Test processing deadline monitoring schedules."""
        # Arrange
        schedule1 = Mock(spec=AlertSchedule)
        schedule1.is_due_for_processing.return_value = True
        schedule1.last_processed = None
        
        schedule2 = Mock(spec=AlertSchedule)
        schedule2.is_due_for_processing.return_value = False
        
        self.mock_db.query.return_value.filter.return_value.all.return_value = [
            schedule1, schedule2
        ]
        
        with patch.object(self.alert_service, '_create_deadline_alert') as mock_create:
            mock_alert = Mock(spec=Alert)
            mock_create.return_value = mock_alert
            
            # Act
            alerts = self.alert_service.process_deadline_monitoring()
            
            # Assert
            assert len(alerts) == 1
            assert alerts[0] == mock_alert
            mock_create.assert_called_once_with(schedule1)
            assert schedule1.last_processed is not None
    
    def test_create_franchise_player_news_alert(self):
        """Test creating a player news alert for a franchise."""
        # Arrange
        news = Mock(spec=PlayerNewsAlert)
        news.id = "news123"
        news.player_id = "player456"
        news.headline = "Player Injured"
        news.content = "Player suffered injury"
        news.fantasy_impact = "Significant impact on fantasy value"
        news.impact_score = 8.5
        news.source = "ESPN"
        news.affected_positions = ["RB"]
        
        franchise = Mock(spec=Franchise)
        franchise.id = "franchise789"
        franchise.league_id = "league123"
        
        player = Mock(spec=Player)
        player.name = "Test Player"
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = player
        
        # Act
        alert = self.alert_service._create_franchise_player_news_alert(news, franchise)
        
        # Assert
        assert alert.alert_type == AlertType.PLAYER_NEWS
        assert alert.priority == AlertPriority.URGENT  # impact_score >= 8.0
        assert "Player News: Test Player" in alert.title
        assert news.headline in alert.message
        assert alert.franchise_id == franchise.id
        assert alert.league_id == franchise.league_id
        assert alert.player_id == news.player_id
    
    def test_is_injury_change_significant(self):
        """Test determining if injury status changes are significant."""
        # Test significant changes
        assert self.alert_service._is_injury_change_significant(
            InjuryStatus.HEALTHY, InjuryStatus.OUT
        )
        assert self.alert_service._is_injury_change_significant(
            InjuryStatus.QUESTIONABLE, InjuryStatus.OUT
        )
        assert self.alert_service._is_injury_change_significant(
            InjuryStatus.OUT, InjuryStatus.HEALTHY
        )
        
        # Test non-significant changes
        assert not self.alert_service._is_injury_change_significant(
            InjuryStatus.HEALTHY, InjuryStatus.HEALTHY
        )
        assert not self.alert_service._is_injury_change_significant(
            InjuryStatus.QUESTIONABLE, InjuryStatus.DOUBTFUL
        )
    
    def test_create_injury_status_alert(self):
        """Test creating an injury status alert."""
        # Arrange
        player = Mock(spec=Player)
        player.id = "player123"
        player.name = "Test Player"
        player.position = PlayerPosition.RB
        player.team = "NYG"
        
        franchise = Mock(spec=Franchise)
        franchise.id = "franchise456"
        franchise.league_id = "league789"
        
        old_status = InjuryStatus.HEALTHY
        new_status = InjuryStatus.OUT
        
        # Act
        alert = self.alert_service._create_injury_status_alert(
            player, franchise, old_status, new_status
        )
        
        # Assert
        assert alert.alert_type == AlertType.INJURY_UPDATE
        assert alert.priority == AlertPriority.URGENT  # new_status == OUT
        assert "Injury Update: Test Player" in alert.title
        assert "HEALTHY to OUT" in alert.message
        assert alert.franchise_id == franchise.id
        assert alert.league_id == franchise.league_id
        assert alert.player_id == player.id
    
    def test_get_active_alerts_with_franchise_filter(self):
        """Test getting active alerts filtered by franchise."""
        # Arrange
        franchise_id = "franchise123"
        mock_alerts = [Mock(spec=Alert), Mock(spec=Alert)]
        
        query_mock = Mock()
        query_mock.filter.return_value = query_mock
        query_mock.order_by.return_value = query_mock
        query_mock.limit.return_value = query_mock
        query_mock.all.return_value = mock_alerts
        
        self.mock_db.query.return_value = query_mock
        
        # Act
        alerts = self.alert_service.get_active_alerts(franchise_id=franchise_id)
        
        # Assert
        assert alerts == mock_alerts
        self.mock_db.query.assert_called_once_with(Alert)
    
    def test_mark_alert_delivered_success(self):
        """Test successfully marking an alert as delivered."""
        # Arrange
        alert_id = "alert123"
        mock_alert = Mock(spec=Alert)
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_alert
        
        # Act
        result = self.alert_service.mark_alert_delivered(alert_id)
        
        # Assert
        assert result is True
        assert mock_alert.status == AlertStatus.DELIVERED
        assert mock_alert.sent_at is not None
        self.mock_db.commit.assert_called_once()
    
    def test_mark_alert_delivered_not_found(self):
        """Test marking a non-existent alert as delivered."""
        # Arrange
        alert_id = "nonexistent"
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Act
        result = self.alert_service.mark_alert_delivered(alert_id)
        
        # Assert
        assert result is False
        self.mock_db.commit.assert_not_called()
    
    def test_dismiss_alert_success(self):
        """Test successfully dismissing an alert."""
        # Arrange
        alert_id = "alert123"
        mock_alert = Mock(spec=Alert)
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_alert
        
        # Act
        result = self.alert_service.dismiss_alert(alert_id)
        
        # Assert
        assert result is True
        assert mock_alert.status == AlertStatus.DISMISSED
        self.mock_db.commit.assert_called_once()
    
    def test_dismiss_alert_not_found(self):
        """Test dismissing a non-existent alert."""
        # Arrange
        alert_id = "nonexistent"
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Act
        result = self.alert_service.dismiss_alert(alert_id)
        
        # Assert
        assert result is False
        self.mock_db.commit.assert_not_called()
    
    def test_create_alert_schedule(self):
        """Test creating an alert schedule."""
        # Arrange
        name = "Keeper Deadline"
        alert_type = AlertType.KEEPER_DEADLINE
        target_datetime = datetime.now() + timedelta(days=7)
        advance_notice_hours = [24, 2]
        league_id = "league123"
        
        # Act
        with patch('app.services.alert_service.AlertSchedule') as mock_schedule_class:
            mock_schedule = Mock()
            mock_schedule.name = name
            mock_schedule.alert_type = alert_type
            mock_schedule.target_datetime = target_datetime
            mock_schedule.advance_notice_hours = advance_notice_hours
            mock_schedule.league_id = league_id
            mock_schedule.is_active = True
            mock_schedule_class.return_value = mock_schedule
            
            schedule = self.alert_service.create_alert_schedule(
                name=name,
                alert_type=alert_type,
                target_datetime=target_datetime,
                advance_notice_hours=advance_notice_hours,
                league_id=league_id
            )
            
            # Assert
            assert schedule.name == name
            assert schedule.alert_type == alert_type
            assert schedule.target_datetime == target_datetime
            assert schedule.advance_notice_hours == advance_notice_hours
            assert schedule.league_id == league_id
            assert schedule.is_active is True
            self.mock_db.add.assert_called_once()
            self.mock_db.commit.assert_called_once()
            self.mock_db.refresh.assert_called_once()
    
    def test_create_player_news_alert(self):
        """Test creating a player news alert."""
        # Arrange
        player_id = "player123"
        headline = "Player Injured"
        content = "Player suffered a knee injury"
        source = "ESPN"
        impact_score = 7.5
        affected_positions = ["RB", "FLEX"]
        fantasy_impact = "Significant impact"
        
        # Act
        with patch('app.services.alert_service.PlayerNewsAlert') as mock_news_class:
            mock_news = Mock()
            mock_news.player_id = player_id
            mock_news.headline = headline
            mock_news.content = content
            mock_news.source = source
            mock_news.impact_score = impact_score
            mock_news.affected_positions = affected_positions
            mock_news.fantasy_impact = fantasy_impact
            mock_news.is_processed = False
            mock_news_class.return_value = mock_news
            
            news = self.alert_service.create_player_news_alert(
                player_id=player_id,
                headline=headline,
                content=content,
                source=source,
                impact_score=impact_score,
                affected_positions=affected_positions,
                fantasy_impact=fantasy_impact
            )
            
            # Assert
            assert news.player_id == player_id
            assert news.headline == headline
            assert news.content == content
            assert news.source == source
            assert news.impact_score == impact_score
            assert news.affected_positions == affected_positions
            assert news.fantasy_impact == fantasy_impact
            assert news.is_processed is False
            self.mock_db.add.assert_called_once()
            self.mock_db.commit.assert_called_once()
            self.mock_db.refresh.assert_called_once()


class TestAlertModel:
    """Test cases for Alert model methods."""
    
    def test_is_expired_with_expiry(self):
        """Test is_expired method with expiry date."""
        # Arrange
        alert = Alert()
        alert.expires_at = datetime.now() - timedelta(hours=1)  # Expired
        
        # Act & Assert
        assert alert.is_expired() is True
    
    def test_is_expired_without_expiry(self):
        """Test is_expired method without expiry date."""
        # Arrange
        alert = Alert()
        alert.expires_at = None
        
        # Act & Assert
        assert alert.is_expired() is False
    
    def test_is_due_with_schedule(self):
        """Test is_due method with scheduled time."""
        # Arrange
        alert = Alert()
        alert.scheduled_for = datetime.now() - timedelta(minutes=1)  # Due
        
        # Act & Assert
        assert alert.is_due() is True
    
    def test_is_due_without_schedule(self):
        """Test is_due method without scheduled time."""
        # Arrange
        alert = Alert()
        alert.scheduled_for = None
        
        # Act & Assert
        assert alert.is_due() is True
    
    def test_should_retry_success(self):
        """Test should_retry method for retryable alert."""
        # Arrange
        alert = Alert()
        alert.status = AlertStatus.FAILED
        alert.delivery_attempts = 2
        alert.expires_at = datetime.now() + timedelta(hours=1)  # Not expired
        
        # Act & Assert
        assert alert.should_retry(max_attempts=3) is True
    
    def test_should_retry_max_attempts_reached(self):
        """Test should_retry method when max attempts reached."""
        # Arrange
        alert = Alert()
        alert.status = AlertStatus.FAILED
        alert.delivery_attempts = 3
        alert.expires_at = datetime.now() + timedelta(hours=1)
        
        # Act & Assert
        assert alert.should_retry(max_attempts=3) is False


class TestAlertScheduleModel:
    """Test cases for AlertSchedule model methods."""
    
    def test_get_next_alert_time(self):
        """Test getting the next alert time."""
        # Arrange
        schedule = AlertSchedule()
        schedule.target_datetime = datetime.now() + timedelta(hours=25)
        schedule.advance_notice_hours = [24, 2, 0.5]
        
        # Act
        next_time = schedule.get_next_alert_time()
        
        # Assert
        assert next_time is not None
        # Should be 24 hours before target (the earliest future alert)
        expected_time = schedule.target_datetime - timedelta(hours=24)
        assert abs((next_time - expected_time).total_seconds()) < 60  # Within 1 minute
    
    def test_get_next_alert_time_no_future_alerts(self):
        """Test getting next alert time when no future alerts."""
        # Arrange
        schedule = AlertSchedule()
        schedule.target_datetime = datetime.now() + timedelta(minutes=10)
        schedule.advance_notice_hours = [24, 2]  # All in the past
        
        # Act
        next_time = schedule.get_next_alert_time()
        
        # Assert
        assert next_time is None
    
    def test_is_due_for_processing_active_and_due(self):
        """Test is_due_for_processing when active and due."""
        # Arrange
        schedule = Mock(spec=AlertSchedule)
        schedule.is_active = True
        schedule.target_datetime = datetime.now() + timedelta(hours=1)
        schedule.advance_notice_hours = [2]  # Due now
        
        # Mock the get_next_alert_time method to return a time in the past
        schedule.get_next_alert_time.return_value = datetime.now() - timedelta(minutes=1)
        
        # Create a real AlertSchedule instance and copy the method
        real_schedule = AlertSchedule()
        schedule.is_due_for_processing = real_schedule.is_due_for_processing.__get__(schedule)
        
        # Act & Assert
        assert schedule.is_due_for_processing() is True
    
    def test_is_due_for_processing_inactive(self):
        """Test is_due_for_processing when inactive."""
        # Arrange
        schedule = AlertSchedule()
        schedule.is_active = False
        schedule.target_datetime = datetime.now() + timedelta(hours=1)
        schedule.advance_notice_hours = [2]
        
        # Act & Assert
        assert schedule.is_due_for_processing() is False


class TestPlayerNewsAlertModel:
    """Test cases for PlayerNewsAlert model methods."""
    
    def test_should_generate_alerts_high_impact(self):
        """Test should_generate_alerts with high impact score."""
        # Arrange
        news = PlayerNewsAlert()
        news.is_processed = False
        news.impact_score = 7.5
        
        # Act & Assert
        assert news.should_generate_alerts() is True
    
    def test_should_generate_alerts_low_impact(self):
        """Test should_generate_alerts with low impact score."""
        # Arrange
        news = PlayerNewsAlert()
        news.is_processed = False
        news.impact_score = 3.0
        
        # Act & Assert
        assert news.should_generate_alerts() is False
    
    def test_should_generate_alerts_already_processed(self):
        """Test should_generate_alerts when already processed."""
        # Arrange
        news = PlayerNewsAlert()
        news.is_processed = True
        news.impact_score = 8.0
        
        # Act & Assert
        assert news.should_generate_alerts() is False
    
    def test_should_generate_alerts_no_impact_score(self):
        """Test should_generate_alerts with no impact score."""
        # Arrange
        news = PlayerNewsAlert()
        news.is_processed = False
        news.impact_score = None
        
        # Act & Assert
        assert news.should_generate_alerts() is False