"""
Unit tests for the keeper optimization API endpoints.
"""
import pytest
from decimal import Decimal
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.services.keeper_optimizer import (
    KeeperCandidate, KeeperRecommendation, KeeperScenario, KeeperAlternative
)
from app.models.roster import Franchise
from app.models.league import League
from app.models.player import PlayerPosition


class TestKeepersAPI:
    """Test cases for the keepers API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def sample_franchise(self):
        """Create a sample franchise for testing."""
        league = League(
            id="test_league",
            name="Test League",
            season=2024,
            keeper_rules={
                "max_keepers": 3,
                "round_escalation": 1
            }
        )
        
        franchise = Franchise(
            id="test_franchise",
            name="Test Team",
            owner_name="Test Owner",
            league_id="test_league"
        )
        franchise.league = league
        return franchise
    
    @pytest.fixture
    def sample_keeper_candidates(self):
        """Create sample keeper candidates."""
        return [
            KeeperCandidate(
                player_id="player1",
                player_name="Elite QB",
                position=PlayerPosition.QB,
                current_cost=5,
                projected_points=Decimal('320'),
                replacement_level=Decimal('250'),
                value_over_replacement=Decimal('70'),
                keeper_cost=4,
                is_eligible=True,
                constraints_violated=[],
                metadata={
                    "team": "KC",
                    "bye_week": 10,
                    "injury_status": "HEALTHY",
                    "projection_confidence": 0.1,
                    "source_count": 4
                }
            ),
            KeeperCandidate(
                player_id="player2",
                player_name="Top RB",
                position=PlayerPosition.RB,
                current_cost=8,
                projected_points=Decimal('280'),
                replacement_level=Decimal('180'),
                value_over_replacement=Decimal('100'),
                keeper_cost=7,
                is_eligible=True,
                constraints_violated=[],
                metadata={
                    "team": "SF",
                    "bye_week": 9,
                    "injury_status": "HEALTHY",
                    "projection_confidence": 0.15,
                    "source_count": 3
                }
            ),
            KeeperCandidate(
                player_id="player3",
                player_name="Injured WR",
                position=PlayerPosition.WR,
                current_cost=10,
                projected_points=Decimal('220'),
                replacement_level=Decimal('160'),
                value_over_replacement=Decimal('60'),
                keeper_cost=9,
                is_eligible=False,
                constraints_violated=["Player is OUT"],
                metadata={
                    "team": "LAR",
                    "bye_week": 6,
                    "injury_status": "OUT",
                    "projection_confidence": 0.2,
                    "source_count": 2
                }
            )
        ]
    
    @pytest.fixture
    def sample_keeper_scenarios(self):
        """Create sample keeper scenarios."""
        alternatives = [
            KeeperAlternative(
                player_id="alt1",
                player_name="Alternative RB",
                position=PlayerPosition.RB,
                keeper_cost=9,
                value_over_replacement=Decimal('80'),
                reason="Alternative RB option"
            )
        ]
        
        recommendations = [
            KeeperRecommendation(
                player_id="player1",
                player_name="Elite QB",
                position=PlayerPosition.QB,
                keeper_cost=4,
                projected_points=Decimal('320'),
                value_over_replacement=Decimal('70'),
                confidence=0.85,
                rationale="Exceptional value with 70.0 points over replacement. Excellent keeper value at round 4.",
                alternatives=[],
                metadata={"team": "KC", "bye_week": 10}
            ),
            KeeperRecommendation(
                player_id="player2",
                player_name="Top RB",
                position=PlayerPosition.RB,
                keeper_cost=7,
                projected_points=Decimal('280'),
                value_over_replacement=Decimal('100'),
                confidence=0.78,
                rationale="Exceptional value with 100.0 points over replacement. Good keeper value at round 7.",
                alternatives=alternatives,
                metadata={"team": "SF", "bye_week": 9}
            )
        ]
        
        return [
            KeeperScenario(
                scenario_name="Optimal",
                selected_keepers=recommendations,
                total_value=Decimal('170'),
                remaining_budget=None,
                constraints_satisfied=True,
                trade_offs=["Passed on Injured WR (60.0 VOR) due to cost/constraint considerations"],
                metadata={
                    "total_cost": 11,
                    "max_keepers": 3,
                    "solver_status": "OPTIMAL"
                }
            ),
            KeeperScenario(
                scenario_name="Value-Focused",
                selected_keepers=recommendations[:1],  # Just QB
                total_value=Decimal('70'),
                remaining_budget=None,
                constraints_satisfied=True,
                trade_offs=[],
                metadata={
                    "total_cost": 4,
                    "max_keepers": 3,
                    "solver_status": "OPTIMAL"
                }
            )
        ]
    
    @patch('app.api.keepers.get_db')
    def test_get_keeper_candidates_success(self, mock_get_db, client, mock_db_session, 
                                         sample_franchise, sample_keeper_candidates):
        """Test successful retrieval of keeper candidates."""
        mock_get_db.return_value = mock_db_session
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_franchise
        
        with patch('app.api.keepers.KeeperOptimizer') as mock_optimizer_class:
            mock_optimizer = Mock()
            mock_optimizer.get_keeper_candidates.return_value = sample_keeper_candidates
            mock_optimizer_class.return_value = mock_optimizer
            
            response = client.get("/api/v1/keepers/candidates/test_franchise?season=2024")
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert len(data) == 3
        
        # Verify first candidate
        candidate = data[0]
        assert candidate["player_id"] == "player1"
        assert candidate["player_name"] == "Elite QB"
        assert candidate["position"] == "QB"
        assert candidate["projected_points"] == 320.0
        assert candidate["value_over_replacement"] == 70.0
        assert candidate["keeper_cost"] == 4
        assert candidate["is_eligible"] == True
        assert len(candidate["constraints_violated"]) == 0
        
        # Verify injured player
        injured_candidate = data[2]
        assert injured_candidate["is_eligible"] == False
        assert len(injured_candidate["constraints_violated"]) == 1
        assert "OUT" in injured_candidate["constraints_violated"][0]
    
    @patch('app.api.keepers.get_db')
    def test_get_keeper_candidates_franchise_not_found(self, mock_get_db, client, mock_db_session):
        """Test keeper candidates endpoint with non-existent franchise."""
        mock_get_db.return_value = mock_db_session
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        response = client.get("/api/v1/keepers/candidates/nonexistent_franchise")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    @patch('app.api.keepers.get_db')
    def test_get_keeper_candidates_error(self, mock_get_db, client, mock_db_session, sample_franchise):
        """Test keeper candidates endpoint with service error."""
        mock_get_db.return_value = mock_db_session
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_franchise
        
        with patch('app.api.keepers.KeeperOptimizer') as mock_optimizer_class:
            mock_optimizer = Mock()
            mock_optimizer.get_keeper_candidates.side_effect = Exception("Service error")
            mock_optimizer_class.return_value = mock_optimizer
            
            response = client.get("/api/v1/keepers/candidates/test_franchise")
        
        assert response.status_code == 500
        assert "Error getting keeper candidates" in response.json()["detail"]
    
    @patch('app.api.keepers.get_db')
    def test_optimize_keepers_success(self, mock_get_db, client, mock_db_session, 
                                    sample_franchise, sample_keeper_scenarios):
        """Test successful keeper optimization."""
        mock_get_db.return_value = mock_db_session
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_franchise
        
        with patch('app.api.keepers.KeeperOptimizer') as mock_optimizer_class:
            mock_optimizer = Mock()
            mock_optimizer.optimize_keepers.return_value = sample_keeper_scenarios
            mock_optimizer_class.return_value = mock_optimizer
            
            response = client.get("/api/v1/keepers/optimize/test_franchise?season=2024&max_scenarios=3")
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert len(data) == 2
        
        # Verify first scenario
        scenario = data[0]
        assert scenario["scenario_name"] == "Optimal"
        assert len(scenario["selected_keepers"]) == 2
        assert scenario["total_value"] == 170.0
        assert scenario["constraints_satisfied"] == True
        assert len(scenario["trade_offs"]) == 1
        
        # Verify keeper details
        keeper = scenario["selected_keepers"][0]
        assert keeper["player_id"] == "player1"
        assert keeper["player_name"] == "Elite QB"
        assert keeper["position"] == "QB"
        assert keeper["keeper_cost"] == 4
        assert keeper["confidence"] == 0.85
        assert "Exceptional value" in keeper["rationale"]
        
        # Verify alternatives in second keeper
        keeper_with_alts = scenario["selected_keepers"][1]
        assert len(keeper_with_alts["alternatives"]) == 1
        alt = keeper_with_alts["alternatives"][0]
        assert alt["player_id"] == "alt1"
        assert alt["reason"] == "Alternative RB option"
    
    @patch('app.api.keepers.get_db')
    def test_optimize_keepers_no_keeper_rules(self, mock_get_db, client, mock_db_session):
        """Test keeper optimization with league that has no keeper rules."""
        franchise_no_rules = Mock()
        franchise_no_rules.league.keeper_rules = None
        mock_get_db.return_value = mock_db_session
        mock_db_session.query.return_value.filter.return_value.first.return_value = franchise_no_rules
        
        response = client.get("/api/v1/keepers/optimize/test_franchise")
        
        assert response.status_code == 400
        assert "no keeper rules configured" in response.json()["detail"]
    
    @patch('app.api.keepers.get_db')
    def test_optimize_keepers_invalid_parameters(self, mock_get_db, client, mock_db_session, sample_franchise):
        """Test keeper optimization with invalid parameters."""
        mock_get_db.return_value = mock_db_session
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_franchise
        
        # Test max_scenarios too high
        response = client.get("/api/v1/keepers/optimize/test_franchise?max_scenarios=15")
        assert response.status_code == 422  # Validation error
        
        # Test max_scenarios too low
        response = client.get("/api/v1/keepers/optimize/test_franchise?max_scenarios=0")
        assert response.status_code == 422  # Validation error
    
    @patch('app.api.keepers.get_db')
    def test_get_replacement_levels_success(self, mock_get_db, client, mock_db_session):
        """Test successful retrieval of replacement levels."""
        mock_get_db.return_value = mock_db_session
        
        replacement_levels = {
            PlayerPosition.QB: Decimal('250'),
            PlayerPosition.RB: Decimal('180'),
            PlayerPosition.WR: Decimal('160'),
            PlayerPosition.TE: Decimal('120')
        }
        
        with patch('app.api.keepers.KeeperOptimizer') as mock_optimizer_class:
            mock_optimizer = Mock()
            mock_optimizer.calculate_replacement_levels.return_value = replacement_levels
            mock_optimizer_class.return_value = mock_optimizer
            
            response = client.get("/api/v1/keepers/replacement-levels/test_league?season=2024")
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert data["league_id"] == "test_league"
        assert data["season"] == 2024
        assert "calculation_timestamp" in data
        
        # Verify replacement levels
        levels = data["replacement_levels"]
        assert levels["QB"] == 250.0
        assert levels["RB"] == 180.0
        assert levels["WR"] == 160.0
        assert levels["TE"] == 120.0
    
    @patch('app.api.keepers.get_db')
    def test_get_replacement_levels_error(self, mock_get_db, client, mock_db_session):
        """Test replacement levels endpoint with service error."""
        mock_get_db.return_value = mock_db_session
        
        with patch('app.api.keepers.KeeperOptimizer') as mock_optimizer_class:
            mock_optimizer = Mock()
            mock_optimizer.calculate_replacement_levels.side_effect = Exception("Calculation error")
            mock_optimizer_class.return_value = mock_optimizer
            
            response = client.get("/api/v1/keepers/replacement-levels/test_league")
        
        assert response.status_code == 500
        assert "Error calculating replacement levels" in response.json()["detail"]
    
    @patch('app.api.keepers.get_db')
    def test_refresh_keeper_cache_success(self, mock_get_db, client, mock_db_session):
        """Test successful cache refresh."""
        mock_get_db.return_value = mock_db_session
        
        with patch('app.api.keepers.KeeperOptimizer') as mock_optimizer_class:
            mock_optimizer = Mock()
            mock_optimizer.refresh_replacement_levels_cache.return_value = None
            mock_optimizer.projections_aggregator.refresh_projections_cache.return_value = None
            mock_optimizer_class.return_value = mock_optimizer
            
            response = client.post("/api/v1/keepers/refresh-cache")
        
        assert response.status_code == 200
        data = response.json()
        assert "cache refreshed successfully" in data["message"]
        
        # Verify both cache refresh methods were called
        mock_optimizer.refresh_replacement_levels_cache.assert_called_once()
        mock_optimizer.projections_aggregator.refresh_projections_cache.assert_called_once()
    
    @patch('app.api.keepers.get_db')
    def test_refresh_keeper_cache_error(self, mock_get_db, client, mock_db_session):
        """Test cache refresh endpoint with service error."""
        mock_get_db.return_value = mock_db_session
        
        with patch('app.api.keepers.KeeperOptimizer') as mock_optimizer_class:
            mock_optimizer = Mock()
            mock_optimizer.refresh_replacement_levels_cache.side_effect = Exception("Cache error")
            mock_optimizer_class.return_value = mock_optimizer
            
            response = client.post("/api/v1/keepers/refresh-cache")
        
        assert response.status_code == 500
        assert "Error refreshing cache" in response.json()["detail"]
    
    def test_api_endpoint_paths(self, client):
        """Test that all keeper API endpoints are properly registered."""
        # This test verifies the endpoints exist (will return 422 for missing params, not 404)
        
        # Test candidates endpoint
        response = client.get("/api/v1/keepers/candidates/")
        assert response.status_code != 404  # Should be 422 (validation error) or other
        
        # Test optimize endpoint  
        response = client.get("/api/v1/keepers/optimize/")
        assert response.status_code != 404
        
        # Test replacement levels endpoint
        response = client.get("/api/v1/keepers/replacement-levels/")
        assert response.status_code != 404
        
        # Test cache refresh endpoint
        response = client.post("/api/v1/keepers/refresh-cache")
        assert response.status_code != 404
    
    def test_response_model_validation(self, client):
        """Test that response models are properly validated."""
        # This is implicitly tested by the successful response tests above,
        # but we can add specific validation tests if needed
        
        # The FastAPI framework will automatically validate response models
        # against the defined Pydantic models, so if our tests pass,
        # the response models are working correctly
        pass