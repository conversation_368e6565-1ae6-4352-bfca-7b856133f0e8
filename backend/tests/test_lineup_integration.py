"""
Integration tests for lineup optimization system.
"""
import pytest
from unittest.mock import Mock

from app.services.lineup_optimizer import LineupOptimizer, LineupSlot, WeatherCondition
from app.models.player import Player, PlayerPosition, InjuryStatus
from app.models.roster import Roster, RosterPlayer, Franchise
from app.models.projection import Projection
from app.models.league import League
from decimal import Decimal


class TestLineupIntegration:
    """Integration tests for lineup optimization."""
    
    def test_basic_lineup_optimization_workflow(self):
        """Test the basic workflow of lineup optimization."""
        # Create mock database
        mock_db = Mock()
        
        # Create optimizer
        optimizer = LineupOptimizer(mock_db)
        
        # Test basic methods work
        assert optimizer is not None
        
        # Test weather adjustments
        qb_clear = optimizer._get_weather_adjustment(PlayerPosition.QB, WeatherCondition.CLEAR)
        assert qb_clear == 1.0
        
        qb_wind = optimizer._get_weather_adjustment(PlayerPosition.QB, WeatherCondition.WIND)
        assert qb_wind < 1.0
        
        # Test opponent adjustments
        best_defense = optimizer._get_opponent_adjustment(PlayerPosition.QB, 1)
        worst_defense = optimizer._get_opponent_adjustment(PlayerPosition.QB, 32)
        assert best_defense < worst_defense
        
        # Test injury adjustments
        healthy = optimizer._get_injury_adjustment(InjuryStatus.HEALTHY)
        out = optimizer._get_injury_adjustment(InjuryStatus.OUT)
        assert healthy == 1.0
        assert out == 0.0
        
        print("✓ Basic lineup optimization workflow test passed")
    
    def test_player_eligibility_logic(self):
        """Test player position eligibility for lineup slots."""
        mock_db = Mock()
        optimizer = LineupOptimizer(mock_db)
        
        # QB should only be eligible for QB slot
        assert optimizer._is_player_eligible_for_slot(PlayerPosition.QB, LineupSlot.QB)
        assert not optimizer._is_player_eligible_for_slot(PlayerPosition.QB, LineupSlot.RB1)
        
        # RB should be eligible for RB and FLEX slots
        assert optimizer._is_player_eligible_for_slot(PlayerPosition.RB, LineupSlot.RB1)
        assert optimizer._is_player_eligible_for_slot(PlayerPosition.RB, LineupSlot.FLEX)
        assert not optimizer._is_player_eligible_for_slot(PlayerPosition.RB, LineupSlot.QB)
        
        # WR should be eligible for WR and FLEX slots
        assert optimizer._is_player_eligible_for_slot(PlayerPosition.WR, LineupSlot.WR1)
        assert optimizer._is_player_eligible_for_slot(PlayerPosition.WR, LineupSlot.FLEX)
        
        # TE should be eligible for TE and FLEX slots
        assert optimizer._is_player_eligible_for_slot(PlayerPosition.TE, LineupSlot.TE)
        assert optimizer._is_player_eligible_for_slot(PlayerPosition.TE, LineupSlot.FLEX)
        
        print("✓ Player eligibility logic test passed")
    
    def test_roster_slot_parsing(self):
        """Test parsing of league roster slot configuration."""
        mock_db = Mock()
        optimizer = LineupOptimizer(mock_db)
        
        # Test standard roster configuration
        roster_config = [
            {"position": "QB", "count": 1, "type": "starting"},
            {"position": "RB", "count": 2, "type": "starting"},
            {"position": "WR", "count": 2, "type": "starting"},
            {"position": "TE", "count": 1, "type": "starting"},
            {"position": "FLEX", "count": 1, "type": "starting"},
            {"position": "K", "count": 1, "type": "starting"},
            {"position": "DEF", "count": 1, "type": "starting"}
        ]
        
        slots = optimizer._parse_roster_slots(roster_config)
        
        # Verify all expected slots are present
        expected_slots = [
            LineupSlot.QB, LineupSlot.RB1, LineupSlot.RB2,
            LineupSlot.WR1, LineupSlot.WR2, LineupSlot.TE,
            LineupSlot.FLEX, LineupSlot.K, LineupSlot.DEF
        ]
        
        for slot in expected_slots:
            assert slot in slots
        
        print("✓ Roster slot parsing test passed")
    
    def test_adjustment_calculations(self):
        """Test various adjustment calculations."""
        mock_db = Mock()
        optimizer = LineupOptimizer(mock_db)
        
        # Test game script adjustments
        # QB should benefit from being behind
        qb_behind = optimizer._get_game_script_adjustment(PlayerPosition.QB, -7.0, 50.0)
        qb_ahead = optimizer._get_game_script_adjustment(PlayerPosition.QB, 7.0, 50.0)
        assert qb_behind > qb_ahead
        
        # RB should benefit from being ahead
        rb_behind = optimizer._get_game_script_adjustment(PlayerPosition.RB, -7.0, 50.0)
        rb_ahead = optimizer._get_game_script_adjustment(PlayerPosition.RB, 7.0, 50.0)
        assert rb_ahead > rb_behind
        
        # High total should benefit all positions
        high_total = optimizer._get_game_script_adjustment(PlayerPosition.QB, 0.0, 55.0)
        low_total = optimizer._get_game_script_adjustment(PlayerPosition.QB, 0.0, 35.0)
        assert high_total > low_total
        
        print("✓ Adjustment calculations test passed")
    
    def test_confidence_and_risk_calculations(self):
        """Test confidence and risk level calculations."""
        mock_db = Mock()
        optimizer = LineupOptimizer(mock_db)
        
        lineup = {LineupSlot.QB: "qb1", LineupSlot.RB1: "rb1"}
        
        # Test confidence calculation
        low_variance_data = {
            "qb1": {"variance": 1.0},
            "rb1": {"variance": 1.0}
        }
        high_confidence = optimizer._calculate_lineup_confidence(lineup, low_variance_data)
        
        high_variance_data = {
            "qb1": {"variance": 10.0},
            "rb1": {"variance": 10.0}
        }
        low_confidence = optimizer._calculate_lineup_confidence(lineup, high_variance_data)
        
        assert high_confidence > low_confidence
        assert 0 <= high_confidence <= 1
        assert 0 <= low_confidence <= 1
        
        # Test risk calculation
        low_risk_data = {
            "qb1": {"adjusted_projection": 20.0, "floor": 18.0},
            "rb1": {"adjusted_projection": 15.0, "floor": 13.0}
        }
        low_risk = optimizer._calculate_lineup_risk(lineup, low_risk_data)
        
        high_risk_data = {
            "qb1": {"adjusted_projection": 20.0, "floor": 10.0},
            "rb1": {"adjusted_projection": 15.0, "floor": 5.0}
        }
        high_risk = optimizer._calculate_lineup_risk(lineup, high_risk_data)
        
        assert high_risk > low_risk
        assert 0 <= low_risk <= 1
        assert 0 <= high_risk <= 1
        
        print("✓ Confidence and risk calculations test passed")
    
    def test_win_probability_simulation(self):
        """Test win probability simulation logic."""
        mock_db = Mock()
        optimizer = LineupOptimizer(mock_db)
        
        lineup = {LineupSlot.QB: "qb1", LineupSlot.RB1: "rb1"}
        players_data = {
            "qb1": {"adjusted_projection": 20.0, "variance": 4.0},
            "rb1": {"adjusted_projection": 15.0, "variance": 3.0}
        }
        
        # Test with different opponent projections
        win_prob_easy = optimizer._simulate_win_probability(
            lineup, players_data, 25.0, 100
        )
        win_prob_hard = optimizer._simulate_win_probability(
            lineup, players_data, 45.0, 100
        )
        
        # Should have higher win probability against weaker opponent
        assert win_prob_easy > win_prob_hard
        assert 0 <= win_prob_easy <= 1
        assert 0 <= win_prob_hard <= 1
        
        print("✓ Win probability simulation test passed")


if __name__ == "__main__":
    test = TestLineupIntegration()
    test.test_basic_lineup_optimization_workflow()
    test.test_player_eligibility_logic()
    test.test_roster_slot_parsing()
    test.test_adjustment_calculations()
    test.test_confidence_and_risk_calculations()
    test.test_win_probability_simulation()
    print("\n✅ All lineup optimization integration tests passed!")