"""
Tests for the rules API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from backend.app.main import app
from backend.app.core.database import get_db, Base
from backend.app.models.league import League
from backend.app.models.roster import <PERSON><PERSON>er, Franchise


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_rules.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


@pytest.fixture(scope="module")
def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def sample_league():
    """Create a sample league for testing."""
    db = TestingSessionLocal()
    
    league = League(
        id="test_league",
        name="Test League",
        season=2024,
        scoring_rules={
            "passing_yards": 0.04,
            "passing_touchdowns": 4.0,
            "rushing_yards": 0.1,
            "rushing_touchdowns": 6.0,
            "receiving_yards": 0.1,
            "receiving_touchdowns": 6.0,
            "receptions": 1.0,
        },
        roster_slots=[
            {"position": "QB", "count": 1, "type": "starting"},
            {"position": "RB", "count": 2, "type": "starting"},
            {"position": "WR", "count": 2, "type": "starting"},
            {"position": "TE", "count": 1, "type": "starting"},
            {"position": "FLEX", "count": 1, "type": "starting", "eligible_positions": ["RB", "WR", "TE"]},
            {"position": "K", "count": 1, "type": "starting"},
            {"position": "DEF", "count": 1, "type": "starting"},
            {"position": "BENCH", "count": 6, "type": "bench"},
        ],
        keeper_rules={
            "max_keepers": 3,
            "round_escalation": True,
        }
    )
    
    db.add(league)
    db.commit()
    db.refresh(league)
    
    yield league
    
    db.delete(league)
    db.commit()
    db.close()


class TestRulesValidation:
    """Test rules validation endpoints."""
    
    def test_validate_valid_rules(self, setup_database):
        """Test validation of valid league rules."""
        valid_rules = {
            "scoring_rules": {
                "passing_yards": 0.04,
                "passing_touchdowns": 4.0,
                "rushing_yards": 0.1,
                "rushing_touchdowns": 6.0,
                "receiving_yards": 0.1,
                "receiving_touchdowns": 6.0,
                "receptions": 1.0,
            },
            "roster_slots": [
                {"position": "QB", "count": 1, "type": "starting"},
                {"position": "RB", "count": 2, "type": "starting"},
                {"position": "WR", "count": 2, "type": "starting"},
                {"position": "TE", "count": 1, "type": "starting"},
                {"position": "BENCH", "count": 6, "type": "bench"},
            ]
        }
        
        response = client.post("/api/v1/rules/validate", json={"rules": valid_rules})
        assert response.status_code == 200
        
        data = response.json()
        assert data["is_valid"] is True
        assert len(data["errors"]) == 0
    
    def test_validate_invalid_rules(self, setup_database):
        """Test validation of invalid league rules."""
        invalid_rules = {
            "scoring_rules": {
                "passing_yards": 0.04,
                # Missing required fields
            },
            "roster_slots": []  # Empty roster slots
        }
        
        response = client.post("/api/v1/rules/validate", json={"rules": invalid_rules})
        assert response.status_code == 200
        
        data = response.json()
        assert data["is_valid"] is False
        assert len(data["errors"]) > 0


class TestScoreCalculation:
    """Test score calculation endpoints."""
    
    def test_calculate_player_score(self, setup_database):
        """Test player score calculation."""
        request_data = {
            "player_stats": {
                "passing_yards": 300,
                "passing_touchdowns": 2,
                "rushing_yards": 50,
                "receiving_yards": 80,
                "receptions": 8,
            },
            "scoring_rules": {
                "passing_yards": 0.04,
                "passing_touchdowns": 4.0,
                "rushing_yards": 0.1,
                "receiving_yards": 0.1,
                "receptions": 1.0,
            }
        }
        
        response = client.post("/api/v1/rules/calculate-score", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        expected_total = (300 * 0.04) + (2 * 4.0) + (50 * 0.1) + (80 * 0.1) + (8 * 1.0)
        assert data["total_points"] == expected_total
        assert "breakdown" in data
        assert data["breakdown"]["passing_yards"] == 12.0
        assert data["breakdown"]["passing_touchdowns"] == 8.0
    
    def test_calculate_score_with_unknown_stats(self, setup_database):
        """Test score calculation with unknown stats (should be ignored)."""
        request_data = {
            "player_stats": {
                "passing_yards": 250,
                "unknown_stat": 100,  # Should be ignored
            },
            "scoring_rules": {
                "passing_yards": 0.04,
                "passing_touchdowns": 4.0,
            }
        }
        
        response = client.post("/api/v1/rules/calculate-score", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["total_points"] == 250 * 0.04
        assert "unknown_stat" not in data["breakdown"]


class TestRosterValidation:
    """Test roster validation endpoints."""
    
    def test_validate_roster_league_not_found(self, setup_database):
        """Test roster validation with non-existent league."""
        request_data = {
            "roster_id": "test_roster",
            "league_id": "nonexistent_league"
        }
        
        response = client.post("/api/v1/rules/validate-roster", json=request_data)
        assert response.status_code == 404
        assert "League not found" in response.json()["detail"]
    
    def test_validate_roster_not_found(self, setup_database, sample_league):
        """Test roster validation with non-existent roster."""
        request_data = {
            "roster_id": "nonexistent_roster",
            "league_id": sample_league.id
        }
        
        response = client.post("/api/v1/rules/validate-roster", json=request_data)
        assert response.status_code == 404
        assert "Roster not found" in response.json()["detail"]


class TestSchemaAndExamples:
    """Test schema and example endpoints."""
    
    def test_get_league_rules_schema(self, setup_database):
        """Test getting league rules JSON schema."""
        response = client.get("/api/v1/rules/schema")
        assert response.status_code == 200
        
        schema = response.json()
        assert "properties" in schema
        assert "scoring_rules" in schema["properties"]
        assert "roster_slots" in schema["properties"]
    
    def test_get_roster_requirements_league_not_found(self, setup_database):
        """Test getting roster requirements for non-existent league."""
        response = client.get("/api/v1/rules/league/nonexistent/requirements")
        assert response.status_code == 404
        assert "League not found" in response.json()["detail"]
    
    def test_get_roster_requirements(self, setup_database, sample_league):
        """Test getting roster requirements for a league."""
        response = client.get(f"/api/v1/rules/league/{sample_league.id}/requirements")
        assert response.status_code == 200
        
        requirements = response.json()
        assert "QB" in requirements
        assert requirements["QB"]["starting"] == 1
        assert "RB" in requirements
        assert requirements["RB"]["starting"] == 2
    
    def test_get_scoring_examples(self, setup_database):
        """Test getting scoring system examples."""
        response = client.get("/api/v1/rules/examples/scoring")
        assert response.status_code == 200
        
        examples = response.json()
        assert "standard" in examples
        assert "ppr" in examples
        assert "half_ppr" in examples
        assert "superflex" in examples
        
        # Check that PPR has reception scoring
        assert examples["ppr"]["receptions"] == 1.0
        assert examples["standard"]["receptions"] == 0.0
        assert examples["half_ppr"]["receptions"] == 0.5
    
    def test_get_roster_examples(self, setup_database):
        """Test getting roster configuration examples."""
        response = client.get("/api/v1/rules/examples/roster")
        assert response.status_code == 200
        
        examples = response.json()
        assert "standard" in examples
        assert "superflex" in examples
        assert "2qb" in examples
        
        # Check standard roster has 1 QB
        standard_qb = [slot for slot in examples["standard"] if slot["position"] == "QB"]
        assert len(standard_qb) == 1
        assert standard_qb[0]["count"] == 1
        
        # Check 2QB roster has 2 QBs
        two_qb_qb = [slot for slot in examples["2qb"] if slot["position"] == "QB"]
        assert len(two_qb_qb) == 1
        assert two_qb_qb[0]["count"] == 2


class TestKeeperValidation:
    """Test keeper validation endpoints."""
    
    def test_validate_keeper_selection_league_not_found(self, setup_database):
        """Test keeper validation with non-existent league."""
        response = client.post(
            "/api/v1/rules/keeper/validate?league_id=nonexistent&keeper_player_ids=player1,player2"
        )
        assert response.status_code == 404
        assert "League not found" in response.json()["detail"]
    
    def test_validate_keeper_selection_placeholder(self, setup_database, sample_league):
        """Test keeper validation placeholder response."""
        response = client.post(
            f"/api/v1/rules/keeper/validate?league_id={sample_league.id}&keeper_player_ids=player1,player2"
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["is_valid"] is True
        assert "implementation pending" in data["message"]