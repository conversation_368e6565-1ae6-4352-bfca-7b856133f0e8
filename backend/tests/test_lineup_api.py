"""
Unit tests for lineup optimization API endpoints.
"""
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from fastapi import HTTPException

from app.main import app
from app.services.lineup_optimizer import LineupRecommendation, LineupSlot, PlayerNews
from app.models.roster import <PERSON>anchi<PERSON>
from app.models.recommendation import Recommendation


class TestLineupAPI:
    """Test cases for lineup optimization API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_lineup_recommendation(self):
        """Create mock lineup recommendation."""
        return LineupRecommendation(
            lineup={
                LineupSlot.QB: "qb1",
                LineupSlot.RB1: "rb1",
                LineupSlot.RB2: "rb2",
                LineupSlot.WR1: "wr1",
                LineupSlot.WR2: "wr2",
                LineupSlot.TE: "te1",
                LineupSlot.FLEX: "wr3",
                LineupSlot.K: "k1",
                LineupSlot.DEF: "def1"
            },
            projected_points=125.5,
            win_probability=0.65,
            confidence=0.82,
            rationale="Optimal lineup based on matchup analysis and player projections.",
            alternatives=[
                {
                    "lineup": {LineupSlot.FLEX: "rb3"},
                    "change": "Start rb3 instead of wr3 at FLEX",
                    "win_probability": 0.63,
                    "projected_points": 124.2
                }
            ],
            risk_level=0.35
        )
    
    @patch('app.api.lineup.get_db')
    @patch('app.api.lineup.LineupOptimizer')
    def test_optimize_lineup_success(self, mock_optimizer_class, mock_get_db, client, mock_lineup_recommendation):
        """Test successful lineup optimization."""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_optimizer = Mock()
        mock_optimizer.optimize_lineup.return_value = mock_lineup_recommendation
        mock_optimizer_class.return_value = mock_optimizer
        
        # Make request
        response = client.post("/api/v1/lineup/optimize", json={
            "franchise_id": "test_franchise",
            "week": 1,
            "season": 2024,
            "opponent_projection": 110.0
        })
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        assert "lineup" in data
        assert "projected_points" in data
        assert "win_probability" in data
        assert "confidence" in data
        assert "rationale" in data
        assert "alternatives" in data
        assert "risk_level" in data
        
        assert data["projected_points"] == 125.5
        assert data["win_probability"] == 0.65
        assert data["confidence"] == 0.82
        assert data["risk_level"] == 0.35
        
        # Verify optimizer was called correctly
        mock_optimizer.optimize_lineup.assert_called_once_with(
            franchise_id="test_franchise",
            week=1,
            season=2024,
            opponent_projection=110.0
        )
    
    @patch('app.api.lineup.get_db')
    @patch('app.api.lineup.LineupOptimizer')
    def test_optimize_lineup_invalid_franchise(self, mock_optimizer_class, mock_get_db, client):
        """Test lineup optimization with invalid franchise."""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_optimizer = Mock()
        mock_optimizer.optimize_lineup.side_effect = ValueError("Franchise test_franchise not found")
        mock_optimizer_class.return_value = mock_optimizer
        
        # Make request
        response = client.post("/api/v1/lineup/optimize", json={
            "franchise_id": "test_franchise",
            "week": 1
        })
        
        # Verify error response
        assert response.status_code == 400
        assert "Franchise test_franchise not found" in response.json()["detail"]
    
    def test_optimize_lineup_validation_error(self, client):
        """Test lineup optimization with validation errors."""
        # Test invalid week
        response = client.post("/api/v1/lineup/optimize", json={
            "franchise_id": "test_franchise",
            "week": 0  # Invalid week
        })
        
        assert response.status_code == 422  # Validation error
        
        # Test missing required fields
        response = client.post("/api/v1/lineup/optimize", json={
            "week": 1  # Missing franchise_id
        })
        
        assert response.status_code == 422
    
    @patch('app.api.lineup.get_db')
    @patch('app.api.lineup.LineupOptimizer')
    def test_start_sit_recommendations_success(self, mock_optimizer_class, mock_get_db, client):
        """Test successful start/sit recommendations."""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_recommendations = [
            {
                "type": "start_sit",
                "slot": "FLEX",
                "start_player_id": "wr3",
                "sit_player_id": "rb3",
                "projected_improvement": 2.3,
                "confidence": 0.75,
                "rationale": "WR3 has better matchup than RB3"
            }
        ]
        
        mock_optimizer = Mock()
        mock_optimizer.get_start_sit_recommendations.return_value = mock_recommendations
        mock_optimizer_class.return_value = mock_optimizer
        
        # Make request
        response = client.post("/api/v1/lineup/start-sit", json={
            "franchise_id": "test_franchise",
            "week": 1,
            "season": 2024
        })
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        assert len(data) == 1
        assert data[0]["type"] == "start_sit"
        assert data[0]["slot"] == "FLEX"
        assert data[0]["start_player_id"] == "wr3"
        assert data[0]["sit_player_id"] == "rb3"
        assert data[0]["projected_improvement"] == 2.3
        assert data[0]["confidence"] == 0.75
    
    @patch('app.api.lineup.get_db')
    @patch('app.api.lineup.LineupOptimizer')
    def test_comprehensive_recommendations_success(self, mock_optimizer_class, mock_get_db, client, mock_lineup_recommendation):
        """Test comprehensive lineup recommendations endpoint."""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_start_sit = [{"type": "start_sit", "slot": "FLEX"}]
        mock_alerts = [{"type": "lineup_lock", "player_id": "qb1"}]
        
        mock_optimizer = Mock()
        mock_optimizer.optimize_lineup.return_value = mock_lineup_recommendation
        mock_optimizer.get_start_sit_recommendations.return_value = mock_start_sit
        mock_optimizer.check_lineup_locks.return_value = mock_alerts
        mock_optimizer_class.return_value = mock_optimizer
        
        # Make request
        response = client.get("/api/v1/lineup/recommendations/test_franchise?week=1&season=2024")
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        assert "optimization" in data
        assert "start_sit_recommendations" in data
        assert "lock_alerts" in data
        assert "generated_at" in data
        
        # Verify optimization data
        opt_data = data["optimization"]
        assert "lineup" in opt_data
        assert "projected_points" in opt_data
        assert "win_probability" in opt_data
        assert opt_data["projected_points"] == 125.5
        
        # Verify other sections
        assert len(data["start_sit_recommendations"]) == 1
        assert len(data["lock_alerts"]) == 1