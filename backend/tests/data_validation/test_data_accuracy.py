"""
Data accuracy validation tests.
Tests to ensure data integrity, consistency, and accuracy across the system.
"""
import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.models.league import League
from app.models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RosterPlayer
from app.models.player import Player, PlayerPosition, InjuryStatus
from app.models.projection import Projection
from app.models.ranking import Ranking
from app.services.mfl_ingestion import MFLIngestionService
from app.services.projections_aggregator import ProjectionsAggregator
from backend.tests.utils.mock_data_generator import MockDataGenerator


class TestDataIntegrityValidation:
    """Tests for data integrity and consistency."""
    
    @pytest.fixture
    def mock_data_generator(self, db_session):
        """Create mock data generator."""
        return MockDataGenerator(db_session)
    
    def test_player_data_consistency(self, db_session, mock_data_generator):
        """Test player data consistency across tables."""
        # Create player with projections and rankings
        player = mock_data_generator.create_player(
            player_id="consistency_test_player",
            name="Test Player",
            position=PlayerPosition.RB,
            team="TEST"
        )
        
        # Create projections
        projection = mock_data_generator.create_projection(
            player_id=player.id,
            projected_points=250,
            week=None,
            season=2024
        )
        
        # Create ranking
        ranking = mock_data_generator.create_ranking(
            player_id=player.id,
            rank=15,
            position_rank=8,
            source="test_source"
        )
        
        # Verify consistency
        db_player = db_session.query(Player).filter(Player.id == player.id).first()
        db_projection = db_session.query(Projection).filter(Projection.player_id == player.id).first()
        db_ranking = db_session.query(Ranking).filter(Ranking.player_id == player.id).first()
        
        assert db_player is not None
        assert db_projection is not None
        assert db_ranking is not None
        
        # All should reference the same player
        assert db_projection.player_id == db_player.id
        assert db_ranking.player_id == db_player.id
        
        # Position should be consistent
        assert db_projection.player.position == db_player.position
        assert db_ranking.player.position == db_player.position
    
    def test_roster_data_integrity(self, db_session, mock_data_generator):
        """Test roster data integrity and constraints."""
        # Create league and franchise
        league = mock_data_generator.create_league(
            league_id="integrity_league",
            name="Integrity Test League"
        )
        
        franchise = mock_data_generator.create_franchise(
            franchise_id="integrity_franchise",
            league_id=league.id,
            name="Test Team"
        )
        
        # Create players
        players = []
        for i in range(5):
            player = mock_data_generator.create_player(
                player_id=f"integrity_player_{i}",
                name=f"Player {i}",
                position=PlayerPosition.RB
            )
            players.append(player)
            
            # Add to roster
            mock_data_generator.add_player_to_roster(
                franchise_id=franchise.id,
                player_id=player.id
            )
        
        # Verify roster integrity
        roster = db_session.query(Roster).filter(Roster.franchise_id == franchise.id).first()
        assert roster is not None
        assert len(roster.roster_players) == 5
        
        # All roster players should reference valid players
        for roster_player in roster.roster_players:
            assert roster_player.player is not None
            assert roster_player.player.id in [p.id for p in players]
            assert roster_player.roster_id == roster.id
        
        # No duplicate players on roster
        player_ids = [rp.player_id for rp in roster.roster_players]
        assert len(player_ids) == len(set(player_ids))
    
    def test_projection_data_validation(self, db_session, mock_data_generator):
        """Test projection data validation and constraints."""
        player = mock_data_generator.create_player(
            player_id="projection_test_player",
            name="Projection Test Player",
            position=PlayerPosition.QB
        )
        
        # Test valid projection
        valid_projection = mock_data_generator.create_projection(
            player_id=player.id,
            projected_points=300,
            floor=250,
            ceiling=350,
            variance=25,
            week=1,
            season=2024
        )
        
        db_projection = db_session.query(Projection).filter(Projection.id == valid_projection.id).first()
        assert db_projection is not None
        
        # Validate projection constraints
        assert db_projection.projected_points > 0
        assert db_projection.floor <= db_projection.projected_points
        assert db_projection.ceiling >= db_projection.projected_points
        assert db_projection.variance >= 0
        assert 1 <= db_projection.week <= 18 or db_projection.week is None
        assert db_projection.season == 2024
        
        # Test projection relationships
        assert db_projection.player is not None
        assert db_projection.player.id == player.id
    
    def test_league_configuration_validation(self, db_session, mock_data_generator):
        """Test league configuration validation."""
        # Create league with comprehensive configuration
        league = mock_data_generator.create_league(
            league_id="config_test_league",
            name="Configuration Test League",
            scoring_rules={
                "passing_yards": 0.04,
                "passing_tds": 4,
                "rushing_yards": 0.1,
                "rushing_tds": 6,
                "receiving_yards": 0.1,
                "receiving_tds": 6,
                "fumbles": -2,
                "interceptions": -2
            },
            roster_slots=[
                {"position": "QB", "count": 1},
                {"position": "RB", "count": 2},
                {"position": "WR", "count": 2},
                {"position": "TE", "count": 1},
                {"position": "FLEX", "count": 1},
                {"position": "K", "count": 1},
                {"position": "DEF", "count": 1}
            ],
            keeper_rules={
                "max_keepers": 3,
                "round_escalation": 1,
                "max_round": 16
            }
        )
        
        db_league = db_session.query(League).filter(League.id == league.id).first()
        assert db_league is not None
        
        # Validate scoring rules
        assert isinstance(db_league.scoring_rules, dict)
        assert "passing_yards" in db_league.scoring_rules
        assert db_league.scoring_rules["passing_tds"] == 4
        
        # Validate roster slots
        assert isinstance(db_league.roster_slots, list)
        assert len(db_league.roster_slots) > 0
        
        total_starting_slots = sum(slot["count"] for slot in db_league.roster_slots)
        assert total_starting_slots > 0
        
        # Validate keeper rules
        assert isinstance(db_league.keeper_rules, dict)
        assert db_league.keeper_rules["max_keepers"] > 0
        assert db_league.keeper_rules["round_escalation"] >= 0
    
    def test_data_timestamp_consistency(self, db_session, mock_data_generator):
        """Test timestamp consistency across related data."""
        current_time = datetime.utcnow()
        
        # Create data with timestamps
        player = mock_data_generator.create_player(
            player_id="timestamp_test_player",
            name="Timestamp Test Player",
            position=PlayerPosition.WR
        )
        
        projection = mock_data_generator.create_projection(
            player_id=player.id,
            projected_points=200,
            created_at=current_time
        )
        
        # Verify timestamps are reasonable
        db_projection = db_session.query(Projection).filter(Projection.id == projection.id).first()
        
        # Timestamps should be recent
        time_diff = datetime.utcnow() - db_projection.created_at
        assert time_diff.total_seconds() < 60  # Within last minute
        
        # Updated timestamp should be >= created timestamp
        if hasattr(db_projection, 'updated_at') and db_projection.updated_at:
            assert db_projection.updated_at >= db_projection.created_at
    
    def test_foreign_key_constraints(self, db_session, mock_data_generator):
        """Test foreign key constraints and referential integrity."""
        # Create valid references
        league = mock_data_generator.create_league(
            league_id="fk_test_league",
            name="FK Test League"
        )
        
        franchise = mock_data_generator.create_franchise(
            franchise_id="fk_test_franchise",
            league_id=league.id,
            name="FK Test Team"
        )
        
        player = mock_data_generator.create_player(
            player_id="fk_test_player",
            name="FK Test Player",
            position=PlayerPosition.RB
        )
        
        # Add player to roster
        roster_player = mock_data_generator.add_player_to_roster(
            franchise_id=franchise.id,
            player_id=player.id
        )
        
        # Verify all relationships exist
        db_roster_player = db_session.query(RosterPlayer).filter(RosterPlayer.id == roster_player.id).first()
        assert db_roster_player is not None
        assert db_roster_player.player is not None
        assert db_roster_player.roster is not None
        assert db_roster_player.roster.franchise is not None
        assert db_roster_player.roster.franchise.league is not None
        
        # Verify cascade relationships
        assert db_roster_player.player.id == player.id
        assert db_roster_player.roster.franchise.id == franchise.id
        assert db_roster_player.roster.franchise.league.id == league.id


class TestDataAccuracyValidation:
    """Tests for data accuracy and business logic validation."""
    
    @pytest.fixture
    def projections_aggregator(self, db_session):
        """Create projections aggregator for testing."""
        return ProjectionsAggregator(db_session)
    
    def test_projection_aggregation_accuracy(self, db_session, mock_data_generator, projections_aggregator):
        """Test accuracy of projection aggregation algorithms."""
        player = mock_data_generator.create_player(
            player_id="aggregation_test_player",
            name="Aggregation Test Player",
            position=PlayerPosition.QB
        )
        
        # Create multiple projections from different sources
        projections_data = [
            {"source": "source_a", "points": 300, "confidence": 0.9},
            {"source": "source_b", "points": 280, "confidence": 0.8},
            {"source": "source_c", "points": 320, "confidence": 0.7},
        ]
        
        for proj_data in projections_data:
            mock_data_generator.create_projection(
                player_id=player.id,
                projected_points=proj_data["points"],
                source=proj_data["source"],
                confidence=proj_data["confidence"],
                week=1,
                season=2024
            )
        
        # Test aggregation
        aggregated = projections_aggregator.aggregate_projections(player.id, 1, 2024)
        
        assert aggregated is not None
        assert aggregated.projected_points > 0
        
        # Aggregated projection should be within reasonable range of inputs
        min_projection = min(p["points"] for p in projections_data)
        max_projection = max(p["points"] for p in projections_data)
        
        assert min_projection <= float(aggregated.projected_points) <= max_projection
        
        # Confidence should be reasonable
        assert 0 <= aggregated.confidence_interval[0] <= aggregated.confidence_interval[1]
    
    def test_scoring_calculation_accuracy(self, db_session, mock_data_generator):
        """Test accuracy of scoring calculations."""
        # Create league with specific scoring rules
        league = mock_data_generator.create_league(
            league_id="scoring_test_league",
            scoring_rules={
                "passing_yards": 0.04,  # 1 point per 25 yards
                "passing_tds": 4,
                "rushing_yards": 0.1,   # 1 point per 10 yards
                "rushing_tds": 6,
                "fumbles": -2
            }
        )
        
        # Test scoring calculation
        stats = {
            "passing_yards": 300,    # 12 points
            "passing_tds": 2,        # 8 points
            "rushing_yards": 50,     # 5 points
            "rushing_tds": 1,        # 6 points
            "fumbles": 1             # -2 points
        }
        
        expected_score = (300 * 0.04) + (2 * 4) + (50 * 0.1) + (1 * 6) + (1 * -2)
        # = 12 + 8 + 5 + 6 - 2 = 29 points
        
        # This would test the actual scoring calculation service
        # For now, we'll verify the expected calculation
        assert expected_score == 29.0
    
    def test_keeper_value_calculation_accuracy(self, db_session, mock_data_generator):
        """Test accuracy of keeper value calculations."""
        league = mock_data_generator.create_league(
            league_id="keeper_value_league",
            keeper_rules={
                "round_escalation": 1,
                "max_round": 16
            }
        )
        
        franchise = mock_data_generator.create_franchise(
            franchise_id="keeper_value_franchise",
            league_id=league.id
        )
        
        player = mock_data_generator.create_player(
            player_id="keeper_value_player",
            name="Keeper Value Player",
            position=PlayerPosition.RB
        )
        
        # Add to roster with specific keeper cost
        original_cost = 8
        mock_data_generator.add_player_to_roster(
            franchise_id=franchise.id,
            player_id=player.id,
            keeper_cost=original_cost
        )
        
        # Create projection
        projected_points = 280
        mock_data_generator.create_projection(
            player_id=player.id,
            projected_points=projected_points
        )
        
        # Test keeper cost calculation with escalation
        expected_keeper_cost = original_cost - 1  # 1 round escalation
        assert expected_keeper_cost == 7
        
        # Test value over replacement calculation
        # This would use actual replacement level calculation
        replacement_level = 180  # Mock replacement level for RB
        expected_vor = projected_points - replacement_level
        assert expected_vor == 100
    
    def test_draft_value_calculation_accuracy(self, db_session, mock_data_generator):
        """Test accuracy of draft value calculations."""
        # Create players with known projections
        players_data = [
            {"name": "Elite QB", "position": PlayerPosition.QB, "points": 320},
            {"name": "Top RB", "position": PlayerPosition.RB, "points": 280},
            {"name": "WR1", "position": PlayerPosition.WR, "points": 250},
            {"name": "Solid TE", "position": PlayerPosition.TE, "points": 180},
        ]
        
        players = []
        for i, data in enumerate(players_data):
            player = mock_data_generator.create_player(
                player_id=f"draft_value_player_{i}",
                name=data["name"],
                position=data["position"]
            )
            
            mock_data_generator.create_projection(
                player_id=player.id,
                projected_points=data["points"]
            )
            
            players.append(player)
        
        # Test value over replacement calculations
        # These would use actual replacement level calculations
        replacement_levels = {
            PlayerPosition.QB: 250,
            PlayerPosition.RB: 180,
            PlayerPosition.WR: 160,
            PlayerPosition.TE: 120
        }
        
        for i, player in enumerate(players):
            projected_points = players_data[i]["points"]
            replacement_level = replacement_levels[player.position]
            expected_vor = projected_points - replacement_level
            
            # Verify VOR calculation
            if player.position == PlayerPosition.QB:
                assert expected_vor == 70  # 320 - 250
            elif player.position == PlayerPosition.RB:
                assert expected_vor == 100  # 280 - 180
            elif player.position == PlayerPosition.WR:
                assert expected_vor == 90   # 250 - 160
            elif player.position == PlayerPosition.TE:
                assert expected_vor == 60   # 180 - 120
    
    def test_lineup_optimization_accuracy(self, db_session, mock_data_generator):
        """Test accuracy of lineup optimization results."""
        league = mock_data_generator.create_league(
            league_id="lineup_opt_league",
            roster_slots=[
                {"position": "QB", "count": 1},
                {"position": "RB", "count": 2},
                {"position": "WR", "count": 2},
                {"position": "TE", "count": 1},
                {"position": "FLEX", "count": 1}
            ]
        )
        
        franchise = mock_data_generator.create_franchise(
            franchise_id="lineup_opt_franchise",
            league_id=league.id
        )
        
        # Create players with known projections
        players_projections = [
            ("QB1", PlayerPosition.QB, 25.0),
            ("RB1", PlayerPosition.RB, 20.0),
            ("RB2", PlayerPosition.RB, 18.0),
            ("RB3", PlayerPosition.RB, 15.0),  # Should be FLEX
            ("WR1", PlayerPosition.WR, 17.0),
            ("WR2", PlayerPosition.WR, 16.0),
            ("WR3", PlayerPosition.WR, 14.0),  # Should not start
            ("TE1", PlayerPosition.TE, 12.0),
        ]
        
        for name, position, points in players_projections:
            player = mock_data_generator.create_player(
                player_id=name.lower(),
                name=name,
                position=position
            )
            
            mock_data_generator.create_projection(
                player_id=player.id,
                projected_points=points,
                week=1
            )
            
            mock_data_generator.add_player_to_roster(
                franchise_id=franchise.id,
                player_id=player.id
            )
        
        # Expected optimal lineup based on projections:
        # QB: QB1 (25.0)
        # RB: RB1 (20.0), RB2 (18.0)
        # WR: WR1 (17.0), WR2 (16.0)
        # TE: TE1 (12.0)
        # FLEX: RB3 (15.0) - highest remaining
        # Total: 25 + 20 + 18 + 17 + 16 + 12 + 15 = 123.0
        
        expected_total = 123.0
        expected_lineup = {
            "QB": "qb1",
            "RB1": "rb1",
            "RB2": "rb2",
            "WR1": "wr1",
            "WR2": "wr2",
            "TE": "te1",
            "FLEX": "rb3"
        }
        
        # This would test actual lineup optimization
        # For now, verify the expected calculation
        assert expected_total == 123.0
    
    def test_trade_fairness_calculation_accuracy(self, db_session, mock_data_generator):
        """Test accuracy of trade fairness calculations."""
        league = mock_data_generator.create_league(
            league_id="trade_fairness_league"
        )
        
        # Create two franchises
        franchise_a = mock_data_generator.create_franchise(
            franchise_id="franchise_a",
            league_id=league.id,
            name="Team A"
        )
        
        franchise_b = mock_data_generator.create_franchise(
            franchise_id="franchise_b",
            league_id=league.id,
            name="Team B"
        )
        
        # Create players for trade
        # Team A gives: RB (20 points) + WR (15 points) = 35 points
        # Team B gives: WR (18 points) + RB (16 points) = 34 points
        # Should be approximately fair (within 1 point)
        
        players_data = [
            ("team_a_rb", PlayerPosition.RB, 20.0, franchise_a.id),
            ("team_a_wr", PlayerPosition.WR, 15.0, franchise_a.id),
            ("team_b_wr", PlayerPosition.WR, 18.0, franchise_b.id),
            ("team_b_rb", PlayerPosition.RB, 16.0, franchise_b.id),
        ]
        
        for player_id, position, points, franchise_id in players_data:
            player = mock_data_generator.create_player(
                player_id=player_id,
                name=player_id.replace("_", " ").title(),
                position=position
            )
            
            mock_data_generator.create_projection(
                player_id=player.id,
                projected_points=points
            )
            
            mock_data_generator.add_player_to_roster(
                franchise_id=franchise_id,
                player_id=player.id
            )
        
        # Calculate trade values
        team_a_value = 20.0 + 15.0  # 35.0
        team_b_value = 18.0 + 16.0  # 34.0
        
        fairness_ratio = min(team_a_value, team_b_value) / max(team_a_value, team_b_value)
        expected_fairness = fairness_ratio  # 34/35 = 0.971
        
        # Trade should be considered fair (> 0.9)
        assert expected_fairness > 0.9
        assert abs(team_a_value - team_b_value) <= 1.0  # Within 1 point


class TestDataConsistencyValidation:
    """Tests for data consistency across different operations."""
    
    def test_projection_ranking_consistency(self, db_session, mock_data_generator):
        """Test consistency between projections and rankings."""
        # Create players with projections and rankings
        players_data = [
            ("player_1", 300, 1),   # Highest projection, rank 1
            ("player_2", 280, 2),   # Second highest, rank 2
            ("player_3", 260, 3),   # Third highest, rank 3
        ]
        
        for player_id, projection, rank in players_data:
            player = mock_data_generator.create_player(
                player_id=player_id,
                name=f"Player {player_id[-1]}",
                position=PlayerPosition.RB
            )
            
            mock_data_generator.create_projection(
                player_id=player.id,
                projected_points=projection
            )
            
            mock_data_generator.create_ranking(
                player_id=player.id,
                rank=rank,
                position_rank=rank,
                source="test_source"
            )
        
        # Verify consistency: higher projections should have better (lower) ranks
        projections = db_session.query(Projection).join(Player).filter(
            Player.position == PlayerPosition.RB
        ).order_by(Projection.projected_points.desc()).all()
        
        rankings = db_session.query(Ranking).join(Player).filter(
            Player.position == PlayerPosition.RB
        ).order_by(Ranking.rank.asc()).all()
        
        # Order should be consistent
        for i in range(len(projections)):
            proj_player_id = projections[i].player_id
            rank_player_id = rankings[i].player_id
            assert proj_player_id == rank_player_id
    
    def test_roster_league_consistency(self, db_session, mock_data_generator):
        """Test consistency between roster composition and league rules."""
        # Create league with specific roster requirements
        league = mock_data_generator.create_league(
            league_id="roster_consistency_league",
            roster_slots=[
                {"position": "QB", "count": 1},
                {"position": "RB", "count": 2},
                {"position": "WR", "count": 3},
                {"position": "TE", "count": 1},
                {"position": "K", "count": 1},
                {"position": "DEF", "count": 1}
            ]
        )
        
        franchise = mock_data_generator.create_franchise(
            franchise_id="roster_consistency_franchise",
            league_id=league.id
        )
        
        # Create roster that matches league requirements
        roster_composition = [
            (PlayerPosition.QB, 1),
            (PlayerPosition.RB, 2),
            (PlayerPosition.WR, 3),
            (PlayerPosition.TE, 1),
            (PlayerPosition.K, 1),
            (PlayerPosition.DEF, 1)
        ]
        
        player_count = 0
        for position, count in roster_composition:
            for i in range(count):
                player = mock_data_generator.create_player(
                    player_id=f"consistency_player_{player_count}",
                    name=f"Player {player_count}",
                    position=position
                )
                
                mock_data_generator.add_player_to_roster(
                    franchise_id=franchise.id,
                    player_id=player.id
                )
                
                player_count += 1
        
        # Verify roster matches league requirements
        roster = db_session.query(Roster).filter(Roster.franchise_id == franchise.id).first()
        
        # Count players by position
        position_counts = {}
        for roster_player in roster.roster_players:
            position = roster_player.player.position
            position_counts[position] = position_counts.get(position, 0) + 1
        
        # Verify counts match league requirements
        for slot in league.roster_slots:
            position = PlayerPosition(slot["position"])
            required_count = slot["count"]
            actual_count = position_counts.get(position, 0)
            
            # For this test, we expect exact matches
            assert actual_count >= required_count, f"Not enough {position.value} players: {actual_count} < {required_count}"
    
    def test_temporal_data_consistency(self, db_session, mock_data_generator):
        """Test temporal consistency of data updates."""
        player = mock_data_generator.create_player(
            player_id="temporal_test_player",
            name="Temporal Test Player",
            position=PlayerPosition.QB
        )
        
        # Create initial projection
        initial_time = datetime.utcnow() - timedelta(hours=1)
        initial_projection = mock_data_generator.create_projection(
            player_id=player.id,
            projected_points=300,
            created_at=initial_time,
            week=1,
            season=2024
        )
        
        # Create updated projection
        updated_time = datetime.utcnow()
        updated_projection = mock_data_generator.create_projection(
            player_id=player.id,
            projected_points=320,
            created_at=updated_time,
            week=1,
            season=2024,
            source="updated_source"
        )
        
        # Verify temporal ordering
        projections = db_session.query(Projection).filter(
            Projection.player_id == player.id,
            Projection.week == 1,
            Projection.season == 2024
        ).order_by(Projection.created_at.asc()).all()
        
        assert len(projections) == 2
        assert projections[0].created_at < projections[1].created_at
        assert projections[0].projected_points == Decimal('300')
        assert projections[1].projected_points == Decimal('320')
    
    def test_cross_service_data_consistency(self, db_session, mock_data_generator):
        """Test data consistency across different services."""
        # Create comprehensive test data
        league = mock_data_generator.create_league(
            league_id="cross_service_league",
            keeper_rules={"max_keepers": 2}
        )
        
        franchise = mock_data_generator.create_franchise(
            franchise_id="cross_service_franchise",
            league_id=league.id
        )
        
        player = mock_data_generator.create_player(
            player_id="cross_service_player",
            name="Cross Service Player",
            position=PlayerPosition.RB
        )
        
        # Add to roster
        mock_data_generator.add_player_to_roster(
            franchise_id=franchise.id,
            player_id=player.id,
            keeper_cost=8
        )
        
        # Create projection
        projection = mock_data_generator.create_projection(
            player_id=player.id,
            projected_points=280
        )
        
        # Create ranking
        ranking = mock_data_generator.create_ranking(
            player_id=player.id,
            rank=15,
            position_rank=8
        )
        
        # Verify all services would see consistent data
        # This would test actual service integration
        
        # Keeper service should see the player with correct cost
        roster_player = db_session.query(RosterPlayer).filter(
            RosterPlayer.player_id == player.id
        ).first()
        assert roster_player.keeper_cost == 8
        
        # Projection service should see the projection
        db_projection = db_session.query(Projection).filter(
            Projection.player_id == player.id
        ).first()
        assert db_projection.projected_points == Decimal('280')
        
        # Ranking service should see the ranking
        db_ranking = db_session.query(Ranking).filter(
            Ranking.player_id == player.id
        ).first()
        assert db_ranking.rank == 15
        
        # All should reference the same player
        assert roster_player.player_id == player.id
        assert db_projection.player_id == player.id
        assert db_ranking.player_id == player.id