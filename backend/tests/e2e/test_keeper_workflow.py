"""
End-to-end tests for keeper optimization workflow.
Tests complete user journey from data ingestion to recommendation display.
"""
import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.league import League
from app.models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RosterPlayer
from app.models.player import Player, PlayerPosition, InjuryStatus
from app.models.projection import Projection
from backend.tests.utils.mock_data_generator import MockDataGenerator


class TestKeeperWorkflowE2E:
    """End-to-end tests for keeper optimization workflow."""
    
    @pytest.fixture
    def client(self, db_session):
        """Create test client with database session."""
        from app.core.database import get_db
        
        def override_get_db():
            try:
                yield db_session
            finally:
                pass
        
        app.dependency_overrides[get_db] = override_get_db
        
        with TestClient(app) as test_client:
            yield test_client
        
        app.dependency_overrides.clear()
    
    @pytest.fixture
    def mock_data_generator(self, db_session):
        """Create mock data generator."""
        return MockDataGenerator(db_session)
    
    @pytest.fixture
    def complete_league_setup(self, mock_data_generator):
        """Create complete league setup with all necessary data."""
        # Create league with keeper rules
        league = mock_data_generator.create_league(
            league_id="test_league_e2e",
            name="E2E Test League",
            keeper_rules={
                "max_keepers": 3,
                "round_escalation": 1,
                "max_round": 16,
                "position_limits": {"QB": 1, "RB": 2, "WR": 2, "TE": 1}
            }
        )
        
        # Create franchise
        franchise = mock_data_generator.create_franchise(
            franchise_id="test_franchise_e2e",
            league_id=league.id,
            name="E2E Test Team"
        )
        
        # Create players with various characteristics
        players = []
        player_configs = [
            {"name": "Elite QB", "position": PlayerPosition.QB, "projected_points": 320},
            {"name": "Top RB", "position": PlayerPosition.RB, "projected_points": 280},
            {"name": "WR1", "position": PlayerPosition.WR, "projected_points": 250},
            {"name": "Injured RB", "position": PlayerPosition.RB, "projected_points": 200, "injury_status": InjuryStatus.OUT},
            {"name": "Bench WR", "position": PlayerPosition.WR, "projected_points": 180},
        ]
        
        for i, config in enumerate(player_configs):
            player = mock_data_generator.create_player(
                player_id=f"player_e2e_{i+1}",
                name=config["name"],
                position=config["position"],
                injury_status=config.get("injury_status", InjuryStatus.HEALTHY)
            )
            players.append(player)
            
            # Create projections
            mock_data_generator.create_projection(
                player_id=player.id,
                projected_points=config["projected_points"],
                week=None,  # Season-long
                season=2024
            )
            
            # Add to roster
            mock_data_generator.add_player_to_roster(
                franchise_id=franchise.id,
                player_id=player.id,
                keeper_cost=5 + i  # Varying keeper costs
            )
        
        return {
            "league": league,
            "franchise": franchise,
            "players": players
        }
    
    def test_complete_keeper_optimization_workflow(self, client, complete_league_setup):
        """Test complete keeper optimization workflow from API perspective."""
        setup = complete_league_setup
        franchise_id = setup["franchise"].id
        
        # Step 1: Get keeper candidates
        response = client.get(f"/api/keepers/{franchise_id}/candidates")
        assert response.status_code == 200
        
        candidates_data = response.json()
        assert "candidates" in candidates_data
        assert len(candidates_data["candidates"]) > 0
        
        # Verify candidate structure
        candidate = candidates_data["candidates"][0]
        required_fields = [
            "player_id", "player_name", "position", "projected_points",
            "value_over_replacement", "keeper_cost", "is_eligible"
        ]
        for field in required_fields:
            assert field in candidate
        
        # Step 2: Get keeper recommendations
        response = client.get(f"/api/keepers/{franchise_id}/recommendations")
        assert response.status_code == 200
        
        recommendations_data = response.json()
        assert "scenarios" in recommendations_data
        assert len(recommendations_data["scenarios"]) > 0
        
        # Verify recommendation structure
        scenario = recommendations_data["scenarios"][0]
        assert "scenario_name" in scenario
        assert "selected_keepers" in scenario
        assert "total_value" in scenario
        assert "constraints_satisfied" in scenario
        
        # Step 3: Get detailed explanation for top recommendation
        if scenario["selected_keepers"]:
            keeper_id = scenario["selected_keepers"][0]["player_id"]
            response = client.post(
                f"/api/recommendations/explain",
                json={
                    "recommendation_type": "keeper",
                    "player_id": keeper_id,
                    "franchise_id": franchise_id,
                    "include_alternatives": True
                }
            )
            assert response.status_code == 200
            
            explanation_data = response.json()
            assert "explanation" in explanation_data
            assert "key_factors" in explanation_data
            assert "confidence_breakdown" in explanation_data
    
    def test_keeper_workflow_with_constraints_violation(self, client, mock_data_generator, db_session):
        """Test keeper workflow when constraints are violated."""
        # Create league with strict constraints
        league = mock_data_generator.create_league(
            league_id="strict_league",
            keeper_rules={
                "max_keepers": 1,  # Very restrictive
                "no_injured_keepers": True,
                "max_keeper_cost": 3
            }
        )
        
        franchise = mock_data_generator.create_franchise(
            franchise_id="strict_franchise",
            league_id=league.id
        )
        
        # Create players that violate constraints
        expensive_player = mock_data_generator.create_player(
            player_id="expensive_player",
            name="Expensive Player",
            position=PlayerPosition.QB
        )
        
        injured_player = mock_data_generator.create_player(
            player_id="injured_player",
            name="Injured Player",
            position=PlayerPosition.RB,
            injury_status=InjuryStatus.OUT
        )
        
        # Add to roster with high keeper costs
        mock_data_generator.add_player_to_roster(
            franchise_id=franchise.id,
            player_id=expensive_player.id,
            keeper_cost=1  # Will escalate to round 0, violating max_keeper_cost
        )
        
        mock_data_generator.add_player_to_roster(
            franchise_id=franchise.id,
            player_id=injured_player.id,
            keeper_cost=5
        )
        
        # Create projections
        mock_data_generator.create_projection(
            player_id=expensive_player.id,
            projected_points=300
        )
        mock_data_generator.create_projection(
            player_id=injured_player.id,
            projected_points=250
        )
        
        # Test API response handles constraints properly
        response = client.get(f"/api/keepers/{franchise.id}/candidates")
        assert response.status_code == 200
        
        candidates_data = response.json()
        
        # Should have candidates but with eligibility issues
        assert len(candidates_data["candidates"]) == 2
        
        # Check that constraint violations are reported
        for candidate in candidates_data["candidates"]:
            if candidate["player_id"] == injured_player.id:
                assert candidate["is_eligible"] == False
                assert len(candidate["constraints_violated"]) > 0
    
    def test_keeper_workflow_performance_with_large_roster(self, client, mock_data_generator):
        """Test keeper workflow performance with large roster."""
        import time
        
        # Create league
        league = mock_data_generator.create_league(
            league_id="large_league",
            keeper_rules={"max_keepers": 5}
        )
        
        franchise = mock_data_generator.create_franchise(
            franchise_id="large_franchise",
            league_id=league.id
        )
        
        # Create large roster (30 players)
        positions = [PlayerPosition.QB, PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE]
        
        for i in range(30):
            player = mock_data_generator.create_player(
                player_id=f"large_player_{i}",
                name=f"Player {i}",
                position=positions[i % len(positions)]
            )
            
            mock_data_generator.create_projection(
                player_id=player.id,
                projected_points=200 + i * 5  # Varying projections
            )
            
            mock_data_generator.add_player_to_roster(
                franchise_id=franchise.id,
                player_id=player.id,
                keeper_cost=5 + (i % 10)
            )
        
        # Test performance
        start_time = time.time()
        
        response = client.get(f"/api/keepers/{franchise.id}/recommendations")
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should complete within reasonable time (5 seconds for 30 players)
        assert processing_time < 5.0
        assert response.status_code == 200
        
        recommendations_data = response.json()
        assert len(recommendations_data["scenarios"]) > 0
    
    def test_keeper_workflow_data_consistency(self, client, complete_league_setup):
        """Test data consistency throughout keeper workflow."""
        setup = complete_league_setup
        franchise_id = setup["franchise"].id
        
        # Get candidates
        candidates_response = client.get(f"/api/keepers/{franchise_id}/candidates")
        candidates_data = candidates_response.json()
        
        # Get recommendations
        recommendations_response = client.get(f"/api/keepers/{franchise_id}/recommendations")
        recommendations_data = recommendations_response.json()
        
        # Verify data consistency
        candidate_ids = {c["player_id"] for c in candidates_data["candidates"]}
        
        for scenario in recommendations_data["scenarios"]:
            for keeper in scenario["selected_keepers"]:
                # All recommended keepers should be in candidates list
                assert keeper["player_id"] in candidate_ids
                
                # Find corresponding candidate
                candidate = next(
                    c for c in candidates_data["candidates"] 
                    if c["player_id"] == keeper["player_id"]
                )
                
                # Verify consistent data
                assert keeper["player_name"] == candidate["player_name"]
                assert keeper["position"] == candidate["position"]
                assert keeper["keeper_cost"] == candidate["keeper_cost"]
    
    def test_keeper_workflow_error_handling(self, client):
        """Test error handling in keeper workflow."""
        # Test non-existent franchise
        response = client.get("/api/keepers/nonexistent_franchise/candidates")
        assert response.status_code == 404
        
        error_data = response.json()
        assert "detail" in error_data
        assert "not found" in error_data["detail"].lower()
        
        # Test invalid franchise ID format
        response = client.get("/api/keepers/invalid-id-format/recommendations")
        assert response.status_code in [400, 404]  # Depending on validation
    
    def test_keeper_workflow_with_missing_projections(self, client, mock_data_generator):
        """Test keeper workflow when some players lack projections."""
        # Create basic setup
        league = mock_data_generator.create_league(
            league_id="missing_proj_league",
            keeper_rules={"max_keepers": 2}
        )
        
        franchise = mock_data_generator.create_franchise(
            franchise_id="missing_proj_franchise",
            league_id=league.id
        )
        
        # Create players - some with projections, some without
        player_with_proj = mock_data_generator.create_player(
            player_id="player_with_proj",
            name="Player With Projection",
            position=PlayerPosition.RB
        )
        
        player_without_proj = mock_data_generator.create_player(
            player_id="player_without_proj",
            name="Player Without Projection",
            position=PlayerPosition.WR
        )
        
        # Only create projection for one player
        mock_data_generator.create_projection(
            player_id=player_with_proj.id,
            projected_points=250
        )
        
        # Add both to roster
        mock_data_generator.add_player_to_roster(
            franchise_id=franchise.id,
            player_id=player_with_proj.id,
            keeper_cost=8
        )
        
        mock_data_generator.add_player_to_roster(
            franchise_id=franchise.id,
            player_id=player_without_proj.id,
            keeper_cost=6
        )
        
        # Test that API handles missing projections gracefully
        response = client.get(f"/api/keepers/{franchise.id}/candidates")
        assert response.status_code == 200
        
        candidates_data = response.json()
        
        # Should still return candidates, but handle missing projections
        assert len(candidates_data["candidates"]) >= 1
        
        # Player with projection should be included
        player_ids = {c["player_id"] for c in candidates_data["candidates"]}
        assert player_with_proj.id in player_ids
        
        # Player without projection might be excluded or have default values
        # This depends on implementation - test should verify graceful handling
    
    def test_keeper_deadline_integration(self, client, complete_league_setup):
        """Test keeper workflow integration with deadline management."""
        setup = complete_league_setup
        franchise_id = setup["franchise"].id
        
        # Test getting keeper deadline info
        response = client.get(f"/api/keepers/{franchise_id}/deadline")
        
        # Should return deadline information (even if mocked)
        if response.status_code == 200:
            deadline_data = response.json()
            expected_fields = ["deadline", "time_remaining", "is_past_deadline"]
            
            for field in expected_fields:
                assert field in deadline_data
        else:
            # If endpoint doesn't exist yet, that's acceptable for this test
            assert response.status_code in [404, 501]
    
    def test_keeper_workflow_caching(self, client, complete_league_setup):
        """Test that keeper workflow properly utilizes caching."""
        setup = complete_league_setup
        franchise_id = setup["franchise"].id
        
        import time
        
        # First request - should populate cache
        start_time = time.time()
        response1 = client.get(f"/api/keepers/{franchise_id}/recommendations")
        first_request_time = time.time() - start_time
        
        assert response1.status_code == 200
        
        # Second request - should use cache and be faster
        start_time = time.time()
        response2 = client.get(f"/api/keepers/{franchise_id}/recommendations")
        second_request_time = time.time() - start_time
        
        assert response2.status_code == 200
        
        # Responses should be identical
        assert response1.json() == response2.json()
        
        # Second request should be faster (though this might be flaky in CI)
        # We'll just verify both completed successfully
        assert first_request_time > 0
        assert second_request_time > 0