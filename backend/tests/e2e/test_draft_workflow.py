"""
End-to-end tests for draft assistance workflow.
Tests complete user journey from draft setup to real-time recommendations.
"""
import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.league import League
from app.models.roster import Franchise
from app.models.player import Player, PlayerPosition
from backend.tests.utils.mock_data_generator import MockDataGenerator


class TestDraftWorkflowE2E:
    """End-to-end tests for draft assistance workflow."""
    
    @pytest.fixture
    def client(self, db_session):
        """Create test client with database session."""
        from app.core.database import get_db
        
        def override_get_db():
            try:
                yield db_session
            finally:
                pass
        
        app.dependency_overrides[get_db] = override_get_db
        
        with TestClient(app) as test_client:
            yield test_client
        
        app.dependency_overrides.clear()
    
    @pytest.fixture
    def mock_data_generator(self, db_session):
        """Create mock data generator."""
        return MockDataGenerator(db_session)
    
    @pytest.fixture
    def draft_league_setup(self, mock_data_generator):
        """Create league setup for draft testing."""
        # Create league
        league = mock_data_generator.create_league(
            league_id="draft_league",
            name="Draft Test League",
            draft_settings={
                "draft_type": "snake",
                "teams": 12,
                "rounds": 16,
                "pick_time_seconds": 90
            }
        )
        
        # Create multiple franchises for draft simulation
        franchises = []
        for i in range(12):
            franchise = mock_data_generator.create_franchise(
                franchise_id=f"draft_franchise_{i+1}",
                league_id=league.id,
                name=f"Team {i+1}",
                draft_position=i+1
            )
            franchises.append(franchise)
        
        # Create player pool with various tiers
        players = []
        positions = [PlayerPosition.QB, PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE, PlayerPosition.K, PlayerPosition.DEF]
        
        for i in range(200):  # Large player pool
            position = positions[i % len(positions)]
            player = mock_data_generator.create_player(
                player_id=f"draft_player_{i+1}",
                name=f"Player {i+1}",
                position=position,
                team=f"TEAM{(i % 32) + 1}"
            )
            
            # Create tiered projections
            if i < 20:  # Tier 1
                projected_points = 300 - i * 5
            elif i < 60:  # Tier 2
                projected_points = 250 - (i - 20) * 3
            elif i < 120:  # Tier 3
                projected_points = 200 - (i - 60) * 2
            else:  # Tier 4+
                projected_points = 150 - (i - 120) * 1
            
            mock_data_generator.create_projection(
                player_id=player.id,
                projected_points=projected_points,
                week=None,
                season=2024
            )
            
            players.append(player)
        
        return {
            "league": league,
            "franchises": franchises,
            "players": players,
            "user_franchise": franchises[0]  # User is team 1
        }
    
    def test_complete_draft_workflow(self, client, draft_league_setup):
        """Test complete draft workflow from start to finish."""
        setup = draft_league_setup
        league_id = setup["league"].id
        user_franchise_id = setup["user_franchise"].id
        
        # Step 1: Initialize draft
        response = client.post(f"/api/draft/{league_id}/initialize")
        assert response.status_code == 200
        
        draft_data = response.json()
        assert "draft_id" in draft_data
        assert "current_pick" in draft_data
        assert draft_data["current_pick"]["pick_number"] == 1
        
        draft_id = draft_data["draft_id"]
        
        # Step 2: Get initial draft board
        response = client.get(f"/api/draft/{draft_id}/board")
        assert response.status_code == 200
        
        board_data = response.json()
        assert "available_players" in board_data
        assert "tiers" in board_data
        assert len(board_data["available_players"]) > 0
        
        # Verify tier structure
        assert len(board_data["tiers"]) > 0
        tier = board_data["tiers"][0]
        assert "tier_number" in tier
        assert "players" in tier
        assert "tier_break_confidence" in tier
        
        # Step 3: Get draft recommendation for user's first pick
        response = client.get(f"/api/draft/{draft_id}/recommendation/{user_franchise_id}")
        assert response.status_code == 200
        
        rec_data = response.json()
        assert "recommended_player" in rec_data
        assert "rationale" in rec_data
        assert "alternatives" in rec_data
        assert "confidence" in rec_data
        
        recommended_player = rec_data["recommended_player"]
        assert "player_id" in recommended_player
        assert "player_name" in recommended_player
        assert "position" in recommended_player
        assert "tier" in recommended_player
        
        # Step 4: Simulate draft picks
        picks_made = []
        
        # Make first pick (user's pick)
        response = client.post(
            f"/api/draft/{draft_id}/pick",
            json={
                "franchise_id": user_franchise_id,
                "player_id": recommended_player["player_id"]
            }
        )
        assert response.status_code == 200
        
        pick_data = response.json()
        assert "pick_number" in pick_data
        assert "player_id" in pick_data
        assert pick_data["player_id"] == recommended_player["player_id"]
        
        picks_made.append(pick_data)
        
        # Simulate several more picks by other teams
        for pick_num in range(2, 13):  # Complete first round
            # Get current pick info
            response = client.get(f"/api/draft/{draft_id}/current-pick")
            assert response.status_code == 200
            
            current_pick = response.json()
            current_franchise_id = current_pick["franchise_id"]
            
            # Get available players
            response = client.get(f"/api/draft/{draft_id}/board")
            board_data = response.json()
            
            # Pick highest available player (simulate other teams)
            if board_data["available_players"]:
                player_to_pick = board_data["available_players"][0]["player_id"]
                
                response = client.post(
                    f"/api/draft/{draft_id}/pick",
                    json={
                        "franchise_id": current_franchise_id,
                        "player_id": player_to_pick
                    }
                )
                assert response.status_code == 200
                picks_made.append(response.json())
        
        # Step 5: Get updated recommendation for user's second pick
        response = client.get(f"/api/draft/{draft_id}/recommendation/{user_franchise_id}")
        assert response.status_code == 200
        
        second_rec = response.json()
        
        # Recommendation should be different and account for first pick
        assert second_rec["recommended_player"]["player_id"] != recommended_player["player_id"]
        
        # Step 6: Test draft simulation
        response = client.post(
            f"/api/draft/{draft_id}/simulate",
            json={
                "franchise_id": user_franchise_id,
                "scenarios": 3,
                "picks_ahead": 5
            }
        )
        assert response.status_code == 200
        
        simulation_data = response.json()
        assert "scenarios" in simulation_data
        assert len(simulation_data["scenarios"]) <= 3
        
        for scenario in simulation_data["scenarios"]:
            assert "scenario_name" in scenario
            assert "projected_picks" in scenario
            assert "team_strength" in scenario
    
    def test_draft_real_time_updates(self, client, draft_league_setup):
        """Test real-time draft updates and recommendation changes."""
        setup = draft_league_setup
        league_id = setup["league"].id
        user_franchise_id = setup["user_franchise"].id
        
        # Initialize draft
        response = client.post(f"/api/draft/{league_id}/initialize")
        draft_id = response.json()["draft_id"]
        
        # Get initial recommendation
        response = client.get(f"/api/draft/{draft_id}/recommendation/{user_franchise_id}")
        initial_rec = response.json()
        
        # Get board state
        response = client.get(f"/api/draft/{draft_id}/board")
        initial_board = response.json()
        
        # Simulate pick by another team (take top player)
        top_player = initial_board["available_players"][0]
        
        # Get current pick (should be pick 1, user's team)
        response = client.get(f"/api/draft/{draft_id}/current-pick")
        current_pick = response.json()
        
        # Skip user's pick and make pick for team 2
        response = client.post(
            f"/api/draft/{draft_id}/pick",
            json={
                "franchise_id": setup["franchises"][1].id,  # Team 2
                "player_id": top_player["player_id"],
                "pick_number": 2  # Force pick number for testing
            }
        )
        
        if response.status_code == 200:
            # Get updated board
            response = client.get(f"/api/draft/{draft_id}/board")
            updated_board = response.json()
            
            # Top player should be removed
            updated_player_ids = {p["player_id"] for p in updated_board["available_players"]}
            assert top_player["player_id"] not in updated_player_ids
            
            # Get updated recommendation
            response = client.get(f"/api/draft/{draft_id}/recommendation/{user_franchise_id}")
            updated_rec = response.json()
            
            # Recommendation might change based on available players
            # At minimum, it should still be valid
            assert "recommended_player" in updated_rec
            assert updated_rec["recommended_player"]["player_id"] in updated_player_ids
    
    def test_draft_position_needs_analysis(self, client, draft_league_setup):
        """Test draft recommendations based on positional needs."""
        setup = draft_league_setup
        league_id = setup["league"].id
        user_franchise_id = setup["user_franchise"].id
        
        # Initialize draft
        response = client.post(f"/api/draft/{league_id}/initialize")
        draft_id = response.json()["draft_id"]
        
        # Simulate several picks to create positional imbalance
        # Make user pick all RBs to test positional need detection
        
        response = client.get(f"/api/draft/{draft_id}/board")
        board_data = response.json()
        
        # Find top RB
        top_rb = None
        for player in board_data["available_players"]:
            if player["position"] == "RB":
                top_rb = player
                break
        
        if top_rb:
            # Make pick
            response = client.post(
                f"/api/draft/{draft_id}/pick",
                json={
                    "franchise_id": user_franchise_id,
                    "player_id": top_rb["player_id"]
                }
            )
            
            # Skip to user's next pick (simulate other picks)
            for _ in range(23):  # Skip to pick 25 (user's 3rd pick in 12-team snake)
                response = client.get(f"/api/draft/{draft_id}/current-pick")
                if response.status_code == 200:
                    current_pick = response.json()
                    if current_pick["franchise_id"] != user_franchise_id:
                        # Make dummy pick for other team
                        response = client.get(f"/api/draft/{draft_id}/board")
                        if response.status_code == 200:
                            available = response.json()["available_players"]
                            if available:
                                client.post(
                                    f"/api/draft/{draft_id}/pick",
                                    json={
                                        "franchise_id": current_pick["franchise_id"],
                                        "player_id": available[0]["player_id"]
                                    }
                                )
            
            # Get recommendation - should prioritize non-RB positions
            response = client.get(f"/api/draft/{draft_id}/recommendation/{user_franchise_id}")
            if response.status_code == 200:
                rec_data = response.json()
                
                # Should include positional need analysis
                assert "positional_needs" in rec_data or "rationale" in rec_data
                
                # Rationale should mention positional balance
                if "rationale" in rec_data:
                    rationale = rec_data["rationale"].lower()
                    assert any(word in rationale for word in ["position", "balance", "need", "diversify"])
    
    def test_draft_performance_large_player_pool(self, client, mock_data_generator):
        """Test draft performance with large player pool."""
        import time
        
        # Create league with large player pool
        league = mock_data_generator.create_league(
            league_id="large_draft_league",
            name="Large Draft League"
        )
        
        franchise = mock_data_generator.create_franchise(
            franchise_id="large_draft_franchise",
            league_id=league.id
        )
        
        # Create 1000 players
        positions = [PlayerPosition.QB, PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE]
        
        for i in range(1000):
            player = mock_data_generator.create_player(
                player_id=f"large_draft_player_{i}",
                name=f"Player {i}",
                position=positions[i % len(positions)]
            )
            
            mock_data_generator.create_projection(
                player_id=player.id,
                projected_points=300 - i * 0.1  # Gradual decline
            )
        
        # Test performance
        start_time = time.time()
        
        response = client.post(f"/api/draft/{league.id}/initialize")
        
        if response.status_code == 200:
            draft_id = response.json()["draft_id"]
            
            # Get board (most expensive operation)
            board_response = client.get(f"/api/draft/{draft_id}/board")
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Should complete within reasonable time (10 seconds for 1000 players)
            assert processing_time < 10.0
            assert board_response.status_code == 200
            
            board_data = board_response.json()
            assert len(board_data["available_players"]) > 0
    
    def test_draft_error_handling(self, client, draft_league_setup):
        """Test error handling in draft workflow."""
        setup = draft_league_setup
        league_id = setup["league"].id
        
        # Test invalid league
        response = client.post("/api/draft/nonexistent_league/initialize")
        assert response.status_code == 404
        
        # Initialize valid draft
        response = client.post(f"/api/draft/{league_id}/initialize")
        assert response.status_code == 200
        draft_id = response.json()["draft_id"]
        
        # Test invalid draft ID
        response = client.get("/api/draft/invalid_draft/board")
        assert response.status_code == 404
        
        # Test invalid pick
        response = client.post(
            f"/api/draft/{draft_id}/pick",
            json={
                "franchise_id": "nonexistent_franchise",
                "player_id": "nonexistent_player"
            }
        )
        assert response.status_code in [400, 404]
        
        # Test picking already drafted player
        response = client.get(f"/api/draft/{draft_id}/board")
        if response.status_code == 200:
            board_data = response.json()
            if board_data["available_players"]:
                player_id = board_data["available_players"][0]["player_id"]
                franchise_id = setup["user_franchise"].id
                
                # Make valid pick
                response = client.post(
                    f"/api/draft/{draft_id}/pick",
                    json={
                        "franchise_id": franchise_id,
                        "player_id": player_id
                    }
                )
                
                if response.status_code == 200:
                    # Try to pick same player again
                    response = client.post(
                        f"/api/draft/{draft_id}/pick",
                        json={
                            "franchise_id": setup["franchises"][1].id,
                            "player_id": player_id
                        }
                    )
                    assert response.status_code == 400
    
    def test_draft_timer_integration(self, client, draft_league_setup):
        """Test draft timer integration."""
        setup = draft_league_setup
        league_id = setup["league"].id
        
        # Initialize draft
        response = client.post(f"/api/draft/{league_id}/initialize")
        assert response.status_code == 200
        draft_id = response.json()["draft_id"]
        
        # Get timer info
        response = client.get(f"/api/draft/{draft_id}/timer")
        
        if response.status_code == 200:
            timer_data = response.json()
            expected_fields = ["time_remaining", "pick_deadline", "is_active"]
            
            for field in expected_fields:
                assert field in timer_data
            
            # Test timer start
            response = client.post(f"/api/draft/{draft_id}/timer/start")
            # Should succeed or return appropriate error
            assert response.status_code in [200, 400, 409]
        else:
            # Timer endpoint might not be implemented yet
            assert response.status_code in [404, 501]
    
    def test_draft_undo_functionality(self, client, draft_league_setup):
        """Test draft undo functionality."""
        setup = draft_league_setup
        league_id = setup["league"].id
        user_franchise_id = setup["user_franchise"].id
        
        # Initialize draft
        response = client.post(f"/api/draft/{league_id}/initialize")
        assert response.status_code == 200
        draft_id = response.json()["draft_id"]
        
        # Get initial state
        response = client.get(f"/api/draft/{draft_id}/board")
        initial_board = response.json()
        initial_available_count = len(initial_board["available_players"])
        
        # Make a pick
        if initial_board["available_players"]:
            player_id = initial_board["available_players"][0]["player_id"]
            
            response = client.post(
                f"/api/draft/{draft_id}/pick",
                json={
                    "franchise_id": user_franchise_id,
                    "player_id": player_id
                }
            )
            
            if response.status_code == 200:
                pick_data = response.json()
                
                # Verify pick was made
                response = client.get(f"/api/draft/{draft_id}/board")
                updated_board = response.json()
                assert len(updated_board["available_players"]) == initial_available_count - 1
                
                # Test undo
                response = client.post(f"/api/draft/{draft_id}/undo")
                
                if response.status_code == 200:
                    # Verify undo worked
                    response = client.get(f"/api/draft/{draft_id}/board")
                    restored_board = response.json()
                    assert len(restored_board["available_players"]) == initial_available_count
                    
                    # Player should be available again
                    available_ids = {p["player_id"] for p in restored_board["available_players"]}
                    assert player_id in available_ids
                else:
                    # Undo might not be implemented yet
                    assert response.status_code in [404, 501]