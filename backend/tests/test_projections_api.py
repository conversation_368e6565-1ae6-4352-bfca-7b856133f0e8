"""
Tests for projections API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
from decimal import Decimal

from app.main import app
from app.services.projections_aggregator import AggregatedProjection, BacktestResult


class TestProjectionsAPI:
    """Test cases for projections API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def sample_aggregated_projection(self):
        """Create sample aggregated projection."""
        return AggregatedProjection(
            player_id="player_123",
            week=1,
            season=2024,
            projected_points=Decimal('15.5'),
            confidence_interval=(Decimal('12.0'), Decimal('19.0')),
            variance=Decimal('2.5'),
            source_count=3,
            source_weights={'FantasyPros': 0.5, 'ESPN': 0.3, 'Yahoo': 0.2},
            metadata={'test': True}
        )
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    @patch('app.api.projections.ProjectionsCacheManager')
    def test_get_aggregated_projection_success(
        self, 
        mock_cache_manager_class,
        mock_aggregator_class,
        mock_get_db,
        client,
        sample_aggregated_projection
    ):
        """Test successful aggregated projection retrieval."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock cache manager
        mock_cache_manager = Mock()
        mock_cache_manager.get_cached_projection.return_value = sample_aggregated_projection
        mock_cache_manager_class.return_value = mock_cache_manager
        
        # Mock aggregator
        mock_aggregator = Mock()
        mock_aggregator_class.return_value = mock_aggregator
        
        response = client.get("/api/v1/projections/aggregate/player_123?week=1&season=2024")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data['player_id'] == 'player_123'
        assert data['week'] == 1
        assert data['season'] == 2024
        assert data['projected_points'] == 15.5
        assert data['source_count'] == 3
        assert len(data['confidence_interval']) == 2
        assert 'source_weights' in data
        assert 'metadata' in data
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    @patch('app.api.projections.ProjectionsCacheManager')
    def test_get_aggregated_projection_not_found(
        self,
        mock_cache_manager_class,
        mock_aggregator_class,
        mock_get_db,
        client
    ):
        """Test aggregated projection not found."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock cache manager returning None
        mock_cache_manager = Mock()
        mock_cache_manager.get_cached_projection.return_value = None
        mock_cache_manager_class.return_value = mock_cache_manager
        
        # Mock aggregator
        mock_aggregator = Mock()
        mock_aggregator_class.return_value = mock_aggregator
        
        response = client.get("/api/v1/projections/aggregate/player_123?week=1&season=2024")
        
        assert response.status_code == 404
        assert "Insufficient projection sources" in response.json()['detail']
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    def test_get_all_aggregated_projections(
        self,
        mock_aggregator_class,
        mock_get_db,
        client,
        sample_aggregated_projection
    ):
        """Test getting all aggregated projections."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock aggregator
        mock_aggregator = Mock()
        mock_aggregator.aggregate_all_projections.return_value = [sample_aggregated_projection]
        mock_aggregator_class.return_value = mock_aggregator
        
        response = client.get("/api/v1/projections/aggregate?week=1&season=2024")
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) == 1
        assert data[0]['player_id'] == 'player_123'
        assert data[0]['projected_points'] == 15.5
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    def test_get_projection_sources(
        self,
        mock_aggregator_class,
        mock_get_db,
        client
    ):
        """Test getting projection sources."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock aggregator
        mock_aggregator = Mock()
        mock_aggregator.get_projection_sources.return_value = ['FantasyPros', 'ESPN', 'Yahoo']
        mock_aggregator_class.return_value = mock_aggregator
        
        response = client.get("/api/v1/projections/sources?season=2024")
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) == 3
        assert 'FantasyPros' in data
        assert 'ESPN' in data
        assert 'Yahoo' in data
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    def test_get_source_statistics(
        self,
        mock_aggregator_class,
        mock_get_db,
        client
    ):
        """Test getting source statistics."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock aggregator
        mock_stats = {
            'source': 'FantasyPros',
            'season': 2024,
            'total_projections': 100,
            'mean_points': 15.2,
            'median_points': 14.8,
            'std_dev': 3.5,
            'min_points': 5.0,
            'max_points': 35.0,
            'weekly_projections': 80,
            'season_projections': 20
        }
        mock_aggregator = Mock()
        mock_aggregator.get_source_statistics.return_value = mock_stats
        mock_aggregator_class.return_value = mock_aggregator
        
        response = client.get("/api/v1/projections/sources/FantasyPros/statistics?season=2024")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data['source'] == 'FantasyPros'
        assert data['season'] == 2024
        assert data['total_projections'] == 100
        assert data['mean_points'] == 15.2
        assert data['std_dev'] == 3.5
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    def test_get_source_statistics_not_found(
        self,
        mock_aggregator_class,
        mock_get_db,
        client
    ):
        """Test getting source statistics when not found."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock aggregator returning empty stats
        mock_aggregator = Mock()
        mock_aggregator.get_source_statistics.return_value = {}
        mock_aggregator_class.return_value = mock_aggregator
        
        response = client.get("/api/v1/projections/sources/NonExistent/statistics?season=2024")
        
        assert response.status_code == 404
        assert "No statistics found" in response.json()['detail']
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    def test_run_backtesting(
        self,
        mock_aggregator_class,
        mock_get_db,
        client
    ):
        """Test running backtesting."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock aggregator
        backtest_results = [
            BacktestResult('FantasyPros', 2.1, 2.8, 0.8, 50, 0.8),
            BacktestResult('ESPN', 2.5, 3.2, 0.6, 45, 0.6)
        ]
        mock_aggregator = Mock()
        mock_aggregator.run_backtesting.return_value = backtest_results
        mock_aggregator_class.return_value = mock_aggregator
        
        response = client.get("/api/v1/projections/backtesting?season=2024")
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) == 2
        assert data[0]['source'] == 'FantasyPros'
        assert data[0]['mae'] == 2.1
        assert data[0]['accuracy_score'] == 0.8
        assert data[1]['source'] == 'ESPN'
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    def test_get_optimized_weights(
        self,
        mock_aggregator_class,
        mock_get_db,
        client
    ):
        """Test getting optimized weights."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock aggregator
        weights = {'FantasyPros': 0.5, 'ESPN': 0.3, 'Yahoo': 0.2}
        mock_aggregator = Mock()
        mock_aggregator.optimize_source_weights.return_value = weights
        mock_aggregator_class.return_value = mock_aggregator
        
        response = client.get("/api/v1/projections/weights?season=2024")
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, dict)
        assert data == weights
        assert abs(sum(data.values()) - 1.0) < 1e-10  # Should sum to 1.0
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    @patch('app.api.projections.ProjectionsCacheManager')
    def test_refresh_cache(
        self,
        mock_cache_manager_class,
        mock_aggregator_class,
        mock_get_db,
        client
    ):
        """Test cache refresh."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock aggregator and cache manager
        mock_aggregator = Mock()
        mock_aggregator_class.return_value = mock_aggregator
        
        mock_cache_manager = Mock()
        mock_cache_manager_class.return_value = mock_cache_manager
        
        response = client.post("/api/v1/projections/cache/refresh")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "cache refreshed successfully" in data['message']
        mock_aggregator.refresh_projections_cache.assert_called_once()
        mock_cache_manager.clear_cache.assert_called_once()
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    @patch('app.api.projections.ProjectionsCacheManager')
    def test_clear_cache_all(
        self,
        mock_cache_manager_class,
        mock_aggregator_class,
        mock_get_db,
        client
    ):
        """Test clearing all cache."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock aggregator and cache manager
        mock_aggregator = Mock()
        mock_aggregator_class.return_value = mock_aggregator
        
        mock_cache_manager = Mock()
        mock_cache_manager_class.return_value = mock_cache_manager
        
        response = client.delete("/api/v1/projections/cache")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "Cleared all cached projections" in data['message']
        mock_cache_manager.clear_cache.assert_called_once()
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    @patch('app.api.projections.ProjectionsCacheManager')
    def test_clear_cache_specific(
        self,
        mock_cache_manager_class,
        mock_aggregator_class,
        mock_get_db,
        client
    ):
        """Test clearing specific cache entries."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock aggregator and cache manager
        mock_aggregator = Mock()
        mock_aggregator_class.return_value = mock_aggregator
        
        mock_cache_manager = Mock()
        mock_cache_manager_class.return_value = mock_cache_manager
        
        response = client.delete("/api/v1/projections/cache?player_id=player_123&week=1")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "player_id=player_123" in data['message']
        assert "week=1" in data['message']
        mock_cache_manager.invalidate_cache.assert_called_once_with(
            player_id="player_123",
            week=1,
            season=None
        )
    
    @patch('app.api.projections.get_db')
    @patch('app.api.projections.ProjectionsAggregator')
    @patch('app.api.projections.ProjectionsCacheManager')
    def test_get_cache_stats(
        self,
        mock_cache_manager_class,
        mock_aggregator_class,
        mock_get_db,
        client
    ):
        """Test getting cache statistics."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock aggregator and cache manager
        mock_aggregator = Mock()
        mock_aggregator_class.return_value = mock_aggregator
        
        cache_stats = {
            'total_entries': 10,
            'expired_entries': 2,
            'active_entries': 8,
            'cache_hit_ratio': 0.75
        }
        mock_cache_manager = Mock()
        mock_cache_manager.get_cache_stats.return_value = cache_stats
        mock_cache_manager_class.return_value = mock_cache_manager
        
        response = client.get("/api/v1/projections/cache/stats")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data == cache_stats
        assert data['total_entries'] == 10
        assert data['active_entries'] == 8