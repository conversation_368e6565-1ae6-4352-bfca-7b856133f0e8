"""
Basic tests for Celery configuration and core functionality.
"""
import pytest
from unittest.mock import Mock, patch

from app.core.celery import celery_app
from app.tasks.monitoring import health_check, system_diagnostics, JobMonitor


class TestCeleryBasicConfiguration:
    """Test basic Celery configuration."""
    
    def test_celery_app_exists(self):
        """Test that Celery app is properly initialized."""
        assert celery_app is not None
        assert celery_app.main == "fantasy_assistant"
    
    def test_celery_configuration(self):
        """Test that Celery app has correct configuration."""
        assert celery_app.conf.task_serializer == "json"
        assert celery_app.conf.result_serializer == "json"
        assert celery_app.conf.timezone == "UTC"
        assert celery_app.conf.enable_utc is True
        assert celery_app.conf.task_track_started is True
        assert celery_app.conf.task_acks_late is True
    
    def test_retry_configuration(self):
        """Test retry configuration."""
        assert celery_app.conf.task_default_retry_delay == 60
        assert celery_app.conf.task_max_retries == 3
        assert celery_app.conf.task_reject_on_worker_lost is True
    
    def test_queue_configuration(self):
        """Test that queues are properly configured."""
        expected_queues = ['default', 'data_refresh', 'alerts', 'file_processing', 'failed']
        configured_queues = list(celery_app.conf.task_queues.keys())
        
        for queue in expected_queues:
            assert queue in configured_queues
    
    def test_beat_schedule_exists(self):
        """Test that beat schedule is configured."""
        beat_schedule = celery_app.conf.beat_schedule
        assert isinstance(beat_schedule, dict)
        assert len(beat_schedule) > 0
        
        # Check for key scheduled tasks
        expected_tasks = [
            'refresh-mfl-data',
            'process-player-news',
            'check-deadlines',
            'cleanup-old-tasks',
            'refresh-projections'
        ]
        
        for task in expected_tasks:
            assert task in beat_schedule


class TestHealthCheckTasks:
    """Test health check functionality."""
    
    def test_health_check_task_structure(self):
        """Test that health check task is properly defined."""
        assert hasattr(health_check, 'delay')
        assert hasattr(health_check, 'apply_async')
        assert callable(health_check)
    
    def test_health_check_execution(self):
        """Test health check task execution."""
        # Test that the task can be called (without request context)
        # In actual execution, Celery provides the request context
        try:
            result = health_check()
            # If it runs without error, the basic structure is correct
            assert 'status' in result
            assert 'timestamp' in result
        except AttributeError:
            # Expected when running without Celery context
            # This just confirms the task is properly structured
            pass
    
    @patch('app.tasks.monitoring.SessionLocal')
    def test_system_diagnostics_execution(self, mock_session):
        """Test system diagnostics task execution."""
        # Mock database session
        mock_db = Mock()
        mock_session.return_value = mock_db
        mock_db.execute.return_value = None
        
        # Test that the task can be called (without request context)
        try:
            result = system_diagnostics()
            # If it runs without error, the basic structure is correct
            assert 'database' in result
            assert 'redis' in result
            assert 'celery' in result
            assert 'timestamp' in result
        except AttributeError:
            # Expected when running without Celery context
            # This just confirms the task is properly structured
            pass


class TestJobMonitor:
    """Test job monitoring functionality."""
    
    def test_job_monitor_initialization(self):
        """Test JobMonitor initialization."""
        monitor = JobMonitor()
        assert monitor.celery_app == celery_app
        assert monitor.state is not None
    
    def test_job_monitor_methods_exist(self):
        """Test that JobMonitor has all required methods."""
        monitor = JobMonitor()
        
        required_methods = [
            'get_active_tasks',
            'get_scheduled_tasks',
            'get_failed_tasks',
            'get_task_status',
            'retry_failed_task',
            'cancel_task',
            'get_worker_stats',
            'get_queue_lengths'
        ]
        
        for method in required_methods:
            assert hasattr(monitor, method)
            assert callable(getattr(monitor, method))
    
    @patch('app.tasks.monitoring.celery_app.control.inspect')
    def test_get_active_tasks_empty(self, mock_inspect):
        """Test getting active tasks when none exist."""
        mock_inspect.return_value.active.return_value = None
        
        monitor = JobMonitor()
        tasks = monitor.get_active_tasks()
        
        assert tasks == []
    
    @patch('app.tasks.monitoring.celery_app.control.inspect')
    def test_get_active_tasks_with_data(self, mock_inspect):
        """Test getting active tasks with data."""
        mock_inspect.return_value.active.return_value = {
            'worker1': [
                {
                    'id': 'task-123',
                    'name': 'test.task',
                    'args': [],
                    'kwargs': {},
                    'acknowledged': True
                }
            ]
        }
        
        monitor = JobMonitor()
        tasks = monitor.get_active_tasks()
        
        assert len(tasks) == 1
        assert tasks[0]['id'] == 'task-123'
        assert tasks[0]['name'] == 'test.task'
        assert tasks[0]['worker'] == 'worker1'
    
    @patch('app.tasks.monitoring.AsyncResult')
    def test_get_task_status(self, mock_async_result):
        """Test getting task status."""
        mock_result = Mock()
        mock_result.status = 'SUCCESS'
        mock_result.result = {'test': 'data'}
        mock_result.ready.return_value = True
        mock_result.traceback = None
        mock_result.date_done = None
        mock_result.name = 'test_task'
        mock_async_result.return_value = mock_result
        
        monitor = JobMonitor()
        status = monitor.get_task_status('test-task-id')
        
        assert status['id'] == 'test-task-id'
        assert status['status'] == 'SUCCESS'
        assert status['result'] == {'test': 'data'}
        assert status['task_name'] == 'test_task'


class TestTaskRegistration:
    """Test that tasks are properly registered."""
    
    def test_core_tasks_registered(self):
        """Test that core tasks are registered with Celery."""
        # Get all registered tasks
        registered_tasks = list(celery_app.tasks.keys())
        
        # Check for monitoring tasks
        monitoring_tasks = [
            'app.tasks.monitoring.health_check',
            'app.tasks.monitoring.system_diagnostics'
        ]
        
        for task in monitoring_tasks:
            assert task in registered_tasks
    
    def test_task_routing(self):
        """Test that task routing is configured."""
        task_routes = celery_app.conf.task_routes
        assert isinstance(task_routes, dict)
        
        # Check that failed tasks route to failed queue
        assert 'app.tasks.*.failed' in task_routes
        assert task_routes['app.tasks.*.failed']['queue'] == 'failed'


class TestErrorHandling:
    """Test error handling and retry logic."""
    
    def test_dead_letter_queue_configured(self):
        """Test that dead letter queue is configured."""
        task_routes = celery_app.conf.task_routes
        assert 'app.tasks.*.failed' in task_routes
        assert task_routes['app.tasks.*.failed']['queue'] == 'failed'
    
    def test_retry_configuration_values(self):
        """Test retry configuration values."""
        assert celery_app.conf.task_default_retry_delay == 60
        assert celery_app.conf.task_max_retries == 3
        assert celery_app.conf.task_acks_late is True
        assert celery_app.conf.task_reject_on_worker_lost is True


if __name__ == "__main__":
    pytest.main([__file__])