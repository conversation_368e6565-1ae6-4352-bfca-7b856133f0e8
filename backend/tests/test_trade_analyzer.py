"""
Unit tests for the trade analysis engine.
"""
import pytest
from decimal import Decimal
from datetime import datetime
from unittest.mock import Mock, patch

from app.services.trade_analyzer import (
    TradeAnalyzer, TeamAnalysis, TradeProposal, PositionNeed, PositionSurplus,
    TradeFairness, TradeType, TradeImpactAnalysis
)
from app.models.league import League
from app.models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RosterPlayer
from app.models.player import Player, PlayerPosition, InjuryStatus
from app.services.projections_aggregator import AggregatedProjection


class TestTradeAnalyzer:
    """Test cases for the TradeAnalyzer service."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()
    
    @pytest.fixture
    def mock_projections_aggregator(self):
        """Mock projections aggregator."""
        return Mock()
    
    @pytest.fixture
    def trade_analyzer(self, mock_db):
        """Create TradeAnalyzer instance with mocked dependencies."""
        analyzer = TradeAnalyzer(mock_db)
        analyzer.projections_aggregator = Mock()
        return analyzer
    
    @pytest.fixture
    def sample_league(self):
        """Create a sample league for testing."""
        league = League(
            id="test_league",
            name="Test League",
            season=2024,
            scoring_rules={
                "passing_yards": 0.04,
                "passing_touchdowns": 4,
                "rushing_yards": 0.1,
                "rushing_touchdowns": 6,
                "receiving_yards": 0.1,
                "receiving_touchdowns": 6
            },
            roster_slots=[
                {"position": "QB", "count": 1},
                {"position": "RB", "count": 2},
                {"position": "WR", "count": 2},
                {"position": "TE", "count": 1},
                {"position": "FLEX", "positions": ["RB", "WR", "TE"], "count": 1},
                {"position": "K", "count": 1},
                {"position": "DEF", "count": 1}
            ]
        )
        return league
    
    @pytest.fixture
    def sample_franchises(self, sample_league):
        """Create sample franchises for testing."""
        franchise_a = Franchise(
            id="team_a",
            name="Team A",
            owner_name="Owner A",
            league_id=sample_league.id,
            league=sample_league
        )
        
        franchise_b = Franchise(
            id="team_b",
            name="Team B",
            owner_name="Owner B",
            league_id=sample_league.id,
            league=sample_league
        )
        
        sample_league.franchises = [franchise_a, franchise_b]
        return franchise_a, franchise_b
    
    @pytest.fixture
    def sample_players(self):
        """Create sample players for testing."""
        players = {
            "qb1": Player(
                id="qb1", name="Elite QB", position=PlayerPosition.QB,
                team="KC", bye_week=10, injury_status=InjuryStatus.HEALTHY
            ),
            "rb1": Player(
                id="rb1", name="Elite RB", position=PlayerPosition.RB,
                team="DAL", bye_week=7, injury_status=InjuryStatus.HEALTHY
            ),
            "rb2": Player(
                id="rb2", name="Good RB", position=PlayerPosition.RB,
                team="SF", bye_week=9, injury_status=InjuryStatus.HEALTHY
            ),
            "wr1": Player(
                id="wr1", name="Elite WR", position=PlayerPosition.WR,
                team="BUF", bye_week=12, injury_status=InjuryStatus.HEALTHY
            ),
            "wr2": Player(
                id="wr2", name="Good WR", position=PlayerPosition.WR,
                team="MIA", bye_week=5, injury_status=InjuryStatus.HEALTHY
            ),
            "te1": Player(
                id="te1", name="Elite TE", position=PlayerPosition.TE,
                team="KC", bye_week=10, injury_status=InjuryStatus.HEALTHY
            )
        }
        return players
    
    @pytest.fixture
    def sample_rosters(self, sample_franchises, sample_players):
        """Create sample rosters for testing."""
        franchise_a, franchise_b = sample_franchises
        
        # Team A roster (strong at RB, weak at WR)
        roster_a = Roster(id="roster_a", franchise_id=franchise_a.id, franchise=franchise_a)
        roster_a.roster_players = [
            RosterPlayer(
                id="rp_a1", roster_id=roster_a.id, player_id="qb1",
                roster_slot="QB", player=sample_players["qb1"], roster=roster_a
            ),
            RosterPlayer(
                id="rp_a2", roster_id=roster_a.id, player_id="rb1",
                roster_slot="RB1", player=sample_players["rb1"], roster=roster_a
            ),
            RosterPlayer(
                id="rp_a3", roster_id=roster_a.id, player_id="rb2",
                roster_slot="RB2", player=sample_players["rb2"], roster=roster_a
            ),
            RosterPlayer(
                id="rp_a4", roster_id=roster_a.id, player_id="wr2",
                roster_slot="WR1", player=sample_players["wr2"], roster=roster_a
            ),
            RosterPlayer(
                id="rp_a5", roster_id=roster_a.id, player_id="te1",
                roster_slot="TE", player=sample_players["te1"], roster=roster_a
            )
        ]
        franchise_a.roster = roster_a
        
        # Team B roster (strong at WR, weak at RB)
        roster_b = Roster(id="roster_b", franchise_id=franchise_b.id, franchise=franchise_b)
        roster_b.roster_players = [
            RosterPlayer(
                id="rp_b1", roster_id=roster_b.id, player_id="qb1",
                roster_slot="QB", player=sample_players["qb1"], roster=roster_b
            ),
            RosterPlayer(
                id="rp_b2", roster_id=roster_b.id, player_id="wr1",
                roster_slot="WR1", player=sample_players["wr1"], roster=roster_b
            ),
            RosterPlayer(
                id="rp_b3", roster_id=roster_b.id, player_id="wr2",
                roster_slot="WR2", player=sample_players["wr2"], roster=roster_b
            )
        ]
        franchise_b.roster = roster_b
        
        return roster_a, roster_b
    
    def test_calculate_need_level(self, trade_analyzer):
        """Test need level calculation."""
        # Mock starter data
        starters = [
            {'projection': Mock(projected_points=Decimal('15'))},
            {'projection': Mock(projected_points=Decimal('12'))}
        ]
        replacement_level = Decimal('10')
        required_starters = 2
        
        need_level = trade_analyzer._calculate_need_level(
            starters, replacement_level, required_starters
        )
        
        # Should have low need since starters are above replacement
        assert 0.0 <= need_level <= 0.4
    
    def test_calculate_need_level_insufficient_starters(self, trade_analyzer):
        """Test need level when not enough starters."""
        starters = [
            {'projection': Mock(projected_points=Decimal('15'))}
        ]
        replacement_level = Decimal('10')
        required_starters = 2
        
        need_level = trade_analyzer._calculate_need_level(
            starters, replacement_level, required_starters
        )
        
        # Should have high need due to insufficient starters
        assert need_level == 1.0
    
    def test_calculate_surplus_level(self, trade_analyzer):
        """Test surplus level calculation."""
        starters = [
            {'projection': Mock(projected_points=Decimal('20'))}
        ]
        bench = [
            {'projection': Mock(projected_points=Decimal('15'))},
            {'projection': Mock(projected_points=Decimal('13'))},
            {'projection': Mock(projected_points=Decimal('8'))}
        ]
        replacement_level = Decimal('10')
        
        surplus_level = trade_analyzer._calculate_surplus_level(
            starters, bench, replacement_level
        )
        
        # Should have surplus due to quality bench players
        assert surplus_level > 0.0
    
    def test_calculate_surplus_level_no_bench(self, trade_analyzer):
        """Test surplus level with no bench players."""
        starters = [
            {'projection': Mock(projected_points=Decimal('20'))}
        ]
        bench = []
        replacement_level = Decimal('10')
        
        surplus_level = trade_analyzer._calculate_surplus_level(
            starters, bench, replacement_level
        )
        
        # Should have no surplus without bench
        assert surplus_level == 0.0
    
    def test_calculate_depth_score(self, trade_analyzer):
        """Test depth score calculation."""
        bench_players = [
            {'projection': Mock(projected_points=Decimal('15'))},
            {'projection': Mock(projected_points=Decimal('12'))},
            {'projection': Mock(projected_points=Decimal('8'))}
        ]
        replacement_level = Decimal('10')
        
        depth_score = trade_analyzer._calculate_depth_score(
            bench_players, replacement_level
        )
        
        # Should have good depth score with players above replacement
        assert 0.0 < depth_score <= 1.0
    
    def test_calculate_injury_risk(self, trade_analyzer):
        """Test injury risk calculation."""
        # Mock players with various injury statuses
        player_projections = [
            {'player': Mock(injury_status=Mock(value='HEALTHY'))},
            {'player': Mock(injury_status=Mock(value='QUESTIONABLE'))},
            {'player': Mock(injury_status=Mock(value='OUT'))},
            {'player': Mock(injury_status=Mock(value='HEALTHY'))}
        ]
        
        injury_risk = trade_analyzer._calculate_injury_risk(player_projections)
        
        # Should have moderate risk with mixed injury statuses
        assert 0.0 < injury_risk < 1.0
    
    def test_calculate_bye_week_coverage(self, trade_analyzer):
        """Test bye week coverage calculation."""
        player_projections = [
            {'player': Mock(bye_week=7)},
            {'player': Mock(bye_week=9)},
            {'player': Mock(bye_week=12)},
            {'player': Mock(bye_week=7)}  # Duplicate bye week
        ]
        
        coverage = trade_analyzer._calculate_bye_week_coverage(player_projections)
        
        # Should have good coverage with diverse bye weeks
        assert 0.0 < coverage <= 1.0
    
    def test_calculate_trade_fairness(self, trade_analyzer):
        """Test trade fairness calculation."""
        # Equal impact trade
        impact_a = Decimal('10')
        impact_b = Decimal('10')
        
        fairness = trade_analyzer._calculate_trade_fairness(impact_a, impact_b)
        
        # Should be perfectly fair
        assert fairness == 0.0
        
        # Unequal impact trade
        impact_a = Decimal('15')
        impact_b = Decimal('5')
        
        fairness = trade_analyzer._calculate_trade_fairness(impact_a, impact_b)
        
        # Should favor team B (negative score)
        assert fairness < 0.0
    
    def test_classify_fairness(self, trade_analyzer):
        """Test fairness classification."""
        # Very fair trade
        fairness = trade_analyzer._classify_fairness(0.05)
        assert fairness == TradeFairness.VERY_FAIR
        
        # Fair trade
        fairness = trade_analyzer._classify_fairness(0.2)
        assert fairness == TradeFairness.FAIR
        
        # Slightly unfair trade
        fairness = trade_analyzer._classify_fairness(0.3)
        assert fairness == TradeFairness.SLIGHTLY_UNFAIR
        
        # Unfair trade
        fairness = trade_analyzer._classify_fairness(0.5)
        assert fairness == TradeFairness.UNFAIR
        
        # Very unfair trade
        fairness = trade_analyzer._classify_fairness(0.8)
        assert fairness == TradeFairness.VERY_UNFAIR
    
    def test_calculate_acceptance_probability(self, trade_analyzer):
        """Test acceptance probability calculation."""
        # Mock trade impacts
        impact_a = Mock(win_probability_change=0.05)
        impact_b = Mock(win_probability_change=0.03)
        fairness_score = 0.1  # Slightly unfair
        
        prob = trade_analyzer._calculate_acceptance_probability(
            impact_a, impact_b, fairness_score
        )
        
        # Should have reasonable acceptance probability
        assert 0.0 <= prob <= 1.0
        
        # Both teams benefit, so should be > 0.5
        assert prob > 0.5
    
    def test_determine_trade_type(self, trade_analyzer):
        """Test trade type determination."""
        # High impact for both teams
        impact_a = Mock(win_probability_change=0.03, overall_impact=Decimal('15'))
        impact_b = Mock(win_probability_change=0.025, overall_impact=Decimal('12'))
        
        trade_type = trade_analyzer._determine_trade_type(impact_a, impact_b)
        
        # Should be WIN_NOW type
        assert trade_type == TradeType.WIN_NOW
        
        # Similar impact values
        impact_a = Mock(win_probability_change=0.01, overall_impact=Decimal('10'))
        impact_b = Mock(win_probability_change=0.01, overall_impact=Decimal('11'))
        
        trade_type = trade_analyzer._determine_trade_type(impact_a, impact_b)
        
        # Should be NEED_BASED type
        assert trade_type == TradeType.NEED_BASED
    
    def test_generate_trade_rationale(self, trade_analyzer):
        """Test trade rationale generation."""
        impact_a = Mock(
            win_probability_change=0.03,
            position_changes={PlayerPosition.RB: Decimal('10'), PlayerPosition.WR: Decimal('-5')}
        )
        impact_b = Mock(
            win_probability_change=0.02,
            position_changes={PlayerPosition.WR: Decimal('8'), PlayerPosition.RB: Decimal('-8')}
        )
        fairness = TradeFairness.FAIR
        
        rationale = trade_analyzer._generate_trade_rationale(impact_a, impact_b, fairness)
        
        # Should contain key information
        assert "fair trade" in rationale.lower()
        assert "team a" in rationale.lower()
        assert "team b" in rationale.lower()
        assert isinstance(rationale, str)
        assert len(rationale) > 0
    
    def test_estimate_win_probability(self, trade_analyzer):
        """Test win probability estimation."""
        # Strong team
        strong_strengths = {
            PlayerPosition.QB: Decimal('25'),
            PlayerPosition.RB: Decimal('40'),
            PlayerPosition.WR: Decimal('35'),
            PlayerPosition.TE: Decimal('15')
        }
        
        win_prob = trade_analyzer._estimate_win_probability(strong_strengths)
        
        # Should have high win probability
        assert 0.5 < win_prob <= 0.9
        
        # Weak team
        weak_strengths = {
            PlayerPosition.QB: Decimal('15'),
            PlayerPosition.RB: Decimal('20'),
            PlayerPosition.WR: Decimal('18'),
            PlayerPosition.TE: Decimal('8')
        }
        
        win_prob = trade_analyzer._estimate_win_probability(weak_strengths)
        
        # Should have lower win probability (adjust range since calculation may vary)
        assert 0.1 <= win_prob <= 0.6
    
    def test_calculate_trade_urgency(self, trade_analyzer):
        """Test trade urgency calculation."""
        # High needs, high surpluses
        needs = {
            PlayerPosition.RB: Mock(need_level=0.8),
            PlayerPosition.WR: Mock(need_level=0.6)
        }
        surpluses = {
            PlayerPosition.QB: Mock(surplus_level=0.7),
            PlayerPosition.TE: Mock(surplus_level=0.5)
        }
        
        urgency = trade_analyzer._calculate_trade_urgency(needs, surpluses)
        
        # Should have high urgency
        assert 0.5 < urgency <= 1.0
        
        # No needs or surpluses
        urgency = trade_analyzer._calculate_trade_urgency({}, {})
        
        # Should have no urgency
        assert urgency == 0.0
    
    @patch('app.services.trade_analyzer.TradeAnalyzer._calculate_replacement_levels')
    def test_analyze_single_team(self, mock_replacement_levels, trade_analyzer, sample_franchises, sample_rosters):
        """Test single team analysis."""
        franchise_a, _ = sample_franchises
        roster_a, _ = sample_rosters
        
        # Mock replacement levels
        mock_replacement_levels.return_value = {
            PlayerPosition.QB: Decimal('20'),
            PlayerPosition.RB: Decimal('15'),
            PlayerPosition.WR: Decimal('12'),
            PlayerPosition.TE: Decimal('10')
        }
        
        # Mock projections
        trade_analyzer.projections_aggregator.aggregate_projections.return_value = Mock(
            projected_points=Decimal('18'),
            variance=Decimal('2')
        )
        
        analysis = trade_analyzer._analyze_single_team(
            franchise_a, mock_replacement_levels.return_value, 2024
        )
        
        # Should return TeamAnalysis object
        assert isinstance(analysis, TeamAnalysis)
        assert analysis.franchise_id == franchise_a.id
        assert analysis.franchise_name == franchise_a.name
        assert isinstance(analysis.needs, dict)
        assert isinstance(analysis.surpluses, dict)
        assert analysis.overall_strength >= 0
        assert 0.0 <= analysis.win_probability <= 1.0
        assert 0.0 <= analysis.trade_urgency <= 1.0
    
    def test_get_position_starter_slots(self, trade_analyzer, sample_league):
        """Test getting starter slots for a position."""
        # QB should have 1 slot
        qb_slots = trade_analyzer._get_position_starter_slots(sample_league, PlayerPosition.QB)
        assert qb_slots == 1
        
        # RB should have 2 slots (plus FLEX eligibility)
        rb_slots = trade_analyzer._get_position_starter_slots(sample_league, PlayerPosition.RB)
        assert rb_slots >= 2
        
        # WR should have 2 slots (plus FLEX eligibility)  
        wr_slots = trade_analyzer._get_position_starter_slots(sample_league, PlayerPosition.WR)
        assert wr_slots >= 2


class TestTradeAnalyzerIntegration:
    """Integration tests for TradeAnalyzer with mocked database."""
    
    def test_trade_analyzer_initialization(self):
        """Test TradeAnalyzer initialization."""
        mock_db = Mock()
        analyzer = TradeAnalyzer(mock_db)
        
        assert analyzer.db == mock_db
        assert hasattr(analyzer, 'projections_aggregator')
        assert hasattr(analyzer, '_replacement_levels_cache')
        assert isinstance(analyzer._replacement_levels_cache, dict)