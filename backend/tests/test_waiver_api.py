"""
Unit tests for waiver wire API endpoints.
"""
import pytest
from decimal import Decimal
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from fastapi import HTTPException

from backend.app.main import app
from backend.app.services.waiver_optimizer import (
    WaiverTarget, WaiverStrategy, StreamingOpportunity, FreeAgent,
    WaiverTargetType, WaiverPriority, DropCandidate
)
from backend.app.models.player import PlayerPosition
from backend.app.models.roster import Franchise


class TestWaiverAPI:
    """Test cases for waiver wire API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock()
    
    @pytest.fixture
    def sample_franchise(self):
        """Create a sample franchise for testing."""
        return Franchise(
            id="test_franchise",
            name="Test Team",
            owner_name="Test Owner",
            league_id="test_league",
            faab_budget=Decimal('100'),
            faab_spent=Decimal('25')
        )
    
    @pytest.fixture
    def sample_free_agents(self):
        """Create sample free agents for testing."""
        return [
            FreeAgent(
                player_id="player_1",
                player_name="Test QB",
                position=PlayerPosition.QB,
                team="TEST",
                projected_points=Decimal('250'),
                points_over_replacement=Decimal('50'),
                ownership_percentage=15.0,
                recent_performance=[Decimal('18'), Decimal('22')],
                upcoming_matchups=["vs TEAM1", "vs TEAM2"],
                bye_week=10,
                injury_status="HEALTHY",
                target_type=WaiverTargetType.STARTER_UPGRADE,
                metadata={'projection_confidence': 0.1, 'source_count': 3}
            ),
            FreeAgent(
                player_id="player_2",
                player_name="Test RB",
                position=PlayerPosition.RB,
                team="TEST",
                projected_points=Decimal('120'),
                points_over_replacement=Decimal('20'),
                ownership_percentage=8.0,
                recent_performance=[Decimal('8'), Decimal('12')],
                upcoming_matchups=["vs TEAM1", "vs TEAM2"],
                bye_week=11,
                injury_status="HEALTHY",
                target_type=WaiverTargetType.HANDCUFF,
                metadata={'projection_confidence': 0.15, 'source_count': 2}
            )
        ]
    
    @pytest.fixture
    def sample_waiver_targets(self):
        """Create sample waiver targets for testing."""
        return [
            WaiverTarget(
                player_id="player_1",
                player_name="Test QB",
                position=PlayerPosition.QB,
                target_type=WaiverTargetType.STARTER_UPGRADE,
                priority=WaiverPriority.HIGH,
                recommended_bid=Decimal('20'),
                max_bid=Decimal('30'),
                points_over_replacement=Decimal('50'),
                weekly_upside=Decimal('25'),
                rationale="High value QB upgrade",
                drop_candidates=[
                    DropCandidate(
                        player_id="drop_1",
                        player_name="Drop Player",
                        position=PlayerPosition.QB,
                        projected_points=Decimal('80'),
                        drop_priority=1,
                        reason="Low projected points"
                    )
                ],
                streaming_weeks=None,
                confidence=0.8,
                metadata={'test': 'data'}
            )
        ]
    
    @patch('backend.app.api.waiver.get_db')
    @patch('backend.app.services.waiver_optimizer.WaiverOptimizer')
    def test_get_free_agents_success(self, mock_optimizer_class, mock_get_db, client, sample_free_agents):
        """Test successful free agents retrieval."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock optimizer
        mock_optimizer = Mock()
        mock_optimizer.get_available_free_agents.return_value = sample_free_agents
        mock_optimizer_class.return_value = mock_optimizer
        
        # Execute
        response = client.get(
            "/api/v1/waiver/free-agents/test_league",
            params={
                "season": 2024,
                "min_projected_points": 10,
                "limit": 50
            }
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["player_name"] == "Test QB"
        assert data[0]["position"] == "QB"
        assert float(data[0]["projected_points"]) == 250.0
        assert data[0]["target_type"] == "starter_upgrade"
    
    @patch('backend.app.api.waiver.get_db')
    @patch('backend.app.services.waiver_optimizer.WaiverOptimizer')
    def test_get_free_agents_with_position_filter(self, mock_optimizer_class, mock_get_db, client, sample_free_agents):
        """Test free agents retrieval with position filter."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock optimizer
        mock_optimizer = Mock()
        mock_optimizer.get_available_free_agents.return_value = sample_free_agents
        mock_optimizer_class.return_value = mock_optimizer
        
        # Execute
        response = client.get(
            "/api/v1/waiver/free-agents/test_league",
            params={
                "season": 2024,
                "position": "QB",
                "limit": 50
            }
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1  # Only QB should be returned
        assert data[0]["position"] == "QB"
    
    @patch('backend.app.api.waiver.get_db')
    @patch('backend.app.services.waiver_optimizer.WaiverOptimizer')
    def test_get_free_agents_error(self, mock_optimizer_class, mock_get_db, client):
        """Test free agents retrieval with error."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock optimizer to raise exception
        mock_optimizer = Mock()
        mock_optimizer.get_available_free_agents.side_effect = Exception("Test error")
        mock_optimizer_class.return_value = mock_optimizer
        
        # Execute
        response = client.get("/api/v1/waiver/free-agents/test_league")
        
        # Verify
        assert response.status_code == 500
        assert "Error retrieving free agents" in response.json()["detail"]
    
    @patch('backend.app.api.waiver.get_db')
    @patch('backend.app.services.waiver_optimizer.WaiverOptimizer')
    def test_get_waiver_targets_success(self, mock_optimizer_class, mock_get_db, client, sample_franchise, sample_waiver_targets):
        """Test successful waiver targets retrieval."""
        # Mock database
        mock_db = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = sample_franchise
        mock_get_db.return_value = mock_db
        
        # Mock optimizer
        mock_optimizer = Mock()
        mock_optimizer.analyze_waiver_targets.return_value = sample_waiver_targets
        mock_optimizer_class.return_value = mock_optimizer
        
        # Execute
        response = client.get(
            "/api/v1/waiver/targets/test_franchise",
            params={
                "week": 5,
                "season": 2024,
                "max_targets": 10
            }
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["player_name"] == "Test QB"
        assert data[0]["priority"] == "high"
        assert float(data[0]["recommended_bid"]) == 20.0
        assert len(data[0]["drop_candidates"]) == 1
    
    @patch('backend.app.api.waiver.get_db')
    def test_get_waiver_targets_franchise_not_found(self, mock_get_db, client):
        """Test waiver targets retrieval with non-existent franchise."""
        # Mock database to return None
        mock_db = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = None
        mock_get_db.return_value = mock_db
        
        # Execute
        response = client.get(
            "/api/v1/waiver/targets/nonexistent_franchise",
            params={"week": 5}
        )
        
        # Verify
        assert response.status_code == 404
        assert "Franchise nonexistent_franchise not found" in response.json()["detail"]
    
    @patch('backend.app.api.waiver.get_db')
    @patch('backend.app.services.waiver_optimizer.WaiverOptimizer')
    def test_optimize_faab_allocation_success(self, mock_optimizer_class, mock_get_db, client, sample_franchise, sample_waiver_targets):
        """Test successful FAAB allocation optimization."""
        # Mock database
        mock_db = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = sample_franchise
        mock_get_db.return_value = mock_db
        
        # Mock optimizer
        mock_optimizer = Mock()
        mock_optimizer.analyze_waiver_targets.return_value = sample_waiver_targets
        
        mock_strategy = WaiverStrategy(
            strategy_name="Optimal",
            targets=sample_waiver_targets,
            total_faab_allocation=Decimal('20'),
            remaining_budget=Decimal('55'),
            expected_value=Decimal('50'),
            risk_level="medium",
            streaming_opportunities=[],
            trade_offs=["No significant trade-offs"],
            metadata={'test': 'data'}
        )
        mock_optimizer.optimize_faab_allocation.return_value = mock_strategy
        mock_optimizer_class.return_value = mock_optimizer
        
        # Execute
        response = client.post(
            "/api/v1/waiver/optimize-faab",
            json={
                "franchise_id": "test_franchise",
                "target_player_ids": ["player_1"],
                "budget_constraint": None
            }
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["strategy_name"] == "Optimal"
        assert float(data["total_faab_allocation"]) == 20.0
        assert float(data["expected_value"]) == 50.0
        assert len(data["targets"]) == 1
    
    @patch('backend.app.api.waiver.get_db')
    def test_optimize_faab_allocation_franchise_not_found(self, mock_get_db, client):
        """Test FAAB optimization with non-existent franchise."""
        # Mock database to return None
        mock_db = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = None
        mock_get_db.return_value = mock_db
        
        # Execute
        response = client.post(
            "/api/v1/waiver/optimize-faab",
            json={
                "franchise_id": "nonexistent_franchise",
                "target_player_ids": ["player_1"]
            }
        )
        
        # Verify
        assert response.status_code == 404
        assert "Franchise nonexistent_franchise not found" in response.json()["detail"]
    
    @patch('backend.app.api.waiver.get_db')
    @patch('backend.app.services.waiver_optimizer.WaiverOptimizer')
    def test_optimize_faab_allocation_no_valid_targets(self, mock_optimizer_class, mock_get_db, client, sample_franchise):
        """Test FAAB optimization with no valid targets."""
        # Mock database
        mock_db = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = sample_franchise
        mock_get_db.return_value = mock_db
        
        # Mock optimizer to return no targets
        mock_optimizer = Mock()
        mock_optimizer.analyze_waiver_targets.return_value = []
        mock_optimizer_class.return_value = mock_optimizer
        
        # Execute
        response = client.post(
            "/api/v1/waiver/optimize-faab",
            json={
                "franchise_id": "test_franchise",
                "target_player_ids": ["nonexistent_player"]
            }
        )
        
        # Verify
        assert response.status_code == 400
        assert "No valid targets found" in response.json()["detail"]
    
    @patch('backend.app.api.waiver.get_db')
    @patch('backend.app.services.waiver_optimizer.WaiverOptimizer')
    def test_get_streaming_opportunities_success(self, mock_optimizer_class, mock_get_db, client, sample_franchise):
        """Test successful streaming opportunities retrieval."""
        # Mock database
        mock_db = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = sample_franchise
        mock_get_db.return_value = mock_db
        
        # Mock streaming opportunity
        streaming_opportunity = StreamingOpportunity(
            position=PlayerPosition.QB,
            weeks=[5, 6, 7, 8],
            targets=[],
            total_value=Decimal('30'),
            strategy="Stream QB based on matchups",
            confidence=0.7
        )
        
        # Mock optimizer
        mock_optimizer = Mock()
        mock_optimizer.identify_streaming_opportunities.return_value = [streaming_opportunity]
        mock_optimizer_class.return_value = mock_optimizer
        
        # Execute
        response = client.get(
            "/api/v1/waiver/streaming/test_franchise",
            params={
                "current_week": 5,
                "weeks_ahead": 4,
                "season": 2024
            }
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["position"] == "QB"
        assert data[0]["weeks"] == [5, 6, 7, 8]
        assert float(data[0]["total_value"]) == 30.0
        assert data[0]["strategy"] == "Stream QB based on matchups"
    
    @patch('backend.app.api.waiver.get_db')
    @patch('backend.app.services.waiver_optimizer.WaiverOptimizer')
    def test_analyze_team_needs_success(self, mock_optimizer_class, mock_get_db, client, sample_franchise):
        """Test successful team needs analysis."""
        # Mock database
        mock_db = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = sample_franchise
        mock_get_db.return_value = mock_db
        
        # Mock team needs
        team_needs = {
            PlayerPosition.QB: {
                'need_level': 'medium',
                'starter_strength': Decimal('200'),
                'depth_count': 2,
                'required_starters': 1,
                'avg_starter_points': Decimal('200')
            },
            PlayerPosition.RB: {
                'need_level': 'high',
                'starter_strength': Decimal('150'),
                'depth_count': 1,
                'required_starters': 2,
                'avg_starter_points': Decimal('75')
            }
        }
        
        # Mock optimizer
        mock_optimizer = Mock()
        mock_optimizer.analyze_team_needs.return_value = team_needs
        mock_optimizer_class.return_value = mock_optimizer
        
        # Execute
        response = client.get(
            "/api/v1/waiver/team-needs/test_franchise",
            params={
                "week": 5,
                "season": 2024
            }
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        
        # Find QB and RB entries
        qb_entry = next(entry for entry in data if entry["position"] == "QB")
        rb_entry = next(entry for entry in data if entry["position"] == "RB")
        
        assert qb_entry["need_level"] == "medium"
        assert qb_entry["depth_count"] == 2
        assert rb_entry["need_level"] == "high"
        assert rb_entry["depth_count"] == 1
    
    @patch('backend.app.api.waiver.get_db')
    @patch('backend.app.services.waiver_optimizer.WaiverOptimizer')
    def test_refresh_replacement_levels_cache_success(self, mock_optimizer_class, mock_get_db, client):
        """Test successful cache refresh."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock optimizer
        mock_optimizer = Mock()
        mock_optimizer.refresh_replacement_levels_cache.return_value = None
        mock_optimizer_class.return_value = mock_optimizer
        
        # Execute
        response = client.post("/api/v1/waiver/refresh-cache")
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert "cache refreshed successfully" in data["message"]
        mock_optimizer.refresh_replacement_levels_cache.assert_called_once()
    
    @patch('backend.app.api.waiver.get_db')
    @patch('backend.app.services.waiver_optimizer.WaiverOptimizer')
    def test_refresh_replacement_levels_cache_error(self, mock_optimizer_class, mock_get_db, client):
        """Test cache refresh with error."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock optimizer to raise exception
        mock_optimizer = Mock()
        mock_optimizer.refresh_replacement_levels_cache.side_effect = Exception("Cache error")
        mock_optimizer_class.return_value = mock_optimizer
        
        # Execute
        response = client.post("/api/v1/waiver/refresh-cache")
        
        # Verify
        assert response.status_code == 500
        assert "Error refreshing cache" in response.json()["detail"]
    
    def test_faab_optimization_request_validation(self, client):
        """Test FAAB optimization request validation."""
        # Test missing required fields
        response = client.post(
            "/api/v1/waiver/optimize-faab",
            json={
                "franchise_id": "test_franchise"
                # Missing target_player_ids
            }
        )
        
        # Verify validation error
        assert response.status_code == 422
        assert "field required" in response.json()["detail"][0]["msg"]
    
    def test_get_waiver_targets_query_params_validation(self, client):
        """Test waiver targets query parameter validation."""
        # Test missing required week parameter
        response = client.get("/api/v1/waiver/targets/test_franchise")
        
        # Verify validation error
        assert response.status_code == 422
        assert "field required" in response.json()["detail"][0]["msg"]
    
    def test_get_streaming_opportunities_query_params_validation(self, client):
        """Test streaming opportunities query parameter validation."""
        # Test missing required current_week parameter
        response = client.get("/api/v1/waiver/streaming/test_franchise")
        
        # Verify validation error
        assert response.status_code == 422
        assert "field required" in response.json()["detail"][0]["msg"]
    
    def test_analyze_team_needs_query_params_validation(self, client):
        """Test team needs analysis query parameter validation."""
        # Test missing required week parameter
        response = client.get("/api/v1/waiver/team-needs/test_franchise")
        
        # Verify validation error
        assert response.status_code == 422
        assert "field required" in response.json()["detail"][0]["msg"]