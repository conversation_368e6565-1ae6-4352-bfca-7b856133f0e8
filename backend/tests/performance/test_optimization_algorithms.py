"""
Performance tests for optimization algorithms.
Tests algorithm performance under various load conditions and data sizes.
"""
import pytest
import time
import statistics
from decimal import Decimal
from unittest.mock import Mock, patch
import numpy as np

from app.services.keeper_optimizer import KeeperOptimizer, KeeperCandidate
from app.services.lineup_optimizer import LineupOptimizer, LineupSlot
from app.services.trade_analyzer import TradeAnalyzer
from app.services.waiver_optimizer import WaiverOptimizer
from app.services.projections_aggregator import ProjectionsAggregator
from app.models.player import PlayerPosition, InjuryStatus
from backend.tests.utils.mock_data_generator import MockDataGenerator


class TestKeeperOptimizerPerformance:
    """Performance tests for keeper optimization algorithms."""
    
    @pytest.fixture
    def keeper_optimizer(self):
        """Create keeper optimizer with mocked database."""
        mock_db = Mock()
        return KeeperOptimizer(mock_db)
    
    def create_keeper_candidates(self, count: int) -> list[KeeperCandidate]:
        """Create a list of keeper candidates for testing."""
        candidates = []
        positions = [PlayerPosition.QB, PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE]
        
        for i in range(count):
            candidate = KeeperCandidate(
                player_id=f"player_{i}",
                player_name=f"Player {i}",
                position=positions[i % len(positions)],
                current_cost=5 + (i % 10),
                projected_points=Decimal(str(300 - i * 2)),
                replacement_level=Decimal(str(200 - (i % 4) * 20)),
                value_over_replacement=Decimal(str(100 - i * 1.5)),
                keeper_cost=4 + (i % 10),
                is_eligible=True,
                constraints_violated=[],
                metadata={"projection_confidence": 0.1 + (i % 10) * 0.05}
            )
            candidates.append(candidate)
        
        return candidates
    
    def test_keeper_optimization_small_roster(self, keeper_optimizer):
        """Test keeper optimization performance with small roster (10 players)."""
        candidates = self.create_keeper_candidates(10)
        
        # Mock the get_keeper_candidates method
        keeper_optimizer.get_keeper_candidates = Mock(return_value=candidates)
        
        # Mock franchise and league
        mock_franchise = Mock()
        mock_franchise.league.keeper_rules = {
            "max_keepers": 3,
            "position_limits": {"QB": 1, "RB": 2, "WR": 2, "TE": 1}
        }
        keeper_optimizer.db.query.return_value.filter.return_value.first.return_value = mock_franchise
        
        # Measure performance
        start_time = time.time()
        scenarios = keeper_optimizer.optimize_keepers("test_franchise", 2024, max_scenarios=5)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Should complete quickly with small roster
        assert processing_time < 0.5  # 500ms
        assert len(scenarios) > 0
        assert scenarios[0].constraints_satisfied
    
    def test_keeper_optimization_medium_roster(self, keeper_optimizer):
        """Test keeper optimization performance with medium roster (25 players)."""
        candidates = self.create_keeper_candidates(25)
        
        keeper_optimizer.get_keeper_candidates = Mock(return_value=candidates)
        
        mock_franchise = Mock()
        mock_franchise.league.keeper_rules = {
            "max_keepers": 5,
            "position_limits": {"QB": 2, "RB": 3, "WR": 3, "TE": 2}
        }
        keeper_optimizer.db.query.return_value.filter.return_value.first.return_value = mock_franchise
        
        start_time = time.time()
        scenarios = keeper_optimizer.optimize_keepers("test_franchise", 2024, max_scenarios=3)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Should complete within reasonable time
        assert processing_time < 2.0  # 2 seconds
        assert len(scenarios) > 0
    
    def test_keeper_optimization_large_roster(self, keeper_optimizer):
        """Test keeper optimization performance with large roster (50 players)."""
        candidates = self.create_keeper_candidates(50)
        
        keeper_optimizer.get_keeper_candidates = Mock(return_value=candidates)
        
        mock_franchise = Mock()
        mock_franchise.league.keeper_rules = {
            "max_keepers": 8,
            "position_limits": {"QB": 2, "RB": 4, "WR": 4, "TE": 2}
        }
        keeper_optimizer.db.query.return_value.filter.return_value.first.return_value = mock_franchise
        
        start_time = time.time()
        scenarios = keeper_optimizer.optimize_keepers("test_franchise", 2024, max_scenarios=3)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Should complete within acceptable time even with large roster
        assert processing_time < 5.0  # 5 seconds
        assert len(scenarios) > 0
    
    def test_keeper_optimization_stress_test(self, keeper_optimizer):
        """Stress test keeper optimization with maximum constraints."""
        candidates = self.create_keeper_candidates(100)  # Very large roster
        
        keeper_optimizer.get_keeper_candidates = Mock(return_value=candidates)
        
        mock_franchise = Mock()
        mock_franchise.league.keeper_rules = {
            "max_keepers": 15,  # High keeper limit
            "position_limits": {"QB": 3, "RB": 6, "WR": 6, "TE": 3},
            "salary_cap": 100,  # Add salary constraint
            "max_keeper_cost": 12
        }
        keeper_optimizer.db.query.return_value.filter.return_value.first.return_value = mock_franchise
        
        start_time = time.time()
        scenarios = keeper_optimizer.optimize_keepers("test_franchise", 2024, max_scenarios=2)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Should complete within reasonable time even under stress
        assert processing_time < 10.0  # 10 seconds max
        assert len(scenarios) > 0
    
    def test_keeper_optimization_repeated_calls(self, keeper_optimizer):
        """Test performance of repeated keeper optimization calls."""
        candidates = self.create_keeper_candidates(20)
        keeper_optimizer.get_keeper_candidates = Mock(return_value=candidates)
        
        mock_franchise = Mock()
        mock_franchise.league.keeper_rules = {"max_keepers": 3}
        keeper_optimizer.db.query.return_value.filter.return_value.first.return_value = mock_franchise
        
        # Measure multiple calls
        times = []
        for _ in range(10):
            start_time = time.time()
            scenarios = keeper_optimizer.optimize_keepers("test_franchise", 2024, max_scenarios=1)
            end_time = time.time()
            times.append(end_time - start_time)
            assert len(scenarios) > 0
        
        # Check consistency and performance
        avg_time = statistics.mean(times)
        max_time = max(times)
        
        assert avg_time < 1.0  # Average under 1 second
        assert max_time < 2.0   # No single call over 2 seconds
        
        # Performance should be consistent (low variance)
        if len(times) > 1:
            std_dev = statistics.stdev(times)
            assert std_dev < avg_time * 0.5  # Standard deviation < 50% of mean


class TestLineupOptimizerPerformance:
    """Performance tests for lineup optimization algorithms."""
    
    @pytest.fixture
    def lineup_optimizer(self):
        """Create lineup optimizer with mocked database."""
        mock_db = Mock()
        return LineupOptimizer(mock_db)
    
    def create_players_data(self, count: int) -> dict:
        """Create players data for lineup optimization testing."""
        players_data = {}
        positions = [PlayerPosition.QB, PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE, PlayerPosition.K, PlayerPosition.DEF]
        
        for i in range(count):
            player_id = f"player_{i}"
            position = positions[i % len(positions)]
            
            players_data[player_id] = {
                "player": Mock(id=player_id, position=position, name=f"Player {i}"),
                "projection": Mock(projected_points=Decimal(str(20 - i * 0.1))),
                "base_projection": 20.0 - i * 0.1,
                "variance": 2.0 + (i % 5) * 0.5,
                "floor": 15.0 - i * 0.1,
                "ceiling": 25.0 - i * 0.1,
                "adjusted_projection": 20.0 - i * 0.1,
                "matchup_context": Mock()
            }
        
        return players_data
    
    def test_lineup_optimization_standard_roster(self, lineup_optimizer):
        """Test lineup optimization with standard roster size."""
        players_data = self.create_players_data(20)  # Typical roster size
        
        roster_slots = {
            LineupSlot.QB: 1,
            LineupSlot.RB1: 1,
            LineupSlot.RB2: 1,
            LineupSlot.WR1: 1,
            LineupSlot.WR2: 1,
            LineupSlot.TE: 1,
            LineupSlot.FLEX: 1,
            LineupSlot.K: 1,
            LineupSlot.DEF: 1
        }
        
        # Mock the optimization methods
        with patch.object(lineup_optimizer, '_get_available_players_with_projections', return_value=players_data):
            with patch.object(lineup_optimizer, '_parse_roster_slots', return_value=roster_slots):
                with patch.object(lineup_optimizer, '_estimate_opponent_projection', return_value=100.0):
                    
                    start_time = time.time()
                    
                    # Mock franchise
                    mock_franchise = Mock()
                    mock_franchise.roster = Mock()
                    mock_franchise.league = Mock()
                    mock_franchise.league.roster_slots = []
                    lineup_optimizer.db.query.return_value.filter.return_value.first.return_value = mock_franchise
                    
                    recommendation = lineup_optimizer.optimize_lineup("test_franchise", 1)
                    
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    # Should complete quickly with standard roster
                    assert processing_time < 1.0  # 1 second
                    assert recommendation is not None
    
    def test_lineup_optimization_large_roster(self, lineup_optimizer):
        """Test lineup optimization with large roster."""
        players_data = self.create_players_data(50)  # Large roster
        
        roster_slots = {
            LineupSlot.QB: 1,
            LineupSlot.RB1: 1,
            LineupSlot.RB2: 1,
            LineupSlot.WR1: 1,
            LineupSlot.WR2: 1,
            LineupSlot.WR3: 1,
            LineupSlot.TE: 1,
            LineupSlot.FLEX: 1,
            LineupSlot.SUPERFLEX: 1,
            LineupSlot.K: 1,
            LineupSlot.DEF: 1
        }
        
        with patch.object(lineup_optimizer, '_get_available_players_with_projections', return_value=players_data):
            with patch.object(lineup_optimizer, '_parse_roster_slots', return_value=roster_slots):
                with patch.object(lineup_optimizer, '_estimate_opponent_projection', return_value=100.0):
                    
                    start_time = time.time()
                    
                    mock_franchise = Mock()
                    mock_franchise.roster = Mock()
                    mock_franchise.league = Mock()
                    mock_franchise.league.roster_slots = []
                    lineup_optimizer.db.query.return_value.filter.return_value.first.return_value = mock_franchise
                    
                    recommendation = lineup_optimizer.optimize_lineup("test_franchise", 1)
                    
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    # Should complete within reasonable time even with large roster
                    assert processing_time < 3.0  # 3 seconds
                    assert recommendation is not None
    
    def test_win_probability_simulation_performance(self, lineup_optimizer):
        """Test performance of win probability simulation."""
        lineup = {
            LineupSlot.QB: "qb1",
            LineupSlot.RB1: "rb1",
            LineupSlot.RB2: "rb2",
            LineupSlot.WR1: "wr1",
            LineupSlot.WR2: "wr2",
            LineupSlot.TE: "te1",
            LineupSlot.FLEX: "flex1",
            LineupSlot.K: "k1",
            LineupSlot.DEF: "def1"
        }
        
        players_data = {}
        for slot, player_id in lineup.items():
            players_data[player_id] = {
                "adjusted_projection": 15.0 + hash(player_id) % 10,
                "variance": 2.0 + hash(player_id) % 3
            }
        
        # Test different simulation sizes
        simulation_sizes = [100, 1000, 10000]
        
        for sim_size in simulation_sizes:
            start_time = time.time()
            
            win_prob = lineup_optimizer._simulate_win_probability(
                lineup, players_data, 120.0, sim_size
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Performance should scale reasonably with simulation size
            if sim_size == 100:
                assert processing_time < 0.1  # 100ms
            elif sim_size == 1000:
                assert processing_time < 0.5  # 500ms
            elif sim_size == 10000:
                assert processing_time < 2.0  # 2 seconds
            
            assert 0 <= win_prob <= 1
    
    def test_lineup_alternatives_generation_performance(self, lineup_optimizer):
        """Test performance of lineup alternatives generation."""
        players_data = self.create_players_data(30)
        
        optimal_lineup = {
            LineupSlot.QB: "player_0",
            LineupSlot.RB1: "player_1",
            LineupSlot.RB2: "player_2",
            LineupSlot.WR1: "player_3",
            LineupSlot.WR2: "player_4",
            LineupSlot.TE: "player_5",
            LineupSlot.FLEX: "player_6"
        }
        
        roster_slots = {slot: 1 for slot in optimal_lineup.keys()}
        
        with patch.object(lineup_optimizer, '_simulate_win_probability', return_value=0.6):
            start_time = time.time()
            
            alternatives = lineup_optimizer._generate_lineup_alternatives(
                players_data, roster_slots, optimal_lineup, 100.0
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Should generate alternatives quickly
            assert processing_time < 1.0  # 1 second
            assert isinstance(alternatives, list)


class TestProjectionsAggregatorPerformance:
    """Performance tests for projections aggregation algorithms."""
    
    @pytest.fixture
    def projections_aggregator(self):
        """Create projections aggregator with mocked database."""
        mock_db = Mock()
        return ProjectionsAggregator(mock_db)
    
    def test_aggregation_performance_many_sources(self, projections_aggregator):
        """Test aggregation performance with many projection sources."""
        # Mock projections from multiple sources
        mock_projections = []
        sources = [f"source_{i}" for i in range(10)]  # 10 different sources
        
        for source in sources:
            for week in range(1, 18):  # Full season
                projection = Mock()
                projection.source = source
                projection.week = week
                projection.projected_points = Decimal(str(15 + hash(f"{source}_{week}") % 10))
                projection.variance = Decimal("2.0")
                projection.confidence = 0.8
                mock_projections.append(projection)
        
        projections_aggregator.db.query.return_value.filter.return_value.all.return_value = mock_projections
        
        start_time = time.time()
        
        aggregated = projections_aggregator.aggregate_projections("test_player", None, 2024)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should aggregate quickly even with many sources
        assert processing_time < 0.5  # 500ms
        assert aggregated is not None
    
    def test_backtesting_performance(self, projections_aggregator):
        """Test backtesting performance with historical data."""
        # Mock historical data
        mock_historical = []
        for week in range(1, 18):
            for player_id in [f"player_{i}" for i in range(20)]:
                historical = Mock()
                historical.player_id = player_id
                historical.week = week
                historical.actual_points = Decimal(str(10 + hash(f"{player_id}_{week}") % 15))
                historical.projected_points = Decimal(str(12 + hash(f"{player_id}_{week}") % 12))
                mock_historical.append(historical)
        
        projections_aggregator.db.query.return_value.filter.return_value.all.return_value = mock_historical
        
        start_time = time.time()
        
        # Mock the backtesting method
        with patch.object(projections_aggregator, '_calculate_source_weights') as mock_weights:
            mock_weights.return_value = {"source_1": 0.4, "source_2": 0.6}
            
            weights = projections_aggregator.optimize_source_weights(2023)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Backtesting should complete in reasonable time
        assert processing_time < 2.0  # 2 seconds
        assert isinstance(weights, dict)
    
    def test_bulk_aggregation_performance(self, projections_aggregator):
        """Test performance of bulk aggregation for many players."""
        player_ids = [f"player_{i}" for i in range(100)]  # 100 players
        
        # Mock projections for all players
        mock_projections = []
        for player_id in player_ids:
            projection = Mock()
            projection.player_id = player_id
            projection.projected_points = Decimal(str(15 + hash(player_id) % 10))
            projection.source = "test_source"
            projection.week = 1
            mock_projections.append(projection)
        
        projections_aggregator.db.query.return_value.filter.return_value.all.return_value = mock_projections
        
        start_time = time.time()
        
        aggregated_projections = projections_aggregator.aggregate_all_projections(1, 2024)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should handle bulk aggregation efficiently
        assert processing_time < 3.0  # 3 seconds for 100 players
        assert len(aggregated_projections) > 0


class TestTradeAnalyzerPerformance:
    """Performance tests for trade analysis algorithms."""
    
    @pytest.fixture
    def trade_analyzer(self):
        """Create trade analyzer with mocked database."""
        mock_db = Mock()
        return TradeAnalyzer(mock_db)
    
    def test_trade_suggestion_performance_large_league(self, trade_analyzer):
        """Test trade suggestion performance with large league."""
        # Mock large league with many franchises and players
        mock_franchises = []
        for i in range(16):  # 16-team league
            franchise = Mock()
            franchise.id = f"franchise_{i}"
            franchise.roster = Mock()
            franchise.roster.roster_players = []
            
            # Each franchise has 20 players
            for j in range(20):
                player = Mock()
                player.id = f"player_{i}_{j}"
                player.position = [PlayerPosition.QB, PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE][j % 4]
                roster_player = Mock()
                roster_player.player = player
                franchise.roster.roster_players.append(roster_player)
            
            mock_franchises.append(franchise)
        
        trade_analyzer.db.query.return_value.filter.return_value.all.return_value = mock_franchises
        
        # Mock projections
        with patch.object(trade_analyzer, '_get_player_projections') as mock_proj:
            mock_proj.return_value = {"projected_points": 15.0, "variance": 2.0}
            
            start_time = time.time()
            
            suggestions = trade_analyzer.suggest_trades("franchise_0", max_suggestions=5)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Should complete within reasonable time even with large league
            assert processing_time < 5.0  # 5 seconds
            assert isinstance(suggestions, list)
    
    def test_trade_fairness_calculation_performance(self, trade_analyzer):
        """Test performance of trade fairness calculations."""
        # Create mock trade with many players
        trade_players_a = [f"player_a_{i}" for i in range(5)]
        trade_players_b = [f"player_b_{i}" for i in range(5)]
        
        # Mock player values
        with patch.object(trade_analyzer, '_calculate_player_value') as mock_value:
            mock_value.return_value = 15.0
            
            start_time = time.time()
            
            fairness = trade_analyzer.calculate_trade_fairness(
                "franchise_a", "franchise_b", trade_players_a, trade_players_b
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Should calculate fairness quickly
            assert processing_time < 0.5  # 500ms
            assert isinstance(fairness, (int, float))


class TestWaiverOptimizerPerformance:
    """Performance tests for waiver wire optimization algorithms."""
    
    @pytest.fixture
    def waiver_optimizer(self):
        """Create waiver optimizer with mocked database."""
        mock_db = Mock()
        return WaiverOptimizer(mock_db)
    
    def test_waiver_analysis_large_free_agent_pool(self, waiver_optimizer):
        """Test waiver analysis with large free agent pool."""
        # Mock large free agent pool
        mock_free_agents = []
        positions = [PlayerPosition.QB, PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE]
        
        for i in range(200):  # 200 free agents
            player = Mock()
            player.id = f"fa_player_{i}"
            player.name = f"Free Agent {i}"
            player.position = positions[i % len(positions)]
            mock_free_agents.append(player)
        
        waiver_optimizer.db.query.return_value.filter.return_value.all.return_value = mock_free_agents
        
        # Mock projections
        with patch.object(waiver_optimizer, '_get_player_projections') as mock_proj:
            mock_proj.return_value = {"projected_points": 10.0, "variance": 3.0}
            
            with patch.object(waiver_optimizer, '_calculate_replacement_level') as mock_repl:
                mock_repl.return_value = 8.0
                
                start_time = time.time()
                
                targets = waiver_optimizer.get_waiver_targets("test_franchise", max_targets=10)
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                # Should analyze large pool efficiently
                assert processing_time < 3.0  # 3 seconds for 200 players
                assert isinstance(targets, list)
    
    def test_faab_bid_optimization_performance(self, waiver_optimizer):
        """Test FAAB bid optimization performance."""
        # Mock multiple bid scenarios
        target_players = [f"target_{i}" for i in range(10)]
        
        with patch.object(waiver_optimizer, '_calculate_player_value') as mock_value:
            mock_value.return_value = 15.0
            
            with patch.object(waiver_optimizer, '_estimate_winning_bid') as mock_bid:
                mock_bid.return_value = 12.0
                
                start_time = time.time()
                
                bid_recommendations = waiver_optimizer.optimize_faab_bids(
                    "test_franchise", target_players, budget=100.0
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                # Should optimize bids quickly
                assert processing_time < 1.0  # 1 second
                assert isinstance(bid_recommendations, list)


class TestPerformanceRegression:
    """Tests to detect performance regressions."""
    
    def test_performance_benchmarks(self):
        """Establish performance benchmarks for key operations."""
        benchmarks = {
            "keeper_optimization_20_players": 1.0,  # seconds
            "lineup_optimization_standard": 0.5,
            "projections_aggregation_10_sources": 0.3,
            "trade_analysis_16_teams": 2.0,
            "waiver_analysis_100_players": 1.5
        }
        
        # This test would run actual benchmarks and compare against baselines
        # For now, we'll just verify the benchmarks are reasonable
        for operation, max_time in benchmarks.items():
            assert max_time > 0
            assert max_time < 10.0  # No operation should take more than 10 seconds
    
    def test_memory_usage_monitoring(self):
        """Monitor memory usage during optimization operations."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Simulate memory-intensive operation
        large_data = [i for i in range(100000)]
        processed_data = [x * 2 for x in large_data]
        
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        
        # Memory increase should be reasonable
        assert memory_increase < 100  # Less than 100MB increase
        
        # Clean up
        del large_data
        del processed_data
    
    def test_concurrent_optimization_performance(self):
        """Test performance under concurrent optimization requests."""
        import threading
        import queue
        
        results_queue = queue.Queue()
        
        def run_optimization():
            """Simulate optimization operation."""
            start_time = time.time()
            
            # Simulate work
            time.sleep(0.1)
            result = sum(i * i for i in range(1000))
            
            end_time = time.time()
            results_queue.put(end_time - start_time)
        
        # Run multiple optimizations concurrently
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=run_optimization)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Collect results
        times = []
        while not results_queue.empty():
            times.append(results_queue.get())
        
        assert len(times) == 5
        
        # All operations should complete within reasonable time
        for time_taken in times:
            assert time_taken < 1.0  # 1 second max per operation
        
        # Average time should be reasonable
        avg_time = sum(times) / len(times)
        assert avg_time < 0.5  # 500ms average