"""
Integration tests for draft assistance API endpoints.

Tests all draft API endpoints including board generation, recommendations,
Monte Carlo simulations, and contingency planning.
"""
import pytest
from decimal import Decimal
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from backend.app.main import app
from backend.app.models.player import Player, PlayerPosition, InjuryStatus
from backend.app.models.league import League
from backend.app.models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RosterPlayer
from backend.app.models.projection import Projection
from backend.app.services.draft_assistant import DraftStrategy

# API base path
API_BASE = "/api/v1"


class TestDraftAPI:
    """Test cases for draft assistance API endpoints."""
    
    @pytest.fixture
    def sample_league(self, db_session: Session):
        """Create sample league for testing."""
        league = League(
            id="test_league_api",
            name="Test League API",
            season=2024,
            scoring_rules={
                "passing_yards": 0.04,
                "passing_touchdowns": 4,
                "rushing_yards": 0.1,
                "rushing_touchdowns": 6,
                "receiving_yards": 0.1,
                "receiving_touchdowns": 6
            },
            roster_slots=[
                {"position": "QB", "count": 1},
                {"position": "RB", "count": 2},
                {"position": "WR", "count": 2},
                {"position": "TE", "count": 1},
                {"position": "FLEX", "count": 1},
                {"position": "K", "count": 1},
                {"position": "DEF", "count": 1}
            ]
        )
        db_session.add(league)
        db_session.commit()
        return league
    
    @pytest.fixture
    def sample_franchises(self, db_session: Session, sample_league: League):
        """Create sample franchises for testing."""
        franchises = []
        for i in range(12):
            franchise = Franchise(
                id=f"franchise_api_{i+1}",
                name=f"Team API {i+1}",
                owner_name=f"Owner API {i+1}",
                league_id=sample_league.id,
                faab_budget=Decimal('100')
            )
            db_session.add(franchise)
            franchises.append(franchise)
        
        db_session.commit()
        return franchises
    
    @pytest.fixture
    def sample_players_api(self, db_session: Session):
        """Create sample players for API testing."""
        players = []
        
        # Create fewer players for faster API tests
        positions_data = [
            (PlayerPosition.QB, 12, "qb_api"),
            (PlayerPosition.RB, 30, "rb_api"),
            (PlayerPosition.WR, 40, "wr_api"),
            (PlayerPosition.TE, 12, "te_api"),
            (PlayerPosition.K, 16, "k_api"),
            (PlayerPosition.DEF, 16, "def_api")
        ]
        
        for position, count, prefix in positions_data:
            for i in range(count):
                player = Player(
                    id=f"{prefix}_{i+1}",
                    name=f"{position.value} Player API {i+1}",
                    position=position,
                    team=f"TEAM{i%32+1}",
                    bye_week=(i % 14) + 1,
                    injury_status=InjuryStatus.HEALTHY
                )
                db_session.add(player)
                players.append(player)
        
        db_session.commit()
        return players
    
    @pytest.fixture
    def sample_projections_api(self, db_session: Session, sample_players_api: list):
        """Create sample projections for API testing."""
        projections = []
        
        position_base_points = {
            PlayerPosition.QB: Decimal('300'),
            PlayerPosition.RB: Decimal('250'),
            PlayerPosition.WR: Decimal('200'),
            PlayerPosition.TE: Decimal('150'),
            PlayerPosition.K: Decimal('120'),
            PlayerPosition.DEF: Decimal('100')
        }
        
        position_counters = {pos: 0 for pos in PlayerPosition}
        
        for player in sample_players_api:
            base_points = position_base_points[player.position]
            counter = position_counters[player.position]
            
            # Decrease points for each subsequent player at position
            projected_points = max(Decimal('50'), base_points - (counter * Decimal('5')))
            
            projection = Projection(
                id=f"proj_api_{player.id}",
                player_id=player.id,
                week=None,  # Season-long
                season=2024,
                source="test_api_source",
                projected_points=projected_points,
                confidence_level=Decimal('0.8'),
                stats={"test_stat": 100}
            )
            db_session.add(projection)
            projections.append(projection)
            
            position_counters[player.position] += 1
        
        db_session.commit()
        return projections
    
    def test_generate_draft_board_endpoint(
        self,
        client: TestClient,
        sample_league: League,
        sample_players_api: list,
        sample_projections_api: list
    ):
        """Test draft board generation endpoint."""
        request_data = {
            "league_id": sample_league.id,
            "season": 2024,
            "strategy": "VALUE_BASED",
            "force_refresh": True
        }
        
        response = client.post(f"{API_BASE}/draft/board", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Check response structure
        assert "tiers" in data
        assert "overall_rankings" in data
        assert "position_rankings" in data
        assert "value_over_replacement" in data
        assert "last_updated" in data
        assert "strategy" in data
        
        assert data["strategy"] == "VALUE_BASED"
        assert len(data["tiers"]) > 0
        assert len(data["overall_rankings"]) > 0
        assert len(data["position_rankings"]) > 0
        
        # Check tier structure
        for tier in data["tiers"]:
            assert "tier_number" in tier
            assert "position" in tier
            assert "players" in tier
            assert "min_value" in tier
            assert "max_value" in tier
            assert "avg_value" in tier
            assert tier["tier_number"] > 0
            assert len(tier["players"]) > 0
    
    def test_get_draft_recommendation_endpoint(
        self,
        client: TestClient,
        sample_league: League,
        sample_franchises: list,
        sample_players_api: list,
        sample_projections_api: list
    ):
        """Test draft recommendation endpoint."""
        franchise = sample_franchises[0]
        available_players = [p.id for p in sample_players_api[:20]]
        
        request_data = {
            "league_id": sample_league.id,
            "franchise_id": franchise.id,
            "available_players": available_players,
            "current_pick": 1,
            "season": 2024,
            "strategy": "VALUE_BASED"
        }
        
        response = client.post(f"{API_BASE}/draft/recommendation", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Check response structure
        required_fields = [
            "player_id", "player_name", "position", "team",
            "projected_points", "value_over_replacement", "tier",
            "confidence", "rationale", "alternatives",
            "positional_need_score", "opportunity_cost"
        ]
        
        for field in required_fields:
            assert field in data
        
        assert data["player_id"] in available_players
        assert data["player_name"] is not None
        assert data["position"] in [pos.value for pos in PlayerPosition]
        assert float(data["projected_points"]) >= 0
        assert float(data["value_over_replacement"]) >= 0
        assert data["tier"] > 0
        assert 0 <= float(data["confidence"]) <= 1
        assert len(data["rationale"]) > 0
        assert isinstance(data["alternatives"], list)
    
    def test_monte_carlo_simulation_endpoint(
        self,
        client: TestClient,
        sample_league: League,
        sample_franchises: list,
        sample_players_api: list,
        sample_projections_api: list
    ):
        """Test Monte Carlo simulation endpoint."""
        franchise = sample_franchises[0]
        
        request_data = {
            "league_id": sample_league.id,
            "franchise_id": franchise.id,
            "num_simulations": 5,  # Small number for testing
            "season": 2024,
            "strategy": "VALUE_BASED"
        }
        
        response = client.post(f"{API_BASE}/draft/simulate", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) == 5
        
        # Check scenario structure
        for scenario in data:
            assert "scenario_id" in scenario
            assert "picks" in scenario
            assert "final_roster" in scenario
            assert "projected_points" in scenario
            assert "win_probability" in scenario
            assert "strategy_score" in scenario
            
            assert len(scenario["picks"]) > 0
            assert float(scenario["projected_points"]) >= 0
            assert 0 <= float(scenario["win_probability"]) <= 1
            assert float(scenario["strategy_score"]) >= 0
            
            # Check pick structure
            for pick in scenario["picks"]:
                assert "round_number" in pick
                assert "pick_number" in pick
                assert "overall_pick" in pick
                assert "franchise_id" in pick
                assert pick["round_number"] > 0
                assert pick["pick_number"] > 0
                assert pick["overall_pick"] > 0
    
    def test_contingency_plans_endpoint(
        self,
        client: TestClient,
        sample_league: League,
        sample_franchises: list,
        sample_players_api: list,
        sample_projections_api: list
    ):
        """Test contingency plans endpoint."""
        franchise = sample_franchises[0]
        target_players = [p.id for p in sample_players_api[:3]]
        
        request_data = {
            "league_id": sample_league.id,
            "franchise_id": franchise.id,
            "target_players": target_players,
            "season": 2024
        }
        
        response = client.post(f"{API_BASE}/draft/contingency", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) > 0
        
        # Check contingency plan structure
        for plan in data:
            assert "scenario_name" in plan
            assert "recommendations" in plan
            assert isinstance(plan["scenario_name"], str)
            assert isinstance(plan["recommendations"], list)
            assert len(plan["recommendations"]) > 0
            
            # Check recommendation structure
            for rec in plan["recommendations"]:
                assert "player_id" in rec
                assert "player_name" in rec
                assert "position" in rec
                assert "rationale" in rec
                assert "confidence" in rec
    
    def test_update_draft_board_endpoint(
        self,
        client: TestClient,
        sample_league: League,
        sample_players_api: list,
        sample_projections_api: list
    ):
        """Test draft board update endpoint."""
        # First generate a board to get a player to pick
        board_request = {
            "league_id": sample_league.id,
            "season": 2024,
            "strategy": "VALUE_BASED",
            "force_refresh": True
        }
        
        board_response = client.post(f"{API_BASE}/draft/board", json=board_request)
        assert board_response.status_code == 200
        
        board_data = board_response.json()
        picked_player_id = board_data["overall_rankings"][0]
        
        # Now update the board after the pick
        update_request = {
            "league_id": sample_league.id,
            "picked_player_id": picked_player_id,
            "season": 2024
        }
        
        response = client.put(f"{API_BASE}/draft/board/update", json=update_request)
        
        assert response.status_code == 200
        data = response.json()
        
        # Check that the picked player is no longer in rankings
        assert picked_player_id not in data["overall_rankings"]
        
        # Check response structure is same as board generation
        assert "tiers" in data
        assert "overall_rankings" in data
        assert "position_rankings" in data
        assert "value_over_replacement" in data
        assert "last_updated" in data
        assert "strategy" in data
    
    def test_get_available_strategies_endpoint(self, client: TestClient):
        """Test get available strategies endpoint."""
        response = client.get(f"{API_BASE}/draft/strategies")
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) > 0
        
        # Check that all strategy values are included
        expected_strategies = [strategy.value for strategy in DraftStrategy]
        for strategy in expected_strategies:
            assert strategy in data
    
    def test_get_cached_draft_board_endpoint(
        self,
        client: TestClient,
        sample_league: League,
        sample_players_api: list,
        sample_projections_api: list
    ):
        """Test get cached draft board endpoint."""
        response = client.get(
            f"{API_BASE}/draft/board/{sample_league.id}",
            params={"season": 2024, "strategy": "VALUE_BASED"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Check response structure
        assert "tiers" in data
        assert "overall_rankings" in data
        assert "position_rankings" in data
        assert "value_over_replacement" in data
        assert "last_updated" in data
        assert "strategy" in data
        
        assert data["strategy"] == "VALUE_BASED"
    
    def test_health_check_endpoint(self, client: TestClient):
        """Test health check endpoint."""
        response = client.get(f"{API_BASE}/draft/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert "service" in data
        assert "timestamp" in data
        assert data["status"] == "healthy"
        assert data["service"] == "draft_assistant"
    
    def test_error_handling_invalid_league(self, client: TestClient):
        """Test error handling for invalid league ID."""
        request_data = {
            "league_id": "non_existent_league",
            "season": 2024,
            "strategy": "VALUE_BASED",
            "force_refresh": True
        }
        
        response = client.post(f"{API_BASE}/draft/board", json=request_data)
        
        assert response.status_code == 400
        assert "detail" in response.json()
    
    def test_error_handling_empty_available_players(
        self,
        client: TestClient,
        sample_league: League,
        sample_franchises: list
    ):
        """Test error handling for empty available players list."""
        franchise = sample_franchises[0]
        
        request_data = {
            "league_id": sample_league.id,
            "franchise_id": franchise.id,
            "available_players": [],  # Empty list
            "current_pick": 1,
            "season": 2024,
            "strategy": "VALUE_BASED"
        }
        
        response = client.post(f"{API_BASE}/draft/recommendation", json=request_data)
        
        assert response.status_code == 400
        assert "detail" in response.json()
    
    def test_validation_errors(self, client: TestClient):
        """Test request validation errors."""
        # Test invalid season
        request_data = {
            "league_id": "test_league",
            "season": 1999,  # Too early
            "strategy": "VALUE_BASED",
            "force_refresh": True
        }
        
        response = client.post(f"{API_BASE}/draft/board", json=request_data)
        assert response.status_code == 422
        
        # Test invalid pick number
        request_data = {
            "league_id": "test_league",
            "franchise_id": "franchise_1",
            "available_players": ["player_1"],
            "current_pick": 0,  # Invalid pick number
            "season": 2024,
            "strategy": "VALUE_BASED"
        }
        
        response = client.post(f"{API_BASE}/draft/recommendation", json=request_data)
        assert response.status_code == 422
        
        # Test invalid simulation count
        request_data = {
            "league_id": "test_league",
            "franchise_id": "franchise_1",
            "num_simulations": 50,  # Too few
            "season": 2024,
            "strategy": "VALUE_BASED"
        }
        
        response = client.post(f"{API_BASE}/draft/simulate", json=request_data)
        assert response.status_code == 422
    
    def test_different_strategies_produce_different_results(
        self,
        client: TestClient,
        sample_league: League,
        sample_players_api: list,
        sample_projections_api: list
    ):
        """Test that different strategies produce different results."""
        # Generate board with VALUE_BASED strategy
        value_request = {
            "league_id": sample_league.id,
            "season": 2024,
            "strategy": "VALUE_BASED",
            "force_refresh": True
        }
        
        value_response = client.post(f"{API_BASE}/draft/board", json=value_request)
        assert value_response.status_code == 200
        value_data = value_response.json()
        
        # Generate board with POSITIONAL_NEED strategy
        positional_request = {
            "league_id": sample_league.id,
            "season": 2024,
            "strategy": "POSITIONAL_NEED",
            "force_refresh": True
        }
        
        positional_response = client.post(f"{API_BASE}/draft/board", json=positional_request)
        assert positional_response.status_code == 200
        positional_data = positional_response.json()
        
        # Strategies should be different
        assert value_data["strategy"] != positional_data["strategy"]
        
        # Should have same players but potentially different orders
        value_players = set(value_data["overall_rankings"])
        positional_players = set(positional_data["overall_rankings"])
        assert value_players == positional_players
    
    def test_recommendation_with_existing_roster(
        self,
        client: TestClient,
        sample_league: League,
        sample_franchises: list,
        sample_players_api: list,
        sample_projections_api: list,
        db_session: Session
    ):
        """Test recommendations with existing roster players."""
        franchise = sample_franchises[0]
        
        # Create roster with some players
        roster = Roster(
            id=f"roster_api_{franchise.id}",
            franchise_id=franchise.id
        )
        db_session.add(roster)
        
        # Add a QB to roster
        qb_player = next(p for p in sample_players_api if p.position == PlayerPosition.QB)
        roster_player = RosterPlayer(
            id=f"rp_api_{qb_player.id}",
            roster_id=roster.id,
            player_id=qb_player.id,
            is_active=True
        )
        db_session.add(roster_player)
        db_session.commit()
        
        # Get recommendation excluding rostered player
        available_players = [p.id for p in sample_players_api if p.id != qb_player.id][:20]
        
        request_data = {
            "league_id": sample_league.id,
            "franchise_id": franchise.id,
            "available_players": available_players,
            "current_pick": 2,
            "season": 2024,
            "strategy": "POSITIONAL_NEED"
        }
        
        response = client.post(f"{API_BASE}/draft/recommendation", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Recommended player should not be the rostered QB
        assert data["player_id"] != qb_player.id
        assert data["player_id"] in available_players
        
        # Should have positional need score
        assert "positional_need_score" in data
        assert float(data["positional_need_score"]) >= 0