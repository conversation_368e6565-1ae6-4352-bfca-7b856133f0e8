"""
Integration tests for league management API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.core.database import get_db
from app.models.league import League
from app.models.roster import Franchise


class TestLeaguesAPI:
    """Test cases for league management API endpoints."""
    
    def test_create_league_success(self, client: TestClient, db_session: Session):
        """Test successful league creation."""
        league_data = {
            "id": "test_league_001",
            "name": "Test League",
            "season": 2024,
            "scoring_rules": {
                "passing_yards": 0.04,
                "passing_touchdowns": 4,
                "rushing_yards": 0.1,
                "rushing_touchdowns": 6,
                "receiving_yards": 0.1,
                "receiving_touchdowns": 6
            },
            "roster_slots": [
                {"position": "QB", "count": 1, "type": "starting"},
                {"position": "RB", "count": 2, "type": "starting"},
                {"position": "WR", "count": 2, "type": "starting"},
                {"position": "TE", "count": 1, "type": "starting"},
                {"position": "FLEX", "count": 1, "type": "starting"},
                {"position": "K", "count": 1, "type": "starting"},
                {"position": "DST", "count": 1, "type": "starting"},
                {"position": "BENCH", "count": 6, "type": "bench"}
            ],
            "description": "Test league for API testing"
        }
        
        response = client.post("/api/v1/leagues/", json=league_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == league_data["id"]
        assert data["name"] == league_data["name"]
        assert data["season"] == league_data["season"]
        assert data["is_active"] == True
        assert data["franchise_count"] == 0
        assert "created_at" in data
        assert "updated_at" in data
    
    def test_create_league_duplicate_id(self, client: TestClient, db_session: Session):
        """Test league creation with duplicate ID fails."""
        # Create first league
        league = League(
            id="duplicate_league",
            name="First League",
            season=2024,
            scoring_rules={},
            roster_slots=[]
        )
        db_session.add(league)
        db_session.commit()
        
        # Try to create second league with same ID
        league_data = {
            "id": "duplicate_league",
            "name": "Second League",
            "season": 2024,
            "scoring_rules": {},
            "roster_slots": []
        }
        
        response = client.post("/api/v1/leagues/", json=league_data)
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]
    
    def test_list_leagues_empty(self, client: TestClient, db_session: Session):
        """Test listing leagues when none exist."""
        response = client.get("/api/v1/leagues/")
        
        assert response.status_code == 200
        assert response.json() == []
    
    def test_list_leagues_with_data(self, client: TestClient, db_session: Session):
        """Test listing leagues with existing data."""
        # Create test leagues
        leagues = [
            League(id="league1", name="League 1", season=2024, scoring_rules={}, roster_slots=[]),
            League(id="league2", name="League 2", season=2024, scoring_rules={}, roster_slots=[]),
            League(id="league3", name="League 3", season=2023, scoring_rules={}, roster_slots=[], is_active=False)
        ]
        
        for league in leagues:
            db_session.add(league)
        db_session.commit()
        
        # Test default filtering (active only)
        response = client.get("/api/v1/leagues/")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert all(league["is_active"] for league in data)
        
        # Test season filtering
        response = client.get("/api/v1/leagues/?season=2023")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 0  # 2023 league is inactive
        
        # Test including inactive leagues
        response = client.get("/api/v1/leagues/?active_only=false")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3
    
    def test_get_league_success(self, client: TestClient, db_session: Session):
        """Test getting a specific league."""
        league = League(
            id="get_test_league",
            name="Get Test League",
            season=2024,
            scoring_rules={"passing_yards": 0.04},
            roster_slots=[{"position": "QB", "count": 1}],
            description="Test league for get endpoint"
        )
        db_session.add(league)
        db_session.commit()
        
        response = client.get("/api/v1/leagues/get_test_league")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "get_test_league"
        assert data["name"] == "Get Test League"
        assert data["season"] == 2024
        assert data["scoring_rules"] == {"passing_yards": 0.04}
        assert data["roster_slots"] == [{"position": "QB", "count": 1}]
        assert data["description"] == "Test league for get endpoint"
        assert data["franchise_count"] == 0
    
    def test_get_league_not_found(self, client: TestClient, db_session: Session):
        """Test getting a non-existent league."""
        response = client.get("/api/v1/leagues/nonexistent_league")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_update_league_success(self, client: TestClient, db_session: Session):
        """Test successful league update."""
        league = League(
            id="update_test_league",
            name="Original Name",
            season=2024,
            scoring_rules={},
            roster_slots=[]
        )
        db_session.add(league)
        db_session.commit()
        
        update_data = {
            "name": "Updated Name",
            "description": "Updated description",
            "scoring_rules": {"passing_yards": 0.04}
        }
        
        response = client.put("/api/v1/leagues/update_test_league", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Name"
        assert data["description"] == "Updated description"
        assert data["scoring_rules"] == {"passing_yards": 0.04}
    
    def test_update_league_not_found(self, client: TestClient, db_session: Session):
        """Test updating a non-existent league."""
        update_data = {"name": "New Name"}
        
        response = client.put("/api/v1/leagues/nonexistent_league", json=update_data)
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_delete_league_success(self, client: TestClient, db_session: Session):
        """Test successful league deletion."""
        league = League(
            id="delete_test_league",
            name="Delete Test League",
            season=2024,
            scoring_rules={},
            roster_slots=[]
        )
        db_session.add(league)
        db_session.commit()
        
        response = client.delete("/api/v1/leagues/delete_test_league")
        
        assert response.status_code == 200
        assert "deleted successfully" in response.json()["message"]
        
        # Verify league is deleted
        deleted_league = db_session.query(League).filter(League.id == "delete_test_league").first()
        assert deleted_league is None
    
    def test_delete_league_not_found(self, client: TestClient, db_session: Session):
        """Test deleting a non-existent league."""
        response = client.delete("/api/v1/leagues/nonexistent_league")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_validate_league_rules_success(self, client: TestClient, db_session: Session):
        """Test successful league rules validation."""
        league = League(
            id="validate_test_league",
            name="Validate Test League",
            season=2024,
            scoring_rules={"passing_yards": 0.04},
            roster_slots=[{"position": "QB", "count": 1}]
        )
        db_session.add(league)
        db_session.commit()
        
        response = client.post("/api/v1/leagues/validate_test_league/validate-rules")
        
        assert response.status_code == 200
        data = response.json()
        assert data["league_id"] == "validate_test_league"
        assert "is_valid" in data
        assert "validation_errors" in data
        assert "timestamp" in data
    
    def test_get_league_franchises(self, client: TestClient, db_session: Session):
        """Test getting franchises for a league."""
        # Create league
        league = League(
            id="franchise_test_league",
            name="Franchise Test League",
            season=2024,
            scoring_rules={},
            roster_slots=[]
        )
        db_session.add(league)
        db_session.commit()
        
        # Create franchises
        franchises = [
            Franchise(id="franchise1", name="Team 1", owner_name="Owner 1", league_id="franchise_test_league"),
            Franchise(id="franchise2", name="Team 2", owner_name="Owner 2", league_id="franchise_test_league")
        ]
        
        for franchise in franchises:
            db_session.add(franchise)
        db_session.commit()
        
        response = client.get("/api/v1/leagues/franchise_test_league/franchises")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert "franchise1" in data
        assert "franchise2" in data
    
    def test_health_check(self, client: TestClient, db_session: Session):
        """Test league service health check."""
        response = client.get("/api/v1/leagues/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "league_management"
        assert "timestamp" in data