"""
Unit tests for projections aggregation service.
"""
import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from typing import List

from app.services.projections_aggregator import (
    ProjectionsAggregator,
    ProjectionsCacheManager,
    AggregatedProjection,
    BacktestResult
)
from app.models.projection import Projection
from app.models.player import Player


class TestProjectionsAggregator:
    """Test cases for ProjectionsAggregator class."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()
    
    @pytest.fixture
    def aggregator(self, mock_db):
        """Create ProjectionsAggregator instance."""
        return ProjectionsAggregator(mock_db)
    
    @pytest.fixture
    def sample_projections(self):
        """Create sample projection data."""
        projections = []
        
        # Create projections from different sources
        sources = ['FantasyPros', 'ESPN', 'Yahoo', 'NFL']
        points = [15.2, 14.8, 16.1, 15.5]
        
        for i, (source, pts) in enumerate(zip(sources, points)):
            proj = Mock(spec=Projection)
            proj.id = f"proj_{i}"
            proj.player_id = "player_123"
            proj.week = 1
            proj.season = 2024
            proj.source = source
            proj.projected_points = Decimal(str(pts))
            proj.floor = Decimal(str(pts * 0.8))
            proj.ceiling = Decimal(str(pts * 1.2))
            proj.is_active = True
            projections.append(proj)
        
        return projections
    
    def test_aggregate_projections_success(self, aggregator, mock_db, sample_projections):
        """Test successful projection aggregation."""
        # Mock database query
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = sample_projections
        mock_db.query.return_value = mock_query
        
        # Mock weight optimization
        aggregator._get_optimized_weights = Mock(return_value={
            'FantasyPros': 0.4,
            'ESPN': 0.3,
            'Yahoo': 0.2,
            'NFL': 0.1
        })
        
        result = aggregator.aggregate_projections(
            player_id="player_123",
            week=1,
            season=2024
        )
        
        assert result is not None
        assert isinstance(result, AggregatedProjection)
        assert result.player_id == "player_123"
        assert result.week == 1
        assert result.season == 2024
        assert result.source_count == 4
        
        # Check that projected points is reasonable weighted average
        expected_points = (15.2 * 0.4 + 14.8 * 0.3 + 16.1 * 0.2 + 15.5 * 0.1)
        assert abs(float(result.projected_points) - expected_points) < 0.1
        
        # Check confidence interval exists
        assert len(result.confidence_interval) == 2
        assert result.confidence_interval[0] < result.projected_points
        assert result.confidence_interval[1] > result.projected_points
    
    def test_aggregate_projections_insufficient_sources(self, aggregator, mock_db):
        """Test aggregation with insufficient sources."""
        # Mock database query returning only one projection
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [Mock(spec=Projection)]
        mock_db.query.return_value = mock_query
        
        result = aggregator.aggregate_projections(
            player_id="player_123",
            week=1,
            season=2024,
            min_sources=2
        )
        
        assert result is None
    
    def test_calculate_weighted_ensemble(self, aggregator, sample_projections):
        """Test weighted ensemble calculation."""
        source_weights = {
            'FantasyPros': 0.4,
            'ESPN': 0.3,
            'Yahoo': 0.2,
            'NFL': 0.1
        }
        
        result = aggregator._calculate_weighted_ensemble(
            sample_projections, source_weights
        )
        
        assert isinstance(result, AggregatedProjection)
        assert result.source_count == 4
        
        # Verify weighted average calculation
        expected = (15.2 * 0.4 + 14.8 * 0.3 + 16.1 * 0.2 + 15.5 * 0.1)
        assert abs(float(result.projected_points) - expected) < 0.01
        
        # Check metadata
        assert 'aggregation_method' in result.metadata
        assert result.metadata['aggregation_method'] == 'weighted_ensemble'
        assert result.metadata['source_count'] == 4
    
    def test_calculate_confidence_interval_multiple_values(self, aggregator):
        """Test confidence interval calculation with multiple values."""
        values = [15.2, 14.8, 16.1, 15.5]
        weights = [0.4, 0.3, 0.2, 0.1]
        mean = 15.25
        
        ci, variance = aggregator._calculate_confidence_interval(
            values, weights, mean
        )
        
        assert len(ci) == 2
        assert ci[0] < Decimal(str(mean))  # Lower bound less than mean
        assert ci[1] > Decimal(str(mean))  # Upper bound greater than mean
        assert variance > 0  # Positive variance
    
    def test_calculate_confidence_interval_single_value(self, aggregator):
        """Test confidence interval calculation with single value."""
        values = [15.2]
        weights = [1.0]
        mean = 15.2
        
        ci, variance = aggregator._calculate_confidence_interval(
            values, weights, mean
        )
        
        assert len(ci) == 2
        assert ci[0] >= 0  # Non-negative lower bound
        assert ci[1] > ci[0]  # Upper bound greater than lower
        assert variance > 0  # Positive variance
    
    def test_normalize_weights(self, aggregator):
        """Test weight normalization."""
        source_weights = {'A': 0.6, 'B': 0.3, 'C': 0.1}
        available_sources = {'A', 'B'}
        
        normalized = aggregator._normalize_weights(source_weights, available_sources)
        
        assert set(normalized.keys()) == available_sources
        assert abs(sum(normalized.values()) - 1.0) < 1e-10  # Sum to 1.0
        assert normalized['A'] > normalized['B']  # Preserve relative ordering
    
    def test_normalize_weights_no_weights(self, aggregator):
        """Test weight normalization with no predefined weights."""
        source_weights = {}
        available_sources = {'A', 'B', 'C'}
        
        normalized = aggregator._normalize_weights(source_weights, available_sources)
        
        assert set(normalized.keys()) == available_sources
        assert abs(sum(normalized.values()) - 1.0) < 1e-10  # Sum to 1.0
        # Should be equal weights
        for weight in normalized.values():
            assert abs(weight - 1.0/3) < 1e-10
    
    def test_optimize_source_weights(self, aggregator, mock_db):
        """Test source weight optimization."""
        # Mock backtesting results
        backtest_results = [
            BacktestResult('FantasyPros', 2.1, 2.8, 0.8, 50, 0.8),
            BacktestResult('ESPN', 2.5, 3.2, 0.6, 45, 0.6),
            BacktestResult('Yahoo', 2.3, 3.0, 0.7, 48, 0.7),
        ]
        
        aggregator.run_backtesting = Mock(return_value=backtest_results)
        
        weights = aggregator.optimize_source_weights(2024)
        
        assert isinstance(weights, dict)
        assert len(weights) == 3
        assert abs(sum(weights.values()) - 1.0) < 1e-10  # Sum to 1.0
        
        # Higher accuracy should get higher weight
        assert weights['FantasyPros'] > weights['ESPN']
        assert weights['Yahoo'] > weights['ESPN']
    
    def test_optimize_source_weights_no_backtest_data(self, aggregator, mock_db):
        """Test weight optimization with no backtesting data."""
        # Mock empty backtesting results
        aggregator.run_backtesting = Mock(return_value=[])
        
        # Mock database query for sources
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.distinct.return_value = mock_query
        mock_query.all.return_value = [('FantasyPros',), ('ESPN',), ('Yahoo',)]
        mock_db.query.return_value = mock_query
        
        weights = aggregator.optimize_source_weights(2024)
        
        assert isinstance(weights, dict)
        assert len(weights) == 3
        assert abs(sum(weights.values()) - 1.0) < 1e-10  # Sum to 1.0
        
        # Should be equal weights
        for weight in weights.values():
            assert abs(weight - 1.0/3) < 1e-10
    
    def test_run_backtesting(self, aggregator, mock_db):
        """Test backtesting execution."""
        # Mock sources query
        sources_query = Mock()
        sources_query.filter.return_value = sources_query
        sources_query.distinct.return_value = sources_query
        sources_query.all.return_value = [('FantasyPros',), ('ESPN',)]
        
        mock_db.query.return_value = sources_query
        
        # Mock individual source backtesting
        aggregator._backtest_source = Mock(side_effect=[
            BacktestResult('FantasyPros', 2.1, 2.8, 0.8, 50, 0.8),
            BacktestResult('ESPN', 2.5, 3.2, 0.6, 45, 0.6)
        ])
        
        results = aggregator.run_backtesting(2024)
        
        assert len(results) == 2
        assert all(isinstance(r, BacktestResult) for r in results)
        assert results[0].source == 'FantasyPros'
        assert results[1].source == 'ESPN'
    
    def test_backtest_source_insufficient_data(self, aggregator, mock_db):
        """Test backtesting with insufficient data."""
        # Mock query returning insufficient projections
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [Mock(spec=Projection)]  # Only 1 projection
        mock_db.query.return_value = mock_query
        
        result = aggregator._backtest_source('FantasyPros', 2024, 8, 10)
        
        assert result is None
    
    def test_get_projection_sources(self, aggregator, mock_db):
        """Test getting available projection sources."""
        # Mock database query
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.distinct.return_value = mock_query
        mock_query.all.return_value = [('FantasyPros',), ('ESPN',), ('Yahoo',)]
        mock_db.query.return_value = mock_query
        
        sources = aggregator.get_projection_sources(2024)
        
        assert sources == ['FantasyPros', 'ESPN', 'Yahoo']
    
    def test_get_source_statistics(self, aggregator, mock_db, sample_projections):
        """Test getting source statistics."""
        # Mock database query
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = sample_projections
        mock_db.query.return_value = mock_query
        
        stats = aggregator.get_source_statistics('FantasyPros', 2024)
        
        assert isinstance(stats, dict)
        assert stats['source'] == 'FantasyPros'
        assert stats['season'] == 2024
        assert stats['total_projections'] == 4
        assert 'mean_points' in stats
        assert 'std_dev' in stats
        assert 'min_points' in stats
        assert 'max_points' in stats
    
    def test_refresh_projections_cache(self, aggregator):
        """Test cache refresh functionality."""
        # Set up cache
        aggregator._source_weights_cache = {'2024': {'A': 0.5, 'B': 0.5}}
        aggregator._cache_expiry = datetime.utcnow() + timedelta(hours=1)
        
        aggregator.refresh_projections_cache()
        
        assert aggregator._source_weights_cache == {}
        assert aggregator._cache_expiry is None


class TestProjectionsCacheManager:
    """Test cases for ProjectionsCacheManager class."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()
    
    @pytest.fixture
    def mock_aggregator(self):
        """Mock ProjectionsAggregator."""
        return Mock(spec=ProjectionsAggregator)
    
    @pytest.fixture
    def cache_manager(self, mock_db, mock_aggregator):
        """Create ProjectionsCacheManager instance."""
        return ProjectionsCacheManager(mock_db, mock_aggregator)
    
    @pytest.fixture
    def sample_aggregated_projection(self):
        """Create sample aggregated projection."""
        return AggregatedProjection(
            player_id="player_123",
            week=1,
            season=2024,
            projected_points=Decimal('15.5'),
            confidence_interval=(Decimal('12.0'), Decimal('19.0')),
            variance=Decimal('2.5'),
            source_count=3,
            source_weights={'A': 0.5, 'B': 0.3, 'C': 0.2},
            metadata={'test': True}
        )
    
    def test_get_cached_projection_cache_hit(self, cache_manager, sample_aggregated_projection):
        """Test cache hit scenario."""
        # Set up cache
        cache_key = "player_123|1|2024"
        cache_manager._cache[cache_key] = sample_aggregated_projection
        cache_manager._cache_timestamps[cache_key] = datetime.utcnow()
        
        result = cache_manager.get_cached_projection("player_123", 1, 2024)
        
        assert result == sample_aggregated_projection
        # Should not call aggregator
        cache_manager.aggregator.aggregate_projections.assert_not_called()
    
    def test_get_cached_projection_cache_miss(self, cache_manager, mock_aggregator, sample_aggregated_projection):
        """Test cache miss scenario."""
        # Mock aggregator to return projection
        mock_aggregator.aggregate_projections.return_value = sample_aggregated_projection
        
        result = cache_manager.get_cached_projection("player_123", 1, 2024)
        
        assert result == sample_aggregated_projection
        # Should call aggregator
        mock_aggregator.aggregate_projections.assert_called_once_with(
            player_id="player_123",
            week=1,
            season=2024
        )
        
        # Should be cached now
        cache_key = "player_123|1|2024"
        assert cache_key in cache_manager._cache
        assert cache_key in cache_manager._cache_timestamps
    
    def test_get_cached_projection_expired(self, cache_manager, mock_aggregator, sample_aggregated_projection):
        """Test expired cache entry."""
        # Set up expired cache entry
        cache_key = "player_123|1|2024"
        cache_manager._cache[cache_key] = sample_aggregated_projection
        cache_manager._cache_timestamps[cache_key] = datetime.utcnow() - timedelta(hours=3)
        
        # Mock aggregator to return new projection
        new_projection = AggregatedProjection(
            player_id="player_123",
            week=1,
            season=2024,
            projected_points=Decimal('16.0'),
            confidence_interval=(Decimal('13.0'), Decimal('19.0')),
            variance=Decimal('2.0'),
            source_count=3,
            source_weights={'A': 0.6, 'B': 0.4},
            metadata={'updated': True}
        )
        mock_aggregator.aggregate_projections.return_value = new_projection
        
        result = cache_manager.get_cached_projection("player_123", 1, 2024)
        
        assert result == new_projection
        # Should call aggregator due to expiration
        mock_aggregator.aggregate_projections.assert_called_once()
    
    def test_invalidate_cache_specific_player(self, cache_manager, sample_aggregated_projection):
        """Test cache invalidation for specific player."""
        # Set up cache with multiple entries
        cache_manager._cache["player_123|1|2024"] = sample_aggregated_projection
        cache_manager._cache["player_456|1|2024"] = sample_aggregated_projection
        cache_manager._cache_timestamps["player_123|1|2024"] = datetime.utcnow()
        cache_manager._cache_timestamps["player_456|1|2024"] = datetime.utcnow()
        
        cache_manager.invalidate_cache(player_id="player_123")
        
        assert "player_123|1|2024" not in cache_manager._cache
        assert "player_456|1|2024" in cache_manager._cache
    
    def test_invalidate_cache_specific_week(self, cache_manager, sample_aggregated_projection):
        """Test cache invalidation for specific week."""
        # Set up cache with multiple entries
        cache_manager._cache["player_123|1|2024"] = sample_aggregated_projection
        cache_manager._cache["player_123|2|2024"] = sample_aggregated_projection
        cache_manager._cache_timestamps["player_123|1|2024"] = datetime.utcnow()
        cache_manager._cache_timestamps["player_123|2|2024"] = datetime.utcnow()
        
        cache_manager.invalidate_cache(week=1)
        
        assert "player_123|1|2024" not in cache_manager._cache
        assert "player_123|2|2024" in cache_manager._cache
    
    def test_clear_cache(self, cache_manager, sample_aggregated_projection):
        """Test clearing all cache entries."""
        # Set up cache
        cache_manager._cache["player_123|1|2024"] = sample_aggregated_projection
        cache_manager._cache_timestamps["player_123|1|2024"] = datetime.utcnow()
        
        cache_manager.clear_cache()
        
        assert len(cache_manager._cache) == 0
        assert len(cache_manager._cache_timestamps) == 0
    
    def test_get_cache_stats(self, cache_manager, sample_aggregated_projection):
        """Test cache statistics."""
        # Set up cache with some entries
        now = datetime.utcnow()
        cache_manager._cache["player_123|1|2024"] = sample_aggregated_projection
        cache_manager._cache["player_456|1|2024"] = sample_aggregated_projection
        cache_manager._cache_timestamps["player_123|1|2024"] = now
        cache_manager._cache_timestamps["player_456|1|2024"] = now - timedelta(hours=3)  # Expired
        
        stats = cache_manager.get_cache_stats()
        
        assert stats['total_entries'] == 2
        assert stats['expired_entries'] == 1
        assert stats['active_entries'] == 1
        assert 'oldest_entry' in stats
        assert 'newest_entry' in stats
    
    def test_make_cache_key(self, cache_manager):
        """Test cache key generation."""
        key1 = cache_manager._make_cache_key("player_123", 1, 2024)
        key2 = cache_manager._make_cache_key("player_123", None, 2024)
        
        assert key1 == "player_123|1|2024"
        assert key2 == "player_123|None|2024"
        assert key1 != key2