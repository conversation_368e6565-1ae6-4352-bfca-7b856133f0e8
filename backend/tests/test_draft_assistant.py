"""
Tests for draft assistance service.

Tests tiered player board generation, Monte Carlo simulations,
real-time recommendations, and contingency planning.
"""
import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from backend.app.services.draft_assistant import (
    DraftAssistant,
    DraftStrategy,
    DraftBoard,
    DraftRecommendation,
    DraftScenario,
    PlayerTier
)
from backend.app.models.player import Player, PlayerPosition, InjuryStatus
from backend.app.models.league import League
from backend.app.models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RosterPlayer
from backend.app.models.projection import Projection
from backend.app.models.ranking import Ranking


class TestDraftAssistant:
    """Test cases for DraftAssistant service."""
    
    @pytest.fixture
    def draft_assistant(self, db_session: Session):
        """Create DraftAssistant instance for testing."""
        return DraftAssistant(db_session)
    
    @pytest.fixture
    def sample_league(self, db_session: Session):
        """Create sample league for testing."""
        league = League(
            id="test_league",
            name="Test League",
            season=2024,
            scoring_rules={
                "passing_yards": 0.04,
                "passing_touchdowns": 4,
                "rushing_yards": 0.1,
                "rushing_touchdowns": 6,
                "receiving_yards": 0.1,
                "receiving_touchdowns": 6
            },
            roster_slots=[
                {"position": "QB", "count": 1},
                {"position": "RB", "count": 2},
                {"position": "WR", "count": 2},
                {"position": "TE", "count": 1},
                {"position": "FLEX", "count": 1},
                {"position": "K", "count": 1},
                {"position": "DEF", "count": 1}
            ]
        )
        db_session.add(league)
        db_session.commit()
        return league
    
    @pytest.fixture
    def sample_franchises(self, db_session: Session, sample_league: League):
        """Create sample franchises for testing."""
        franchises = []
        for i in range(12):
            franchise = Franchise(
                id=f"franchise_{i+1}",
                name=f"Team {i+1}",
                owner_name=f"Owner {i+1}",
                league_id=sample_league.id,
                faab_budget=Decimal('100')
            )
            db_session.add(franchise)
            franchises.append(franchise)
        
        db_session.commit()
        return franchises
    
    @pytest.fixture
    def sample_players(self, db_session: Session):
        """Create sample players for testing."""
        players = []
        
        # Create QBs
        for i in range(24):
            player = Player(
                id=f"qb_{i+1}",
                name=f"QB Player {i+1}",
                position=PlayerPosition.QB,
                team=f"TEAM{i%32+1}",
                bye_week=(i % 14) + 1,
                injury_status=InjuryStatus.HEALTHY
            )
            db_session.add(player)
            players.append(player)
        
        # Create RBs
        for i in range(60):
            player = Player(
                id=f"rb_{i+1}",
                name=f"RB Player {i+1}",
                position=PlayerPosition.RB,
                team=f"TEAM{i%32+1}",
                bye_week=(i % 14) + 1,
                injury_status=InjuryStatus.HEALTHY
            )
            db_session.add(player)
            players.append(player)
        
        # Create WRs
        for i in range(80):
            player = Player(
                id=f"wr_{i+1}",
                name=f"WR Player {i+1}",
                position=PlayerPosition.WR,
                team=f"TEAM{i%32+1}",
                bye_week=(i % 14) + 1,
                injury_status=InjuryStatus.HEALTHY
            )
            db_session.add(player)
            players.append(player)
        
        # Create TEs
        for i in range(24):
            player = Player(
                id=f"te_{i+1}",
                name=f"TE Player {i+1}",
                position=PlayerPosition.TE,
                team=f"TEAM{i%32+1}",
                bye_week=(i % 14) + 1,
                injury_status=InjuryStatus.HEALTHY
            )
            db_session.add(player)
            players.append(player)
        
        # Create Kickers
        for i in range(32):
            player = Player(
                id=f"k_{i+1}",
                name=f"K Player {i+1}",
                position=PlayerPosition.K,
                team=f"TEAM{i%32+1}",
                bye_week=(i % 14) + 1,
                injury_status=InjuryStatus.HEALTHY
            )
            db_session.add(player)
            players.append(player)
        
        # Create Defenses
        for i in range(32):
            player = Player(
                id=f"def_{i+1}",
                name=f"DEF Player {i+1}",
                position=PlayerPosition.DEF,
                team=f"TEAM{i+1}",
                bye_week=(i % 14) + 1,
                injury_status=InjuryStatus.HEALTHY
            )
            db_session.add(player)
            players.append(player)
        
        db_session.commit()
        return players
    
    @pytest.fixture
    def sample_projections(self, db_session: Session, sample_players: list):
        """Create sample projections for testing."""
        projections = []
        
        # Create projections from multiple sources to avoid aggregation warnings
        sources = ["test_source_1", "test_source_2"]
        
        for player in sample_players:
            # Create season-long projections with realistic values
            if player.position == PlayerPosition.QB:
                base_points = Decimal('300') - (len([p for p in projections if 'qb_' in p.player_id and p.source == sources[0]]) * Decimal('10'))
            elif player.position == PlayerPosition.RB:
                base_points = Decimal('250') - (len([p for p in projections if 'rb_' in p.player_id and p.source == sources[0]]) * Decimal('3'))
            elif player.position == PlayerPosition.WR:
                base_points = Decimal('200') - (len([p for p in projections if 'wr_' in p.player_id and p.source == sources[0]]) * Decimal('2'))
            elif player.position == PlayerPosition.TE:
                base_points = Decimal('150') - (len([p for p in projections if 'te_' in p.player_id and p.source == sources[0]]) * Decimal('5'))
            elif player.position == PlayerPosition.K:
                base_points = Decimal('120') - (len([p for p in projections if 'k_' in p.player_id and p.source == sources[0]]) * Decimal('1'))
            else:  # DEF
                base_points = Decimal('100') - (len([p for p in projections if 'def_' in p.player_id and p.source == sources[0]]) * Decimal('1'))
            
            # Create projections from multiple sources
            for i, source in enumerate(sources):
                # Add some variation between sources
                variation = Decimal('5') if i == 1 else Decimal('0')
                projected_points = max(Decimal('50'), base_points + variation)
                
                projection = Projection(
                    id=f"proj_{player.id}_{source}",
                    player_id=player.id,
                    week=None,  # Season-long
                    season=2024,
                    source=source,
                    projected_points=projected_points,
                    confidence_level=Decimal('0.8'),
                    stats={"test_stat": 100}
                )
                db_session.add(projection)
                projections.append(projection)
        
        db_session.commit()
        return projections
    
    def test_generate_draft_board_basic(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_players: list,
        sample_projections: list
    ):
        """Test basic draft board generation."""
        draft_board = draft_assistant.generate_draft_board(
            league_id=sample_league.id,
            season=2024,
            strategy=DraftStrategy.VALUE_BASED
        )
        
        assert isinstance(draft_board, DraftBoard)
        assert len(draft_board.tiers) > 0
        assert len(draft_board.overall_rankings) > 0
        assert len(draft_board.position_rankings) > 0
        assert len(draft_board.value_over_replacement) > 0
        assert draft_board.strategy == DraftStrategy.VALUE_BASED
        
        # Check that tiers are properly structured
        for tier in draft_board.tiers:
            assert isinstance(tier, PlayerTier)
            assert tier.tier_number > 0
            assert len(tier.players) > 0
            assert tier.min_value <= tier.max_value
        
        # Check that all positions are represented
        positions_in_rankings = set(draft_board.position_rankings.keys())
        expected_positions = {PlayerPosition.QB, PlayerPosition.RB, PlayerPosition.WR, 
                            PlayerPosition.TE, PlayerPosition.K, PlayerPosition.DEF}
        assert positions_in_rankings == expected_positions
    
    def test_generate_draft_board_caching(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_players: list,
        sample_projections: list
    ):
        """Test draft board caching functionality."""
        # Generate board first time
        board1 = draft_assistant.generate_draft_board(
            league_id=sample_league.id,
            season=2024,
            strategy=DraftStrategy.VALUE_BASED
        )
        
        # Generate board second time (should use cache)
        board2 = draft_assistant.generate_draft_board(
            league_id=sample_league.id,
            season=2024,
            strategy=DraftStrategy.VALUE_BASED
        )
        
        # Should be the same object from cache
        assert board1.last_updated == board2.last_updated
        assert board1.overall_rankings == board2.overall_rankings
        
        # Force refresh should create new board
        board3 = draft_assistant.generate_draft_board(
            league_id=sample_league.id,
            season=2024,
            strategy=DraftStrategy.VALUE_BASED,
            force_refresh=True
        )
        
        assert board3.last_updated >= board1.last_updated
    
    def test_get_draft_recommendation(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_franchises: list,
        sample_players: list,
        sample_projections: list
    ):
        """Test getting draft recommendations."""
        franchise = sample_franchises[0]
        available_players = [p.id for p in sample_players[:50]]  # First 50 players available
        
        recommendation = draft_assistant.get_draft_recommendation(
            league_id=sample_league.id,
            franchise_id=franchise.id,
            available_players=available_players,
            current_pick=1,
            season=2024,
            strategy=DraftStrategy.VALUE_BASED
        )
        
        assert isinstance(recommendation, DraftRecommendation)
        assert recommendation.player_id in available_players
        assert recommendation.player_name is not None
        assert recommendation.position in PlayerPosition
        assert recommendation.projected_points >= 0
        assert recommendation.value_over_replacement >= 0
        assert recommendation.tier > 0
        assert 0 <= recommendation.confidence <= 1
        assert len(recommendation.rationale) > 0
        assert len(recommendation.alternatives) >= 0
    
    def test_get_draft_recommendation_with_roster(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_franchises: list,
        sample_players: list,
        sample_projections: list,
        db_session: Session
    ):
        """Test draft recommendations with existing roster."""
        franchise = sample_franchises[0]
        
        # Create roster with some players
        roster = Roster(
            id=f"roster_{franchise.id}",
            franchise_id=franchise.id
        )
        db_session.add(roster)
        
        # Add a QB to roster
        qb_player = next(p for p in sample_players if p.position == PlayerPosition.QB)
        roster_player = RosterPlayer(
            id=f"rp_{qb_player.id}",
            roster_id=roster.id,
            player_id=qb_player.id,
            is_active=True
        )
        db_session.add(roster_player)
        db_session.commit()
        
        # Get available players (excluding rostered QB)
        available_players = [p.id for p in sample_players if p.id != qb_player.id][:50]
        
        recommendation = draft_assistant.get_draft_recommendation(
            league_id=sample_league.id,
            franchise_id=franchise.id,
            available_players=available_players,
            current_pick=2,
            season=2024,
            strategy=DraftStrategy.POSITIONAL_NEED
        )
        
        assert isinstance(recommendation, DraftRecommendation)
        assert recommendation.player_id != qb_player.id
        assert recommendation.positional_need_score >= 0
    
    def test_monte_carlo_simulation(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_franchises: list,
        sample_players: list,
        sample_projections: list
    ):
        """Test Monte Carlo draft simulation."""
        franchise = sample_franchises[0]
        
        scenarios = draft_assistant.run_monte_carlo_simulation(
            league_id=sample_league.id,
            franchise_id=franchise.id,
            num_simulations=10,  # Small number for testing
            season=2024,
            strategy=DraftStrategy.VALUE_BASED
        )
        
        assert len(scenarios) == 10
        
        for scenario in scenarios:
            assert isinstance(scenario, DraftScenario)
            assert len(scenario.picks) > 0
            assert scenario.projected_points >= 0
            assert 0 <= scenario.win_probability <= 1
            assert scenario.strategy_score >= 0
            
            # Check that picks are properly structured
            for pick in scenario.picks:
                assert pick.round_number > 0
                assert pick.pick_number > 0
                assert pick.overall_pick > 0
                assert pick.franchise_id in [f.id for f in sample_franchises]
    
    def test_generate_contingency_plans(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_franchises: list,
        sample_players: list,
        sample_projections: list
    ):
        """Test contingency plan generation."""
        franchise = sample_franchises[0]
        target_players = [p.id for p in sample_players[:5]]  # First 5 players as targets
        
        contingency_plans = draft_assistant.generate_contingency_plans(
            league_id=sample_league.id,
            franchise_id=franchise.id,
            target_players=target_players,
            season=2024
        )
        
        assert isinstance(contingency_plans, dict)
        assert len(contingency_plans) > 0
        
        # Should have plans for with/without each target player
        expected_scenarios = len(target_players) * 2  # with and without each
        assert len(contingency_plans) <= expected_scenarios
        
        for scenario_name, recommendations in contingency_plans.items():
            assert isinstance(scenario_name, str)
            assert isinstance(recommendations, list)
            assert len(recommendations) > 0
            
            for rec in recommendations:
                assert isinstance(rec, DraftRecommendation)
    
    def test_update_draft_board_real_time(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_franchises: list,
        sample_players: list,
        sample_projections: list,
        db_session: Session
    ):
        """Test real-time draft board updates."""
        # Generate initial board
        initial_board = draft_assistant.generate_draft_board(
            league_id=sample_league.id,
            season=2024,
            strategy=DraftStrategy.VALUE_BASED
        )
        
        # Pick a player
        picked_player_id = initial_board.overall_rankings[0]
        
        # Simulate the pick by adding player to a roster
        franchise = sample_franchises[0]
        roster = Roster(
            id=f"roster_update_{franchise.id}",
            franchise_id=franchise.id
        )
        db_session.add(roster)
        
        roster_player = RosterPlayer(
            id=f"rp_update_{picked_player_id}",
            roster_id=roster.id,
            player_id=picked_player_id,
            is_active=True
        )
        db_session.add(roster_player)
        db_session.commit()
        
        # Update board after pick
        updated_board = draft_assistant.update_draft_board_real_time(
            league_id=sample_league.id,
            picked_player_id=picked_player_id,
            season=2024
        )
        
        assert isinstance(updated_board, DraftBoard)
        assert updated_board.last_updated >= initial_board.last_updated
        
        # Picked player should not be in updated rankings
        assert picked_player_id not in updated_board.overall_rankings
    
    def test_different_draft_strategies(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_players: list,
        sample_projections: list
    ):
        """Test different draft strategies produce different results."""
        value_board = draft_assistant.generate_draft_board(
            league_id=sample_league.id,
            season=2024,
            strategy=DraftStrategy.VALUE_BASED
        )
        
        positional_board = draft_assistant.generate_draft_board(
            league_id=sample_league.id,
            season=2024,
            strategy=DraftStrategy.POSITIONAL_NEED,
            force_refresh=True
        )
        
        # Boards should have same players but potentially different orders
        assert set(value_board.overall_rankings) == set(positional_board.overall_rankings)
        assert value_board.strategy != positional_board.strategy
    
    def test_replacement_level_calculation(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_players: list,
        sample_projections: list
    ):
        """Test replacement level calculation."""
        available_players = draft_assistant._get_available_players(sample_league.id, 2024)
        replacement_levels = draft_assistant._calculate_replacement_levels(
            sample_league, available_players, 2024
        )
        
        assert isinstance(replacement_levels, dict)
        assert len(replacement_levels) > 0
        
        # All positions should have replacement levels
        for position in PlayerPosition:
            assert position in replacement_levels
            assert replacement_levels[position] >= 0
        
        # All positions should have non-negative replacement levels
        # The specific ordering may vary based on the test data setup
        for position in PlayerPosition:
            assert replacement_levels[position] >= 0
    
    def test_value_over_replacement_calculation(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_players: list,
        sample_projections: list
    ):
        """Test value over replacement calculation."""
        available_players = draft_assistant._get_available_players(sample_league.id, 2024)
        replacement_levels = draft_assistant._calculate_replacement_levels(
            sample_league, available_players, 2024
        )
        vor_values = draft_assistant._calculate_value_over_replacement(
            available_players, replacement_levels, 2024
        )
        
        assert isinstance(vor_values, dict)
        assert len(vor_values) > 0
        
        # All available players should have VOR values
        for player in available_players:
            assert player.id in vor_values
            assert vor_values[player.id] >= 0
        
        # Top players should have higher VOR
        sorted_players = sorted(vor_values.items(), key=lambda x: x[1], reverse=True)
        top_vor = sorted_players[0][1]
        bottom_vor = sorted_players[-1][1]
        assert top_vor >= bottom_vor
    
    def test_player_tier_generation(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_players: list,
        sample_projections: list
    ):
        """Test player tier generation."""
        available_players = draft_assistant._get_available_players(sample_league.id, 2024)
        replacement_levels = draft_assistant._calculate_replacement_levels(
            sample_league, available_players, 2024
        )
        vor_values = draft_assistant._calculate_value_over_replacement(
            available_players, replacement_levels, 2024
        )
        
        tiers = draft_assistant._generate_player_tiers(vor_values, available_players)
        
        assert isinstance(tiers, list)
        assert len(tiers) > 0
        
        # Tiers should be properly structured
        for tier in tiers:
            assert isinstance(tier, PlayerTier)
            assert tier.tier_number > 0
            assert len(tier.players) > 0
            assert tier.min_value <= tier.max_value
            assert tier.avg_value >= tier.min_value
            assert tier.avg_value <= tier.max_value
        
        # Tier numbers should be sequential
        tier_numbers = [t.tier_number for t in tiers]
        assert tier_numbers == sorted(set(tier_numbers))
    
    def test_error_handling(
        self,
        draft_assistant: DraftAssistant,
        sample_league: League
    ):
        """Test error handling for invalid inputs."""
        # Test with non-existent league
        with pytest.raises(ValueError):
            draft_assistant.generate_draft_board(
                league_id="non_existent_league",
                season=2024
            )
        
        # Test with empty available players list
        with pytest.raises(ValueError):
            draft_assistant.get_draft_recommendation(
                league_id=sample_league.id,
                franchise_id="franchise_1",
                available_players=[],
                current_pick=1
            )
    
    @patch('backend.app.services.draft_assistant.random.choice')
    @patch('backend.app.services.draft_assistant.random.random')
    def test_monte_carlo_deterministic(
        self,
        mock_random,
        mock_choice,
        draft_assistant: DraftAssistant,
        sample_league: League,
        sample_franchises: list,
        sample_players: list,
        sample_projections: list
    ):
        """Test Monte Carlo simulation with mocked randomness for deterministic results."""
        # Mock random functions to return predictable values
        mock_random.return_value = 0.5  # Always return middle value
        mock_choice.side_effect = lambda x: x[0] if x else None  # Always return first choice
        
        franchise = sample_franchises[0]
        
        scenarios = draft_assistant.run_monte_carlo_simulation(
            league_id=sample_league.id,
            franchise_id=franchise.id,
            num_simulations=2,
            season=2024,
            strategy=DraftStrategy.VALUE_BASED
        )
        
        assert len(scenarios) == 2
        
        # With mocked randomness, scenarios should be similar
        scenario1, scenario2 = scenarios
        assert len(scenario1.picks) == len(scenario2.picks)
        assert scenario1.projected_points > 0
        assert scenario2.projected_points > 0