"""
Unit tests for database models.
"""
import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.models.base import Base
from app.models import (
    League, Player, PlayerPosition, InjuryStatus, Franchise, Roster, 
    RosterPlayer, Projection, Recommendation, RecommendationType, 
    RecommendationPriority, RecommendationStatus
)


@pytest.fixture
def db_session():
    """Create a test database session."""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    Base.metadata.create_all(engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    yield session
    
    session.close()


@pytest.fixture
def sample_league(db_session):
    """Create a sample league for testing."""
    league = League(
        id="test_league",
        name="Test League",
        season=2024,
        scoring_rules={
            "passing_yards": 0.04,
            "rushing_yards": 0.1,
            "touchdowns": 6
        },
        roster_slots=[
            {"position": "QB", "count": 1},
            {"position": "RB", "count": 2}
        ]
    )
    db_session.add(league)
    db_session.commit()
    return league


@pytest.fixture
def sample_player(db_session):
    """Create a sample player for testing."""
    player = Player(
        id="test_player",
        name="Test Player",
        position=PlayerPosition.QB,
        team="KC",
        bye_week=10
    )
    db_session.add(player)
    db_session.commit()
    return player


class TestLeague:
    """Test cases for League model."""
    
    def test_league_creation(self, db_session):
        """Test basic league creation."""
        league = League(
            id="test_league",
            name="Test League",
            season=2024,
            scoring_rules={"passing_yards": 0.04},
            roster_slots=[{"position": "QB", "count": 1}]
        )
        
        db_session.add(league)
        db_session.commit()
        
        assert league.id == "test_league"
        assert league.name == "Test League"
        assert league.season == 2024
        assert league.is_active is True
        assert league.created_at is not None
        assert league.updated_at is not None
    
    def test_league_scoring_rules_validation(self, sample_league):
        """Test scoring rules validation."""
        # Valid scoring rules
        sample_league.scoring_rules = {
            "passing_yards": 0.04,
            "rushing_yards": 0.1,
            "receiving_yards": 0.1,
            "touchdowns": 6
        }
        assert sample_league.validate_scoring_rules() is True
        
        # Invalid scoring rules (missing required fields)
        sample_league.scoring_rules = {"passing_yards": 0.04}
        assert sample_league.validate_scoring_rules() is False
    
    def test_get_roster_slot_count(self, sample_league):
        """Test roster slot count calculation."""
        sample_league.roster_slots = [
            {"position": "QB", "count": 1},
            {"position": "RB", "count": 2},
            {"position": "WR", "count": 3}
        ]
        
        assert sample_league.get_roster_slot_count("QB") == 1
        assert sample_league.get_roster_slot_count("RB") == 2
        assert sample_league.get_roster_slot_count("WR") == 3
        assert sample_league.get_roster_slot_count("TE") == 0


class TestPlayer:
    """Test cases for Player model."""
    
    def test_player_creation(self, db_session):
        """Test basic player creation."""
        player = Player(
            id="test_player",
            name="Test Player",
            position=PlayerPosition.QB,
            team="KC"
        )
        
        db_session.add(player)
        db_session.commit()
        
        assert player.id == "test_player"
        assert player.name == "Test Player"
        assert player.position == PlayerPosition.QB
        assert player.team == "KC"
        assert player.injury_status == InjuryStatus.HEALTHY
        assert player.metadata == {}
    
    def test_player_availability(self, sample_player):
        """Test player availability checking."""
        # Player should be available initially
        assert sample_player.is_available() is True
        
        # Player should not be available when on a roster
        # This would be tested with roster relationships
    
    def test_player_position_enum(self, db_session):
        """Test player position enumeration."""
        player = Player(
            id="test_qb",
            name="Test QB",
            position=PlayerPosition.QB,
            team="KC"
        )
        
        assert player.position == PlayerPosition.QB
        assert player.position.value == "QB"


class TestFranchise:
    """Test cases for Franchise model."""
    
    def test_franchise_creation(self, db_session, sample_league):
        """Test basic franchise creation."""
        franchise = Franchise(
            id="test_franchise",
            name="Test Team",
            owner_name="Test Owner",
            league_id=sample_league.id,
            faab_budget=Decimal("100.00")
        )
        
        db_session.add(franchise)
        db_session.commit()
        
        assert franchise.id == "test_franchise"
        assert franchise.name == "Test Team"
        assert franchise.owner_name == "Test Owner"
        assert franchise.league_id == sample_league.id
        assert franchise.faab_budget == Decimal("100.00")
        assert franchise.faab_spent == Decimal("0")
    
    def test_remaining_faab_calculation(self, db_session, sample_league):
        """Test FAAB budget calculation."""
        franchise = Franchise(
            id="test_franchise",
            name="Test Team",
            owner_name="Test Owner",
            league_id=sample_league.id,
            faab_budget=Decimal("100.00"),
            faab_spent=Decimal("25.50")
        )
        
        assert franchise.get_remaining_faab() == Decimal("74.50")
        
        # Test with no budget
        franchise.faab_budget = None
        assert franchise.get_remaining_faab() == Decimal("0")


class TestRoster:
    """Test cases for Roster and RosterPlayer models."""
    
    def test_roster_creation(self, db_session, sample_league):
        """Test basic roster creation."""
        franchise = Franchise(
            id="test_franchise",
            name="Test Team",
            owner_name="Test Owner",
            league_id=sample_league.id
        )
        db_session.add(franchise)
        
        roster = Roster(
            id="test_roster",
            franchise_id=franchise.id
        )
        db_session.add(roster)
        db_session.commit()
        
        assert roster.id == "test_roster"
        assert roster.franchise_id == franchise.id
        assert roster.is_valid is True
    
    def test_roster_player_assignment(self, db_session, sample_league, sample_player):
        """Test player assignment to roster."""
        franchise = Franchise(
            id="test_franchise",
            name="Test Team",
            owner_name="Test Owner",
            league_id=sample_league.id
        )
        db_session.add(franchise)
        
        roster = Roster(
            id="test_roster",
            franchise_id=franchise.id
        )
        db_session.add(roster)
        
        roster_player = RosterPlayer(
            id="test_rp",
            roster_id=roster.id,
            player_id=sample_player.id,
            roster_slot="QB",
            is_keeper=True,
            keeper_cost=3
        )
        db_session.add(roster_player)
        db_session.commit()
        
        assert roster_player.roster_id == roster.id
        assert roster_player.player_id == sample_player.id
        assert roster_player.roster_slot == "QB"
        assert roster_player.is_keeper is True
        assert roster_player.keeper_cost == 3
        assert roster_player.is_starting() is True
    
    def test_roster_player_bench_assignment(self, db_session, sample_league, sample_player):
        """Test bench player assignment."""
        franchise = Franchise(
            id="test_franchise",
            name="Test Team", 
            owner_name="Test Owner",
            league_id=sample_league.id
        )
        db_session.add(franchise)
        
        roster = Roster(
            id="test_roster",
            franchise_id=franchise.id
        )
        db_session.add(roster)
        
        roster_player = RosterPlayer(
            id="test_rp",
            roster_id=roster.id,
            player_id=sample_player.id,
            roster_slot="BENCH"
        )
        db_session.add(roster_player)
        db_session.commit()
        
        assert roster_player.is_starting() is False


class TestProjection:
    """Test cases for Projection model."""
    
    def test_projection_creation(self, db_session, sample_player):
        """Test basic projection creation."""
        projection = Projection(
            id="test_projection",
            player_id=sample_player.id,
            week=1,
            season=2024,
            source="TestSource",
            projected_points=Decimal("18.5"),
            confidence_level=Decimal("0.85")
        )
        
        db_session.add(projection)
        db_session.commit()
        
        assert projection.id == "test_projection"
        assert projection.player_id == sample_player.id
        assert projection.week == 1
        assert projection.season == 2024
        assert projection.source == "TestSource"
        assert projection.projected_points == Decimal("18.5")
        assert projection.confidence_level == Decimal("0.85")
    
    def test_season_long_projection(self, db_session, sample_player):
        """Test season-long projection."""
        projection = Projection(
            id="test_season_projection",
            player_id=sample_player.id,
            week=None,  # Season-long
            season=2024,
            source="TestSource",
            projected_points=Decimal("285.6")
        )
        
        db_session.add(projection)
        db_session.commit()
        
        assert projection.is_season_long() is True
        assert projection.week is None
    
    def test_confidence_interval(self, db_session, sample_player):
        """Test confidence interval handling."""
        projection = Projection(
            id="test_projection",
            player_id=sample_player.id,
            season=2024,
            source="TestSource",
            projected_points=Decimal("18.5"),
            floor=Decimal("12.0"),
            ceiling=Decimal("25.0")
        )
        
        interval = projection.get_confidence_interval()
        assert interval == (Decimal("12.0"), Decimal("25.0"))
        
        # Test updating confidence interval
        projection.update_confidence_interval(Decimal("10.0"), Decimal("27.0"))
        assert projection.floor == Decimal("10.0")
        assert projection.ceiling == Decimal("27.0")
        assert projection.variance is not None


class TestRecommendation:
    """Test cases for Recommendation model."""
    
    def test_recommendation_creation(self, db_session, sample_league):
        """Test basic recommendation creation."""
        franchise = Franchise(
            id="test_franchise",
            name="Test Team",
            owner_name="Test Owner",
            league_id=sample_league.id
        )
        db_session.add(franchise)
        
        recommendation = Recommendation(
            id="test_recommendation",
            league_id=sample_league.id,
            franchise_id=franchise.id,
            type=RecommendationType.KEEPER,
            title="Test Recommendation",
            description="Test description",
            rationale="Test rationale",
            confidence=Decimal("0.85")
        )
        
        db_session.add(recommendation)
        db_session.commit()
        
        assert recommendation.id == "test_recommendation"
        assert recommendation.type == RecommendationType.KEEPER
        assert recommendation.priority == RecommendationPriority.MEDIUM
        assert recommendation.status == RecommendationStatus.ACTIVE
        assert recommendation.confidence == Decimal("0.85")
    
    def test_recommendation_expiration(self, db_session, sample_league):
        """Test recommendation expiration logic."""
        recommendation = Recommendation(
            id="test_recommendation",
            league_id=sample_league.id,
            type=RecommendationType.LINEUP,
            title="Test Recommendation",
            description="Test description",
            rationale="Test rationale",
            confidence=Decimal("0.75"),
            expires_at=datetime.now() - timedelta(hours=1)  # Expired
        )
        
        assert recommendation.is_expired() is True
        
        # Test non-expired recommendation
        recommendation.expires_at = datetime.now() + timedelta(hours=1)
        assert recommendation.is_expired() is False
    
    def test_recommendation_urgency(self, db_session, sample_league):
        """Test recommendation urgency logic."""
        recommendation = Recommendation(
            id="test_recommendation",
            league_id=sample_league.id,
            type=RecommendationType.WAIVER,
            title="Test Recommendation",
            description="Test description",
            rationale="Test rationale",
            confidence=Decimal("0.75"),
            deadline_at=datetime.now() + timedelta(minutes=30)  # Urgent
        )
        
        assert recommendation.is_urgent() is True
        
        # Test non-urgent recommendation
        recommendation.deadline_at = datetime.now() + timedelta(hours=5)
        assert recommendation.is_urgent() is False
    
    def test_recommendation_alternatives(self, db_session, sample_league):
        """Test recommendation alternatives handling."""
        recommendation = Recommendation(
            id="test_recommendation",
            league_id=sample_league.id,
            type=RecommendationType.TRADE,
            title="Test Recommendation",
            description="Test description",
            rationale="Test rationale",
            confidence=Decimal("0.75")
        )
        
        # Add alternative
        alternative = {
            "title": "Alternative option",
            "rationale": "Alternative rationale",
            "confidence": 0.65
        }
        recommendation.add_alternative(alternative)
        
        assert len(recommendation.alternatives) == 1
        assert recommendation.get_primary_alternative() == alternative
    
    def test_recommendation_status_updates(self, db_session, sample_league):
        """Test recommendation status updates."""
        recommendation = Recommendation(
            id="test_recommendation",
            league_id=sample_league.id,
            type=RecommendationType.DRAFT,
            title="Test Recommendation",
            description="Test description",
            rationale="Test rationale",
            confidence=Decimal("0.80")
        )
        
        # Test acceptance
        recommendation.mark_accepted()
        assert recommendation.status == RecommendationStatus.ACCEPTED
        
        # Test rejection
        recommendation.mark_rejected()
        assert recommendation.status == RecommendationStatus.REJECTED
    
    def test_confidence_update(self, db_session, sample_league):
        """Test confidence level updates."""
        recommendation = Recommendation(
            id="test_recommendation",
            league_id=sample_league.id,
            type=RecommendationType.LINEUP,
            title="Test Recommendation",
            description="Test description",
            rationale="Test rationale",
            confidence=Decimal("0.75")
        )
        
        # Test valid confidence update
        recommendation.update_confidence(Decimal("0.90"))
        assert recommendation.confidence == Decimal("0.90")
        
        # Test boundary conditions
        recommendation.update_confidence(Decimal("1.5"))  # Should cap at 1.0
        assert recommendation.confidence == Decimal("1.0")
        
        recommendation.update_confidence(Decimal("-0.1"))  # Should floor at 0.0
        assert recommendation.confidence == Decimal("0.0")