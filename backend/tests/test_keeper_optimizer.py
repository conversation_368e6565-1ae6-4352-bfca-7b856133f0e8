"""
Unit tests for the keeper optimization service.
"""
import pytest
from decimal import Decimal
from datetime import datetime
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.services.keeper_optimizer import (
    KeeperOptimizer, KeeperCandidate, KeeperRecommendation, 
    KeeperScenario, KeeperConstraintType
)
from app.models.league import League
from app.models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RosterPlayer
from app.models.player import Player, PlayerPosition, InjuryStatus
from app.models.projection import Projection
from app.services.projections_aggregator import AggregatedProjection


class TestKeeperOptimizer:
    """Test cases for the KeeperOptimizer service."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def mock_projections_aggregator(self):
        """Create a mock projections aggregator."""
        return Mock()
    
    @pytest.fixture
    def keeper_optimizer(self, mock_db, mock_projections_aggregator):
        """Create a KeeperOptimizer instance with mocked dependencies."""
        optimizer = KeeperOptimizer(mock_db)
        optimizer.projections_aggregator = mock_projections_aggregator
        return optimizer
    
    @pytest.fixture
    def sample_league(self):
        """Create a sample league with keeper rules."""
        return League(
            id="test_league",
            name="Test League",
            season=2024,
            keeper_rules={
                "max_keepers": 3,
                "round_escalation": 1,
                "max_round": 16,
                "position_limits": {"QB": 1, "RB": 2, "WR": 2, "TE": 1},
                "salary_cap": None,
                "no_injured_keepers": False,
                "no_traded_players": False,
                "min_ownership_weeks": 0
            },
            roster_slots=[
                {"position": "QB", "count": 1},
                {"position": "RB", "count": 2},
                {"position": "WR", "count": 2},
                {"position": "TE", "count": 1},
                {"position": "FLEX", "count": 1, "positions": ["RB", "WR", "TE"]},
                {"position": "K", "count": 1},
                {"position": "DEF", "count": 1}
            ]
        )
    
    @pytest.fixture
    def sample_franchise(self, sample_league):
        """Create a sample franchise."""
        franchise = Franchise(
            id="test_franchise",
            name="Test Team",
            owner_name="Test Owner",
            league_id=sample_league.id,
            faab_budget=Decimal('100'),
            faab_spent=Decimal('25')
        )
        franchise.league = sample_league
        return franchise
    
    @pytest.fixture
    def sample_players(self):
        """Create sample players."""
        return [
            Player(
                id="player1",
                name="Elite QB",
                position=PlayerPosition.QB,
                team="KC",
                bye_week=10,
                injury_status=InjuryStatus.HEALTHY
            ),
            Player(
                id="player2",
                name="Top RB",
                position=PlayerPosition.RB,
                team="SF",
                bye_week=9,
                injury_status=InjuryStatus.HEALTHY
            ),
            Player(
                id="player3",
                name="WR1",
                position=PlayerPosition.WR,
                team="BUF",
                bye_week=7,
                injury_status=InjuryStatus.QUESTIONABLE
            ),
            Player(
                id="player4",
                name="Injured RB",
                position=PlayerPosition.RB,
                team="LAR",
                bye_week=6,
                injury_status=InjuryStatus.OUT
            )
        ]
    
    @pytest.fixture
    def sample_roster_players(self, sample_players):
        """Create sample roster players."""
        return [
            RosterPlayer(
                id="rp1",
                roster_id="test_roster",
                player_id="player1",
                keeper_cost=5,
                is_keeper=False,
                is_active=True
            ),
            RosterPlayer(
                id="rp2",
                roster_id="test_roster",
                player_id="player2",
                keeper_cost=8,
                is_keeper=False,
                is_active=True
            ),
            RosterPlayer(
                id="rp3",
                roster_id="test_roster",
                player_id="player3",
                keeper_cost=12,
                is_keeper=False,
                is_active=True
            ),
            RosterPlayer(
                id="rp4",
                roster_id="test_roster",
                player_id="player4",
                keeper_cost=10,
                is_keeper=False,
                is_active=True
            )
        ]
    
    @pytest.fixture
    def sample_roster(self, sample_franchise, sample_roster_players, sample_players):
        """Create a sample roster with players."""
        roster = Roster(
            id="test_roster",
            franchise_id=sample_franchise.id,
            is_valid=True
        )
        roster.franchise = sample_franchise
        roster.roster_players = sample_roster_players
        
        # Link players to roster players
        for rp, player in zip(sample_roster_players, sample_players):
            rp.player = player
            rp.roster = roster
        
        sample_franchise.roster = roster
        return roster
    
    def test_calculate_replacement_levels(self, keeper_optimizer, mock_db, sample_league):
        """Test replacement level calculation."""
        # Mock the league query
        mock_db.query.return_value.filter.return_value.first.return_value = sample_league
        
        # Mock aggregated projections
        mock_projections = [
            AggregatedProjection(
                player_id=f"player{i}",
                week=None,
                season=2024,
                projected_points=Decimal(str(300 - i * 10)),
                confidence_interval=(Decimal('250'), Decimal('350')),
                variance=Decimal('100'),
                source_count=3,
                source_weights={},
                metadata={}
            )
            for i in range(20)  # 20 players
        ]
        
        keeper_optimizer.projections_aggregator.aggregate_all_projections.return_value = mock_projections
        
        # Mock player queries for position mapping
        mock_players = []
        for i, position in enumerate([PlayerPosition.QB] * 5 + [PlayerPosition.RB] * 8 + [PlayerPosition.WR] * 7):
            player = Mock()
            player.position = position
            mock_players.append(player)
        
        def mock_player_query(player_id):
            index = int(player_id.replace('player', ''))
            return mock_players[index] if index < len(mock_players) else None
        
        mock_db.query.return_value.filter.return_value.first.side_effect = lambda: mock_player_query("player0")
        
        # Mock the player query in the loop
        with patch.object(keeper_optimizer.db, 'query') as mock_query:
            mock_query.return_value.filter.return_value.first.side_effect = mock_player_query
            
            replacement_levels = keeper_optimizer.calculate_replacement_levels("test_league", 2024)
        
        # Verify replacement levels were calculated
        assert isinstance(replacement_levels, dict)
        assert len(replacement_levels) > 0
        
        # Verify cache was updated
        cache_key = "test_league_2024"
        assert cache_key in keeper_optimizer._replacement_levels_cache
    
    def test_get_keeper_candidates(self, keeper_optimizer, mock_db, sample_franchise, sample_roster):
        """Test getting keeper candidates for a franchise."""
        # Mock franchise query
        mock_db.query.return_value.filter.return_value.first.return_value = sample_franchise
        
        # Mock aggregated projections for each player
        mock_projections = {
            "player1": AggregatedProjection(
                player_id="player1",
                week=None,
                season=2024,
                projected_points=Decimal('320'),
                confidence_interval=(Decimal('280'), Decimal('360')),
                variance=Decimal('80'),
                source_count=4,
                source_weights={},
                metadata={}
            ),
            "player2": AggregatedProjection(
                player_id="player2",
                week=None,
                season=2024,
                projected_points=Decimal('280'),
                confidence_interval=(Decimal('240'), Decimal('320')),
                variance=Decimal('100'),
                source_count=3,
                source_weights={},
                metadata={}
            ),
            "player3": AggregatedProjection(
                player_id="player3",
                week=None,
                season=2024,
                projected_points=Decimal('250'),
                confidence_interval=(Decimal('200'), Decimal('300')),
                variance=Decimal('120'),
                source_count=2,
                source_weights={},
                metadata={}
            ),
            "player4": AggregatedProjection(
                player_id="player4",
                week=None,
                season=2024,
                projected_points=Decimal('200'),
                confidence_interval=(Decimal('150'), Decimal('250')),
                variance=Decimal('150'),
                source_count=2,
                source_weights={},
                metadata={}
            )
        }
        
        def mock_aggregate_projections(player_id, week, season):
            return mock_projections.get(player_id)
        
        keeper_optimizer.projections_aggregator.aggregate_projections.side_effect = mock_aggregate_projections
        
        # Mock replacement levels
        replacement_levels = {
            PlayerPosition.QB: Decimal('250'),
            PlayerPosition.RB: Decimal('180'),
            PlayerPosition.WR: Decimal('160'),
            PlayerPosition.TE: Decimal('120')
        }
        keeper_optimizer._replacement_levels_cache["test_league_2024"] = replacement_levels
        
        candidates = keeper_optimizer.get_keeper_candidates("test_franchise", 2024)
        
        # Verify candidates were returned
        assert len(candidates) == 4
        
        # Verify candidates are sorted by VOR (descending)
        vor_values = [c.value_over_replacement for c in candidates]
        assert vor_values == sorted(vor_values, reverse=True)
        
        # Verify specific candidate properties
        qb_candidate = next(c for c in candidates if c.player_id == "player1")
        assert qb_candidate.position == PlayerPosition.QB
        assert qb_candidate.projected_points == Decimal('320')
        assert qb_candidate.value_over_replacement == Decimal('70')  # 320 - 250
        assert qb_candidate.keeper_cost == 4  # 5 - 1 (escalation)
        assert qb_candidate.is_eligible == True
    
    def test_keeper_cost_calculation(self, keeper_optimizer):
        """Test keeper cost calculation with escalation rules."""
        keeper_rules = {
            "round_escalation": 2,
            "max_round": 16
        }
        
        # Test normal escalation
        assert keeper_optimizer._calculate_keeper_cost(10, keeper_rules) == 8
        
        # Test escalation that would go below round 1
        assert keeper_optimizer._calculate_keeper_cost(1, keeper_rules) == 1
        
        # Test with different escalation
        keeper_rules["round_escalation"] = 1
        assert keeper_optimizer._calculate_keeper_cost(5, keeper_rules) == 4
    
    def test_keeper_eligibility_check(self, keeper_optimizer, sample_roster_players):
        """Test keeper eligibility checking."""
        # Test healthy player eligibility
        healthy_player = sample_roster_players[0]  # Elite QB
        keeper_rules = {"no_injured_keepers": False}
        
        is_eligible, violations = keeper_optimizer._check_keeper_eligibility(
            healthy_player, keeper_rules
        )
        assert is_eligible == True
        assert len(violations) == 0
        
        # Test injured player with injury restriction
        injured_player = sample_roster_players[3]  # Injured RB
        injured_player.player.injury_status = InjuryStatus.OUT
        keeper_rules = {"no_injured_keepers": True}
        
        is_eligible, violations = keeper_optimizer._check_keeper_eligibility(
            injured_player, keeper_rules
        )
        assert is_eligible == False
        assert len(violations) == 1
        assert "OUT" in violations[0]
        
        # Test keeper cost limit
        expensive_player = sample_roster_players[1]  # Top RB
        keeper_rules = {"max_keeper_cost": 5, "round_escalation": 1}
        
        is_eligible, violations = keeper_optimizer._check_keeper_eligibility(
            expensive_player, keeper_rules
        )
        assert is_eligible == False
        assert len(violations) == 1
        assert "exceeds maximum" in violations[0]
    
    def test_optimize_keepers_basic(self, keeper_optimizer, mock_db, sample_franchise, sample_roster):
        """Test basic keeper optimization."""
        # Mock franchise query
        mock_db.query.return_value.filter.return_value.first.return_value = sample_franchise
        
        # Create mock candidates
        candidates = [
            KeeperCandidate(
                player_id="player1",
                player_name="Elite QB",
                position=PlayerPosition.QB,
                current_cost=5,
                projected_points=Decimal('320'),
                replacement_level=Decimal('250'),
                value_over_replacement=Decimal('70'),
                keeper_cost=4,
                is_eligible=True,
                constraints_violated=[],
                metadata={"projection_confidence": 0.1, "source_count": 4}
            ),
            KeeperCandidate(
                player_id="player2",
                player_name="Top RB",
                position=PlayerPosition.RB,
                current_cost=8,
                projected_points=Decimal('280'),
                replacement_level=Decimal('180'),
                value_over_replacement=Decimal('100'),
                keeper_cost=7,
                is_eligible=True,
                constraints_violated=[],
                metadata={"projection_confidence": 0.15, "source_count": 3}
            ),
            KeeperCandidate(
                player_id="player3",
                player_name="WR1",
                position=PlayerPosition.WR,
                current_cost=12,
                projected_points=Decimal('250'),
                replacement_level=Decimal('160'),
                value_over_replacement=Decimal('90'),
                keeper_cost=11,
                is_eligible=True,
                constraints_violated=[],
                metadata={"projection_confidence": 0.2, "source_count": 2}
            )
        ]
        
        # Mock get_keeper_candidates
        keeper_optimizer.get_keeper_candidates = Mock(return_value=candidates)
        
        scenarios = keeper_optimizer.optimize_keepers("test_franchise", 2024, max_scenarios=3)
        
        # Verify scenarios were generated
        assert len(scenarios) > 0
        
        # Verify primary scenario
        primary_scenario = scenarios[0]
        assert primary_scenario.scenario_name == "Optimal"
        assert len(primary_scenario.selected_keepers) <= 3  # Max keepers constraint
        assert primary_scenario.constraints_satisfied == True
        
        # Verify keepers are high-value
        total_vor = sum(k.value_over_replacement for k in primary_scenario.selected_keepers)
        assert total_vor > 0
    
    def test_optimize_keepers_with_constraints(self, keeper_optimizer, mock_db, sample_franchise):
        """Test keeper optimization with various constraints."""
        # Update league rules with stricter constraints
        sample_franchise.league.keeper_rules.update({
            "max_keepers": 2,
            "position_limits": {"QB": 1, "RB": 1, "WR": 1},
            "salary_cap": 15
        })
        
        mock_db.query.return_value.filter.return_value.first.return_value = sample_franchise
        
        # Create candidates that test constraints
        candidates = [
            KeeperCandidate(
                player_id="player1",
                player_name="Expensive QB",
                position=PlayerPosition.QB,
                current_cost=3,
                projected_points=Decimal('320'),
                replacement_level=Decimal('250'),
                value_over_replacement=Decimal('70'),
                keeper_cost=2,  # Very expensive
                is_eligible=True,
                constraints_violated=[],
                metadata={}
            ),
            KeeperCandidate(
                player_id="player2",
                player_name="Value RB",
                position=PlayerPosition.RB,
                current_cost=10,
                projected_points=Decimal('280'),
                replacement_level=Decimal('180'),
                value_over_replacement=Decimal('100'),
                keeper_cost=9,
                is_eligible=True,
                constraints_violated=[],
                metadata={}
            ),
            KeeperCandidate(
                player_id="player3",
                player_name="Cheap WR",
                position=PlayerPosition.WR,
                current_cost=14,
                projected_points=Decimal('220'),
                replacement_level=Decimal('160'),
                value_over_replacement=Decimal('60'),
                keeper_cost=13,
                is_eligible=True,
                constraints_violated=[],
                metadata={}
            )
        ]
        
        keeper_optimizer.get_keeper_candidates = Mock(return_value=candidates)
        
        scenarios = keeper_optimizer.optimize_keepers("test_franchise", 2024, max_scenarios=1)
        
        # Verify constraint satisfaction
        assert len(scenarios) > 0
        scenario = scenarios[0]
        
        # Check max keepers constraint
        assert len(scenario.selected_keepers) <= 2
        
        # Check salary cap constraint
        total_cost = sum(k.keeper_cost for k in scenario.selected_keepers)
        assert total_cost <= 15
        
        # Check position limits
        position_counts = {}
        for keeper in scenario.selected_keepers:
            pos = keeper.position.value
            position_counts[pos] = position_counts.get(pos, 0) + 1
        
        assert position_counts.get("QB", 0) <= 1
        assert position_counts.get("RB", 0) <= 1
        assert position_counts.get("WR", 0) <= 1
    
    def test_generate_multiple_scenarios(self, keeper_optimizer, mock_db, sample_franchise):
        """Test generation of multiple keeper scenarios."""
        mock_db.query.return_value.filter.return_value.first.return_value = sample_franchise
        
        # Create diverse candidates
        candidates = [
            KeeperCandidate(
                player_id=f"player{i}",
                player_name=f"Player {i}",
                position=PlayerPosition.RB if i % 2 == 0 else PlayerPosition.WR,
                current_cost=10 + i,
                projected_points=Decimal(str(300 - i * 10)),
                replacement_level=Decimal('180'),
                value_over_replacement=Decimal(str(120 - i * 10)),
                keeper_cost=9 + i,
                is_eligible=True,
                constraints_violated=[],
                metadata={"projection_confidence": 0.1 + i * 0.05}
            )
            for i in range(6)
        ]
        
        keeper_optimizer.get_keeper_candidates = Mock(return_value=candidates)
        
        scenarios = keeper_optimizer.optimize_keepers("test_franchise", 2024, max_scenarios=5)
        
        # Verify multiple scenarios generated
        assert len(scenarios) >= 2
        
        # Verify scenarios have different names
        scenario_names = [s.scenario_name for s in scenarios]
        assert len(set(scenario_names)) == len(scenario_names)  # All unique
        
        # Verify scenarios are sorted by total value
        total_values = [s.total_value for s in scenarios]
        assert total_values == sorted(total_values, reverse=True)
    
    def test_confidence_calculation(self, keeper_optimizer):
        """Test confidence score calculation for recommendations."""
        # High confidence candidate
        high_confidence_candidate = KeeperCandidate(
            player_id="player1",
            player_name="Reliable Player",
            position=PlayerPosition.RB,
            current_cost=8,
            projected_points=Decimal('280'),
            replacement_level=Decimal('180'),
            value_over_replacement=Decimal('100'),
            keeper_cost=7,
            is_eligible=True,
            constraints_violated=[],
            metadata={
                "projection_confidence": 0.05,  # Low variance = high confidence
                "source_count": 5,
                "injury_status": "HEALTHY"
            }
        )
        
        confidence = keeper_optimizer._calculate_confidence(high_confidence_candidate)
        assert confidence > 0.8
        
        # Low confidence candidate
        low_confidence_candidate = KeeperCandidate(
            player_id="player2",
            player_name="Risky Player",
            position=PlayerPosition.WR,
            current_cost=10,
            projected_points=Decimal('220'),
            replacement_level=Decimal('160'),
            value_over_replacement=Decimal('60'),
            keeper_cost=9,
            is_eligible=True,
            constraints_violated=[],
            metadata={
                "projection_confidence": 0.3,  # High variance = low confidence
                "source_count": 1,
                "injury_status": "QUESTIONABLE"
            }
        )
        
        confidence = keeper_optimizer._calculate_confidence(low_confidence_candidate)
        assert confidence < 0.7
    
    def test_rationale_generation(self, keeper_optimizer):
        """Test rationale text generation for recommendations."""
        candidate = KeeperCandidate(
            player_id="player1",
            player_name="Elite RB",
            position=PlayerPosition.RB,
            current_cost=8,
            projected_points=Decimal('300'),
            replacement_level=Decimal('180'),
            value_over_replacement=Decimal('120'),
            keeper_cost=7,
            is_eligible=True,
            constraints_violated=[],
            metadata={}
        )
        
        rationale = keeper_optimizer._generate_rationale(candidate)
        
        # Verify rationale contains key information
        assert "120.0 points over replacement" in rationale
        assert "round 7" in rationale
        assert rationale.endswith(".")
        assert rationale[0].isupper()  # Starts with capital letter
    
    def test_alternative_finding(self, keeper_optimizer):
        """Test finding alternative keeper options."""
        selected_candidate = KeeperCandidate(
            player_id="player1",
            player_name="Selected RB",
            position=PlayerPosition.RB,
            current_cost=8,
            projected_points=Decimal('280'),
            replacement_level=Decimal('180'),
            value_over_replacement=Decimal('100'),
            keeper_cost=7,
            is_eligible=True,
            constraints_violated=[],
            metadata={}
        )
        
        all_candidates = [
            selected_candidate,
            KeeperCandidate(
                player_id="player2",
                player_name="Alternative RB1",
                position=PlayerPosition.RB,
                current_cost=10,
                projected_points=Decimal('260'),
                replacement_level=Decimal('180'),
                value_over_replacement=Decimal('80'),
                keeper_cost=9,
                is_eligible=True,
                constraints_violated=[],
                metadata={}
            ),
            KeeperCandidate(
                player_id="player3",
                player_name="Alternative RB2",
                position=PlayerPosition.RB,
                current_cost=12,
                projected_points=Decimal('240'),
                replacement_level=Decimal('180'),
                value_over_replacement=Decimal('60'),
                keeper_cost=11,
                is_eligible=True,
                constraints_violated=[],
                metadata={}
            ),
            KeeperCandidate(
                player_id="player4",
                player_name="Different Position",
                position=PlayerPosition.WR,
                current_cost=9,
                projected_points=Decimal('250'),
                replacement_level=Decimal('160'),
                value_over_replacement=Decimal('90'),
                keeper_cost=8,
                is_eligible=True,
                constraints_violated=[],
                metadata={}
            )
        ]
        
        alternatives = keeper_optimizer._find_alternatives(selected_candidate, all_candidates)
        
        # Verify alternatives found
        assert len(alternatives) == 2  # Top 2 same-position alternatives
        
        # Verify all alternatives are same position
        for alt in alternatives:
            assert alt.position == PlayerPosition.RB
        
        # Verify alternatives are sorted by VOR
        vor_values = [alt.value_over_replacement for alt in alternatives]
        assert vor_values == sorted(vor_values, reverse=True)
    
    def test_cache_management(self, keeper_optimizer):
        """Test replacement levels cache management."""
        # Test cache is initially empty
        assert len(keeper_optimizer._replacement_levels_cache) == 0
        
        # Add some cache entries
        keeper_optimizer._replacement_levels_cache["league1_2024"] = {
            PlayerPosition.QB: Decimal('250')
        }
        keeper_optimizer._replacement_levels_cache["league2_2024"] = {
            PlayerPosition.RB: Decimal('180')
        }
        
        assert len(keeper_optimizer._replacement_levels_cache) == 2
        
        # Test cache refresh
        keeper_optimizer.refresh_replacement_levels_cache()
        assert len(keeper_optimizer._replacement_levels_cache) == 0
    
    def test_error_handling(self, keeper_optimizer, mock_db):
        """Test error handling in keeper optimization."""
        # Test with non-existent franchise
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(ValueError, match="Franchise .* not found"):
            keeper_optimizer.optimize_keepers("nonexistent_franchise", 2024)
        
        # Test with league without keeper rules
        franchise_no_rules = Mock()
        franchise_no_rules.league.keeper_rules = None
        mock_db.query.return_value.filter.return_value.first.return_value = franchise_no_rules
        
        with pytest.raises(ValueError, match="no keeper rules configured"):
            keeper_optimizer.optimize_keepers("franchise_no_rules", 2024)
    
    def test_position_roster_slots(self, keeper_optimizer, sample_league):
        """Test getting roster slots for positions."""
        # Test standard position
        qb_slots = keeper_optimizer._get_position_roster_slots(sample_league, PlayerPosition.QB)
        assert qb_slots == 1
        
        rb_slots = keeper_optimizer._get_position_roster_slots(sample_league, PlayerPosition.RB)
        assert rb_slots == 2  # 2 RB slots + 1 FLEX that accepts RB
        
        # Test position not in roster
        k_slots = keeper_optimizer._get_position_roster_slots(sample_league, PlayerPosition.K)
        assert k_slots == 1