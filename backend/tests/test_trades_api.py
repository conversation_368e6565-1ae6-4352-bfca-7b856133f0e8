"""
Unit tests for the trade analysis API endpoints.
"""
import pytest
from decimal import Decimal
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient

from app.main import app
from app.services.trade_analyzer import (
    TeamAnalysis, TradeProposal, PositionNeed, PositionSurplus,
    TradeFairness, TradeType, TradeImpactAnalysis
)
from app.models.player import PlayerPosition


class TestTradesAPI:
    """Test cases for the trades API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_team_analysis(self):
        """Create mock team analysis data."""
        needs = {
            PlayerPosition.RB: PositionNeed(
                position=PlayerPosition.RB,
                need_level=0.8,
                current_strength=Decimal('25'),
                replacement_level=Decimal('15'),
                depth_score=0.3,
                injury_risk=0.6,
                bye_week_coverage=0.4
            )
        }
        
        surpluses = {
            PlayerPosition.WR: PositionSurplus(
                position=PlayerPosition.WR,
                surplus_level=0.7,
                tradeable_players=["wr1", "wr2"],
                surplus_value=Decimal('20'),
                depth_quality=0.8
            )
        }
        
        return TeamAnalysis(
            franchise_id="team_a",
            franchise_name="Team A",
            needs=needs,
            surpluses=surpluses,
            overall_strength=Decimal('150'),
            win_probability=0.65,
            trade_urgency=0.75,
            metadata={"analysis_timestamp": "2024-01-01T00:00:00"}
        )
    
    @patch('app.api.trades.TradeAnalyzer')
    def test_analyze_league_teams_success(self, mock_analyzer_class, client, mock_team_analysis):
        """Test successful league team analysis."""
        # Mock the analyzer
        mock_analyzer = Mock()
        mock_analyzer.analyze_all_teams.return_value = {
            "team_a": mock_team_analysis
        }
        mock_analyzer_class.return_value = mock_analyzer
        
        response = client.get("/api/v1/trades/analyze-league/test_league?season=2024")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "team_a" in data
        team_data = data["team_a"]
        assert team_data["franchise_id"] == "team_a"
        assert team_data["franchise_name"] == "Team A"
        assert team_data["overall_strength"] == 150.0
        assert team_data["win_probability"] == 0.65
        assert team_data["trade_urgency"] == 0.75
        
        # Check needs structure
        assert "needs" in team_data
        assert "RB" in team_data["needs"]
        rb_need = team_data["needs"]["RB"]
        assert rb_need["need_level"] == 0.8
        assert rb_need["current_strength"] == 25.0
        
        # Check surpluses structure
        assert "surpluses" in team_data
        assert "WR" in team_data["surpluses"]
        wr_surplus = team_data["surpluses"]["WR"]
        assert wr_surplus["surplus_level"] == 0.7
        assert wr_surplus["tradeable_players"] == ["wr1", "wr2"]