"""
Integration tests for the alert system.
"""
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.core.database import get_db
from app.models.base import Base
from app.models.alert import <PERSON><PERSON>, AlertSchedule, PlayerNewsAlert, AlertType, AlertPriority
from app.models.league import League
from app.models.roster import Franchise
from app.models.player import Player, PlayerPosition
from app.services.alert_service import AlertService


# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_alerts.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


@pytest.fixture(scope="module")
def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def db_session():
    """Create a database session for testing."""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


@pytest.fixture
def sample_data(db_session):
    """Create sample data for testing."""
    # Create league
    league = League(
        id="test_league",
        name="Test League",
        season=2024,
        scoring_rules={},
        roster_slots=[]
    )
    db_session.add(league)
    
    # Create franchise
    franchise = Franchise(
        id="test_franchise",
        name="Test Team",
        owner_name="Test Owner",
        league_id="test_league"
    )
    db_session.add(franchise)
    
    # Create player
    player = Player(
        id="test_player",
        name="Test Player",
        position=PlayerPosition.RB,
        team="NYG"
    )
    db_session.add(player)
    
    db_session.commit()
    
    return {
        "league": league,
        "franchise": franchise,
        "player": player
    }


class TestAlertIntegration:
    """Integration tests for the alert system."""
    
    def test_create_and_retrieve_alert(self, setup_database, sample_data):
        """Test creating and retrieving alerts through the API."""
        # Create an alert
        alert_data = {
            "alert_type": "KEEPER_DEADLINE",
            "title": "Test Alert",
            "message": "This is a test alert",
            "priority": "HIGH",
            "franchise_id": "test_franchise"
        }
        
        response = client.post("/api/v1/alerts/", json=alert_data)
        assert response.status_code == 200
        
        created_alert = response.json()
        assert created_alert["title"] == "Test Alert"
        assert created_alert["franchise_id"] == "test_franchise"
        
        # Retrieve alerts
        response = client.get("/api/v1/alerts/?franchise_id=test_franchise")
        assert response.status_code == 200
        
        alerts = response.json()
        assert len(alerts) == 1
        assert alerts[0]["id"] == created_alert["id"]
    
    def test_alert_summary(self, setup_database, sample_data):
        """Test getting alert summary."""
        # Create alerts with different priorities
        alert_data_urgent = {
            "alert_type": "LINEUP_LOCK",
            "title": "Urgent Alert",
            "message": "Urgent message",
            "priority": "URGENT",
            "franchise_id": "test_franchise"
        }
        
        alert_data_medium = {
            "alert_type": "PLAYER_NEWS",
            "title": "Medium Alert",
            "message": "Medium message",
            "priority": "MEDIUM",
            "franchise_id": "test_franchise"
        }
        
        client.post("/api/v1/alerts/", json=alert_data_urgent)
        client.post("/api/v1/alerts/", json=alert_data_medium)
        
        # Get summary
        response = client.get("/api/v1/alerts/summary?franchise_id=test_franchise")
        assert response.status_code == 200
        
        summary = response.json()
        assert summary["urgent"] >= 1
        assert summary["medium"] >= 1
        assert summary["total"] >= 2
    
    def test_alert_schedule_creation(self, setup_database, sample_data):
        """Test creating alert schedules."""
        schedule_data = {
            "name": "Keeper Deadline Schedule",
            "alert_type": "KEEPER_DEADLINE",
            "target_datetime": (datetime.now() + timedelta(days=7)).isoformat(),
            "advance_notice_hours": [24, 2],
            "league_id": "test_league"
        }
        
        response = client.post("/api/v1/alerts/schedules", json=schedule_data)
        assert response.status_code == 200
        
        schedule = response.json()
        assert schedule["name"] == "Keeper Deadline Schedule"
        assert schedule["league_id"] == "test_league"
        
        # Retrieve schedules
        response = client.get("/api/v1/alerts/schedules?league_id=test_league")
        assert response.status_code == 200
        
        schedules = response.json()
        assert len(schedules) == 1
        assert schedules[0]["id"] == schedule["id"]
    
    def test_player_news_creation(self, setup_database, sample_data):
        """Test creating player news alerts."""
        news_data = {
            "player_id": "test_player",
            "headline": "Player Injured",
            "content": "Test player suffered an injury during practice",
            "source": "ESPN",
            "impact_score": 7.5,
            "affected_positions": ["RB"],
            "fantasy_impact": "Significant impact on fantasy value"
        }
        
        response = client.post("/api/v1/alerts/news", json=news_data)
        assert response.status_code == 200
        
        news = response.json()
        assert news["headline"] == "Player Injured"
        assert news["player_id"] == "test_player"
        assert news["impact_score"] == 7.5
        
        # Retrieve news
        response = client.get("/api/v1/alerts/news?player_id=test_player")
        assert response.status_code == 200
        
        news_items = response.json()
        assert len(news_items) == 1
        assert news_items[0]["id"] == news["id"]
    
    def test_alert_service_deadline_monitoring(self, setup_database, sample_data, db_session):
        """Test deadline monitoring service functionality."""
        alert_service = AlertService(db_session)
        
        # Create a schedule that should trigger an alert
        target_time = datetime.now() + timedelta(hours=1)
        schedule = alert_service.create_alert_schedule(
            name="Test Deadline",
            alert_type=AlertType.KEEPER_DEADLINE,
            target_datetime=target_time,
            advance_notice_hours=[2],  # Should trigger now
            franchise_id="test_franchise"
        )
        
        # Process deadline monitoring
        alerts_created = alert_service.process_deadline_monitoring()
        
        # Should have created an alert
        assert len(alerts_created) == 1
        assert alerts_created[0].alert_type == AlertType.KEEPER_DEADLINE
        assert alerts_created[0].franchise_id == "test_franchise"
    
    def test_alert_service_player_news_processing(self, setup_database, sample_data, db_session):
        """Test player news processing functionality."""
        alert_service = AlertService(db_session)
        
        # Create player news with high impact
        news = alert_service.create_player_news_alert(
            player_id="test_player",
            headline="Major Injury",
            content="Player out for season",
            source="ESPN",
            impact_score=9.0,  # High impact
            fantasy_impact="Season-ending injury"
        )
        
        # Process player news
        alerts_created = alert_service.process_player_news()
        
        # Should have created alerts (though no franchises have this player)
        # The service should still process the news
        assert news.is_processed is True
    
    def test_alert_delivery_and_dismissal(self, setup_database, sample_data):
        """Test alert delivery and dismissal functionality."""
        # Create an alert
        alert_data = {
            "alert_type": "PLAYER_NEWS",
            "title": "Test Delivery",
            "message": "Test message",
            "priority": "MEDIUM",
            "franchise_id": "test_franchise"
        }
        
        response = client.post("/api/v1/alerts/", json=alert_data)
        alert_id = response.json()["id"]
        
        # Mark as delivered
        response = client.post(f"/api/v1/alerts/{alert_id}/deliver")
        assert response.status_code == 200
        assert response.json()["message"] == "Alert marked as delivered"
        
        # Dismiss alert
        response = client.post(f"/api/v1/alerts/{alert_id}/dismiss")
        assert response.status_code == 200
        assert response.json()["message"] == "Alert dismissed"
    
    def test_process_endpoints(self, setup_database, sample_data):
        """Test the processing endpoints."""
        # Test deadline processing endpoint
        response = client.post("/api/v1/alerts/process-deadlines")
        assert response.status_code == 200
        assert "alerts_created" in response.json()
        
        # Test news processing endpoint
        response = client.post("/api/v1/alerts/process-news")
        assert response.status_code == 200
        assert "alerts_created" in response.json()