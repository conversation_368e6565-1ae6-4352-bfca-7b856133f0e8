"""
Unit tests for lineup optimization service.
"""
import pytest
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from unittest.mock import Mock, patch
import numpy as np

from app.services.lineup_optimizer import (
    LineupOptimizer, LineupSlot, WeatherCondition, MatchupContext, PlayerNews, LineupRecommendation
)
from app.models.player import Player, PlayerPosition, InjuryStatus
from app.models.roster import <PERSON><PERSON>er, RosterPlayer, Franchise
from app.models.projection import Projection
from app.models.league import League


class TestLineupOptimizer:
    """Test cases for LineupOptimizer service."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()
    
    @pytest.fixture
    def lineup_optimizer(self, mock_db):
        """Create LineupOptimizer instance with mocked database."""
        return LineupOptimizer(mock_db)
    
    @pytest.fixture
    def sample_league(self):
        """Create sample league with roster slots."""
        return League(
            id="test_league",
            name="Test League",
            season=2024,
            roster_slots=[
                {"position": "QB", "count": 1, "type": "starting"},
                {"position": "RB", "count": 2, "type": "starting"},
                {"position": "WR", "count": 2, "type": "starting"},
                {"position": "TE", "count": 1, "type": "starting"},
                {"position": "FLEX", "count": 1, "type": "starting"},
                {"position": "K", "count": 1, "type": "starting"},
                {"position": "DEF", "count": 1, "type": "starting"}
            ]
        )
    
    @pytest.fixture
    def sample_franchise(self, sample_league):
        """Create sample franchise with roster."""
        franchise = Franchise(
            id="test_franchise",
            name="Test Team",
            owner_name="Test Owner",
            league_id=sample_league.id
        )
        franchise.league = sample_league
        
        roster = Roster(
            id="test_roster",
            franchise_id=franchise.id
        )
        roster.franchise = franchise
        franchise.roster = roster
        
        return franchise
    
    @pytest.fixture
    def sample_players(self):
        """Create sample players for testing."""
        return [
            Player(
                id="qb1",
                name="Test QB",
                position=PlayerPosition.QB,
                team="TEST",
                injury_status=InjuryStatus.HEALTHY
            ),
            Player(
                id="rb1",
                name="Test RB1",
                position=PlayerPosition.RB,
                team="TEST",
                injury_status=InjuryStatus.HEALTHY
            ),
            Player(
                id="rb2",
                name="Test RB2",
                position=PlayerPosition.RB,
                team="TEST",
                injury_status=InjuryStatus.HEALTHY
            ),
            Player(
                id="wr1",
                name="Test WR1",
                position=PlayerPosition.WR,
                team="TEST",
                injury_status=InjuryStatus.HEALTHY
            ),
            Player(
                id="wr2",
                name="Test WR2",
                position=PlayerPosition.WR,
                team="TEST",
                injury_status=InjuryStatus.HEALTHY
            ),
            Player(
                id="te1",
                name="Test TE",
                position=PlayerPosition.TE,
                team="TEST",
                injury_status=InjuryStatus.HEALTHY
            ),
            Player(
                id="k1",
                name="Test K",
                position=PlayerPosition.K,
                team="TEST",
                injury_status=InjuryStatus.HEALTHY
            ),
            Player(
                id="def1",
                name="Test DEF",
                position=PlayerPosition.DEF,
                team="TEST",
                injury_status=InjuryStatus.HEALTHY
            )
        ]
    
    @pytest.fixture
    def sample_projections(self, sample_players):
        """Create sample projections for players."""
        projections = []
        base_points = [20.0, 15.0, 12.0, 14.0, 11.0, 10.0, 8.0, 9.0]
        
        for i, player in enumerate(sample_players):
            projection = Projection(
                id=f"proj_{player.id}",
                player_id=player.id,
                week=1,
                season=2024,
                source="test",
                projected_points=Decimal(str(base_points[i])),
                floor=Decimal(str(base_points[i] * 0.7)),
                ceiling=Decimal(str(base_points[i] * 1.3)),
                variance=Decimal("2.0"),
                is_active=True
            )
            projection.player = player
            projections.append(projection)
        
        return projections
    
    @pytest.fixture
    def sample_roster_players(self, sample_franchise, sample_players):
        """Create sample roster players."""
        roster_players = []
        
        for player in sample_players:
            roster_player = RosterPlayer(
                id=f"rp_{player.id}",
                roster_id=sample_franchise.roster.id,
                player_id=player.id,
                is_active=True
            )
            roster_player.player = player
            roster_player.roster = sample_franchise.roster
            roster_players.append(roster_player)
        
        sample_franchise.roster.roster_players = roster_players
        return roster_players
    
    def test_optimize_lineup_basic(self, lineup_optimizer, sample_franchise, sample_projections):
        """Test basic lineup optimization."""
        # Mock database queries
        lineup_optimizer.db.query.return_value.filter.return_value.first.return_value = sample_franchise
        
        # Mock projection queries
        def mock_projection_query(*args):
            mock_query = Mock()
            mock_query.filter.return_value.first.return_value = sample_projections[0]
            return mock_query
        
        lineup_optimizer.db.query.side_effect = [
            Mock(return_value=Mock(filter=Mock(return_value=Mock(first=Mock(return_value=sample_franchise))))),
            mock_projection_query
        ]
        
        # Mock _get_available_players_with_projections
        players_data = {}
        for i, projection in enumerate(sample_projections):
            players_data[projection.player_id] = {
                "player": projection.player,
                "projection": projection,
                "base_projection": float(projection.projected_points),
                "variance": 2.0,
                "floor": float(projection.floor),
                "ceiling": float(projection.ceiling),
                "adjusted_projection": float(projection.projected_points),
                "matchup_context": MatchupContext(
                    opponent_team="OPP",
                    opponent_rank_vs_position=16,
                    weather_condition=WeatherCondition.CLEAR,
                    temperature=70,
                    wind_speed=5,
                    is_home_game=True,
                    game_total=45.0,
                    spread=3.0,
                    implied_team_total=24.0
                )
            }
        
        with patch.object(lineup_optimizer, '_get_available_players_with_projections', return_value=players_data):
            recommendation = lineup_optimizer.optimize_lineup("test_franchise", 1)
            
            assert isinstance(recommendation, LineupRecommendation)
            assert recommendation.projected_points > 0
            assert 0 <= recommendation.win_probability <= 1
            assert 0 <= recommendation.confidence <= 1
            assert len(recommendation.lineup) > 0
            assert recommendation.rationale
    
    def test_parse_roster_slots(self, lineup_optimizer, sample_league):
        """Test parsing of roster slots configuration."""
        slots = lineup_optimizer._parse_roster_slots(sample_league.roster_slots)
        
        assert LineupSlot.QB in slots
        assert LineupSlot.RB1 in slots
        assert LineupSlot.RB2 in slots
        assert LineupSlot.WR1 in slots
        assert LineupSlot.WR2 in slots
        assert LineupSlot.TE in slots
        assert LineupSlot.FLEX in slots
        assert LineupSlot.K in slots
        assert LineupSlot.DEF in slots
    
    def test_is_player_eligible_for_slot(self, lineup_optimizer):
        """Test player position eligibility for lineup slots."""
        # QB only eligible for QB slot
        assert lineup_optimizer._is_player_eligible_for_slot(PlayerPosition.QB, LineupSlot.QB)
        assert not lineup_optimizer._is_player_eligible_for_slot(PlayerPosition.QB, LineupSlot.RB1)
        
        # RB eligible for RB and FLEX slots
        assert lineup_optimizer._is_player_eligible_for_slot(PlayerPosition.RB, LineupSlot.RB1)
        assert lineup_optimizer._is_player_eligible_for_slot(PlayerPosition.RB, LineupSlot.RB2)
        assert lineup_optimizer._is_player_eligible_for_slot(PlayerPosition.RB, LineupSlot.FLEX)
        assert not lineup_optimizer._is_player_eligible_for_slot(PlayerPosition.RB, LineupSlot.QB)
        
        # WR eligible for WR and FLEX slots
        assert lineup_optimizer._is_player_eligible_for_slot(PlayerPosition.WR, LineupSlot.WR1)
        assert lineup_optimizer._is_player_eligible_for_slot(PlayerPosition.WR, LineupSlot.WR2)
        assert lineup_optimizer._is_player_eligible_for_slot(PlayerPosition.WR, LineupSlot.FLEX)
        
        # TE eligible for TE and FLEX slots
        assert lineup_optimizer._is_player_eligible_for_slot(PlayerPosition.TE, LineupSlot.TE)
        assert lineup_optimizer._is_player_eligible_for_slot(PlayerPosition.TE, LineupSlot.FLEX)
    
    def test_weather_adjustments(self, lineup_optimizer):
        """Test weather adjustment calculations."""
        # QB should be negatively affected by wind
        wind_adj = lineup_optimizer._get_weather_adjustment(PlayerPosition.QB, WeatherCondition.WIND)
        assert wind_adj < 1.0
        
        # RB should benefit from bad weather
        rain_adj = lineup_optimizer._get_weather_adjustment(PlayerPosition.RB, WeatherCondition.RAIN)
        assert rain_adj >= 1.0
        
        # Dome should benefit passing positions
        dome_adj = lineup_optimizer._get_weather_adjustment(PlayerPosition.QB, WeatherCondition.DOME)
        assert dome_adj > 1.0
    
    def test_opponent_adjustment(self, lineup_optimizer):
        """Test opponent strength adjustments."""
        # Better defense (rank 1) should lower projection
        best_defense_adj = lineup_optimizer._get_opponent_adjustment(PlayerPosition.QB, 1)
        assert best_defense_adj < 1.0
        
        # Worse defense (rank 32) should increase projection
        worst_defense_adj = lineup_optimizer._get_opponent_adjustment(PlayerPosition.QB, 32)
        assert worst_defense_adj > 1.0
        
        # Average defense (rank 16) should be neutral
        avg_defense_adj = lineup_optimizer._get_opponent_adjustment(PlayerPosition.QB, 16)
        assert abs(avg_defense_adj - 1.0) < 0.1
    
    def test_injury_adjustments(self, lineup_optimizer):
        """Test injury status adjustments."""
        # Healthy players should have no adjustment
        healthy_adj = lineup_optimizer._get_injury_adjustment(InjuryStatus.HEALTHY)
        assert healthy_adj == 1.0
        
        # Questionable players should have slight reduction
        questionable_adj = lineup_optimizer._get_injury_adjustment(InjuryStatus.QUESTIONABLE)
        assert questionable_adj < 1.0
        
        # Doubtful players should have significant reduction
        doubtful_adj = lineup_optimizer._get_injury_adjustment(InjuryStatus.DOUBTFUL)
        assert doubtful_adj < 0.9
        
        # Out players should have zero projection
        out_adj = lineup_optimizer._get_injury_adjustment(InjuryStatus.OUT)
        assert out_adj == 0.0
    
    def test_game_script_adjustments(self, lineup_optimizer):
        """Test game script adjustments based on spread and total."""
        # QB should benefit from being behind (negative spread)
        qb_behind_adj = lineup_optimizer._get_game_script_adjustment(PlayerPosition.QB, -7.0, 50.0)
        qb_ahead_adj = lineup_optimizer._get_game_script_adjustment(PlayerPosition.QB, 7.0, 50.0)
        assert qb_behind_adj > qb_ahead_adj
        
        # RB should benefit from being ahead (positive spread)
        rb_behind_adj = lineup_optimizer._get_game_script_adjustment(PlayerPosition.RB, -7.0, 50.0)
        rb_ahead_adj = lineup_optimizer._get_game_script_adjustment(PlayerPosition.RB, 7.0, 50.0)
        assert rb_ahead_adj > rb_behind_adj
        
        # High total should benefit all positions
        high_total_adj = lineup_optimizer._get_game_script_adjustment(PlayerPosition.QB, 0.0, 55.0)
        low_total_adj = lineup_optimizer._get_game_script_adjustment(PlayerPosition.QB, 0.0, 35.0)
        assert high_total_adj > low_total_adj
    
    def test_win_probability_simulation(self, lineup_optimizer):
        """Test win probability simulation."""
        # Create mock lineup and player data
        lineup = {LineupSlot.QB: "qb1", LineupSlot.RB1: "rb1"}
        players_data = {
            "qb1": {"adjusted_projection": 20.0, "variance": 4.0},
            "rb1": {"adjusted_projection": 15.0, "variance": 3.0}
        }
        
        # Test with different opponent projections
        win_prob_easy = lineup_optimizer._simulate_win_probability(
            lineup, players_data, 25.0, 100
        )
        win_prob_hard = lineup_optimizer._simulate_win_probability(
            lineup, players_data, 45.0, 100
        )
        
        # Should have higher win probability against weaker opponent
        assert win_prob_easy > win_prob_hard
        assert 0 <= win_prob_easy <= 1
        assert 0 <= win_prob_hard <= 1
    
    def test_lineup_confidence_calculation(self, lineup_optimizer):
        """Test lineup confidence calculation."""
        lineup = {LineupSlot.QB: "qb1", LineupSlot.RB1: "rb1"}
        
        # Low variance should give high confidence
        low_variance_data = {
            "qb1": {"variance": 1.0},
            "rb1": {"variance": 1.0}
        }
        high_confidence = lineup_optimizer._calculate_lineup_confidence(lineup, low_variance_data)
        
        # High variance should give low confidence
        high_variance_data = {
            "qb1": {"variance": 10.0},
            "rb1": {"variance": 10.0}
        }
        low_confidence = lineup_optimizer._calculate_lineup_confidence(lineup, high_variance_data)
        
        assert high_confidence > low_confidence
        assert 0 <= high_confidence <= 1
        assert 0 <= low_confidence <= 1
    
    def test_lineup_risk_calculation(self, lineup_optimizer):
        """Test lineup risk calculation."""
        lineup = {LineupSlot.QB: "qb1", LineupSlot.RB1: "rb1"}
        
        # High floor relative to projection = low risk
        low_risk_data = {
            "qb1": {"adjusted_projection": 20.0, "floor": 18.0},
            "rb1": {"adjusted_projection": 15.0, "floor": 13.0}
        }
        low_risk = lineup_optimizer._calculate_lineup_risk(lineup, low_risk_data)
        
        # Low floor relative to projection = high risk
        high_risk_data = {
            "qb1": {"adjusted_projection": 20.0, "floor": 10.0},
            "rb1": {"adjusted_projection": 15.0, "floor": 5.0}
        }
        high_risk = lineup_optimizer._calculate_lineup_risk(lineup, high_risk_data)
        
        assert high_risk > low_risk
        assert 0 <= low_risk <= 1
        assert 0 <= high_risk <= 1
    
    def test_start_sit_recommendations(self, lineup_optimizer, sample_franchise):
        """Test start/sit recommendation generation."""
        # Mock the optimize_lineup method
        mock_optimal = LineupRecommendation(
            lineup={LineupSlot.QB: "qb1", LineupSlot.RB1: "rb1"},
            projected_points=35.0,
            win_probability=0.6,
            confidence=0.8,
            rationale="Test rationale",
            alternatives=[],
            risk_level=0.3
        )
        
        with patch.object(lineup_optimizer, 'optimize_lineup', return_value=mock_optimal):
            with patch.object(lineup_optimizer, '_get_current_lineup', return_value={LineupSlot.QB: "qb2"}):
                with patch.object(lineup_optimizer, '_calculate_improvement', return_value=2.5):
                    recommendations = lineup_optimizer.get_start_sit_recommendations("test_franchise", 1)
                    
                    assert len(recommendations) > 0
                    assert recommendations[0]["type"] == "start_sit"
                    assert "start_player_id" in recommendations[0]
                    assert "projected_improvement" in recommendations[0]
    
    def test_lineup_lock_alerts(self, lineup_optimizer, sample_franchise, sample_roster_players):
        """Test lineup lock alert generation."""
        # Mock franchise query
        lineup_optimizer.db.query.return_value.filter.return_value.first.return_value = sample_franchise
        
        # Mock lock time to be soon
        future_time = datetime.now(timezone.utc) + timedelta(minutes=30)
        
        with patch.object(lineup_optimizer, '_get_player_lock_time', return_value=future_time):
            alerts = lineup_optimizer.check_lineup_locks("test_franchise", 1)
            
            # Should generate alerts for starting players with upcoming locks
            assert isinstance(alerts, list)
            # Note: alerts will be empty since no players are marked as starting in fixture
    
    def test_late_breaking_news_application(self, lineup_optimizer):
        """Test application of late-breaking news to lineup recommendations."""
        news_items = [
            PlayerNews(
                player_id="qb1",
                news_type="injury",
                severity="high",
                description="QB1 ruled out with injury",
                timestamp=datetime.now(),
                impact_on_projection=-1.0  # 100% reduction
            )
        ]
        
        # Mock the optimization methods
        mock_current = LineupRecommendation(
            lineup={LineupSlot.QB: "qb1"},
            projected_points=20.0,
            win_probability=0.5,
            confidence=0.7,
            rationale="Original lineup",
            alternatives=[],
            risk_level=0.4
        )
        
        mock_new = LineupRecommendation(
            lineup={LineupSlot.QB: "qb2"},
            projected_points=18.0,
            win_probability=0.45,
            confidence=0.6,
            rationale="Adjusted lineup",
            alternatives=[],
            risk_level=0.5
        )
        
        with patch.object(lineup_optimizer, 'optimize_lineup', return_value=mock_current):
            with patch.object(lineup_optimizer, '_reoptimize_with_news', return_value=mock_new):
                adjustments = lineup_optimizer.apply_late_breaking_news(
                    "test_franchise", 1, news_items
                )
                
                assert isinstance(adjustments, list)
                if adjustments:  # May be empty if no changes needed
                    assert adjustments[0]["type"] == "news_adjustment"
                    assert "old_player_id" in adjustments[0]
                    assert "new_player_id" in adjustments[0]
    
    def test_matchup_context_creation(self, lineup_optimizer, sample_players):
        """Test matchup context creation."""
        player = sample_players[0]  # QB
        context = lineup_optimizer._get_matchup_context(player, 1)
        
        assert isinstance(context, MatchupContext)
        assert context.opponent_team
        assert context.weather_condition in WeatherCondition
        assert isinstance(context.is_home_game, bool)
    
    def test_eligible_players_by_slot(self, lineup_optimizer, sample_players, sample_projections):
        """Test getting eligible players for each slot."""
        # Create players data
        players_data = {}
        for player, projection in zip(sample_players, sample_projections):
            players_data[player.id] = {
                "player": player,
                "adjusted_projection": float(projection.projected_points)
            }
        
        roster_slots = {
            LineupSlot.QB: 1,
            LineupSlot.RB1: 1,
            LineupSlot.WR1: 1,
            LineupSlot.FLEX: 1
        }
        
        eligible = lineup_optimizer._get_eligible_players_by_slot(players_data, roster_slots)
        
        # QB should only be eligible for QB slot
        assert "qb1" in eligible[LineupSlot.QB]
        assert "qb1" not in eligible.get(LineupSlot.RB1, [])
        
        # RB should be eligible for RB and FLEX slots
        assert "rb1" in eligible[LineupSlot.RB1]
        assert "rb1" in eligible[LineupSlot.FLEX]
        
        # WR should be eligible for WR and FLEX slots
        assert "wr1" in eligible[LineupSlot.WR1]
        assert "wr1" in eligible[LineupSlot.FLEX]
    
    def test_lineup_alternatives_generation(self, lineup_optimizer, sample_players, sample_projections):
        """Test generation of lineup alternatives."""
        # Create players data
        players_data = {}
        for player, projection in zip(sample_players, sample_projections):
            players_data[player.id] = {
                "player": player,
                "adjusted_projection": float(projection.projected_points),
                "variance": 2.0
            }
        
        optimal_lineup = {LineupSlot.QB: "qb1", LineupSlot.RB1: "rb1"}
        roster_slots = {LineupSlot.QB: 1, LineupSlot.RB1: 1}
        
        with patch.object(lineup_optimizer, '_simulate_win_probability', return_value=0.6):
            alternatives = lineup_optimizer._generate_lineup_alternatives(
                players_data, roster_slots, optimal_lineup, 100.0
            )
            
            assert isinstance(alternatives, list)
            assert len(alternatives) <= 3  # Should return top 3 alternatives
            
            for alt in alternatives:
                assert "lineup" in alt
                assert "change" in alt
                assert "win_probability" in alt
                assert "projected_points" in alt


class TestLineupOptimizerIntegration:
    """Integration tests for lineup optimizer with real database scenarios."""
    
    def test_full_optimization_workflow(self):
        """Test complete optimization workflow with mocked components."""
        # This would be an integration test with a real database
        # For now, we'll test the workflow with mocks
        
        mock_db = Mock()
        optimizer = LineupOptimizer(mock_db)
        
        # Mock all the database interactions and external dependencies
        with patch.multiple(
            optimizer,
            _get_available_players_with_projections=Mock(return_value={}),
            _parse_roster_slots=Mock(return_value={}),
            _apply_context_adjustments=Mock(return_value={}),
            _estimate_opponent_projection=Mock(return_value=100.0),
            _optimize_for_win_probability=Mock(return_value={}),
            _generate_lineup_alternatives=Mock(return_value=[]),
            _calculate_lineup_confidence=Mock(return_value=0.8),
            _calculate_lineup_risk=Mock(return_value=0.3),
            _generate_lineup_rationale=Mock(return_value="Test rationale")
        ):
            # Mock franchise query
            mock_franchise = Mock()
            mock_franchise.roster = Mock()
            mock_franchise.league = Mock()
            mock_franchise.league.roster_slots = []
            
            mock_db.query.return_value.filter.return_value.first.return_value = mock_franchise
            
            # This should not raise an exception
            try:
                result = optimizer.optimize_lineup("test_franchise", 1)
                # The result will be mostly empty due to mocking, but workflow should complete
                assert isinstance(result, LineupRecommendation)
            except Exception as e:
                # If there are issues with the mocking, we'll see them here
                pytest.fail(f"Optimization workflow failed: {e}")
    
    def test_error_handling(self):
        """Test error handling in various scenarios."""
        mock_db = Mock()
        optimizer = LineupOptimizer(mock_db)
        
        # Test franchise not found
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(ValueError, match="Franchise .* not found"):
            optimizer.optimize_lineup("nonexistent_franchise", 1)
        
        # Test no roster
        mock_franchise = Mock()
        mock_franchise.roster = None
        mock_db.query.return_value.filter.return_value.first.return_value = mock_franchise
        
        with pytest.raises(ValueError, match="No roster found"):
            optimizer.optimize_lineup("test_franchise", 1)
    
    def test_performance_with_large_roster(self):
        """Test performance with a large number of players."""
        mock_db = Mock()
        optimizer = LineupOptimizer(mock_db)
        
        # Create a large number of mock players
        large_players_data = {}
        for i in range(50):  # 50 players
            large_players_data[f"player_{i}"] = {
                "player": Mock(position=PlayerPosition.RB),
                "adjusted_projection": 10.0 + i * 0.1,
                "variance": 2.0,
                "floor": 8.0,
                "ceiling": 15.0
            }
        
        roster_slots = {LineupSlot.RB1: 1, LineupSlot.RB2: 1, LineupSlot.FLEX: 1}
        
        # This should complete in reasonable time
        import time
        start_time = time.time()
        
        eligible = optimizer._get_eligible_players_by_slot(large_players_data, roster_slots)
        combinations = optimizer._generate_lineup_combinations(eligible, roster_slots)
        
        end_time = time.time()
        
        # Should complete within 1 second even with 50 players
        assert end_time - start_time < 1.0
        assert len(combinations) > 0