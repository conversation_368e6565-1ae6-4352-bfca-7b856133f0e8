"""
Integration tests for background job processing system.
"""
import pytest
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from celery.result import AsyncResult

from app.core.celery import celery_app
from app.tasks.monitoring import JobMonitor, health_check, system_diagnostics
from app.tasks.data_refresh import refresh_mfl_data, refresh_projection_aggregates, cleanup_old_task_results
from app.tasks.alert_processing import check_upcoming_deadlines, process_player_news, send_pending_notifications
from app.tasks.file_processing import process_uploaded_file, cleanup_old_data, validate_uploaded_data


class TestCeleryConfiguration:
    """Test Celery configuration and basic functionality."""
    
    def test_celery_app_configuration(self):
        """Test that Celery app is properly configured."""
        assert celery_app.conf.task_serializer == "json"
        assert celery_app.conf.result_serializer == "json"
        assert celery_app.conf.timezone == "UTC"
        assert celery_app.conf.task_track_started is True
        assert celery_app.conf.task_acks_late is True
        assert celery_app.conf.task_max_retries == 3
    
    def test_celery_queues_configured(self):
        """Test that all required queues are configured."""
        expected_queues = ['default', 'data_refresh', 'alerts', 'file_processing', 'failed']
        configured_queues = list(celery_app.conf.task_queues.keys())
        
        for queue in expected_queues:
            assert queue in configured_queues
    
    def test_beat_schedule_configured(self):
        """Test that beat schedule is properly configured."""
        beat_schedule = celery_app.conf.beat_schedule
        
        expected_tasks = [
            'refresh-mfl-data',
            'process-player-news',
            'check-deadlines',
            'cleanup-old-tasks',
            'refresh-projections'
        ]
        
        for task in expected_tasks:
            assert task in beat_schedule


class TestJobMonitoring:
    """Test job monitoring functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.job_monitor = JobMonitor()
    
    def test_job_monitor_initialization(self):
        """Test that JobMonitor initializes correctly."""
        assert self.job_monitor.celery_app == celery_app
        assert self.job_monitor.state is not None
    
    @patch('app.tasks.monitoring.celery_app.control.inspect')
    def test_get_active_tasks(self, mock_inspect):
        """Test getting active tasks."""
        # Mock the inspect response
        mock_inspect.return_value.active.return_value = {
            'worker1': [
                {
                    'id': 'task-123',
                    'name': 'app.tasks.data_refresh.refresh_mfl_data',
                    'args': [],
                    'kwargs': {},
                    'time_start': time.time(),
                    'acknowledged': True
                }
            ]
        }
        
        active_tasks = self.job_monitor.get_active_tasks()
        
        assert len(active_tasks) == 1
        assert active_tasks[0]['id'] == 'task-123'
        assert active_tasks[0]['name'] == 'app.tasks.data_refresh.refresh_mfl_data'
        assert active_tasks[0]['worker'] == 'worker1'
    
    @patch('app.tasks.monitoring.celery_app.control.inspect')
    def test_get_worker_stats(self, mock_inspect):
        """Test getting worker statistics."""
        # Mock the inspect response
        mock_inspect.return_value.stats.return_value = {
            'worker1': {
                'total': {'app.tasks.data_refresh.refresh_mfl_data': 5},
                'active': [],
                'rusage': {'utime': 10.5, 'maxrss': 1024}
            }
        }
        
        stats = self.job_monitor.get_worker_stats()
        
        assert stats['workers'] == 1
        assert 'worker1' in stats['details']
        assert stats['details']['worker1']['status'] == 'online'
    
    def test_get_task_status(self):
        """Test getting task status."""
        # Create a mock task result
        with patch('app.tasks.monitoring.AsyncResult') as mock_result:
            mock_result.return_value.status = 'SUCCESS'
            mock_result.return_value.result = {'test': 'data'}
            mock_result.return_value.ready.return_value = True
            mock_result.return_value.traceback = None
            mock_result.return_value.date_done = datetime.utcnow()
            mock_result.return_value.name = 'test_task'
            
            status = self.job_monitor.get_task_status('test-task-id')
            
            assert status['id'] == 'test-task-id'
            assert status['status'] == 'SUCCESS'
            assert status['result'] == {'test': 'data'}


class TestHealthChecks:
    """Test health check functionality."""
    
    def test_health_check_task(self):
        """Test basic health check task."""
        # This would normally be run asynchronously, but for testing we'll call directly
        with patch('app.tasks.monitoring.health_check.request') as mock_request:
            mock_request.hostname = 'test-worker'
            mock_request.id = 'test-task-id'
            
            # Mock the task binding
            health_check.request = mock_request
            result = health_check()
            
            assert result['status'] == 'healthy'
            assert 'timestamp' in result
            assert result['worker_id'] == 'test-worker'
            assert result['task_id'] == 'test-task-id'
    
    @patch('app.tasks.monitoring.SessionLocal')
    def test_system_diagnostics_task(self, mock_session):
        """Test system diagnostics task."""
        # Mock database session
        mock_db = Mock()
        mock_session.return_value = mock_db
        mock_db.execute.return_value = None
        
        with patch('app.tasks.monitoring.system_diagnostics.request') as mock_request:
            mock_request.hostname = 'test-worker'
            
            system_diagnostics.request = mock_request
            result = system_diagnostics()
            
            assert 'database' in result
            assert 'redis' in result
            assert 'celery' in result
            assert 'timestamp' in result
            assert result['database'] == 'healthy'
            assert result['redis'] == 'healthy'


class TestDataRefreshTasks:
    """Test data refresh background tasks."""
    
    @patch('app.tasks.data_refresh.SessionLocal')
    @patch('app.tasks.data_refresh.MFLIngestionService')
    def test_refresh_mfl_data_task(self, mock_mfl_service, mock_session):
        """Test MFL data refresh task."""
        # Mock database and service
        mock_db = Mock()
        mock_session.return_value = mock_db
        
        mock_league = Mock()
        mock_league.id = 'test-league'
        mock_league.name = 'Test League'
        mock_league.is_active = True
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_league]
        
        mock_service_instance = Mock()
        mock_mfl_service.return_value = mock_service_instance
        mock_service_instance.refresh_league_data.return_value = {'players_updated': 10}
        
        # Mock the task binding
        with patch('app.tasks.data_refresh.refresh_mfl_data.update_state') as mock_update:
            refresh_mfl_data.update_state = mock_update
            
            result = refresh_mfl_data()
            
            assert result['status'] == 'SUCCESS'
            assert result['results']['leagues_processed'] == 1
            assert result['results']['total_players_updated'] == 10
            assert result['results']['leagues_failed'] == 0
    
    @patch('app.tasks.data_refresh.SessionLocal')
    def test_cleanup_old_task_results(self, mock_session):
        """Test cleanup of old task results."""
        # Mock database
        mock_db = Mock()
        mock_session.return_value = mock_db
        mock_db.query.return_value.filter.return_value.delete.return_value = 5
        
        with patch('app.tasks.data_refresh.cleanup_old_task_results.update_state') as mock_update:
            cleanup_old_task_results.update_state = mock_update
            
            result = cleanup_old_task_results()
            
            assert result['status'] == 'SUCCESS'
            assert result['results']['old_projections_deleted'] == 5
            assert result['results']['old_rankings_deleted'] == 5


class TestAlertProcessingTasks:
    """Test alert processing background tasks."""
    
    @patch('app.tasks.alert_processing.SessionLocal')
    @patch('app.tasks.alert_processing.AlertService')
    def test_check_upcoming_deadlines(self, mock_alert_service, mock_session):
        """Test deadline checking task."""
        # Mock database and service
        mock_db = Mock()
        mock_session.return_value = mock_db
        
        mock_league = Mock()
        mock_league.id = 'test-league'
        mock_league.name = 'Test League'
        mock_league.is_active = True
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_league]
        
        mock_service_instance = Mock()
        mock_alert_service.return_value = mock_service_instance
        mock_service_instance.check_keeper_deadlines.return_value = ['alert1']
        mock_service_instance.check_waiver_deadlines.return_value = ['alert2']
        mock_service_instance.check_lineup_deadlines.return_value = []
        mock_service_instance.check_trade_deadlines.return_value = []
        
        with patch('app.tasks.alert_processing.check_upcoming_deadlines.update_state') as mock_update:
            check_upcoming_deadlines.update_state = mock_update
            
            result = check_upcoming_deadlines()
            
            assert result['status'] == 'SUCCESS'
            assert result['results']['leagues_checked'] == 1
            assert result['results']['alerts_created'] == 2
            assert result['results']['deadline_types']['keeper'] == 1
            assert result['results']['deadline_types']['waiver'] == 1
    
    @patch('app.tasks.alert_processing.SessionLocal')
    @patch('app.tasks.alert_processing.AlertService')
    def test_send_pending_notifications(self, mock_alert_service, mock_session):
        """Test sending pending notifications."""
        # Mock database and service
        mock_db = Mock()
        mock_session.return_value = mock_db
        
        mock_alert = Mock()
        mock_alert.id = 'alert-123'
        mock_alert.status = 'pending'
        mock_alert.scheduled_for = datetime.utcnow() - timedelta(minutes=5)
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_alert]
        
        mock_service_instance = Mock()
        mock_alert_service.return_value = mock_service_instance
        mock_service_instance.send_notification.return_value = {
            'success': True,
            'type': 'email'
        }
        
        with patch('app.tasks.alert_processing.send_pending_notifications.update_state') as mock_update:
            send_pending_notifications.update_state = mock_update
            
            result = send_pending_notifications()
            
            assert result['status'] == 'SUCCESS'
            assert result['results']['notifications_sent'] == 1
            assert result['results']['notifications_failed'] == 0
            assert result['results']['notification_types']['email'] == 1


class TestFileProcessingTasks:
    """Test file processing background tasks."""
    
    @patch('app.tasks.file_processing.SessionLocal')
    @patch('app.tasks.file_processing.FileUploadService')
    def test_process_uploaded_file(self, mock_upload_service, mock_session):
        """Test file processing task."""
        # Mock database and service
        mock_db = Mock()
        mock_session.return_value = mock_db
        
        mock_service_instance = Mock()
        mock_upload_service.return_value = mock_service_instance
        mock_service_instance.process_file.return_value = {
            'saved_count': 100,
            'total_rows': 100
        }
        
        # Test data
        file_content = b"test,data\n1,2\n3,4"
        filename = "test.csv"
        data_type = "projections"
        source = "test_source"
        season = 2024
        
        with patch('app.tasks.file_processing.process_uploaded_file.update_state') as mock_update:
            process_uploaded_file.update_state = mock_update
            
            result = process_uploaded_file(file_content, filename, data_type, source, season)
            
            assert result['status'] == 'SUCCESS'
            assert result['result']['saved_count'] == 100
            assert result['result']['total_rows'] == 100


class TestTaskRetryLogic:
    """Test task retry and error handling."""
    
    def test_task_retry_configuration(self):
        """Test that tasks are configured with proper retry logic."""
        # Check that tasks have retry configuration
        task_names = [
            'app.tasks.data_refresh.refresh_mfl_data',
            'app.tasks.alert_processing.check_upcoming_deadlines',
            'app.tasks.file_processing.process_uploaded_file'
        ]
        
        for task_name in task_names:
            task = celery_app.tasks.get(task_name)
            if task:
                # Tasks should have autoretry configured
                assert hasattr(task, 'autoretry_for')
                assert hasattr(task, 'retry_kwargs')
    
    @patch('app.tasks.data_refresh.SessionLocal')
    def test_task_failure_handling(self, mock_session):
        """Test task failure handling and dead letter queue."""
        # Mock database to raise an exception
        mock_session.side_effect = Exception("Database connection failed")
        
        with patch('app.tasks.data_refresh.refresh_mfl_data.update_state') as mock_update:
            refresh_mfl_data.update_state = mock_update
            
            with pytest.raises(Exception) as exc_info:
                refresh_mfl_data()
            
            assert "Database connection failed" in str(exc_info.value)
            # Verify that failure state was updated
            mock_update.assert_called()


class TestJobAPIIntegration:
    """Test integration with job management API."""
    
    def test_job_monitor_integration(self):
        """Test that JobMonitor integrates properly with API."""
        monitor = JobMonitor()
        
        # Test that monitor can be instantiated and has required methods
        assert hasattr(monitor, 'get_active_tasks')
        assert hasattr(monitor, 'get_scheduled_tasks')
        assert hasattr(monitor, 'get_worker_stats')
        assert hasattr(monitor, 'get_task_status')
        assert hasattr(monitor, 'retry_failed_task')
        assert hasattr(monitor, 'cancel_task')
    
    def test_task_triggering(self):
        """Test that tasks can be triggered programmatically."""
        # Test that we can create task instances (without executing them)
        tasks_to_test = [
            refresh_mfl_data,
            refresh_projection_aggregates,
            check_upcoming_deadlines,
            process_player_news
        ]
        
        for task in tasks_to_test:
            assert callable(task)
            assert hasattr(task, 'delay')
            assert hasattr(task, 'apply_async')


if __name__ == "__main__":
    pytest.main([__file__])