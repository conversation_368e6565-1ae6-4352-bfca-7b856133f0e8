"""
Unit tests for file upload and data processing functionality.
"""
import io
import pytest
import pandas as pd
from decimal import Decimal
from fastapi import UploadFile
from sqlalchemy.orm import Session
from unittest.mock import Mock, patch

from app.services.file_upload import (
    FileUploadService, 
    FileUploadError, 
    DataValidationError,
    ProjectionData,
    RankingData
)
from app.models import Player, Projection, Ranking
from tests.conftest import TestingSessionLocal


class TestFileUploadService:
    """Test cases for FileUploadService."""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, db_session):
        """Set up test fixtures."""
        self.db = db_session
        self.service = FileUploadService(self.db)
    
    def create_mock_upload_file(self, content: str, filename: str) -> UploadFile:
        """Create a mock UploadFile for testing."""
        file_obj = io.BytesIO(content.encode())
        return UploadFile(filename=filename, file=file_obj)
    
    def test_validate_file_success(self):
        """Test successful file validation."""
        file = self.create_mock_upload_file("test content", "test.csv")
        # Should not raise any exception
        self.service.validate_file(file)
    
    def test_validate_file_no_filename(self):
        """Test file validation with no filename."""
        file = Mock()
        file.filename = None
        
        with pytest.raises(FileUploadError, match="No filename provided"):
            self.service.validate_file(file)
    
    def test_validate_file_unsupported_extension(self):
        """Test file validation with unsupported extension."""
        file = self.create_mock_upload_file("test content", "test.txt")
        
        with pytest.raises(FileUploadError, match="Unsupported file format"):
            self.service.validate_file(file)
    
    def test_validate_file_too_large(self):
        """Test file validation with file too large."""
        # Create a large content string
        large_content = "x" * (11 * 1024 * 1024)  # 11MB
        file_obj = io.BytesIO(large_content.encode())
        file = UploadFile(filename="large.csv", file=file_obj)
        
        with pytest.raises(FileUploadError, match="File too large"):
            self.service.validate_file(file)
    
    def test_read_csv_file_success(self):
        """Test successful CSV file reading."""
        csv_content = "player_name,projected_points\nJosh Allen,25.5\nLamar Jackson,24.2"
        file = self.create_mock_upload_file(csv_content, "test.csv")
        
        df = self.service.read_file_to_dataframe(file)
        
        assert len(df) == 2
        assert "player_name" in df.columns
        assert "projected_points" in df.columns
        assert df.iloc[0]["player_name"] == "Josh Allen"
    
    def test_read_empty_file(self):
        """Test reading empty file."""
        file = self.create_mock_upload_file("", "empty.csv")
        
        with pytest.raises(FileUploadError, match="File is empty"):
            self.service.read_file_to_dataframe(file)
    
    def test_normalize_column_names(self):
        """Test column name normalization."""
        df = pd.DataFrame({
            "Player": ["Josh Allen"],
            "Proj Pts": [25.5],
            "Pos": ["QB"],
            "Team": ["BUF"]
        })
        
        normalized_df = self.service.normalize_column_names(df)
        
        assert "player_name" in normalized_df.columns
        assert "projected_points" in normalized_df.columns
        assert "position" in normalized_df.columns
        assert "team" in normalized_df.columns
    
    def test_parse_projections_success(self):
        """Test successful projection parsing."""
        df = pd.DataFrame({
            "player_name": ["Josh Allen", "Lamar Jackson"],
            "projected_points": [25.5, 24.2],
            "position": ["QB", "QB"],
            "team": ["BUF", "BAL"],
            "passing_yards": [4200, 3800],
            "passing_tds": [32, 28]
        })
        
        projections = self.service.parse_projections(df, "test_source", 2024)
        
        assert len(projections) == 2
        assert projections[0].player_name == "Josh Allen"
        assert projections[0].projected_points == 25.5
        assert projections[0].position == "QB"
        assert projections[0].passing_yards == 4200.0
    
    def test_parse_projections_missing_required_column(self):
        """Test projection parsing with missing required column."""
        df = pd.DataFrame({
            "player_name": ["Josh Allen"],
            # Missing projected_points
            "position": ["QB"]
        })
        
        with pytest.raises(DataValidationError, match="Missing required columns"):
            self.service.parse_projections(df, "test_source", 2024)
    
    def test_parse_rankings_success(self):
        """Test successful ranking parsing."""
        df = pd.DataFrame({
            "player_name": ["Josh Allen", "Lamar Jackson"],
            "position": ["QB", "QB"],
            "overall_rank": [1, 2],
            "position_rank": [1, 2],
            "adp": [1.5, 2.3]
        })
        
        rankings = self.service.parse_rankings(df, "test_source", 2024, "expert")
        
        assert len(rankings) == 2
        assert rankings[0].player_name == "Josh Allen"
        assert rankings[0].overall_rank == 1
        assert rankings[0].adp == 1.5
    
    def test_find_or_create_player_existing(self):
        """Test finding existing player."""
        # Create a test player
        player = Player(
            id="test_player_1",
            name="Josh Allen",
            position="QB",
            team="BUF"
        )
        self.db.add(player)
        self.db.commit()
        
        found_player = self.service.find_or_create_player("Josh Allen", "QB", "BUF")
        
        assert found_player.id == "test_player_1"
        assert found_player.name == "Josh Allen"
    
    def test_find_or_create_player_new(self):
        """Test creating new player."""
        player = self.service.find_or_create_player("New Player", "RB", "NYJ")
        
        assert player.name == "New Player"
        assert player.position == "RB"
        assert player.team == "NYJ"
        assert player.player_metadata.get("created_from_upload") is True
    
    def test_save_projections_success(self):
        """Test successful projection saving."""
        # Create test player
        player = Player(
            id="test_player_1",
            name="Josh Allen",
            position="QB",
            team="BUF"
        )
        self.db.add(player)
        self.db.commit()
        
        projection_data = [
            ProjectionData(
                player_name="Josh Allen",
                position="QB",
                team="BUF",
                season=2024,
                projected_points=25.5,
                passing_yards=4200,
                passing_tds=32
            )
        ]
        
        saved_count, errors = self.service.save_projections(projection_data, "test_source")
        
        assert saved_count == 1
        assert len(errors) == 0
        
        # Verify projection was saved
        projection = self.db.query(Projection).first()
        assert projection is not None
        assert projection.player_id == "test_player_1"
        assert projection.projected_points == Decimal("25.5")
        assert projection.stats["passing_yards"] == 4200
    
    def test_save_rankings_success(self):
        """Test successful ranking saving."""
        # Create test player
        player = Player(
            id="test_player_1",
            name="Josh Allen",
            position="QB",
            team="BUF"
        )
        self.db.add(player)
        self.db.commit()
        
        ranking_data = [
            RankingData(
                player_name="Josh Allen",
                position="QB",
                team="BUF",
                season=2024,
                overall_rank=1,
                position_rank=1,
                adp=1.5
            )
        ]
        
        saved_count, errors = self.service.save_rankings(ranking_data, "test_source", "expert")
        
        assert saved_count == 1
        assert len(errors) == 0
        
        # Verify ranking was saved
        ranking = self.db.query(Ranking).first()
        assert ranking is not None
        assert ranking.player_id == "test_player_1"
        assert ranking.overall_rank == 1
        assert ranking.adp == Decimal("1.5")
    
    def test_process_file_projections(self):
        """Test complete file processing for projections."""
        csv_content = "player_name,projected_points,position,team\nJosh Allen,25.5,QB,BUF"
        file = self.create_mock_upload_file(csv_content, "projections.csv")
        
        result = self.service.process_file(file, "projections", "test_source", 2024)
        
        assert result["success"] is True
        assert result["data_type"] == "projections"
        assert result["saved_count"] == 1
        assert result["total_rows"] == 1
        assert len(result["errors"]) == 0
    
    def test_process_file_rankings(self):
        """Test complete file processing for rankings."""
        csv_content = "player_name,overall_rank,position\nJosh Allen,1,QB"
        file = self.create_mock_upload_file(csv_content, "rankings.csv")
        
        result = self.service.process_file(file, "rankings", "test_source", 2024)
        
        assert result["success"] is True
        assert result["data_type"] == "rankings"
        assert result["saved_count"] == 1
        assert result["total_rows"] == 1
    
    def test_process_file_unsupported_type(self):
        """Test processing file with unsupported data type."""
        csv_content = "player_name,value\nJosh Allen,100"
        file = self.create_mock_upload_file(csv_content, "test.csv")
        
        with pytest.raises(FileUploadError, match="Unsupported data type"):
            self.service.process_file(file, "unsupported", "test_source", 2024)


class TestProjectionData:
    """Test cases for ProjectionData validation."""
    
    def test_valid_projection_data(self):
        """Test valid projection data creation."""
        data = ProjectionData(
            player_name="Josh Allen",
            position="QB",
            team="BUF",
            season=2024,
            projected_points=25.5,
            passing_yards=4200,
            passing_tds=32
        )
        
        assert data.player_name == "Josh Allen"
        assert data.projected_points == 25.5
        assert data.passing_yards == 4200
    
    def test_projection_data_missing_required(self):
        """Test projection data with missing required fields."""
        with pytest.raises(ValueError):
            ProjectionData(
                # Missing player_name
                position="QB",
                season=2024,
                projected_points=25.5
            )


class TestRankingData:
    """Test cases for RankingData validation."""
    
    def test_valid_ranking_data(self):
        """Test valid ranking data creation."""
        data = RankingData(
            player_name="Josh Allen",
            position="QB",
            season=2024,
            overall_rank=1,
            adp=1.5
        )
        
        assert data.player_name == "Josh Allen"
        assert data.overall_rank == 1
        assert data.adp == 1.5
    
    def test_ranking_data_optional_fields(self):
        """Test ranking data with only required fields."""
        data = RankingData(
            player_name="Josh Allen",
            season=2024
        )
        
        assert data.player_name == "Josh Allen"
        assert data.overall_rank is None
        assert data.adp is None