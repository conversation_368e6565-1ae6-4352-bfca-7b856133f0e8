"""
Mock data generator for testing scenarios.
Provides utilities to create realistic test data for various testing scenarios.
"""
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Optional, Dict, List, Any
import random
import uuid

from sqlalchemy.orm import Session
from app.models.league import League
from app.models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RosterPlayer
from app.models.player import Player, PlayerPosition, InjuryStatus
from app.models.projection import Projection
from app.models.ranking import Ranking
from app.models.recommendation import Recommendation, RecommendationType


class MockDataGenerator:
    """Generates mock data for testing scenarios."""
    
    def __init__(self, db_session: Session):
        """Initialize with database session."""
        self.db = db_session
        self._player_counter = 0
        self._franchise_counter = 0
        self._league_counter = 0
    
    def create_league(
        self,
        league_id: Optional[str] = None,
        name: Optional[str] = None,
        season: int = 2024,
        scoring_rules: Optional[Dict[str, Any]] = None,
        roster_slots: Optional[List[Dict[str, Any]]] = None,
        keeper_rules: Optional[Dict[str, Any]] = None,
        draft_settings: Optional[Dict[str, Any]] = None
    ) -> League:
        """Create a mock league with specified parameters."""
        if league_id is None:
            self._league_counter += 1
            league_id = f"mock_league_{self._league_counter}"
        
        if name is None:
            name = f"Mock League {self._league_counter}"
        
        if scoring_rules is None:
            scoring_rules = {
                "passing_yards": 0.04,
                "passing_tds": 4,
                "rushing_yards": 0.1,
                "rushing_tds": 6,
                "receiving_yards": 0.1,
                "receiving_tds": 6,
                "fumbles": -2,
                "interceptions": -2
            }
        
        if roster_slots is None:
            roster_slots = [
                {"position": "QB", "count": 1},
                {"position": "RB", "count": 2},
                {"position": "WR", "count": 2},
                {"position": "TE", "count": 1},
                {"position": "FLEX", "count": 1},
                {"position": "K", "count": 1},
                {"position": "DEF", "count": 1}
            ]
        
        league = League(
            id=league_id,
            name=name,
            season=season,
            scoring_rules=scoring_rules,
            roster_slots=roster_slots,
            keeper_rules=keeper_rules,
            draft_settings=draft_settings,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(league)
        self.db.commit()
        self.db.refresh(league)
        
        return league
    
    def create_franchise(
        self,
        franchise_id: Optional[str] = None,
        league_id: str = None,
        name: Optional[str] = None,
        owner_name: Optional[str] = None,
        draft_position: Optional[int] = None,
        faab_budget: Optional[Decimal] = None,
        faab_spent: Optional[Decimal] = None
    ) -> Franchise:
        """Create a mock franchise."""
        if franchise_id is None:
            self._franchise_counter += 1
            franchise_id = f"mock_franchise_{self._franchise_counter}"
        
        if name is None:
            name = f"Mock Team {self._franchise_counter}"
        
        if owner_name is None:
            owner_name = f"Owner {self._franchise_counter}"
        
        if faab_budget is None:
            faab_budget = Decimal('100')
        
        if faab_spent is None:
            faab_spent = Decimal('0')
        
        franchise = Franchise(
            id=franchise_id,
            name=name,
            owner_name=owner_name,
            league_id=league_id,
            draft_position=draft_position,
            faab_budget=faab_budget,
            faab_spent=faab_spent,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(franchise)
        
        # Create associated roster
        roster = Roster(
            id=f"{franchise_id}_roster",
            franchise_id=franchise_id,
            is_valid=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(roster)
        self.db.commit()
        
        self.db.refresh(franchise)
        self.db.refresh(roster)
        
        return franchise
    
    def create_player(
        self,
        player_id: Optional[str] = None,
        name: Optional[str] = None,
        position: PlayerPosition = PlayerPosition.RB,
        team: str = "TEST",
        bye_week: Optional[int] = None,
        injury_status: InjuryStatus = InjuryStatus.HEALTHY,
        age: Optional[int] = None,
        experience: Optional[int] = None
    ) -> Player:
        """Create a mock player."""
        if player_id is None:
            self._player_counter += 1
            player_id = f"mock_player_{self._player_counter}"
        
        if name is None:
            name = f"Mock Player {self._player_counter}"
        
        if bye_week is None:
            bye_week = random.randint(4, 14)
        
        if age is None:
            age = random.randint(22, 35)
        
        if experience is None:
            experience = random.randint(1, 15)
        
        player = Player(
            id=player_id,
            name=name,
            position=position,
            team=team,
            bye_week=bye_week,
            injury_status=injury_status,
            age=age,
            experience=experience,
            metadata={},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(player)
        self.db.commit()
        self.db.refresh(player)
        
        return player
    
    def create_projection(
        self,
        player_id: str,
        projected_points: float,
        week: Optional[int] = None,
        season: int = 2024,
        source: str = "mock_source",
        floor: Optional[float] = None,
        ceiling: Optional[float] = None,
        variance: Optional[float] = None,
        confidence: Optional[float] = None,
        created_at: Optional[datetime] = None
    ) -> Projection:
        """Create a mock projection."""
        if floor is None:
            floor = projected_points * 0.7
        
        if ceiling is None:
            ceiling = projected_points * 1.3
        
        if variance is None:
            variance = projected_points * 0.15
        
        if confidence is None:
            confidence = random.uniform(0.6, 0.9)
        
        if created_at is None:
            created_at = datetime.utcnow()
        
        projection = Projection(
            id=str(uuid.uuid4()),
            player_id=player_id,
            week=week,
            season=season,
            source=source,
            projected_points=Decimal(str(projected_points)),
            floor=Decimal(str(floor)),
            ceiling=Decimal(str(ceiling)),
            variance=Decimal(str(variance)),
            confidence=confidence,
            is_active=True,
            created_at=created_at,
            updated_at=created_at
        )
        
        self.db.add(projection)
        self.db.commit()
        self.db.refresh(projection)
        
        return projection
    
    def create_ranking(
        self,
        player_id: str,
        rank: int,
        position_rank: int,
        source: str = "mock_source",
        tier: Optional[int] = None,
        week: Optional[int] = None,
        season: int = 2024
    ) -> Ranking:
        """Create a mock ranking."""
        if tier is None:
            if rank <= 12:
                tier = 1
            elif rank <= 24:
                tier = 2
            elif rank <= 36:
                tier = 3
            else:
                tier = 4
        
        ranking = Ranking(
            id=str(uuid.uuid4()),
            player_id=player_id,
            rank=rank,
            position_rank=position_rank,
            tier=tier,
            source=source,
            week=week,
            season=season,
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(ranking)
        self.db.commit()
        self.db.refresh(ranking)
        
        return ranking
    
    def add_player_to_roster(
        self,
        franchise_id: str,
        player_id: str,
        keeper_cost: Optional[int] = None,
        is_keeper: bool = False,
        is_active: bool = True,
        acquisition_type: str = "draft"
    ) -> RosterPlayer:
        """Add a player to a franchise's roster."""
        # Get the roster for this franchise
        roster = self.db.query(Roster).filter(Roster.franchise_id == franchise_id).first()
        
        if not roster:
            # Create roster if it doesn't exist
            roster = Roster(
                id=f"{franchise_id}_roster",
                franchise_id=franchise_id,
                is_valid=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            self.db.add(roster)
            self.db.commit()
            self.db.refresh(roster)
        
        if keeper_cost is None:
            keeper_cost = random.randint(1, 16)
        
        roster_player = RosterPlayer(
            id=str(uuid.uuid4()),
            roster_id=roster.id,
            player_id=player_id,
            keeper_cost=keeper_cost,
            is_keeper=is_keeper,
            is_active=is_active,
            acquisition_type=acquisition_type,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(roster_player)
        self.db.commit()
        self.db.refresh(roster_player)
        
        return roster_player
    
    def create_recommendation(
        self,
        recommendation_type: RecommendationType,
        franchise_id: str,
        title: str,
        description: str,
        rationale: str,
        confidence: float = 0.8,
        player_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        expires_at: Optional[datetime] = None
    ) -> Recommendation:
        """Create a mock recommendation."""
        if metadata is None:
            metadata = {}
        
        if expires_at is None:
            expires_at = datetime.utcnow() + timedelta(days=7)
        
        recommendation = Recommendation(
            id=str(uuid.uuid4()),
            type=recommendation_type,
            franchise_id=franchise_id,
            player_id=player_id,
            title=title,
            description=description,
            rationale=rationale,
            confidence=confidence,
            metadata=metadata,
            expires_at=expires_at,
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(recommendation)
        self.db.commit()
        self.db.refresh(recommendation)
        
        return recommendation
    
    def create_complete_league_setup(
        self,
        num_franchises: int = 12,
        players_per_franchise: int = 16,
        include_projections: bool = True,
        include_rankings: bool = True,
        season: int = 2024
    ) -> Dict[str, Any]:
        """Create a complete league setup with franchises, players, projections, etc."""
        # Create league
        league = self.create_league(
            name=f"Complete Test League {season}",
            season=season
        )
        
        # Create franchises
        franchises = []
        for i in range(num_franchises):
            franchise = self.create_franchise(
                league_id=league.id,
                name=f"Team {i+1}",
                draft_position=i+1
            )
            franchises.append(franchise)
        
        # Create player pool
        positions = [PlayerPosition.QB, PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE, PlayerPosition.K, PlayerPosition.DEF]
        all_players = []
        
        total_players = num_franchises * players_per_franchise
        
        for i in range(total_players):
            position = positions[i % len(positions)]
            
            player = self.create_player(
                name=f"Player {i+1}",
                position=position,
                team=f"TEAM{(i % 32) + 1}"  # 32 NFL teams
            )
            all_players.append(player)
            
            # Create projections if requested
            if include_projections:
                # Create tiered projections based on player index
                if position == PlayerPosition.QB:
                    base_points = 300 - (i // 4) * 10  # QBs: 300, 290, 280, etc.
                elif position == PlayerPosition.RB:
                    base_points = 250 - (i // 4) * 8   # RBs: 250, 242, 234, etc.
                elif position == PlayerPosition.WR:
                    base_points = 220 - (i // 4) * 6   # WRs: 220, 214, 208, etc.
                elif position == PlayerPosition.TE:
                    base_points = 180 - (i // 4) * 5   # TEs: 180, 175, 170, etc.
                else:
                    base_points = 120 - (i // 4) * 3   # K/DEF: 120, 117, 114, etc.
                
                # Add some randomness
                projected_points = max(50, base_points + random.randint(-20, 20))
                
                self.create_projection(
                    player_id=player.id,
                    projected_points=projected_points,
                    season=season
                )
            
            # Create rankings if requested
            if include_rankings:
                overall_rank = i + 1
                position_players = [p for p in all_players if p.position == position]
                position_rank = len(position_players)
                
                self.create_ranking(
                    player_id=player.id,
                    rank=overall_rank,
                    position_rank=position_rank,
                    season=season
                )
        
        # Distribute players to franchises
        random.shuffle(all_players)
        
        for i, player in enumerate(all_players):
            franchise_index = i % num_franchises
            franchise = franchises[franchise_index]
            
            self.add_player_to_roster(
                franchise_id=franchise.id,
                player_id=player.id
            )
        
        return {
            "league": league,
            "franchises": franchises,
            "players": all_players,
            "total_players": len(all_players)
        }
    
    def create_draft_scenario(
        self,
        num_teams: int = 12,
        num_rounds: int = 16,
        snake_draft: bool = True
    ) -> Dict[str, Any]:
        """Create a draft scenario with pick order and available players."""
        league = self.create_league(
            name="Draft Scenario League",
            draft_settings={
                "draft_type": "snake" if snake_draft else "linear",
                "teams": num_teams,
                "rounds": num_rounds
            }
        )
        
        # Create franchises with draft positions
        franchises = []
        for i in range(num_teams):
            franchise = self.create_franchise(
                league_id=league.id,
                name=f"Draft Team {i+1}",
                draft_position=i+1
            )
            franchises.append(franchise)
        
        # Create large player pool for drafting
        positions = [PlayerPosition.QB, PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE, PlayerPosition.K, PlayerPosition.DEF]
        players = []
        
        for i in range(300):  # Large player pool
            position = positions[i % len(positions)]
            
            player = self.create_player(
                name=f"Draft Player {i+1}",
                position=position
            )
            
            # Create tiered projections for draft value
            tier = i // 20  # 20 players per tier
            base_points = 300 - tier * 15
            projected_points = base_points + random.randint(-10, 10)
            
            self.create_projection(
                player_id=player.id,
                projected_points=projected_points
            )
            
            players.append(player)
        
        # Generate pick order
        pick_order = []
        for round_num in range(1, num_rounds + 1):
            if snake_draft and round_num % 2 == 0:
                # Reverse order for even rounds in snake draft
                round_picks = list(reversed(range(1, num_teams + 1)))
            else:
                round_picks = list(range(1, num_teams + 1))
            
            for pick_pos in round_picks:
                pick_number = (round_num - 1) * num_teams + (pick_pos if round_num % 2 == 1 or not snake_draft else num_teams - pick_pos + 1)
                pick_order.append({
                    "pick_number": pick_number,
                    "round": round_num,
                    "franchise_id": franchises[pick_pos - 1].id,
                    "draft_position": pick_pos
                })
        
        return {
            "league": league,
            "franchises": franchises,
            "players": players,
            "pick_order": pick_order,
            "total_picks": len(pick_order)
        }
    
    def create_keeper_scenario(
        self,
        num_keepers: int = 3,
        escalation_rounds: int = 1,
        max_round: int = 16
    ) -> Dict[str, Any]:
        """Create a keeper scenario with eligible players and constraints."""
        league = self.create_league(
            name="Keeper Scenario League",
            keeper_rules={
                "max_keepers": num_keepers,
                "round_escalation": escalation_rounds,
                "max_round": max_round,
                "position_limits": {"QB": 1, "RB": 2, "WR": 2, "TE": 1}
            }
        )
        
        franchise = self.create_franchise(
            league_id=league.id,
            name="Keeper Test Team"
        )
        
        # Create players with various keeper values
        keeper_candidates = [
            {"name": "Elite QB", "position": PlayerPosition.QB, "points": 320, "cost": 3},
            {"name": "Top RB1", "position": PlayerPosition.RB, "points": 280, "cost": 5},
            {"name": "Top RB2", "position": PlayerPosition.RB, "points": 270, "cost": 7},
            {"name": "WR1", "position": PlayerPosition.WR, "points": 250, "cost": 4},
            {"name": "WR2", "position": PlayerPosition.WR, "points": 240, "cost": 6},
            {"name": "Solid TE", "position": PlayerPosition.TE, "points": 180, "cost": 10},
            {"name": "Injured RB", "position": PlayerPosition.RB, "points": 200, "cost": 8, "injury": InjuryStatus.OUT},
            {"name": "Expensive QB", "position": PlayerPosition.QB, "points": 300, "cost": 1},  # Too expensive after escalation
        ]
        
        players = []
        for candidate in keeper_candidates:
            player = self.create_player(
                name=candidate["name"],
                position=candidate["position"],
                injury_status=candidate.get("injury", InjuryStatus.HEALTHY)
            )
            
            self.create_projection(
                player_id=player.id,
                projected_points=candidate["points"]
            )
            
            self.add_player_to_roster(
                franchise_id=franchise.id,
                player_id=player.id,
                keeper_cost=candidate["cost"]
            )
            
            players.append(player)
        
        return {
            "league": league,
            "franchise": franchise,
            "players": players,
            "keeper_candidates": keeper_candidates
        }
    
    def cleanup_test_data(self):
        """Clean up all test data created by this generator."""
        # This would delete all test data
        # For now, we rely on test database cleanup
        pass