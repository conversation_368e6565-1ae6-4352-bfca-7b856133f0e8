"""
Test data factory for generating realistic test scenarios.
Provides pre-built scenarios for common testing situations.
"""
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal
import random

from app.models.player import PlayerPosition, InjuryStatus
from backend.tests.utils.mock_data_generator import MockDataGenerator


class TestDataFactory:
    """Factory for creating realistic test data scenarios."""
    
    def __init__(self, mock_data_generator: MockDataGenerator):
        """Initialize with mock data generator."""
        self.generator = mock_data_generator
    
    def create_realistic_keeper_scenario(self) -> Dict[str, Any]:
        """Create a realistic keeper scenario with typical player values and constraints."""
        league = self.generator.create_league(
            league_id="realistic_keeper_league",
            name="Realistic Keeper League",
            keeper_rules={
                "max_keepers": 3,
                "round_escalation": 1,
                "max_round": 16,
                "position_limits": {"QB": 1, "RB": 2, "WR": 2, "TE": 1},
                "no_injured_keepers": True,
                "salary_cap": None
            }
        )
        
        franchise = self.generator.create_franchise(
            franchise_id="realistic_keeper_franchise",
            league_id=league.id,
            name="The Realistic Team"
        )
        
        # Create realistic player scenarios
        realistic_players = [
            # Elite players with good value
            {
                "name": "Josh <PERSON>",
                "position": PlayerPosition.QB,
                "projected_points": 385,
                "keeper_cost": 6,
                "injury_status": InjuryStatus.HEALTHY,
                "tier": 1
            },
            {
                "name": "Christian McCaffrey",
                "position": PlayerPosition.RB,
                "projected_points": 320,
                "keeper_cost": 2,
                "injury_status": InjuryStatus.HEALTHY,
                "tier": 1
            },
            # Good players with decent value
            {
                "name": "Stefon Diggs",
                "position": PlayerPosition.WR,
                "projected_points": 285,
                "keeper_cost": 4,
                "injury_status": InjuryStatus.HEALTHY,
                "tier": 2
            },
            {
                "name": "Saquon Barkley",
                "position": PlayerPosition.RB,
                "projected_points": 275,
                "keeper_cost": 3,
                "injury_status": InjuryStatus.QUESTIONABLE,
                "tier": 2
            },
            # Solid players with questionable value
            {
                "name": "Mike Evans",
                "position": PlayerPosition.WR,
                "projected_points": 245,
                "keeper_cost": 5,
                "injury_status": InjuryStatus.HEALTHY,
                "tier": 3
            },
            {
                "name": "Travis Kelce",
                "position": PlayerPosition.TE,
                "projected_points": 220,
                "keeper_cost": 7,
                "injury_status": InjuryStatus.HEALTHY,
                "tier": 2
            },
            # Poor value players
            {
                "name": "Aaron Rodgers",
                "position": PlayerPosition.QB,
                "projected_points": 310,
                "keeper_cost": 1,  # Too expensive after escalation
                "injury_status": InjuryStatus.HEALTHY,
                "tier": 2
            },
            {
                "name": "Injured Star RB",
                "position": PlayerPosition.RB,
                "projected_points": 290,
                "keeper_cost": 4,
                "injury_status": InjuryStatus.OUT,  # Violates no injured keepers rule
                "tier": 1
            },
            # Bench players
            {
                "name": "Backup QB",
                "position": PlayerPosition.QB,
                "projected_points": 180,
                "keeper_cost": 12,
                "injury_status": InjuryStatus.HEALTHY,
                "tier": 4
            },
            {
                "name": "Handcuff RB",
                "position": PlayerPosition.RB,
                "projected_points": 150,
                "keeper_cost": 14,
                "injury_status": InjuryStatus.HEALTHY,
                "tier": 4
            }
        ]
        
        players = []
        for player_data in realistic_players:
            player = self.generator.create_player(
                name=player_data["name"],
                position=player_data["position"],
                injury_status=player_data["injury_status"]
            )
            
            # Create realistic projections with variance
            base_points = player_data["projected_points"]
            variance = base_points * 0.15  # 15% variance
            
            self.generator.create_projection(
                player_id=player.id,
                projected_points=base_points,
                floor=base_points * 0.75,
                ceiling=base_points * 1.25,
                variance=variance,
                confidence=0.8 if player_data["tier"] <= 2 else 0.6
            )
            
            # Add to roster
            self.generator.add_player_to_roster(
                franchise_id=franchise.id,
                player_id=player.id,
                keeper_cost=player_data["keeper_cost"]
            )
            
            players.append(player)
        
        return {
            "league": league,
            "franchise": franchise,
            "players": players,
            "expected_keepers": ["Christian McCaffrey", "Josh Allen", "Stefon Diggs"],
            "expected_total_value": 990,  # Approximate total projected points
            "constraint_violations": ["Aaron Rodgers", "Injured Star RB"]
        }
    
    def create_competitive_draft_scenario(self) -> Dict[str, Any]:
        """Create a competitive 12-team draft scenario with realistic player values."""
        league = self.generator.create_league(
            league_id="competitive_draft_league",
            name="Competitive Draft League",
            draft_settings={
                "draft_type": "snake",
                "teams": 12,
                "rounds": 16,
                "pick_time_seconds": 90
            }
        )
        
        # Create 12 franchises
        franchises = []
        for i in range(12):
            franchise = self.generator.create_franchise(
                franchise_id=f"draft_team_{i+1}",
                league_id=league.id,
                name=f"Team {i+1}",
                draft_position=i+1
            )
            franchises.append(franchise)
        
        # Create tiered player pool with realistic NFL players
        player_tiers = {
            1: [  # Elite tier (picks 1-12)
                ("Christian McCaffrey", PlayerPosition.RB, 320),
                ("Austin Ekeler", PlayerPosition.RB, 315),
                ("Josh Allen", PlayerPosition.QB, 385),
                ("Cooper Kupp", PlayerPosition.WR, 310),
                ("Stefon Diggs", PlayerPosition.WR, 295),
                ("Davante Adams", PlayerPosition.WR, 290),
                ("Travis Kelce", PlayerPosition.TE, 220),
                ("Derrick Henry", PlayerPosition.RB, 285),
                ("Tyreek Hill", PlayerPosition.WR, 285),
                ("Jonathan Taylor", PlayerPosition.RB, 280),
                ("Ja'Marr Chase", PlayerPosition.WR, 280),
                ("Nick Chubb", PlayerPosition.RB, 275)
            ],
            2: [  # Second tier (picks 13-36)
                ("Saquon Barkley", PlayerPosition.RB, 270),
                ("CeeDee Lamb", PlayerPosition.WR, 275),
                ("A.J. Brown", PlayerPosition.WR, 270),
                ("Mike Evans", PlayerPosition.WR, 265),
                ("Lamar Jackson", PlayerPosition.QB, 360),
                ("Aaron Jones", PlayerPosition.RB, 260),
                ("Alvin Kamara", PlayerPosition.RB, 255),
                ("DeAndre Hopkins", PlayerPosition.WR, 250),
                ("Keenan Allen", PlayerPosition.WR, 245),
                ("DK Metcalf", PlayerPosition.WR, 240),
                ("Mark Andrews", PlayerPosition.TE, 195),
                ("Joe Mixon", PlayerPosition.RB, 250),
                ("Dalvin Cook", PlayerPosition.RB, 245),
                ("Calvin Ridley", PlayerPosition.WR, 235),
                ("Chris Godwin", PlayerPosition.WR, 230),
                ("Amari Cooper", PlayerPosition.WR, 225),
                ("Josh Jacobs", PlayerPosition.RB, 240),
                ("Kenneth Walker III", PlayerPosition.RB, 235),
                ("Jaylen Waddle", PlayerPosition.WR, 220),
                ("Tee Higgins", PlayerPosition.WR, 215),
                ("George Kittle", PlayerPosition.TE, 185),
                ("T.J. Hockenson", PlayerPosition.TE, 175),
                ("Najee Harris", PlayerPosition.RB, 230),
                ("Breece Hall", PlayerPosition.RB, 225)
            ],
            3: [  # Third tier (picks 37-72)
                ("Patrick Mahomes", PlayerPosition.QB, 340),
                ("Joe Burrow", PlayerPosition.QB, 330),
                ("Dak Prescott", PlayerPosition.QB, 320),
                ("Justin Herbert", PlayerPosition.QB, 315),
                ("Jalen Hurts", PlayerPosition.QB, 350),
                ("Tony Pollard", PlayerPosition.RB, 220),
                ("Miles Sanders", PlayerPosition.RB, 215),
                ("Cam Akers", PlayerPosition.RB, 210),
                ("Tyler Lockett", PlayerPosition.WR, 210),
                ("Courtland Sutton", PlayerPosition.WR, 205),
                ("Michael Pittman Jr.", PlayerPosition.WR, 200),
                ("Jerry Jeudy", PlayerPosition.WR, 195),
                ("Kyle Pitts", PlayerPosition.TE, 170),
                ("Dallas Goedert", PlayerPosition.TE, 165),
                ("Pat Freiermuth", PlayerPosition.TE, 160)
            ] + [(f"Tier 3 Player {i}", PlayerPosition.RB if i % 2 == 0 else PlayerPosition.WR, 190 - i) for i in range(21)]
        }
        
        # Create all players
        all_players = []
        for tier, players_data in player_tiers.items():
            for name, position, points in players_data:
                player = self.generator.create_player(
                    name=name,
                    position=position,
                    team=f"TEAM{random.randint(1, 32)}"
                )
                
                # Add some variance to projections
                variance = random.uniform(0.1, 0.2)
                actual_points = points + random.uniform(-points * variance, points * variance)
                
                self.generator.create_projection(
                    player_id=player.id,
                    projected_points=actual_points,
                    floor=actual_points * 0.7,
                    ceiling=actual_points * 1.3,
                    variance=actual_points * variance
                )
                
                all_players.append({
                    "player": player,
                    "tier": tier,
                    "adp": len(all_players) + 1
                })
        
        # Generate realistic draft order
        pick_order = []
        for round_num in range(1, 17):  # 16 rounds
            if round_num % 2 == 1:  # Odd rounds: 1-12
                round_picks = list(range(1, 13))
            else:  # Even rounds: 12-1 (snake)
                round_picks = list(range(12, 0, -1))
            
            for position in round_picks:
                pick_number = (round_num - 1) * 12 + (position if round_num % 2 == 1 else 13 - position)
                pick_order.append({
                    "pick_number": pick_number,
                    "round": round_num,
                    "franchise_id": franchises[position - 1].id,
                    "draft_position": position
                })
        
        return {
            "league": league,
            "franchises": franchises,
            "players": all_players,
            "pick_order": pick_order,
            "total_picks": len(pick_order),
            "user_franchise": franchises[0],  # User is team 1
            "expected_first_pick": "Christian McCaffrey"
        }
    
    def create_trade_analysis_scenario(self) -> Dict[str, Any]:
        """Create a scenario for testing trade analysis with realistic team needs."""
        league = self.generator.create_league(
            league_id="trade_analysis_league",
            name="Trade Analysis League"
        )
        
        # Create two franchises with complementary needs
        franchise_a = self.generator.create_franchise(
            franchise_id="team_rb_heavy",
            league_id=league.id,
            name="RB Heavy Team"
        )
        
        franchise_b = self.generator.create_franchise(
            franchise_id="team_wr_heavy",
            league_id=league.id,
            name="WR Heavy Team"
        )
        
        # Team A: Strong at RB, weak at WR
        team_a_players = [
            ("Elite RB1", PlayerPosition.RB, 300, True),  # Surplus
            ("Elite RB2", PlayerPosition.RB, 280, True),  # Surplus
            ("Solid RB3", PlayerPosition.RB, 220, True),  # Surplus
            ("Average QB", PlayerPosition.QB, 250, False),
            ("Weak WR1", PlayerPosition.WR, 180, False),  # Need
            ("Weak WR2", PlayerPosition.WR, 160, False),  # Need
            ("Average TE", PlayerPosition.TE, 150, False)
        ]
        
        # Team B: Strong at WR, weak at RB
        team_b_players = [
            ("Elite WR1", PlayerPosition.WR, 290, True),  # Surplus
            ("Elite WR2", PlayerPosition.WR, 270, True),  # Surplus
            ("Solid WR3", PlayerPosition.WR, 240, True),  # Surplus
            ("Average QB", PlayerPosition.QB, 260, False),
            ("Weak RB1", PlayerPosition.RB, 190, False),  # Need
            ("Weak RB2", PlayerPosition.RB, 170, False),  # Need
            ("Good TE", PlayerPosition.TE, 180, False)
        ]
        
        # Create players for Team A
        team_a_roster = []
        for name, position, points, is_surplus in team_a_players:
            player = self.generator.create_player(
                name=name,
                position=position
            )
            
            self.generator.create_projection(
                player_id=player.id,
                projected_points=points
            )
            
            self.generator.add_player_to_roster(
                franchise_id=franchise_a.id,
                player_id=player.id
            )
            
            team_a_roster.append({
                "player": player,
                "is_surplus": is_surplus,
                "projected_points": points
            })
        
        # Create players for Team B
        team_b_roster = []
        for name, position, points, is_surplus in team_b_players:
            player = self.generator.create_player(
                name=name,
                position=position
            )
            
            self.generator.create_projection(
                player_id=player.id,
                projected_points=points
            )
            
            self.generator.add_player_to_roster(
                franchise_id=franchise_b.id,
                player_id=player.id
            )
            
            team_b_roster.append({
                "player": player,
                "is_surplus": is_surplus,
                "projected_points": points
            })
        
        # Define expected optimal trades
        optimal_trades = [
            {
                "team_a_gives": ["Solid RB3"],  # 220 points
                "team_b_gives": ["Solid WR3"],  # 240 points
                "fairness_score": 0.92,  # 220/240
                "mutual_benefit": True
            },
            {
                "team_a_gives": ["Elite RB2"],  # 280 points
                "team_b_gives": ["Elite WR2"],  # 270 points
                "fairness_score": 0.96,  # 270/280
                "mutual_benefit": True
            }
        ]
        
        return {
            "league": league,
            "franchise_a": franchise_a,
            "franchise_b": franchise_b,
            "team_a_roster": team_a_roster,
            "team_b_roster": team_b_roster,
            "optimal_trades": optimal_trades,
            "team_a_needs": [PlayerPosition.WR],
            "team_b_needs": [PlayerPosition.RB],
            "team_a_surplus": [PlayerPosition.RB],
            "team_b_surplus": [PlayerPosition.WR]
        }
    
    def create_lineup_optimization_scenario(self) -> Dict[str, Any]:
        """Create a scenario for testing lineup optimization with tough decisions."""
        league = self.generator.create_league(
            league_id="lineup_opt_league",
            name="Lineup Optimization League",
            roster_slots=[
                {"position": "QB", "count": 1},
                {"position": "RB", "count": 2},
                {"position": "WR", "count": 2},
                {"position": "TE", "count": 1},
                {"position": "FLEX", "count": 1},
                {"position": "K", "count": 1},
                {"position": "DEF", "count": 1}
            ]
        )
        
        franchise = self.generator.create_franchise(
            franchise_id="lineup_opt_franchise",
            league_id=league.id,
            name="Lineup Optimization Team"
        )
        
        # Create players with close projections to make decisions interesting
        roster_players = [
            # QBs
            ("Josh Allen", PlayerPosition.QB, 28.5, InjuryStatus.HEALTHY, True),
            ("Backup QB", PlayerPosition.QB, 18.2, InjuryStatus.HEALTHY, False),
            
            # RBs - tough decisions between similar players
            ("Christian McCaffrey", PlayerPosition.RB, 22.8, InjuryStatus.HEALTHY, True),
            ("Saquon Barkley", PlayerPosition.RB, 21.5, InjuryStatus.QUESTIONABLE, True),  # Injury concern
            ("Josh Jacobs", PlayerPosition.RB, 19.8, InjuryStatus.HEALTHY, True),
            ("Tony Pollard", PlayerPosition.RB, 18.9, InjuryStatus.HEALTHY, False),
            
            # WRs - close projections
            ("Cooper Kupp", PlayerPosition.WR, 20.2, InjuryStatus.HEALTHY, True),
            ("Stefon Diggs", PlayerPosition.WR, 19.8, InjuryStatus.HEALTHY, True),
            ("Mike Evans", PlayerPosition.WR, 18.5, InjuryStatus.HEALTHY, True),
            ("Keenan Allen", PlayerPosition.WR, 17.9, InjuryStatus.HEALTHY, False),
            ("DK Metcalf", PlayerPosition.WR, 17.2, InjuryStatus.HEALTHY, False),
            
            # TEs
            ("Travis Kelce", PlayerPosition.TE, 16.8, InjuryStatus.HEALTHY, True),
            ("Mark Andrews", PlayerPosition.TE, 14.2, InjuryStatus.HEALTHY, False),
            
            # K and DEF
            ("Justin Tucker", PlayerPosition.K, 9.5, InjuryStatus.HEALTHY, True),
            ("49ers DEF", PlayerPosition.DEF, 11.2, InjuryStatus.HEALTHY, True)
        ]
        
        players = []
        for name, position, points, injury_status, should_start in roster_players:
            player = self.generator.create_player(
                name=name,
                position=position,
                injury_status=injury_status
            )
            
            # Add variance based on injury status
            variance = 2.5 if injury_status == InjuryStatus.HEALTHY else 4.0
            floor = points * 0.6 if injury_status == InjuryStatus.HEALTHY else points * 0.3
            ceiling = points * 1.4 if injury_status == InjuryStatus.HEALTHY else points * 1.2
            
            self.generator.create_projection(
                player_id=player.id,
                projected_points=points,
                floor=floor,
                ceiling=ceiling,
                variance=variance,
                week=1
            )
            
            self.generator.add_player_to_roster(
                franchise_id=franchise.id,
                player_id=player.id
            )
            
            players.append({
                "player": player,
                "projected_points": points,
                "should_start": should_start,
                "injury_concern": injury_status != InjuryStatus.HEALTHY
            })
        
        # Expected optimal lineup
        expected_lineup = {
            "QB": "Josh Allen",
            "RB1": "Christian McCaffrey",
            "RB2": "Josh Jacobs",  # Safer than injured Saquon
            "WR1": "Cooper Kupp",
            "WR2": "Stefon Diggs",
            "TE": "Travis Kelce",
            "FLEX": "Mike Evans",  # Best remaining player
            "K": "Justin Tucker",
            "DEF": "49ers DEF"
        }
        
        expected_total_points = 28.5 + 22.8 + 19.8 + 20.2 + 19.8 + 16.8 + 18.5 + 9.5 + 11.2  # 167.1
        
        return {
            "league": league,
            "franchise": franchise,
            "players": players,
            "expected_lineup": expected_lineup,
            "expected_total_points": expected_total_points,
            "tough_decisions": [
                ("Saquon Barkley vs Josh Jacobs", "Injury risk vs safety"),
                ("Mike Evans vs Keenan Allen", "FLEX position decision")
            ]
        }
    
    def create_waiver_wire_scenario(self) -> Dict[str, Any]:
        """Create a scenario for testing waiver wire optimization."""
        league = self.generator.create_league(
            league_id="waiver_wire_league",
            name="Waiver Wire League"
        )
        
        franchise = self.generator.create_franchise(
            franchise_id="waiver_franchise",
            league_id=league.id,
            name="Waiver Wire Team",
            faab_budget=Decimal('100'),
            faab_spent=Decimal('25')  # $75 remaining
        )
        
        # Create current roster with weaknesses
        current_roster = [
            ("Starting QB", PlayerPosition.QB, 280, False),
            ("Elite RB", PlayerPosition.RB, 300, False),
            ("Solid RB", PlayerPosition.RB, 220, False),
            ("WR1", PlayerPosition.WR, 250, False),
            ("Weak WR2", PlayerPosition.WR, 160, True),  # Weakness - needs upgrade
            ("Average TE", PlayerPosition.TE, 140, True),  # Weakness - needs upgrade
            ("Injured RB", PlayerPosition.RB, 200, True)  # Injured - needs replacement
        ]
        
        for name, position, points, is_droppable in current_roster:
            player = self.generator.create_player(
                name=name,
                position=position,
                injury_status=InjuryStatus.OUT if "Injured" in name else InjuryStatus.HEALTHY
            )
            
            self.generator.create_projection(
                player_id=player.id,
                projected_points=points
            )
            
            self.generator.add_player_to_roster(
                franchise_id=franchise.id,
                player_id=player.id
            )
        
        # Create available free agents with various values
        free_agents = [
            # High-value pickups
            ("Breakout WR", PlayerPosition.WR, 200, 15, "High"),  # Great value
            ("Emerging TE", PlayerPosition.TE, 180, 12, "High"),  # Solid upgrade
            ("Handcuff RB", PlayerPosition.RB, 150, 8, "Medium"),  # Insurance
            
            # Medium-value pickups
            ("Streaming QB", PlayerPosition.QB, 220, 5, "Medium"),  # Bye week fill-in
            ("Depth WR", PlayerPosition.WR, 140, 6, "Medium"),  # Depth piece
            ("Backup TE", PlayerPosition.TE, 120, 4, "Low"),  # Minimal upgrade
            
            # Low-value pickups
            ("Lottery RB", PlayerPosition.RB, 80, 3, "Low"),  # Long shot
            ("Deep WR", PlayerPosition.WR, 90, 2, "Low"),  # Deep depth
            
            # Streaming options
            ("Stream DEF", PlayerPosition.DEF, 110, 1, "Stream"),  # Weekly stream
            ("Stream K", PlayerPosition.K, 85, 1, "Stream")  # Weekly stream
        ]
        
        available_players = []
        for name, position, points, suggested_bid, priority in free_agents:
            player = self.generator.create_player(
                name=name,
                position=position
            )
            
            self.generator.create_projection(
                player_id=player.id,
                projected_points=points
            )
            
            available_players.append({
                "player": player,
                "projected_points": points,
                "suggested_bid": suggested_bid,
                "priority": priority,
                "points_over_replacement": points - (120 if position == PlayerPosition.WR else 100)
            })
        
        # Expected optimal waiver claims
        expected_claims = [
            {
                "player": "Breakout WR",
                "bid": 15,
                "rationale": "Significant upgrade over current WR2",
                "priority": 1
            },
            {
                "player": "Emerging TE",
                "bid": 12,
                "rationale": "Major upgrade at TE position",
                "priority": 2
            },
            {
                "player": "Handcuff RB",
                "bid": 8,
                "rationale": "Insurance for elite RB",
                "priority": 3
            }
        ]
        
        return {
            "league": league,
            "franchise": franchise,
            "available_players": available_players,
            "expected_claims": expected_claims,
            "remaining_budget": 75,
            "positional_needs": [PlayerPosition.WR, PlayerPosition.TE, PlayerPosition.RB],
            "droppable_players": ["Weak WR2", "Average TE", "Injured RB"]
        }
    
    def create_performance_test_dataset(self, size: str = "medium") -> Dict[str, Any]:
        """Create datasets of various sizes for performance testing."""
        sizes = {
            "small": {"leagues": 1, "franchises": 4, "players_per_franchise": 10},
            "medium": {"leagues": 1, "franchises": 12, "players_per_franchise": 20},
            "large": {"leagues": 1, "franchises": 16, "players_per_franchise": 30},
            "xlarge": {"leagues": 2, "franchises": 24, "players_per_franchise": 25}
        }
        
        config = sizes.get(size, sizes["medium"])
        
        datasets = []
        for league_num in range(config["leagues"]):
            dataset = self.generator.create_complete_league_setup(
                num_franchises=config["franchises"],
                players_per_franchise=config["players_per_franchise"],
                include_projections=True,
                include_rankings=True
            )
            datasets.append(dataset)
        
        total_players = config["leagues"] * config["franchises"] * config["players_per_franchise"]
        
        return {
            "datasets": datasets,
            "size": size,
            "total_leagues": config["leagues"],
            "total_franchises": config["leagues"] * config["franchises"],
            "total_players": total_players,
            "expected_processing_time": {
                "small": 1.0,    # 1 second
                "medium": 3.0,   # 3 seconds
                "large": 8.0,    # 8 seconds
                "xlarge": 20.0   # 20 seconds
            }[size]
        }