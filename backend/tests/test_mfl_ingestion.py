"""
Integration tests for MFL data ingestion service.

Tests the MFL ingestion service with mock data and sandbox scenarios.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
from decimal import Decimal
from sqlalchemy.orm import Session

from backend.app.services.mfl_ingestion import (
    MFLIngestionService,
    MFLDataNormalizer,
    DataProvenance,
    MFLIngestionError,
    create_mfl_ingestion_service
)
from backend.app.models.league import League
from backend.app.models.player import Player, PlayerPosition, InjuryStatus
from backend.app.models.roster import <PERSON><PERSON><PERSON><PERSON>, R<PERSON>er, RosterPlayer
from backend.app.core.database import get_db


class TestDataProvenance:
    """Test data provenance tracking."""
    
    def test_provenance_creation(self):
        """Test creating a provenance record."""
        timestamp = datetime.now(timezone.utc)
        metadata = {"test": "data"}
        
        provenance = DataProvenance("test_source", timestamp, metadata)
        
        assert provenance.source == "test_source"
        assert provenance.timestamp == timestamp
        assert provenance.metadata == metadata
        assert provenance.id is not None
    
    def test_provenance_to_dict(self):
        """Test converting provenance to dictionary."""
        timestamp = datetime.now(timezone.utc)
        provenance = DataProvenance("test_source", timestamp, {"key": "value"})
        
        result = provenance.to_dict()
        
        assert result["source"] == "test_source"
        assert result["timestamp"] == timestamp.isoformat()
        assert result["metadata"] == {"key": "value"}
        assert "provenance_id" in result


class TestMFLDataNormalizer:
    """Test MFL data normalization functions."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.provenance = DataProvenance(
            "test_source",
            datetime.now(timezone.utc),
            {"test": "metadata"}
        )
    
    def test_normalize_league_data(self):
        """Test normalizing MFL league data."""
        mfl_league = {
            "id": "12345",
            "name": "Test League",
            "season": "2024",
            "description": "Test league description",
            "scoring": {
                "pass_yd": "0.04",
                "pass_td": "4",
                "rush_yd": "0.1",
                "rec": "1"  # PPR
            },
            "rosterSize": {
                "QB": "1",
                "RB": "2",
                "WR": "3",
                "BENCH": "6"
            }
        }
        
        result = MFLDataNormalizer.normalize_league_data(mfl_league, self.provenance)
        
        assert result["id"] == "mfl_12345"
        assert result["name"] == "Test League"
        assert result["season"] == 2024
        assert result["mfl_league_id"] == "12345"
        assert result["scoring_rules"]["passing_yards"] == 0.04
        assert result["scoring_rules"]["receptions"] == 1.0  # PPR
        assert len(result["roster_slots"]) == 4
        assert "provenance" in result["metadata"]
    
    def test_normalize_player_data(self):
        """Test normalizing MFL player data."""
        mfl_player = {
            "id": "9999",
            "name": "Test Player",
            "position": "RB",
            "team": "KC",
            "bye_week": "7",
            "injury_status": "Q",
            "jersey_number": "25",
            "height": "6-0",
            "weight": "210"
        }
        
        result = MFLDataNormalizer.normalize_player_data(mfl_player, self.provenance)
        
        assert result["id"] == "mfl_9999"
        assert result["name"] == "Test Player"
        assert result["position"] == PlayerPosition.RB
        assert result["team"] == "KC"
        assert result["bye_week"] == 7
        assert result["injury_status"] == InjuryStatus.QUESTIONABLE
        assert result["mfl_id"] == "9999"
        assert result["metadata"]["jersey_number"] == "25"
        assert "provenance" in result["metadata"]
    
    def test_normalize_franchise_data(self):
        """Test normalizing MFL franchise data."""
        mfl_franchise = {
            "id": "0001",
            "name": "Test Team",
            "owner_name": "Test Owner",
            "salary_cap": "200.00",
            "faab_budget": "100.00",
            "faab_spent": "25.50"
        }
        
        result = MFLDataNormalizer.normalize_franchise_data(
            mfl_franchise, "mfl_12345", self.provenance
        )
        
        assert result["id"] == "mfl_mfl_12345_0001"
        assert result["name"] == "Test Team"
        assert result["owner_name"] == "Test Owner"
        assert result["league_id"] == "mfl_12345"
        assert result["mfl_franchise_id"] == "0001"
        assert result["salary_cap"] == Decimal("200.00")
        assert result["faab_budget"] == Decimal("100.00")
        assert result["faab_spent"] == Decimal("25.50")
        assert "provenance" in result["metadata"]
    
    def test_normalize_roster_data(self):
        """Test normalizing MFL roster data."""
        mfl_roster = {
            "players": [
                {
                    "id": "9999",
                    "status": "QB",
                    "salary": "15.00",
                    "is_keeper": True,
                    "keeper_cost": "3"
                },
                {
                    "id": "8888",
                    "status": "BENCH"
                }
            ]
        }
        
        roster_data, roster_players = MFLDataNormalizer.normalize_roster_data(
            mfl_roster, "test_franchise", self.provenance
        )
        
        assert roster_data["id"] == "roster_test_franchise"
        assert roster_data["franchise_id"] == "test_franchise"
        assert len(roster_players) == 2
        
        # Check first player (starter with keeper info)
        player1 = roster_players[0]
        assert player1["player_id"] == "mfl_9999"
        assert player1["roster_slot"] == "QB"
        assert player1["salary"] == Decimal("15.00")
        assert player1["is_keeper"] is True
        assert player1["keeper_cost"] == 3
        
        # Check second player (bench)
        player2 = roster_players[1]
        assert player2["player_id"] == "mfl_8888"
        assert player2["roster_slot"] == "BENCH"
        assert player2["is_keeper"] is False
    
    def test_position_mapping(self):
        """Test MFL position mapping."""
        test_cases = [
            ("QB", PlayerPosition.QB),
            ("RB", PlayerPosition.RB),
            ("WR", PlayerPosition.WR),
            ("TE", PlayerPosition.TE),
            ("K", PlayerPosition.K),
            ("Def", PlayerPosition.DEF),
            ("UNKNOWN", PlayerPosition.RB)  # Default fallback
        ]
        
        for mfl_pos, expected_pos in test_cases:
            mfl_player = {"id": "test", "name": "Test", "position": mfl_pos, "team": "TEST"}
            result = MFLDataNormalizer.normalize_player_data(mfl_player, self.provenance)
            assert result["position"] == expected_pos
    
    def test_injury_status_mapping(self):
        """Test MFL injury status mapping."""
        test_cases = [
            ("", InjuryStatus.HEALTHY),
            ("Q", InjuryStatus.QUESTIONABLE),
            ("D", InjuryStatus.DOUBTFUL),
            ("O", InjuryStatus.OUT),
            ("IR", InjuryStatus.IR),
            ("PUP", InjuryStatus.PUP),
            ("SUSP", InjuryStatus.SUSPENDED),
            ("UNKNOWN", InjuryStatus.HEALTHY)  # Default fallback
        ]
        
        for mfl_status, expected_status in test_cases:
            mfl_player = {
                "id": "test", 
                "name": "Test", 
                "position": "RB", 
                "team": "TEST",
                "injury_status": mfl_status
            }
            result = MFLDataNormalizer.normalize_player_data(mfl_player, self.provenance)
            assert result["injury_status"] == expected_status


class TestMFLIngestionService:
    """Test MFL ingestion service functionality."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        session = Mock(spec=Session)
        session.query.return_value.filter.return_value.first.return_value = None
        session.query.return_value.filter.return_value.all.return_value = []
        session.query.return_value.count.return_value = 0
        session.query.return_value.join.return_value.filter.return_value.count.return_value = 0
        return session
    
    @pytest.fixture
    def ingestion_service(self, mock_db_session):
        """Create MFL ingestion service with mock database."""
        return MFLIngestionService(mock_db_session)
    
    @pytest.fixture
    def mock_mfl_client(self):
        """Create a mock MFL client."""
        client = Mock()
        
        # Mock league data
        client.get_league.return_value = {
            "id": "12345",
            "name": "Test League",
            "season": "2024",
            "scoring": {"pass_td": "4", "rush_td": "6"},
            "rosterSize": {"QB": "1", "RB": "2"}
        }
        
        # Mock player data
        client.get_players.return_value = [
            {
                "id": "9999",
                "name": "Test Player 1",
                "position": "QB",
                "team": "KC"
            },
            {
                "id": "8888",
                "name": "Test Player 2",
                "position": "RB",
                "team": "SF"
            }
        ]
        
        # Mock franchise data
        client.get_franchises.return_value = [
            {
                "id": "0001",
                "name": "Test Team 1",
                "owner_name": "Owner 1"
            },
            {
                "id": "0002",
                "name": "Test Team 2",
                "owner_name": "Owner 2"
            }
        ]
        
        # Mock roster data
        client.get_roster.return_value = {
            "players": [
                {"id": "9999", "status": "QB"},
                {"id": "8888", "status": "BENCH"}
            ]
        }
        
        return client
    
    @patch('backend.app.services.mfl_ingestion.MFL')
    def test_ingest_league_data_success(self, mock_mfl_class, ingestion_service, mock_mfl_client):
        """Test successful league data ingestion."""
        mock_mfl_class.return_value = mock_mfl_client
        
        result = ingestion_service.ingest_league_data("12345", "test_api_key")
        
        assert result["league_id"] == "12345"
        assert result["leagues_processed"] == 1
        assert result["players_processed"] == 2
        assert result["franchises_processed"] == 2
        assert result["rosters_processed"] == 2
        assert len(result["errors"]) == 0
        
        # Verify MFL client was called correctly
        mock_mfl_class.assert_called_once_with(league_id="12345", api_key="test_api_key")
        mock_mfl_client.get_league.assert_called_once()
        mock_mfl_client.get_players.assert_called_once()
        mock_mfl_client.get_franchises.assert_called_once()
        assert mock_mfl_client.get_roster.call_count == 2
    
    @patch('backend.app.services.mfl_ingestion.MFL')
    def test_ingest_league_data_api_failure(self, mock_mfl_class, ingestion_service):
        """Test handling of MFL API failures."""
        mock_mfl_client = Mock()
        mock_mfl_client.get_league.side_effect = Exception("API Error")
        mock_mfl_class.return_value = mock_mfl_client
        
        result = ingestion_service.ingest_league_data("12345", "test_api_key")
        
        assert result["leagues_processed"] == 0
        assert len(result["errors"]) > 0
        assert "Failed to ingest league configuration" in result["errors"][0]
    
    @patch('backend.app.services.mfl_ingestion.MFL')
    def test_ingest_league_data_partial_failure(self, mock_mfl_class, ingestion_service, mock_mfl_client):
        """Test handling of partial ingestion failures."""
        # League succeeds, players fail
        mock_mfl_client.get_players.side_effect = Exception("Player API Error")
        mock_mfl_class.return_value = mock_mfl_client
        
        result = ingestion_service.ingest_league_data("12345", "test_api_key")
        
        assert result["leagues_processed"] == 1
        assert result["players_processed"] == 0
        assert len(result["errors"]) > 0
        assert "Failed to ingest players" in result["errors"][0]
    
    def test_validate_ingested_data_success(self, ingestion_service, mock_db_session):
        """Test successful data validation."""
        # Mock successful validation scenario
        mock_league = Mock()
        mock_league.id = "mfl_12345"
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_league
        
        mock_franchise = Mock()
        mock_franchise.id = "test_franchise"
        mock_franchise.roster = Mock()
        mock_franchise.roster.roster_players = [Mock()]
        mock_db_session.query.return_value.filter.return_value.all.return_value = [mock_franchise]
        
        mock_db_session.query.return_value.count.return_value = 100  # Players exist
        mock_db_session.query.return_value.join.return_value.filter.return_value.count.return_value = 0  # No orphans
        
        result = ingestion_service.validate_ingested_data("mfl_12345")
        
        assert result["is_valid"] is True
        assert len(result["issues"]) == 0
    
    def test_validate_ingested_data_missing_league(self, ingestion_service, mock_db_session):
        """Test validation with missing league."""
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        result = ingestion_service.validate_ingested_data("mfl_12345")
        
        assert result["is_valid"] is False
        assert "League not found" in result["issues"]
    
    def test_validate_ingested_data_empty_rosters(self, ingestion_service, mock_db_session):
        """Test validation with empty rosters."""
        mock_league = Mock()
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_league
        
        mock_franchise = Mock()
        mock_franchise.id = "test_franchise"
        mock_franchise.roster = Mock()
        mock_franchise.roster.roster_players = []  # Empty roster
        mock_db_session.query.return_value.filter.return_value.all.return_value = [mock_franchise]
        
        mock_db_session.query.return_value.count.return_value = 100  # Players exist
        mock_db_session.query.return_value.join.return_value.filter.return_value.count.return_value = 0
        
        result = ingestion_service.validate_ingested_data("mfl_12345")
        
        assert result["is_valid"] is False
        assert "Empty roster for franchise test_franchise" in result["issues"]
    
    def test_validate_ingested_data_orphaned_players(self, ingestion_service, mock_db_session):
        """Test validation with orphaned roster players."""
        mock_league = Mock()
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_league
        
        mock_franchise = Mock()
        mock_franchise.roster = Mock()
        mock_franchise.roster.roster_players = [Mock()]
        mock_db_session.query.return_value.filter.return_value.all.return_value = [mock_franchise]
        
        mock_db_session.query.return_value.count.return_value = 100
        mock_db_session.query.return_value.join.return_value.filter.return_value.count.return_value = 5  # Orphans
        
        result = ingestion_service.validate_ingested_data("mfl_12345")
        
        assert result["is_valid"] is False
        assert "5 roster players reference non-existent players" in result["issues"]


class TestMFLIngestionIntegration:
    """Integration tests with actual database operations."""
    
    @pytest.fixture
    def db_session(self):
        """Create a test database session."""
        # This would typically use a test database
        # For now, we'll mock it since we don't have a test DB setup
        return Mock(spec=Session)
    
    def test_create_mfl_ingestion_service(self):
        """Test factory function for creating ingestion service."""
        with patch('backend.app.services.mfl_ingestion.get_db') as mock_get_db:
            mock_session = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_session])
            
            service = create_mfl_ingestion_service()
            
            assert isinstance(service, MFLIngestionService)
            assert service.db == mock_session
    
    def test_create_mfl_ingestion_service_with_session(self):
        """Test factory function with provided session."""
        mock_session = Mock(spec=Session)
        
        service = create_mfl_ingestion_service(mock_session)
        
        assert isinstance(service, MFLIngestionService)
        assert service.db == mock_session


class TestMFLIngestionErrorHandling:
    """Test error handling in MFL ingestion."""
    
    def test_mfl_ingestion_error(self):
        """Test custom MFL ingestion error."""
        error = MFLIngestionError("Test error message")
        assert str(error) == "Test error message"
    
    def test_normalization_error_handling(self):
        """Test error handling in data normalization."""
        provenance = DataProvenance("test", datetime.now(timezone.utc))
        
        # Test with invalid league data
        with pytest.raises(MFLIngestionError):
            MFLDataNormalizer.normalize_league_data(None, provenance)
        
        # Test with invalid player data
        with pytest.raises(MFLIngestionError):
            MFLDataNormalizer.normalize_player_data(None, provenance)
        
        # Test with invalid franchise data
        with pytest.raises(MFLIngestionError):
            MFLDataNormalizer.normalize_franchise_data(None, "league_id", provenance)


# Mock data for sandbox testing
MOCK_MFL_SANDBOX_DATA = {
    "league": {
        "id": "sandbox_league",
        "name": "MFL Sandbox League",
        "season": "2024",
        "description": "Test league for development",
        "scoring": {
            "pass_yd": "0.04",
            "pass_td": "4",
            "pass_int": "-2",
            "rush_yd": "0.1",
            "rush_td": "6",
            "rec_yd": "0.1",
            "rec_td": "6",
            "rec": "0.5"  # Half PPR
        },
        "rosterSize": {
            "QB": "1",
            "RB": "2",
            "WR": "2",
            "TE": "1",
            "FLEX": "1",
            "K": "1",
            "DEF": "1",
            "BENCH": "6"
        },
        "keeperRules": {
            "max_keepers": "3",
            "deadline": "2024-08-15",
            "round_escalation": True,
            "franchise_tags": "1"
        }
    },
    "players": [
        {
            "id": "14001",
            "name": "Josh Allen",
            "position": "QB",
            "team": "BUF",
            "bye_week": "12",
            "injury_status": "",
            "jersey_number": "17"
        },
        {
            "id": "13982",
            "name": "Christian McCaffrey",
            "position": "RB",
            "team": "SF",
            "bye_week": "9",
            "injury_status": "Q"
        },
        {
            "id": "14692",
            "name": "Cooper Kupp",
            "position": "WR",
            "team": "LAR",
            "bye_week": "6",
            "injury_status": ""
        }
    ],
    "franchises": [
        {
            "id": "0001",
            "name": "Team Alpha",
            "owner_name": "Test Owner 1",
            "faab_budget": "100.00",
            "faab_spent": "15.50"
        },
        {
            "id": "0002",
            "name": "Team Beta",
            "owner_name": "Test Owner 2",
            "faab_budget": "100.00",
            "faab_spent": "22.00"
        }
    ],
    "rosters": {
        "0001": {
            "players": [
                {"id": "14001", "status": "QB", "is_keeper": True, "keeper_cost": "1"},
                {"id": "13982", "status": "RB", "salary": "25.00"},
                {"id": "14692", "status": "BENCH"}
            ]
        },
        "0002": {
            "players": [
                {"id": "14001", "status": "BENCH"},
                {"id": "13982", "status": "RB", "is_keeper": True, "keeper_cost": "2"}
            ]
        }
    }
}


class TestMFLSandboxIntegration:
    """Test MFL ingestion with sandbox data."""
    
    @pytest.fixture
    def sandbox_mfl_client(self):
        """Create a mock MFL client with sandbox data."""
        client = Mock()
        
        client.get_league.return_value = MOCK_MFL_SANDBOX_DATA["league"]
        client.get_players.return_value = MOCK_MFL_SANDBOX_DATA["players"]
        client.get_franchises.return_value = MOCK_MFL_SANDBOX_DATA["franchises"]
        
        def get_roster_side_effect(franchise_id):
            return MOCK_MFL_SANDBOX_DATA["rosters"].get(franchise_id, {"players": []})
        
        client.get_roster.side_effect = get_roster_side_effect
        
        return client
    
    @patch('backend.app.services.mfl_ingestion.MFL')
    def test_sandbox_data_ingestion(self, mock_mfl_class, sandbox_mfl_client):
        """Test ingestion with comprehensive sandbox data."""
        mock_mfl_class.return_value = sandbox_mfl_client
        mock_db_session = Mock(spec=Session)
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        service = MFLIngestionService(mock_db_session)
        result = service.ingest_league_data("sandbox_league", "sandbox_key")
        
        assert result["leagues_processed"] == 1
        assert result["players_processed"] == 3
        assert result["franchises_processed"] == 2
        assert result["rosters_processed"] == 2
        assert len(result["errors"]) == 0
        
        # Verify database operations were called
        assert mock_db_session.add.call_count > 0
        mock_db_session.commit.assert_called_once()
    
    def test_sandbox_data_normalization(self):
        """Test normalization of sandbox data."""
        provenance = DataProvenance("sandbox", datetime.now(timezone.utc))
        
        # Test league normalization
        league_data = MFLDataNormalizer.normalize_league_data(
            MOCK_MFL_SANDBOX_DATA["league"], provenance
        )
        
        assert league_data["id"] == "mfl_sandbox_league"
        assert league_data["name"] == "MFL Sandbox League"
        assert league_data["scoring_rules"]["receptions"] == 0.5  # Half PPR
        assert league_data["keeper_rules"]["max_keepers"] == 3
        
        # Test player normalization
        josh_allen = MOCK_MFL_SANDBOX_DATA["players"][0]
        player_data = MFLDataNormalizer.normalize_player_data(josh_allen, provenance)
        
        assert player_data["id"] == "mfl_14001"
        assert player_data["name"] == "Josh Allen"
        assert player_data["position"] == PlayerPosition.QB
        assert player_data["team"] == "BUF"
        assert player_data["bye_week"] == 12
        assert player_data["injury_status"] == InjuryStatus.HEALTHY
        
        # Test franchise normalization
        franchise_data = MFLDataNormalizer.normalize_franchise_data(
            MOCK_MFL_SANDBOX_DATA["franchises"][0], "mfl_sandbox_league", provenance
        )
        
        assert franchise_data["id"] == "mfl_mfl_sandbox_league_0001"
        assert franchise_data["name"] == "Team Alpha"
        assert franchise_data["faab_budget"] == Decimal("100.00")
        assert franchise_data["faab_spent"] == Decimal("15.50")
        
        # Test roster normalization
        roster_data, roster_players = MFLDataNormalizer.normalize_roster_data(
            MOCK_MFL_SANDBOX_DATA["rosters"]["0001"], "test_franchise", provenance
        )
        
        assert len(roster_players) == 3
        
        # Check keeper player
        keeper_player = next(rp for rp in roster_players if rp["is_keeper"])
        assert keeper_player["player_id"] == "mfl_14001"
        assert keeper_player["keeper_cost"] == 1
        assert keeper_player["roster_slot"] == "QB"


if __name__ == "__main__":
    pytest.main([__file__])