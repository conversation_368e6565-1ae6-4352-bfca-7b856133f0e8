"""
Enhanced test configuration and fixtures for the AI Fantasy Assistant backend.
"""
import pytest
import asyncio
from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient

from app.models.base import Base
from app.main import app
from app.core.database import get_db
from tests.utils.mock_data_generator import MockDataGenerator


# Create in-memory SQLite database for testing
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Drop all tables after test
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """Create a test client for API testing."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def mock_data_generator(db_session):
    """Create a mock data generator for tests."""
    return MockDataGenerator(db_session)


@pytest.fixture(scope="function")
def sample_league(mock_data_generator):
    """Create a sample league for testing."""
    return mock_data_generator.create_league(
        league_id="test_league",
        name="Test League",
        keeper_rules={
            "max_keepers": 3,
            "round_escalation": 1,
            "max_round": 16
        }
    )


@pytest.fixture(scope="function")
def sample_franchise(mock_data_generator, sample_league):
    """Create a sample franchise for testing."""
    return mock_data_generator.create_franchise(
        franchise_id="test_franchise",
        league_id=sample_league.id,
        name="Test Team"
    )


@pytest.fixture(scope="function")
def sample_players(mock_data_generator):
    """Create sample players for testing."""
    from app.models.player import PlayerPosition
    
    players = []
    positions = [PlayerPosition.QB, PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE]
    
    for i, position in enumerate(positions):
        player = mock_data_generator.create_player(
            player_id=f"test_player_{i+1}",
            name=f"Test Player {i+1}",
            position=position
        )
        players.append(player)
    
    return players


@pytest.fixture(scope="function")
def complete_test_setup(mock_data_generator):
    """Create a complete test setup with league, franchise, and players."""
    return mock_data_generator.create_complete_league_setup(
        num_franchises=4,
        players_per_franchise=8,
        include_projections=True,
        include_rankings=True
    )


# Performance test fixtures
@pytest.fixture(scope="function")
def performance_timer():
    """Timer fixture for performance tests."""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()


@pytest.fixture(scope="function")
def memory_monitor():
    """Memory monitoring fixture for performance tests."""
    import psutil
    import os
    
    class MemoryMonitor:
        def __init__(self):
            self.process = psutil.Process(os.getpid())
            self.initial_memory = None
            self.peak_memory = None
        
        def start(self):
            self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        def check_peak(self):
            current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            if self.peak_memory is None or current_memory > self.peak_memory:
                self.peak_memory = current_memory
        
        @property
        def memory_increase(self):
            if self.initial_memory and self.peak_memory:
                return self.peak_memory - self.initial_memory
            return None
    
    return MemoryMonitor()


# Data validation fixtures
@pytest.fixture(scope="function")
def data_validator():
    """Data validation utilities for tests."""
    class DataValidator:
        @staticmethod
        def validate_projection(projection):
            """Validate projection data integrity."""
            assert projection.projected_points > 0
            assert projection.floor <= projection.projected_points
            assert projection.ceiling >= projection.projected_points
            assert projection.variance >= 0
            if projection.week is not None:
                assert 1 <= projection.week <= 18
            assert projection.season >= 2020
        
        @staticmethod
        def validate_player(player):
            """Validate player data integrity."""
            assert player.name
            assert player.position
            assert player.team
            if player.bye_week is not None:
                assert 1 <= player.bye_week <= 18
        
        @staticmethod
        def validate_roster_integrity(roster):
            """Validate roster data integrity."""
            assert roster.franchise_id
            player_ids = [rp.player_id for rp in roster.roster_players]
            # No duplicate players
            assert len(player_ids) == len(set(player_ids))
            # All roster players have valid references
            for rp in roster.roster_players:
                assert rp.player is not None
                assert rp.roster_id == roster.id
    
    return DataValidator()


# Async test fixtures
@pytest.fixture(scope="function")
async def async_client(db_session):
    """Create an async test client."""
    from httpx import AsyncClient
    
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()


# Mock external services
@pytest.fixture(scope="function")
def mock_mfl_api():
    """Mock MFL API responses."""
    from unittest.mock import Mock, patch
    
    mock_api = Mock()
    mock_api.get_league_info.return_value = {
        "league_id": "test_league",
        "name": "Test League",
        "franchises": []
    }
    mock_api.get_rosters.return_value = []
    mock_api.get_players.return_value = []
    
    with patch('app.services.mfl_ingestion.MFLIngestionService') as mock_service:
        mock_service.return_value = mock_api
        yield mock_api


@pytest.fixture(scope="function")
def mock_redis():
    """Mock Redis for caching tests."""
    from unittest.mock import Mock, patch
    
    mock_redis = Mock()
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = True
    mock_redis.exists.return_value = False
    
    with patch('app.core.cache.redis_client', mock_redis):
        yield mock_redis


# Test data cleanup
@pytest.fixture(scope="function", autouse=True)
def cleanup_test_data():
    """Automatically cleanup test data after each test."""
    yield
    # Cleanup happens automatically with in-memory database
    pass


# Parametrized fixtures for different test scenarios
@pytest.fixture(params=[
    {"max_keepers": 1, "escalation": 0},
    {"max_keepers": 3, "escalation": 1},
    {"max_keepers": 5, "escalation": 2},
])
def keeper_rules_scenarios(request):
    """Parametrized keeper rules for testing different scenarios."""
    return request.param


@pytest.fixture(params=[
    PlayerPosition.QB,
    PlayerPosition.RB,
    PlayerPosition.WR,
    PlayerPosition.TE
])
def position_scenarios(request):
    """Parametrized positions for testing different player types."""
    from app.models.player import PlayerPosition
    return request.param


# Test markers for different test types
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "e2e: End-to-end tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "data_validation: Data validation tests")
    config.addinivalue_line("markers", "slow: Slow running tests")
    config.addinivalue_line("markers", "requires_db: Tests that require database")
    config.addinivalue_line("markers", "requires_redis: Tests that require Redis")
    config.addinivalue_line("markers", "requires_external_api: Tests that require external API access")


# Test collection hooks
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test location."""
    for item in items:
        # Add markers based on test file location
        if "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
            item.add_marker(pytest.mark.slow)
        elif "data_validation" in str(item.fspath):
            item.add_marker(pytest.mark.data_validation)
        elif "integration" in item.name or "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        else:
            item.add_marker(pytest.mark.unit)
        
        # Add database marker for tests that use db_session
        if "db_session" in item.fixturenames:
            item.add_marker(pytest.mark.requires_db)
        
        # Add Redis marker for tests that use mock_redis
        if "mock_redis" in item.fixturenames:
            item.add_marker(pytest.mark.requires_redis)