"""
Unit tests for the alerts API endpoints.
"""
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from fastapi import HTTPException

from app.main import app
from app.models.alert import (
    <PERSON>ert, AlertSchedule, PlayerNewsAlert,
    AlertType, AlertPriority, AlertStatus
)


client = TestClient(app)


class TestAlertsAPI:
    """Test cases for alerts API endpoints."""
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_get_alerts_success(self, mock_alert_service_class, mock_get_db):
        """Test successfully getting alerts."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        
        mock_alert = Mock(spec=Alert)
        mock_alert.id = "alert123"
        mock_alert.alert_type = AlertType.KEEPER_DEADLINE
        mock_alert.priority = AlertPriority.HIGH
        mock_alert.status = AlertStatus.PENDING
        mock_alert.title = "Test Alert"
        mock_alert.message = "Test message"
        mock_alert.league_id = None
        mock_alert.franchise_id = "franchise123"
        mock_alert.player_id = None
        mock_alert.scheduled_for = None
        mock_alert.sent_at = None
        mock_alert.expires_at = None
        mock_alert.delivery_methods = ["IN_APP"]
        mock_alert.alert_data = {}
        mock_alert.created_at = datetime.now()
        mock_alert.updated_at = datetime.now()
        
        mock_alert_service.get_active_alerts.return_value = [mock_alert]
        
        # Act
        response = client.get("/api/v1/alerts/?franchise_id=franchise123")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["id"] == "alert123"
        assert data[0]["alert_type"] == "KEEPER_DEADLINE"
        assert data[0]["franchise_id"] == "franchise123"
        
        mock_alert_service.get_active_alerts.assert_called_once_with(
            franchise_id="franchise123",
            league_id=None,
            limit=50
        )
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_get_alerts_with_filters(self, mock_alert_service_class, mock_get_db):
        """Test getting alerts with type and priority filters."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        
        mock_alert = Mock(spec=Alert)
        mock_alert.alert_type = AlertType.PLAYER_NEWS
        mock_alert.priority = AlertPriority.URGENT
        
        mock_alert_service.get_active_alerts.return_value = [mock_alert]
        
        # Act
        response = client.get(
            "/api/v1/alerts/?alert_type=PLAYER_NEWS&priority=URGENT&limit=10"
        )
        
        # Assert
        assert response.status_code == 200
        mock_alert_service.get_active_alerts.assert_called_once_with(
            franchise_id=None,
            league_id=None,
            limit=10
        )
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_get_alert_summary_success(self, mock_alert_service_class, mock_get_db):
        """Test successfully getting alert summary."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        
        # Create mock alerts with different priorities
        urgent_alert = Mock(spec=Alert)
        urgent_alert.priority = AlertPriority.URGENT
        
        high_alert = Mock(spec=Alert)
        high_alert.priority = AlertPriority.HIGH
        
        medium_alert = Mock(spec=Alert)
        medium_alert.priority = AlertPriority.MEDIUM
        
        mock_alert_service.get_active_alerts.return_value = [
            urgent_alert, urgent_alert, high_alert, medium_alert
        ]
        
        # Act
        response = client.get("/api/v1/alerts/summary")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["urgent"] == 2
        assert data["high"] == 1
        assert data["medium"] == 1
        assert data["low"] == 0
        assert data["total"] == 4
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_create_alert_success(self, mock_alert_service_class, mock_get_db):
        """Test successfully creating an alert."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        
        mock_alert = Mock(spec=Alert)
        mock_alert.id = "alert123"
        mock_alert.alert_type = AlertType.KEEPER_DEADLINE
        mock_alert.priority = AlertPriority.HIGH
        mock_alert.status = AlertStatus.PENDING
        mock_alert.title = "Test Alert"
        mock_alert.message = "Test message"
        mock_alert.league_id = "league123"
        mock_alert.franchise_id = None
        mock_alert.player_id = None
        mock_alert.scheduled_for = None
        mock_alert.sent_at = None
        mock_alert.expires_at = None
        mock_alert.delivery_methods = ["IN_APP"]
        mock_alert.alert_data = {"test": "data"}
        mock_alert.created_at = datetime.now()
        mock_alert.updated_at = datetime.now()
        
        mock_alert_service.create_alert.return_value = mock_alert
        
        request_data = {
            "alert_type": "KEEPER_DEADLINE",
            "title": "Test Alert",
            "message": "Test message",
            "priority": "HIGH",
            "league_id": "league123",
            "alert_data": {"test": "data"}
        }
        
        # Act
        response = client.post("/api/v1/alerts/", json=request_data)
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "alert123"
        assert data["title"] == "Test Alert"
        assert data["league_id"] == "league123"
        
        mock_alert_service.create_alert.assert_called_once()
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_mark_alert_delivered_success(self, mock_alert_service_class, mock_get_db):
        """Test successfully marking an alert as delivered."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        mock_alert_service.mark_alert_delivered.return_value = True
        
        # Act
        response = client.post("/api/v1/alerts/alert123/deliver")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Alert marked as delivered"
        assert data["alert_id"] == "alert123"
        
        mock_alert_service.mark_alert_delivered.assert_called_once_with("alert123")
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_mark_alert_delivered_not_found(self, mock_alert_service_class, mock_get_db):
        """Test marking a non-existent alert as delivered."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        mock_alert_service.mark_alert_delivered.return_value = False
        
        # Act
        response = client.post("/api/v1/alerts/nonexistent/deliver")
        
        # Assert
        assert response.status_code == 404
        data = response.json()
        assert "Alert not found" in data["detail"]
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_dismiss_alert_success(self, mock_alert_service_class, mock_get_db):
        """Test successfully dismissing an alert."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        mock_alert_service.dismiss_alert.return_value = True
        
        # Act
        response = client.post("/api/v1/alerts/alert123/dismiss")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Alert dismissed"
        assert data["alert_id"] == "alert123"
        
        mock_alert_service.dismiss_alert.assert_called_once_with("alert123")
    
    @patch('app.api.alerts.get_db')
    def test_get_alert_schedules_success(self, mock_get_db):
        """Test successfully getting alert schedules."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_schedule = Mock(spec=AlertSchedule)
        mock_schedule.id = "schedule123"
        mock_schedule.name = "Keeper Deadline"
        mock_schedule.alert_type = AlertType.KEEPER_DEADLINE
        mock_schedule.league_id = "league123"
        mock_schedule.franchise_id = None
        mock_schedule.target_datetime = datetime.now() + timedelta(days=1)
        mock_schedule.advance_notice_hours = [24, 2]
        mock_schedule.is_active = True
        mock_schedule.last_processed = None
        mock_schedule.schedule_data = {}
        mock_schedule.created_at = datetime.now()
        
        query_mock = Mock()
        query_mock.filter.return_value = query_mock
        query_mock.order_by.return_value = query_mock
        query_mock.all.return_value = [mock_schedule]
        
        mock_db.query.return_value = query_mock
        
        # Act
        response = client.get("/api/v1/alerts/schedules?league_id=league123")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["id"] == "schedule123"
        assert data[0]["name"] == "Keeper Deadline"
        assert data[0]["league_id"] == "league123"
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_create_alert_schedule_success(self, mock_alert_service_class, mock_get_db):
        """Test successfully creating an alert schedule."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        
        mock_schedule = Mock(spec=AlertSchedule)
        mock_schedule.id = "schedule123"
        mock_schedule.name = "Keeper Deadline"
        mock_schedule.alert_type = AlertType.KEEPER_DEADLINE
        mock_schedule.league_id = "league123"
        mock_schedule.franchise_id = None
        mock_schedule.target_datetime = datetime.now() + timedelta(days=1)
        mock_schedule.advance_notice_hours = [24, 2]
        mock_schedule.is_active = True
        mock_schedule.last_processed = None
        mock_schedule.schedule_data = {}
        mock_schedule.created_at = datetime.now()
        
        mock_alert_service.create_alert_schedule.return_value = mock_schedule
        
        request_data = {
            "name": "Keeper Deadline",
            "alert_type": "KEEPER_DEADLINE",
            "target_datetime": (datetime.now() + timedelta(days=1)).isoformat(),
            "advance_notice_hours": [24, 2],
            "league_id": "league123"
        }
        
        # Act
        response = client.post("/api/v1/alerts/schedules", json=request_data)
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "schedule123"
        assert data["name"] == "Keeper Deadline"
        assert data["league_id"] == "league123"
        
        mock_alert_service.create_alert_schedule.assert_called_once()
    
    @patch('app.api.alerts.get_db')
    def test_get_player_news_success(self, mock_get_db):
        """Test successfully getting player news."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_news = Mock(spec=PlayerNewsAlert)
        mock_news.id = "news123"
        mock_news.player_id = "player456"
        mock_news.headline = "Player Injured"
        mock_news.content = "Player suffered injury"
        mock_news.source = "ESPN"
        mock_news.impact_score = 7.5
        mock_news.affected_positions = ["RB"]
        mock_news.fantasy_impact = "Significant impact"
        mock_news.is_processed = False
        mock_news.alerts_generated = 0
        mock_news.created_at = datetime.now()
        
        query_mock = Mock()
        query_mock.filter.return_value = query_mock
        query_mock.order_by.return_value = query_mock
        query_mock.limit.return_value = query_mock
        query_mock.all.return_value = [mock_news]
        
        mock_db.query.return_value = query_mock
        
        # Act
        response = client.get("/api/v1/alerts/news?player_id=player456")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["id"] == "news123"
        assert data[0]["player_id"] == "player456"
        assert data[0]["headline"] == "Player Injured"
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_create_player_news_success(self, mock_alert_service_class, mock_get_db):
        """Test successfully creating player news."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        
        mock_news = Mock(spec=PlayerNewsAlert)
        mock_news.id = "news123"
        mock_news.player_id = "player456"
        mock_news.headline = "Player Injured"
        mock_news.content = "Player suffered injury"
        mock_news.source = "ESPN"
        mock_news.impact_score = 7.5
        mock_news.affected_positions = ["RB"]
        mock_news.fantasy_impact = "Significant impact"
        mock_news.is_processed = False
        mock_news.alerts_generated = 0
        mock_news.created_at = datetime.now()
        
        mock_alert_service.create_player_news_alert.return_value = mock_news
        
        request_data = {
            "player_id": "player456",
            "headline": "Player Injured",
            "content": "Player suffered injury",
            "source": "ESPN",
            "impact_score": 7.5,
            "affected_positions": ["RB"],
            "fantasy_impact": "Significant impact"
        }
        
        # Act
        response = client.post("/api/v1/alerts/news", json=request_data)
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "news123"
        assert data["player_id"] == "player456"
        assert data["headline"] == "Player Injured"
        
        mock_alert_service.create_player_news_alert.assert_called_once()
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_process_deadline_monitoring_success(self, mock_alert_service_class, mock_get_db):
        """Test successfully processing deadline monitoring."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        
        mock_alert1 = Mock(spec=Alert)
        mock_alert1.id = "alert1"
        mock_alert2 = Mock(spec=Alert)
        mock_alert2.id = "alert2"
        
        mock_alert_service.process_deadline_monitoring.return_value = [
            mock_alert1, mock_alert2
        ]
        
        # Act
        response = client.post("/api/v1/alerts/process-deadlines")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Deadline monitoring processed"
        assert data["alerts_created"] == 2
        assert data["alert_ids"] == ["alert1", "alert2"]
        
        mock_alert_service.process_deadline_monitoring.assert_called_once()
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_process_player_news_success(self, mock_alert_service_class, mock_get_db):
        """Test successfully processing player news."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        
        mock_alert = Mock(spec=Alert)
        mock_alert.id = "alert123"
        
        mock_alert_service.process_player_news.return_value = [mock_alert]
        
        # Act
        response = client.post("/api/v1/alerts/process-news")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Player news processed"
        assert data["alerts_created"] == 1
        assert data["alert_ids"] == ["alert123"]
        
        mock_alert_service.process_player_news.assert_called_once()
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_create_alert_validation_error(self, mock_alert_service_class, mock_get_db):
        """Test creating an alert with validation errors."""
        # Arrange
        request_data = {
            "alert_type": "INVALID_TYPE",
            "title": "",  # Empty title should fail validation
            "message": "Test message"
        }
        
        # Act
        response = client.post("/api/v1/alerts/", json=request_data)
        
        # Assert
        assert response.status_code == 422  # Validation error
    
    @patch('app.api.alerts.get_db')
    @patch('app.api.alerts.AlertService')
    def test_get_alerts_service_error(self, mock_alert_service_class, mock_get_db):
        """Test handling service errors when getting alerts."""
        # Arrange
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_alert_service = Mock()
        mock_alert_service_class.return_value = mock_alert_service
        mock_alert_service.get_active_alerts.side_effect = Exception("Database error")
        
        # Act
        response = client.get("/api/v1/alerts/")
        
        # Assert
        assert response.status_code == 500
        data = response.json()
        assert "Failed to get alerts" in data["detail"]