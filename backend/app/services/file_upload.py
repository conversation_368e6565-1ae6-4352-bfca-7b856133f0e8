"""
File upload and data processing service for external data sources.
"""
import io
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple
from decimal import Decimal
from datetime import datetime, timezone
import pandas as pd
from fastapi import UploadFile, HTTPException
from pydantic import BaseModel, ValidationError
from sqlalchemy.orm import Session
from sqlalchemy import func

from ..models import Player, Projection, Ranking, PlayerPosition
from ..core.database import get_db


class FileUploadError(Exception):
    """Custom exception for file upload errors."""
    pass


class DataValidationError(Exception):
    """Custom exception for data validation errors."""
    pass


class ProjectionData(BaseModel):
    """Pydantic model for projection data validation."""
    player_name: str
    position: Optional[str] = None
    team: Optional[str] = None
    week: Optional[int] = None
    season: int
    projected_points: float
    floor: Optional[float] = None
    ceiling: Optional[float] = None
    # Statistical projections
    passing_yards: Optional[float] = None
    passing_tds: Optional[float] = None
    rushing_yards: Optional[float] = None
    rushing_tds: Optional[float] = None
    receiving_yards: Optional[float] = None
    receiving_tds: Optional[float] = None
    receptions: Optional[float] = None


class RankingData(BaseModel):
    """Pydantic model for ranking data validation."""
    player_name: str
    position: Optional[str] = None
    team: Optional[str] = None
    season: int
    overall_rank: Optional[int] = None
    position_rank: Optional[int] = None
    tier: Optional[int] = None
    adp: Optional[float] = None
    adp_std: Optional[float] = None
    draft_count: Optional[int] = None


class FileUploadService:
    """Service for handling file uploads and data processing."""
    
    SUPPORTED_EXTENSIONS = {'.csv', '.xlsx', '.xls'}
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    def __init__(self, db: Session):
        self.db = db
    
    def _infer_multi_adp_map(self, df: pd.DataFrame) -> Dict[str, str]:
        """Infer multi-provider ADP columns from common provider headers.

        Returns mapping of normalized column name -> provider label.
        """
        cols = [
            str(c).strip().lower().replace('-', '_').replace(' ', '_')
            for c in list(df.columns)
        ]
        mapping: Dict[str, str] = {}
        known = {
            'espn': 'ESPN',
            'sleeper': 'SLEEPER',
            'cbs': 'CBS',
            'nfl': 'NFL',
            'rtsports': 'RTSPORTS',
            'fantrax': 'FANTRAX',
        }
        for k, v in known.items():
            if k in cols:
                mapping[k] = v
        return mapping
    
    def validate_file(self, file: UploadFile) -> None:
        """Validate uploaded file format and size."""
        if not file.filename:
            raise FileUploadError("No filename provided")
        
        # Check file extension
        file_ext = '.' + file.filename.split('.')[-1].lower()
        if file_ext not in self.SUPPORTED_EXTENSIONS:
            raise FileUploadError(
                f"Unsupported file format. Supported formats: {', '.join(self.SUPPORTED_EXTENSIONS)}"
            )
        
        # Check file size (approximate)
        if hasattr(file.file, 'seek') and hasattr(file.file, 'tell'):
            file.file.seek(0, 2)  # Seek to end
            size = file.file.tell()
            file.file.seek(0)  # Reset to beginning
            
            if size > self.MAX_FILE_SIZE:
                raise FileUploadError(f"File too large. Maximum size: {self.MAX_FILE_SIZE / 1024 / 1024}MB")
    
    def read_file_to_dataframe(self, file: UploadFile) -> pd.DataFrame:
        """Read uploaded file into a pandas DataFrame."""
        try:
            file_content = file.file.read()
            file_ext = '.' + file.filename.split('.')[-1].lower()
            
            if file_ext == '.csv':
                # Use delimiter sniffing to support comma- and tab-separated files
                try:
                    df = pd.read_csv(io.BytesIO(file_content), sep=None, engine='python')
                except Exception:
                    df = pd.read_csv(io.BytesIO(file_content))
            elif file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(io.BytesIO(file_content))
            else:
                raise FileUploadError(f"Unsupported file format: {file_ext}")
            
            if df.empty:
                raise FileUploadError("File is empty or contains no data")
            
            return df
            
        except pd.errors.EmptyDataError:
            raise FileUploadError("File is empty or contains no data")
        except pd.errors.ParserError as e:
            raise FileUploadError(f"Error parsing file: {str(e)}")
        except Exception as e:
            raise FileUploadError(f"Error reading file: {str(e)}")
    
    def normalize_column_names(self, df: pd.DataFrame, source: Optional[str] = None) -> pd.DataFrame:
        """Normalize column names to standard format, with optional per-source overrides."""
        from .ranking_sources import get_source_column_mapping
        # Create mapping for common column name variations
        column_mapping = {
            # Player identification
            'player': 'player_name',
            'name': 'player_name',
            'full_name': 'player_name',
            'player_name': 'player_name',
            
            # Position and team
            'pos': 'position',
            'position': 'position',
            'tm': 'team',
            'team': 'team',
            
            # Time period
            'wk': 'week',
            'week': 'week',
            'yr': 'season',
            'year': 'season',
            'season': 'season',
            
            # Projections
            'proj_pts': 'projected_points',
            'projected_points': 'projected_points',
            'fantasy_points': 'projected_points',
            'fpts': 'projected_points',
            'points': 'projected_points',
            
            # Rankings
            'rank': 'overall_rank',
            'overall_rank': 'overall_rank',
            'pos_rank': 'position_rank',
            'position_rank': 'position_rank',
            'tier': 'tier',
            
            # ADP
            'adp': 'adp',
            'avg_draft_pos': 'adp',
            'average_draft_position': 'adp',
            'adp_std': 'adp_std',
            'adp_stdev': 'adp_std',
            
            # Statistical projections
            'pass_yds': 'passing_yards',
            'passing_yards': 'passing_yards',
            'pass_td': 'passing_tds',
            'passing_tds': 'passing_tds',
            'rush_yds': 'rushing_yards',
            'rushing_yards': 'rushing_yards',
            'rush_td': 'rushing_tds',
            'rushing_tds': 'rushing_tds',
            'rec_yds': 'receiving_yards',
            'receiving_yards': 'receiving_yards',
            'rec_td': 'receiving_tds',
            'receiving_tds': 'receiving_tds',
            'rec': 'receptions',
            'receptions': 'receptions',
        }
        
        # Normalize column names (lowercase, replace spaces/special chars)
        df.columns = (
            df.columns
              .str.strip()
              .str.lower()
              .str.replace(r"\s+", "_", regex=True)
              .str.replace('-', '_')
        )
        
        # Merge in per-source overrides (keys expected lowercase)
        src_map = get_source_column_mapping(source)
        if src_map:
            # Ensure target values are strings and keys are lowercase
            src_map_norm = {str(k).lower(): str(v) for k, v in src_map.items()}
            column_mapping.update(src_map_norm)
        # Apply column mapping
        df = df.rename(columns=column_mapping)
        
        return df
    
    def parse_projections(self, df: pd.DataFrame, source: str, season: int) -> List[ProjectionData]:
        """Parse DataFrame into projection data objects."""
        df = self.normalize_column_names(df, source)
        
        # Validate required columns
        required_cols = ['player_name', 'projected_points']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise DataValidationError(f"Missing required columns: {', '.join(missing_cols)}")
        
        # Add season if not present
        if 'season' not in df.columns:
            df['season'] = season
        
        projections = []
        errors = []
        
        for idx, row in df.iterrows():
            try:
                # Create stats dictionary for additional statistical projections
                stats = {}
                stat_columns = [
                    'passing_yards', 'passing_tds', 'rushing_yards', 'rushing_tds',
                    'receiving_yards', 'receiving_tds', 'receptions'
                ]
                
                for col in stat_columns:
                    if col in row and pd.notna(row[col]):
                        stats[col] = float(row[col])
                
                projection_data = ProjectionData(
                    player_name=str(row['player_name']).strip(),
                    position=str(row['position']).strip().upper() if pd.notna(row.get('position')) else None,
                    team=str(row['team']).strip().upper() if pd.notna(row.get('team')) else None,
                    week=int(row['week']) if pd.notna(row.get('week')) else None,
                    season=int(row['season']),
                    projected_points=float(row['projected_points']),
                    floor=float(row['floor']) if pd.notna(row.get('floor')) else None,
                    ceiling=float(row['ceiling']) if pd.notna(row.get('ceiling')) else None,
                    **stats
                )
                projections.append(projection_data)
                
            except (ValueError, ValidationError) as e:
                errors.append(f"Row {idx + 1}: {str(e)}")
        
        if errors:
            raise DataValidationError(f"Data validation errors:\n" + "\n".join(errors[:10]))  # Show first 10 errors
        
        return projections
    
    def parse_rankings(self, df: pd.DataFrame, source: str, season: int, ranking_type: str = 'expert') -> List[RankingData]:
        """Parse DataFrame into ranking data objects."""
        df = self.normalize_column_names(df, source)
        
        # Validate required columns
        required_cols = ['player_name']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise DataValidationError(f"Missing required columns: {', '.join(missing_cols)}")
        
        # Add season if not present
        if 'season' not in df.columns:
            df['season'] = season
        
        rankings = []
        errors = []
        
        for idx, row in df.iterrows():
            try:
                ranking_data = RankingData(
                    player_name=str(row['player_name']).strip(),
                    position=str(row['position']).strip().upper() if pd.notna(row.get('position')) else None,
                    team=str(row['team']).strip().upper() if pd.notna(row.get('team')) else None,
                    season=int(row['season']),
                    overall_rank=int(row['overall_rank']) if pd.notna(row.get('overall_rank')) else None,
                    position_rank=int(row['position_rank']) if pd.notna(row.get('position_rank')) else None,
                    tier=int(row['tier']) if pd.notna(row.get('tier')) else None,
                    adp=float(row['adp']) if pd.notna(row.get('adp')) else None,
                    adp_std=float(row['adp_std']) if pd.notna(row.get('adp_std')) else None,
                    draft_count=int(row['draft_count']) if pd.notna(row.get('draft_count')) else None,
                )
                rankings.append(ranking_data)
                
            except (ValueError, ValidationError) as e:
                errors.append(f"Row {idx + 1}: {str(e)}")
        
        if errors:
            raise DataValidationError(f"Data validation errors:\n" + "\n".join(errors[:10]))  # Show first 10 errors
        
        return rankings
    
    def _normalize_player_name(self, name: str) -> str:
        """Normalize common variations like 'Last, First' to 'First Last' and collapse whitespace."""
        try:
            s = str(name or '').strip()
            if not s:
                return s
            # If there's a single comma, assume 'Last, First [Middle]' and flip
            if s.count(',') == 1:
                last, first = [part.strip() for part in s.split(',', 1)]
                if last and first:
                    s = f"{first} {last}"
            # Collapse multiple spaces
            s = ' '.join(s.split())
            return s
        except Exception:
            return str(name or '').strip()

    def _tokenize_name(self, name: str) -> list[str]:
        s = (name or '').replace(',', ' ').replace('.', ' ')
        tokens = [t for t in s.split() if t]
        return tokens

    def _pick_best_candidate(self, candidates: list[Player], pos_enum: Optional[PlayerPosition], team_upper: Optional[str]) -> Optional[Player]:
        if not candidates:
            return None
        # Prefer exact team match then position match, then one with mfl_id present
        def score(p: Player) -> tuple[int, int, int]:
            team_score = 1 if (team_upper and getattr(p, 'team', None) == team_upper) else 0
            pos_score = 1
            try:
                if pos_enum is not None and getattr(p, 'position', None) == pos_enum:
                    pos_score = 2
                elif pos_enum is None:
                    pos_score = 1
                else:
                    pos_score = 0
            except Exception:
                pos_score = 0
            mfl_score = 1 if getattr(p, 'mfl_id', None) else 0
            return (team_score, pos_score, mfl_score)
        # Sort by scores descending
        candidates_sorted = sorted(candidates, key=score, reverse=True)
        return candidates_sorted[0]

    def find_or_create_player(self, player_name: str, position: Optional[str] = None, team: Optional[str] = None) -> Player:
        """Find existing player or create new one.

        Avoids duplicates by normalizing names (e.g., 'Gibbs, Jahmyr' -> 'Jahmyr Gibbs') and matching by tokens.
        Ensures position is coerced to PlayerPosition enum to satisfy DB constraints.
        """
        # Coerce position string to enum if provided
        pos_enum: Optional[PlayerPosition] = None
        if position:
            try:
                pos_upper = str(position).strip().upper()
                pos_enum = getattr(PlayerPosition, pos_upper)
            except Exception:
                pos_enum = None
        team_upper = str(team).strip().upper() if team else None

        # Normalize name variants
        raw_name = str(player_name or '').strip()
        normalized = self._normalize_player_name(raw_name)
        tokens = self._tokenize_name(normalized)

        # 1) Try exact (case-insensitive) match on normalized full name
        try:
            q = self.db.query(Player).filter(func.lower(Player.name) == normalized.lower())
            if pos_enum is not None:
                q = q.filter(Player.position == pos_enum)
            if team_upper:
                q = q.filter(Player.team == team_upper)
            candidates = q.all()
            if candidates:
                picked = self._pick_best_candidate(candidates, pos_enum, team_upper)
                if picked:
                    return picked
        except Exception:
            pass

        # 2) Token-based match: require all tokens to be present (order-agnostic)
        if tokens:
            q_tokens = self.db.query(Player)
            for t in tokens:
                q_tokens = q_tokens.filter(Player.name.ilike(f"%{t}%"))
            if pos_enum is not None:
                q_tokens = q_tokens.filter(Player.position == pos_enum)
            if team_upper:
                q_tokens = q_tokens.filter(Player.team == team_upper)
            candidates = q_tokens.all()
            if candidates:
                picked = self._pick_best_candidate(candidates, pos_enum, team_upper)
                if picked:
                    return picked

        # 3) Relax position/team constraints for token match if still nothing
        if tokens:
            q_tokens = self.db.query(Player)
            for t in tokens:
                q_tokens = q_tokens.filter(Player.name.ilike(f"%{t}%"))
            candidates = q_tokens.all()
            if candidates:
                picked = self._pick_best_candidate(candidates, None, None)
                if picked:
                    return picked

        # Create new player with safe defaults for required fields; use normalized name to avoid future dupes
        player_id = str(uuid.uuid4())
        safe_team = team_upper if team_upper else 'FA'
        safe_pos = pos_enum if pos_enum is not None else PlayerPosition.WR
        player = Player(
            id=player_id,
            name=normalized or raw_name,
            position=safe_pos,
            team=safe_team,
            player_metadata={'created_from_upload': True}
        )
        self.db.add(player)
        return player
    
    def save_projections(self, projections: List[ProjectionData], source: str) -> Tuple[int, List[str]]:
        """Save projection data to database."""
        saved_count = 0
        errors = []
        
        for proj_data in projections:
            try:
                # Find or create player
                player = self.find_or_create_player(
                    proj_data.player_name, 
                    proj_data.position, 
                    proj_data.team
                )
                
                # Create stats dictionary
                stats = {}
                for field in ['passing_yards', 'passing_tds', 'rushing_yards', 'rushing_tds',
                             'receiving_yards', 'receiving_tds', 'receptions']:
                    value = getattr(proj_data, field)
                    if value is not None:
                        stats[field] = value
                
                # Create projection
                projection = Projection(
                    id=str(uuid.uuid4()),
                    player_id=player.id,
                    week=proj_data.week,
                    season=proj_data.season,
                    source=source,
                    projected_points=Decimal(str(proj_data.projected_points)),
                    floor=Decimal(str(proj_data.floor)) if proj_data.floor else None,
                    ceiling=Decimal(str(proj_data.ceiling)) if proj_data.ceiling else None,
                    stats=stats,
                    projection_metadata={'uploaded_at': datetime.now(timezone.utc).isoformat()}
                )
                
                self.db.add(projection)
                saved_count += 1
                
            except Exception as e:
                # Reset failed transaction to allow subsequent rows to proceed
                try:
                    self.db.rollback()
                except Exception:
                    pass
                errors.append(f"Error saving projection for {proj_data.player_name}: {str(e)}")
        
        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            raise FileUploadError(f"Error saving projections to database: {str(e)}")
        
        return saved_count, errors
    
    def save_rankings(self, rankings: List[RankingData], source: str, ranking_type: str = 'expert') -> Tuple[int, List[str]]:
        """Save ranking data to database."""
        saved_count = 0
        errors = []
        
        for rank_data in rankings:
            try:
                # Find or create player
                player = self.find_or_create_player(
                    rank_data.player_name, 
                    rank_data.position, 
                    rank_data.team
                )
                
                # Skip empty ADP rows for 'adp' ranking_type
                if ranking_type == 'adp' and (rank_data.adp is None):
                    continue
                # Create ranking
                ranking = Ranking(
                    id=str(uuid.uuid4()),
                    player_id=player.id,
                    season=rank_data.season,
                    source=source,
                    ranking_type=ranking_type,
                    overall_rank=rank_data.overall_rank,
                    position_rank=rank_data.position_rank,
                    tier=rank_data.tier,
                    adp=Decimal(str(rank_data.adp)) if rank_data.adp else None,
                    adp_std=Decimal(str(rank_data.adp_std)) if rank_data.adp_std else None,
                    draft_count=rank_data.draft_count,
                    ranking_metadata={'uploaded_at': datetime.now(timezone.utc).isoformat()}
                )
                
                self.db.add(ranking)
                saved_count += 1
                
            except Exception as e:
                # Reset failed transaction to allow subsequent rows to proceed
                try:
                    self.db.rollback()
                except Exception:
                    pass
                errors.append(f"Error saving ranking for {rank_data.player_name}: {str(e)}")
        
        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            raise FileUploadError(f"Error saving rankings to database: {str(e)}")
        
        return saved_count, errors
    
    def _infer_multi_adp_map(self, df: pd.DataFrame) -> Dict[str, str]:
        """Infer multi-provider ADP columns from common provider headers.

        Returns mapping of normalized column name -> provider label.
        """
        # Normalize headers minimally for detection
        cols = [
            str(c).strip().lower().replace('-', '_').replace(' ', '_')
            for c in list(df.columns)
        ]
        mapping: Dict[str, str] = {}
        known = {
            'espn': 'ESPN',
            'sleeper': 'SLEEPER',
            'cbs': 'CBS',
            'nfl': 'NFL',
            'rtsports': 'RTSPORTS',
            'fantrax': 'FANTRAX',
        }
        for k, v in known.items():
            if k in cols:
                mapping[k] = v
        return mapping

    def save_multi_provider_adp(self, df: pd.DataFrame, aggregator_source: str, season: int, multi_map: Dict[str, str]) -> Tuple[int, List[str]]:
        """Explode a single CSV containing multiple provider ADP columns into per-provider Ranking rows.

        multi_map: mapping of CSV column name (lowercased) -> provider label to store in Ranking.source
        """
        # Normalize and ensure basic fields
        df = self.normalize_column_names(df, aggregator_source)
        if 'player_name' not in df.columns:
            raise DataValidationError("Missing required columns: player_name")
        if 'season' not in df.columns:
            df['season'] = season
        saved_count = 0
        errors: List[str] = []
        now_iso = datetime.now(timezone.utc).isoformat()
        # Iterate rows
        for idx, row in df.iterrows():
            try:
                player = self.find_or_create_player(
                    str(row['player_name']).strip(),
                    str(row['position']).strip().upper() if 'position' in df.columns and pd.notna(row.get('position')) else None,
                    str(row['team']).strip().upper() if 'team' in df.columns and pd.notna(row.get('team')) else None,
                )
                for col, provider_label in multi_map.items():
                    if col not in df.columns:
                        continue
                    val = row.get(col)
                    try:
                        adp_val = float(val)
                    except Exception:
                        continue
                    # Upsert to avoid duplicates if re-uploaded
                    existing = (
                        self.db.query(Ranking)
                        .filter(
                            Ranking.player_id == player.id,
                            Ranking.season == int(row['season']),
                            Ranking.source == provider_label,
                            Ranking.ranking_type == 'adp',
                        )
                        .first()
                    )
                    if not existing:
                        ranking = Ranking(
                            id=str(uuid.uuid4()),
                            player_id=player.id,
                            season=int(row['season']),
                            source=provider_label,
                            ranking_type='adp',
                            overall_rank=None,
                            position_rank=None,
                            tier=None,
                            adp=Decimal(str(adp_val)),
                            adp_std=None,
                            draft_count=None,
                            ranking_metadata={
                                'uploaded_at': now_iso,
                                'from_multi_source': aggregator_source,
                                'provider_column': col,
                            },
                        )
                        self.db.add(ranking)
                    else:
                        existing.adp = Decimal(str(adp_val))
                        md = dict(existing.ranking_metadata or {})
                        md.update({'uploaded_at': now_iso, 'from_multi_source': aggregator_source, 'provider_column': col})
                        existing.ranking_metadata = md
                    saved_count += 1
            except Exception as e:
                try:
                    self.db.rollback()
                except Exception:
                    pass
                errors.append(f"Row {idx + 1}: {str(e)}")
        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            raise FileUploadError(f"Error saving multi-provider ADP to database: {str(e)}")
        return saved_count, errors
    
    def process_file(self, file: UploadFile, data_type: str, source: str, season: int, **kwargs) -> Dict[str, Any]:
        """Process uploaded file and save data to database."""
        # Validate file
        self.validate_file(file)
        
        # Read file to DataFrame
        df = self.read_file_to_dataframe(file)
        
        # Process based on data type
        if data_type == 'projections':
            parsed_data = self.parse_projections(df, source, season)
            saved_count, errors = self.save_projections(parsed_data, source)
            
        elif data_type == 'rankings':
            ranking_type = kwargs.get('ranking_type', 'expert')
            parsed_data = self.parse_rankings(df, source, season, ranking_type)
            saved_count, errors = self.save_rankings(parsed_data, source, ranking_type)
            
        elif data_type == 'adp':
            # Check for multi-provider ADP mapping for this source
            from .ranking_sources import get_multi_adp_columns
            multi_map = get_multi_adp_columns(source)
            if not multi_map:
                # Attempt to infer well-known provider columns from the uploaded file
                inferred = self._infer_multi_adp_map(df)
                if inferred:
                    multi_map = inferred
            if multi_map:
                saved_count, errors = self.save_multi_provider_adp(df, source, season, multi_map)
            else:
                parsed_data = self.parse_rankings(df, source, season, 'adp')
                saved_count, errors = self.save_rankings(parsed_data, source, 'adp')
            
        else:
            raise FileUploadError(f"Unsupported data type: {data_type}")
        
        return {
            'filename': file.filename,
            'data_type': data_type,
            'source': source,
            'season': season,
            'total_rows': len(df),
            'saved_count': saved_count,
            'errors': errors,
            'success': len(errors) == 0
        }


def get_file_upload_service(db: Session = None) -> FileUploadService:
    """Dependency to get file upload service."""
    if db is None:
        db = next(get_db())
    return FileUploadService(db)