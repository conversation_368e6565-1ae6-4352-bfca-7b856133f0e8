"""
File upload and data processing service for external data sources.
"""
import io
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple
from decimal import Decimal
from datetime import datetime, timezone
import pandas as pd
from fastapi import UploadFile, HTTPException
from pydantic import BaseModel, ValidationError
from sqlalchemy.orm import Session

from ..models import Player, Projection, Ranking
from ..core.database import get_db


class FileUploadError(Exception):
    """Custom exception for file upload errors."""
    pass


class DataValidationError(Exception):
    """Custom exception for data validation errors."""
    pass


class ProjectionData(BaseModel):
    """Pydantic model for projection data validation."""
    player_name: str
    position: Optional[str] = None
    team: Optional[str] = None
    week: Optional[int] = None
    season: int
    projected_points: float
    floor: Optional[float] = None
    ceiling: Optional[float] = None
    # Statistical projections
    passing_yards: Optional[float] = None
    passing_tds: Optional[float] = None
    rushing_yards: Optional[float] = None
    rushing_tds: Optional[float] = None
    receiving_yards: Optional[float] = None
    receiving_tds: Optional[float] = None
    receptions: Optional[float] = None


class RankingData(BaseModel):
    """Pydantic model for ranking data validation."""
    player_name: str
    position: Optional[str] = None
    team: Optional[str] = None
    season: int
    overall_rank: Optional[int] = None
    position_rank: Optional[int] = None
    tier: Optional[int] = None
    adp: Optional[float] = None
    adp_std: Optional[float] = None
    draft_count: Optional[int] = None


class FileUploadService:
    """Service for handling file uploads and data processing."""
    
    SUPPORTED_EXTENSIONS = {'.csv', '.xlsx', '.xls'}
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    def __init__(self, db: Session):
        self.db = db
    
    def validate_file(self, file: UploadFile) -> None:
        """Validate uploaded file format and size."""
        if not file.filename:
            raise FileUploadError("No filename provided")
        
        # Check file extension
        file_ext = '.' + file.filename.split('.')[-1].lower()
        if file_ext not in self.SUPPORTED_EXTENSIONS:
            raise FileUploadError(
                f"Unsupported file format. Supported formats: {', '.join(self.SUPPORTED_EXTENSIONS)}"
            )
        
        # Check file size (approximate)
        if hasattr(file.file, 'seek') and hasattr(file.file, 'tell'):
            file.file.seek(0, 2)  # Seek to end
            size = file.file.tell()
            file.file.seek(0)  # Reset to beginning
            
            if size > self.MAX_FILE_SIZE:
                raise FileUploadError(f"File too large. Maximum size: {self.MAX_FILE_SIZE / 1024 / 1024}MB")
    
    def read_file_to_dataframe(self, file: UploadFile) -> pd.DataFrame:
        """Read uploaded file into a pandas DataFrame."""
        try:
            file_content = file.file.read()
            file_ext = '.' + file.filename.split('.')[-1].lower()
            
            if file_ext == '.csv':
                df = pd.read_csv(io.BytesIO(file_content))
            elif file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(io.BytesIO(file_content))
            else:
                raise FileUploadError(f"Unsupported file format: {file_ext}")
            
            if df.empty:
                raise FileUploadError("File is empty or contains no data")
            
            return df
            
        except pd.errors.EmptyDataError:
            raise FileUploadError("File is empty or contains no data")
        except pd.errors.ParserError as e:
            raise FileUploadError(f"Error parsing file: {str(e)}")
        except Exception as e:
            raise FileUploadError(f"Error reading file: {str(e)}")
    
    def normalize_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """Normalize column names to standard format."""
        # Create mapping for common column name variations
        column_mapping = {
            # Player identification
            'player': 'player_name',
            'name': 'player_name',
            'full_name': 'player_name',
            'player_name': 'player_name',
            
            # Position and team
            'pos': 'position',
            'position': 'position',
            'tm': 'team',
            'team': 'team',
            
            # Time period
            'wk': 'week',
            'week': 'week',
            'yr': 'season',
            'year': 'season',
            'season': 'season',
            
            # Projections
            'proj_pts': 'projected_points',
            'projected_points': 'projected_points',
            'fantasy_points': 'projected_points',
            'fpts': 'projected_points',
            'points': 'projected_points',
            
            # Rankings
            'rank': 'overall_rank',
            'overall_rank': 'overall_rank',
            'pos_rank': 'position_rank',
            'position_rank': 'position_rank',
            'tier': 'tier',
            
            # ADP
            'adp': 'adp',
            'avg_draft_pos': 'adp',
            'average_draft_position': 'adp',
            'adp_std': 'adp_std',
            'adp_stdev': 'adp_std',
            
            # Statistical projections
            'pass_yds': 'passing_yards',
            'passing_yards': 'passing_yards',
            'pass_td': 'passing_tds',
            'passing_tds': 'passing_tds',
            'rush_yds': 'rushing_yards',
            'rushing_yards': 'rushing_yards',
            'rush_td': 'rushing_tds',
            'rushing_tds': 'rushing_tds',
            'rec_yds': 'receiving_yards',
            'receiving_yards': 'receiving_yards',
            'rec_td': 'receiving_tds',
            'receiving_tds': 'receiving_tds',
            'rec': 'receptions',
            'receptions': 'receptions',
        }
        
        # Normalize column names (lowercase, replace spaces/special chars)
        df.columns = df.columns.str.lower().str.replace(' ', '_').str.replace('-', '_')
        
        # Apply column mapping
        df = df.rename(columns=column_mapping)
        
        return df
    
    def parse_projections(self, df: pd.DataFrame, source: str, season: int) -> List[ProjectionData]:
        """Parse DataFrame into projection data objects."""
        df = self.normalize_column_names(df)
        
        # Validate required columns
        required_cols = ['player_name', 'projected_points']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise DataValidationError(f"Missing required columns: {', '.join(missing_cols)}")
        
        # Add season if not present
        if 'season' not in df.columns:
            df['season'] = season
        
        projections = []
        errors = []
        
        for idx, row in df.iterrows():
            try:
                # Create stats dictionary for additional statistical projections
                stats = {}
                stat_columns = [
                    'passing_yards', 'passing_tds', 'rushing_yards', 'rushing_tds',
                    'receiving_yards', 'receiving_tds', 'receptions'
                ]
                
                for col in stat_columns:
                    if col in row and pd.notna(row[col]):
                        stats[col] = float(row[col])
                
                projection_data = ProjectionData(
                    player_name=str(row['player_name']).strip(),
                    position=str(row['position']).strip().upper() if pd.notna(row.get('position')) else None,
                    team=str(row['team']).strip().upper() if pd.notna(row.get('team')) else None,
                    week=int(row['week']) if pd.notna(row.get('week')) else None,
                    season=int(row['season']),
                    projected_points=float(row['projected_points']),
                    floor=float(row['floor']) if pd.notna(row.get('floor')) else None,
                    ceiling=float(row['ceiling']) if pd.notna(row.get('ceiling')) else None,
                    **stats
                )
                projections.append(projection_data)
                
            except (ValueError, ValidationError) as e:
                errors.append(f"Row {idx + 1}: {str(e)}")
        
        if errors:
            raise DataValidationError(f"Data validation errors:\n" + "\n".join(errors[:10]))  # Show first 10 errors
        
        return projections
    
    def parse_rankings(self, df: pd.DataFrame, source: str, season: int, ranking_type: str = 'expert') -> List[RankingData]:
        """Parse DataFrame into ranking data objects."""
        df = self.normalize_column_names(df)
        
        # Validate required columns
        required_cols = ['player_name']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise DataValidationError(f"Missing required columns: {', '.join(missing_cols)}")
        
        # Add season if not present
        if 'season' not in df.columns:
            df['season'] = season
        
        rankings = []
        errors = []
        
        for idx, row in df.iterrows():
            try:
                ranking_data = RankingData(
                    player_name=str(row['player_name']).strip(),
                    position=str(row['position']).strip().upper() if pd.notna(row.get('position')) else None,
                    team=str(row['team']).strip().upper() if pd.notna(row.get('team')) else None,
                    season=int(row['season']),
                    overall_rank=int(row['overall_rank']) if pd.notna(row.get('overall_rank')) else None,
                    position_rank=int(row['position_rank']) if pd.notna(row.get('position_rank')) else None,
                    tier=int(row['tier']) if pd.notna(row.get('tier')) else None,
                    adp=float(row['adp']) if pd.notna(row.get('adp')) else None,
                    adp_std=float(row['adp_std']) if pd.notna(row.get('adp_std')) else None,
                    draft_count=int(row['draft_count']) if pd.notna(row.get('draft_count')) else None,
                )
                rankings.append(ranking_data)
                
            except (ValueError, ValidationError) as e:
                errors.append(f"Row {idx + 1}: {str(e)}")
        
        if errors:
            raise DataValidationError(f"Data validation errors:\n" + "\n".join(errors[:10]))  # Show first 10 errors
        
        return rankings
    
    def find_or_create_player(self, player_name: str, position: Optional[str] = None, team: Optional[str] = None) -> Player:
        """Find existing player or create new one."""
        # Try to find existing player by name
        query = self.db.query(Player).filter(Player.name.ilike(f"%{player_name}%"))
        
        if position:
            query = query.filter(Player.position == position)
        if team:
            query = query.filter(Player.team == team)
        
        player = query.first()
        
        if not player:
            # Create new player
            player_id = str(uuid.uuid4())
            player = Player(
                id=player_id,
                name=player_name,
                position=position or 'UNKNOWN',
                team=team or 'UNKNOWN',
                player_metadata={'created_from_upload': True}
            )
            self.db.add(player)
            self.db.flush()  # Get the ID without committing
        
        return player
    
    def save_projections(self, projections: List[ProjectionData], source: str) -> Tuple[int, List[str]]:
        """Save projection data to database."""
        saved_count = 0
        errors = []
        
        for proj_data in projections:
            try:
                # Find or create player
                player = self.find_or_create_player(
                    proj_data.player_name, 
                    proj_data.position, 
                    proj_data.team
                )
                
                # Create stats dictionary
                stats = {}
                for field in ['passing_yards', 'passing_tds', 'rushing_yards', 'rushing_tds',
                             'receiving_yards', 'receiving_tds', 'receptions']:
                    value = getattr(proj_data, field)
                    if value is not None:
                        stats[field] = value
                
                # Create projection
                projection = Projection(
                    id=str(uuid.uuid4()),
                    player_id=player.id,
                    week=proj_data.week,
                    season=proj_data.season,
                    source=source,
                    projected_points=Decimal(str(proj_data.projected_points)),
                    floor=Decimal(str(proj_data.floor)) if proj_data.floor else None,
                    ceiling=Decimal(str(proj_data.ceiling)) if proj_data.ceiling else None,
                    stats=stats,
                    projection_metadata={'uploaded_at': datetime.now(timezone.utc).isoformat()}
                )
                
                self.db.add(projection)
                saved_count += 1
                
            except Exception as e:
                errors.append(f"Error saving projection for {proj_data.player_name}: {str(e)}")
        
        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            raise FileUploadError(f"Error saving projections to database: {str(e)}")
        
        return saved_count, errors
    
    def save_rankings(self, rankings: List[RankingData], source: str, ranking_type: str = 'expert') -> Tuple[int, List[str]]:
        """Save ranking data to database."""
        saved_count = 0
        errors = []
        
        for rank_data in rankings:
            try:
                # Find or create player
                player = self.find_or_create_player(
                    rank_data.player_name, 
                    rank_data.position, 
                    rank_data.team
                )
                
                # Create ranking
                ranking = Ranking(
                    id=str(uuid.uuid4()),
                    player_id=player.id,
                    season=rank_data.season,
                    source=source,
                    ranking_type=ranking_type,
                    overall_rank=rank_data.overall_rank,
                    position_rank=rank_data.position_rank,
                    tier=rank_data.tier,
                    adp=Decimal(str(rank_data.adp)) if rank_data.adp else None,
                    adp_std=Decimal(str(rank_data.adp_std)) if rank_data.adp_std else None,
                    draft_count=rank_data.draft_count,
                    ranking_metadata={'uploaded_at': datetime.now(timezone.utc).isoformat()}
                )
                
                self.db.add(ranking)
                saved_count += 1
                
            except Exception as e:
                errors.append(f"Error saving ranking for {rank_data.player_name}: {str(e)}")
        
        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            raise FileUploadError(f"Error saving rankings to database: {str(e)}")
        
        return saved_count, errors
    
    def process_file(self, file: UploadFile, data_type: str, source: str, season: int, **kwargs) -> Dict[str, Any]:
        """Process uploaded file and save data to database."""
        # Validate file
        self.validate_file(file)
        
        # Read file to DataFrame
        df = self.read_file_to_dataframe(file)
        
        # Process based on data type
        if data_type == 'projections':
            parsed_data = self.parse_projections(df, source, season)
            saved_count, errors = self.save_projections(parsed_data, source)
            
        elif data_type == 'rankings':
            ranking_type = kwargs.get('ranking_type', 'expert')
            parsed_data = self.parse_rankings(df, source, season, ranking_type)
            saved_count, errors = self.save_rankings(parsed_data, source, ranking_type)
            
        elif data_type == 'adp':
            parsed_data = self.parse_rankings(df, source, season, 'adp')
            saved_count, errors = self.save_rankings(parsed_data, source, 'adp')
            
        else:
            raise FileUploadError(f"Unsupported data type: {data_type}")
        
        return {
            'filename': file.filename,
            'data_type': data_type,
            'source': source,
            'season': season,
            'total_rows': len(df),
            'saved_count': saved_count,
            'errors': errors,
            'success': len(errors) == 0
        }


def get_file_upload_service(db: Session = None) -> FileUploadService:
    """Dependency to get file upload service."""
    if db is None:
        db = next(get_db())
    return FileUploadService(db)