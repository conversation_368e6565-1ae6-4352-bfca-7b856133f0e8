"""
Custom ranking source configuration and column mappings.

Allows registering up to two additional CSV ranking providers with
source-specific column name mappings. The configuration is stored as JSON
under data/rankings/custom_sources.json at runtime and loaded on demand.
"""
from __future__ import annotations

from typing import Dict, Any, List
import json
import os
from pathlib import Path

# Discover project root similar to adp_config to remain robust in different envs
_CURRENT = Path(__file__).resolve()
_PARENTS = list(_CURRENT.parents)
_BASE = None
for p in _PARENTS:
    if (p / "app").exists():
        _BASE = p
        break
if _BASE is None:
    try:
        _BASE = _PARENTS[2]
    except Exception:
        _BASE = Path("/app")
if str(_BASE) == "/":
    _BASE = Path("/app")
BASE_DIR = str(_BASE)
DATA_DIR = os.path.join(BASE_DIR, "data", "rankings")
CONFIG_PATH = os.path.join(DATA_DIR, "custom_sources.json")

MAX_CUSTOM_SOURCES = 2

# In-memory override for tests or dynamic updates without touching disk
_OVERRIDE_CONFIG: Dict[str, Dict[str, Any]] | None = None


def _normalize_source_name(name: str | None) -> str:
    return (name or "").strip().upper()


def _ensure_dirs() -> None:
    os.makedirs(DATA_DIR, exist_ok=True)


def load_custom_sources_config() -> Dict[str, Any]:
    global _Override
    _ensure_dirs()
    # If override set (tests), return it
    global _OVERRIDE_CONFIG
    if _OVERRIDE_CONFIG is not None:
        return _OVERRIDE_CONFIG
    if not os.path.exists(CONFIG_PATH):
        return {"sources": []}
    try:
        with open(CONFIG_PATH, "r") as f:
            raw = json.load(f) or {}
        # sanitize
        items = raw.get("sources", [])
        if not isinstance(items, list):
            items = []
        # limit to MAX_CUSTOM_SOURCES
        items = items[:MAX_CUSTOM_SOURCES]
        # normalize names and mapping keys to lowercase for matching pipeline
        norm_items: List[Dict[str, Any]] = []
        for it in items:
            name = _normalize_source_name(it.get("name"))
            mapping = it.get("column_mapping") or {}
            # normalize mapping keys to lowercase (since we lowercase headers first)
            norm_map = {str(k).lower(): str(v) for k, v in mapping.items()}
            # normalize multi-provider ADP columns if provided
            multi = it.get("adp_provider_columns") or {}
            # keys are CSV headers (lowercased), values are provider names (normalize to uppercase labels)
            norm_multi = {str(k).lower(): _normalize_source_name(v) for k, v in multi.items()} if isinstance(multi, dict) else {}
            norm_items.append({
                "name": name,
                "column_mapping": norm_map,
                "adp_provider_columns": norm_multi,
            })
        return {"sources": norm_items}
    except Exception:
        return {"sources": []}


def save_custom_sources_config(payload: Dict[str, Any]) -> Dict[str, Any]:
    """Persist custom source config to disk. Enforces max sources and normalization."""
    _ensure_dirs()
    items = (payload or {}).get("sources", [])
    items = items[:MAX_CUSTOM_SOURCES]
    norm_items: List[Dict[str, Any]] = []
    names_seen: set[str] = set()
    for it in items:
        name = _normalize_source_name(it.get("name"))
        if not name or name in names_seen:
            # skip invalid/duplicate names
            continue
        names_seen.add(name)
        mapping = it.get("column_mapping") or {}
        norm_map = {str(k).lower(): str(v) for k, v in mapping.items()}
        # normalize multi-provider ADP columns
        multi = it.get("adp_provider_columns") or {}
        norm_multi = {str(k).lower(): _normalize_source_name(v) for k, v in multi.items()} if isinstance(multi, dict) else {}
        norm_items.append({
            "name": name,
            "column_mapping": norm_map,
            "adp_provider_columns": norm_multi,
        })
    cfg = {"sources": norm_items}
    with open(CONFIG_PATH, "w") as f:
        json.dump(cfg, f, indent=2)
    # Clear override when persisting
    global _OVERRIDE_CONFIG
    _OVERRIDE_CONFIG = None
    return cfg


def set_override_config(cfg: Dict[str, Any] | None) -> None:
    """Set in-memory override for tests; pass None to clear."""
    global _OVERRIDE_CONFIG
    _OVERRIDE_CONFIG = cfg


def get_source_column_mapping(source: str | None) -> Dict[str, str]:
    """Return per-source column mapping for the given source name.

    Keys in the returned dict should be lowercase to match normalized headers.
    Values must be canonical target column names (e.g., 'player_name', 'overall_rank').
    """
    src = _normalize_source_name(source)
    if not src:
        return {}
    cfg = load_custom_sources_config()
    for it in cfg.get("sources", []):
        if _normalize_source_name(it.get("name")) == src:
            return dict(it.get("column_mapping") or {})
    return {}


def get_multi_adp_columns(source: str | None) -> Dict[str, str]:
    """Return mapping of CSV column name -> provider source label for multi-provider ADP uploads.

    Keys are expected lowercase headers in the CSV. Values are provider names to set in Ranking.source.
    """
    src = _normalize_source_name(source)
    if not src:
        return {}
    cfg = load_custom_sources_config()
    for it in cfg.get("sources", []):
        if _normalize_source_name(it.get("name")) == src:
            multi = it.get("adp_provider_columns") or {}
            if isinstance(multi, dict):
                return {str(k).lower(): _normalize_source_name(v) for k, v in multi.items()}
            return {}
    return {}


def get_config_info() -> Dict[str, Any]:
    """Summarize current custom source support for API exposure."""
    cfg = load_custom_sources_config()
    return {
        "max_custom_sources": MAX_CUSTOM_SOURCES,
        "configured_sources": [
            {
                "name": it.get("name"),
                "column_mapping_keys": sorted(list((it.get("column_mapping") or {}).keys())),
                "adp_provider_columns": sorted(list((it.get("adp_provider_columns") or {}).keys())),
            }
            for it in cfg.get("sources", [])
        ],
        "config_path": CONFIG_PATH,
    }
