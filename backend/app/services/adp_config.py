"""
ADP configuration storage and defaults.

Stores and retrieves per-league ADP config as JSON files under data/adp/.
Avoids DB schema changes by persisting config locally.
"""
from __future__ import annotations

import json
import os
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import Dict, Any, List

DEFAULT_CONFIG = {
    "providers": ["MFL", "FFC"],
    "weights": {"MFL": 0.5, "FFC": 0.5},
    "scoring": "PPR",
    "exclude_positions": ["K", "DL", "LB", "DB"],
    "staleness_days": 14,
}

ALLOWED_PROVIDERS = ("MFL", "SLEEPER", "FFC", "ESPN", "CBS", "NFL", "RTSPORTS", "FANTRAX")

# Resolve base directory to project root mounted at /app in the container.
# Try to find '/app' by walking up; fallback to '/app'.
_current = Path(__file__).resolve()
parents = list(_current.parents)
_base = None
for p in parents:
    # choose the first directory that contains an 'app' subdir (our package root)
    if (p / 'app').exists():
        _base = p
        break
if _base is None:
    # Try parent[2] if available
    try:
        _base = parents[2]
    except Exception:
        _base = Path('/app')
# Final fallback if we somehow got root
if str(_base) == '/':
    _base = Path('/app')
BASE_DIR = str(_base)
DATA_DIR = os.path.join(BASE_DIR, "data", "adp")


def _config_path(league_id: str) -> str:
    os.makedirs(DATA_DIR, exist_ok=True)
    return os.path.join(DATA_DIR, f"{league_id}.json")


def load_adp_config(league_id: str) -> Dict[str, Any]:
    path = _config_path(league_id)
    if not os.path.exists(path):
        return DEFAULT_CONFIG.copy()
    try:
        with open(path, "r") as f:
            raw = json.load(f)
        # merge defaults to ensure new keys appear
        cfg = DEFAULT_CONFIG.copy()
        cfg.update(raw or {})
        # Repair missing weights entries if needed
        weights = cfg.get("weights", {})
        for k, v in DEFAULT_CONFIG["weights"].items():
            weights.setdefault(k, v)
        cfg["weights"] = weights
        return cfg
    except Exception:
        return DEFAULT_CONFIG.copy()


def save_adp_config(league_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
    cfg = DEFAULT_CONFIG.copy()
    cfg.update(config or {})
    # normalize providers
    providers = [p.upper() for p in cfg.get("providers", [])]
    cfg["providers"] = [p for p in providers if p in ALLOWED_PROVIDERS]
    # normalize weights and ensure they exist for enabled providers
    weights = cfg.get("weights", {})
    weights = {k.upper(): float(v) for k, v in weights.items() if k.upper() in ALLOWED_PROVIDERS}
    for p in cfg["providers"]:
        # default to 0.0 for new providers unless explicitly set by user
        weights.setdefault(p, float(DEFAULT_CONFIG["weights"].get(p, 0.0)))
    cfg["weights"] = weights
    # normalize scoring
    scoring = str(cfg.get("scoring", "PPR")).upper()
    cfg["scoring"] = scoring
    # normalize exclude positions
    cfg["exclude_positions"] = [str(p).upper() for p in cfg.get("exclude_positions", DEFAULT_CONFIG["exclude_positions"])]
    # staleness
    try:
        cfg["staleness_days"] = int(cfg.get("staleness_days", DEFAULT_CONFIG["staleness_days"]))
    except Exception:
        cfg["staleness_days"] = DEFAULT_CONFIG["staleness_days"]

    path = _config_path(league_id)
    with open(path, "w") as f:
        json.dump(cfg, f, indent=2)
    return cfg

