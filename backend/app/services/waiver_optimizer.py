"""
Waiver wire optimization service for fantasy football waiver/FAAB decisions.

This service implements free agent value analysis using points over replacement,
FAAB bid optimization considering salary cap constraints, streaming opportunity
identification, and waiver priority strategy recommendations.
"""
from typing import Dict, List, Optional, Tuple, Any, Set
from decimal import Decimal
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
import logging
import math

from sqlalchemy.orm import Session
from sqlalchemy import and_, func, not_

from ..models.league import League
from ..models.roster import <PERSON><PERSON><PERSON>, RosterPlayer, Franchise
from ..models.player import Player, PlayerPosition
from ..models.projection import Projection
from ..services.projections_aggregator import ProjectionsAggregator
from ..core.database import get_db

logger = logging.getLogger(__name__)


class WaiverTargetType(str, Enum):
    """Types of waiver wire targets."""
    STARTER_UPGRADE = "starter_upgrade"
    HANDCUFF = "handcuff"
    STREAMING = "streaming"
    STASH = "stash"
    INJURY_REPLACEMENT = "injury_replacement"


class WaiverPriority(str, Enum):
    """Waiver priority levels."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class FreeAgent:
    """Represents a free agent player available on waivers."""
    player_id: str
    player_name: str
    position: PlayerPosition
    team: str
    projected_points: Decimal
    points_over_replacement: Decimal
    ownership_percentage: Optional[float]
    recent_performance: List[Decimal]
    upcoming_matchups: List[str]
    bye_week: Optional[int]
    injury_status: str
    target_type: WaiverTargetType
    metadata: Dict[str, Any]


@dataclass
class WaiverTarget:
    """Represents a waiver wire target with bid recommendation."""
    player_id: str
    player_name: str
    position: PlayerPosition
    target_type: WaiverTargetType
    priority: WaiverPriority
    recommended_bid: Decimal
    max_bid: Decimal
    points_over_replacement: Decimal
    weekly_upside: Decimal
    rationale: str
    drop_candidates: List['DropCandidate']
    streaming_weeks: Optional[List[int]]
    confidence: float
    metadata: Dict[str, Any]


@dataclass
class DropCandidate:
    """Represents a player that could be dropped for a waiver pickup."""
    player_id: str
    player_name: str
    position: PlayerPosition
    projected_points: Decimal
    drop_priority: int  # Lower number = higher priority to drop
    reason: str


@dataclass
class StreamingOpportunity:
    """Represents a streaming opportunity for a specific position."""
    position: PlayerPosition
    weeks: List[int]
    targets: List[WaiverTarget]
    total_value: Decimal
    strategy: str
    confidence: float


@dataclass
class WaiverStrategy:
    """Represents a complete waiver wire strategy."""
    strategy_name: str
    targets: List[WaiverTarget]
    total_faab_allocation: Decimal
    remaining_budget: Decimal
    expected_value: Decimal
    risk_level: str
    streaming_opportunities: List[StreamingOpportunity]
    trade_offs: List[str]
    metadata: Dict[str, Any]


class WaiverOptimizer:
    """
    Service for optimizing waiver wire decisions and FAAB bid amounts.
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.projections_aggregator = ProjectionsAggregator(db)
        self._replacement_levels_cache: Dict[str, Dict[PlayerPosition, Decimal]] = {}
    
    def analyze_waiver_targets(
        self,
        franchise_id: str,
        week: int,
        season: int = 2024,
        max_targets: int = 10
    ) -> List[WaiverTarget]:
        """
        Analyze available free agents and recommend waiver targets.
        
        Args:
            franchise_id: Franchise identifier
            week: Current week number
            season: Season year
            max_targets: Maximum number of targets to return
            
        Returns:
            List of WaiverTarget objects ordered by priority
        """
        # Get franchise and league information
        franchise = self.db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise:
            raise ValueError(f"Franchise {franchise_id} not found")
        
        league = franchise.league
        
        # Get available free agents
        free_agents = self.get_available_free_agents(league.id, season)
        if not free_agents:
            logger.warning(f"No free agents found for league {league.id}")
            return []
        
        # Analyze team needs
        team_needs = self.analyze_team_needs(franchise_id, week, season)
        
        # Get replacement levels
        replacement_levels = self.calculate_replacement_levels(league.id, season)
        
        # Generate waiver targets
        targets = []
        
        for free_agent in free_agents:
            # Calculate points over replacement
            replacement_level = replacement_levels.get(free_agent.position, Decimal('0'))
            por = free_agent.projected_points - replacement_level
            
            if por <= 0:
                continue  # Skip players below replacement level
            
            # Determine target type and priority
            target_type, priority = self._determine_target_type_and_priority(
                free_agent, team_needs, franchise
            )
            
            # Calculate bid recommendation
            recommended_bid, max_bid = self._calculate_bid_amounts(
                free_agent, por, franchise, priority
            )
            
            # Find drop candidates
            drop_candidates = self._find_drop_candidates(
                franchise, free_agent.position, por
            )
            
            # Calculate weekly upside
            weekly_upside = self._calculate_weekly_upside(free_agent, week, season)
            
            # Determine streaming weeks if applicable
            streaming_weeks = None
            if target_type == WaiverTargetType.STREAMING:
                streaming_weeks = self._identify_streaming_weeks(
                    free_agent, week, season
                )
            
            # Generate rationale
            rationale = self._generate_waiver_rationale(
                free_agent, target_type, por, weekly_upside
            )
            
            # Calculate confidence
            confidence = self._calculate_waiver_confidence(free_agent, target_type)
            
            target = WaiverTarget(
                player_id=free_agent.player_id,
                player_name=free_agent.player_name,
                position=free_agent.position,
                target_type=target_type,
                priority=priority,
                recommended_bid=recommended_bid,
                max_bid=max_bid,
                points_over_replacement=por,
                weekly_upside=weekly_upside,
                rationale=rationale,
                drop_candidates=drop_candidates,
                streaming_weeks=streaming_weeks,
                confidence=confidence,
                metadata=free_agent.metadata
            )
            
            targets.append(target)
        
        # Sort by priority and points over replacement
        targets.sort(key=lambda t: (
            self._priority_sort_key(t.priority),
            -float(t.points_over_replacement)
        ))
        
        return targets[:max_targets]
    
    def optimize_faab_allocation(
        self,
        franchise_id: str,
        targets: List[WaiverTarget],
        budget_constraint: Optional[Decimal] = None
    ) -> WaiverStrategy:
        """
        Optimize FAAB allocation across multiple waiver targets.
        
        Args:
            franchise_id: Franchise identifier
            targets: List of waiver targets to consider
            budget_constraint: Optional budget constraint (uses remaining FAAB if None)
            
        Returns:
            WaiverStrategy with optimized allocations
        """
        franchise = self.db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise:
            raise ValueError(f"Franchise {franchise_id} not found")
        
        # Determine available budget
        available_budget = budget_constraint or franchise.get_remaining_faab()
        if available_budget <= 0:
            logger.warning(f"No FAAB budget available for franchise {franchise_id}")
            return self._create_empty_strategy(available_budget)
        
        # Filter targets that fit within budget
        affordable_targets = [
            t for t in targets 
            if t.recommended_bid <= available_budget
        ]
        
        if not affordable_targets:
            logger.warning(f"No affordable targets for franchise {franchise_id}")
            return self._create_empty_strategy(available_budget)
        
        # Generate different allocation strategies
        strategies = []
        
        # Conservative strategy - focus on high-priority targets
        conservative_strategy = self._generate_conservative_strategy(
            affordable_targets, available_budget
        )
        strategies.append(conservative_strategy)
        
        # Aggressive strategy - maximize total value
        aggressive_strategy = self._generate_aggressive_strategy(
            affordable_targets, available_budget
        )
        strategies.append(aggressive_strategy)
        
        # Balanced strategy - mix of priorities
        balanced_strategy = self._generate_balanced_strategy(
            affordable_targets, available_budget
        )
        strategies.append(balanced_strategy)
        
        # Select the best strategy based on expected value and risk
        best_strategy = max(strategies, key=lambda s: s.expected_value)
        
        return best_strategy
    
    def identify_streaming_opportunities(
        self,
        franchise_id: str,
        current_week: int,
        weeks_ahead: int = 4,
        season: int = 2024
    ) -> List[StreamingOpportunity]:
        """
        Identify streaming opportunities for upcoming weeks.
        
        Args:
            franchise_id: Franchise identifier
            current_week: Current week number
            weeks_ahead: Number of weeks to look ahead
            season: Season year
            
        Returns:
            List of StreamingOpportunity objects
        """
        franchise = self.db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise:
            raise ValueError(f"Franchise {franchise_id} not found")
        
        # Positions that are commonly streamed
        streaming_positions = [PlayerPosition.QB, PlayerPosition.TE, PlayerPosition.DEF, PlayerPosition.K]
        
        opportunities = []
        
        for position in streaming_positions:
            # Analyze streaming potential for this position
            opportunity = self._analyze_position_streaming(
                franchise, position, current_week, weeks_ahead, season
            )
            
            if opportunity and opportunity.total_value > 0:
                opportunities.append(opportunity)
        
        # Sort by total value
        opportunities.sort(key=lambda o: o.total_value, reverse=True)
        
        return opportunities
    
    def get_available_free_agents(
        self,
        league_id: str,
        season: int = 2024,
        min_projected_points: Decimal = Decimal('5')
    ) -> List[FreeAgent]:
        """
        Get all available free agents in the league.
        
        Args:
            league_id: League identifier
            season: Season year
            min_projected_points: Minimum projected points threshold
            
        Returns:
            List of FreeAgent objects
        """
        # Get all players not currently on any roster in the league
        rostered_player_ids = self.db.query(RosterPlayer.player_id).join(
            Roster
        ).join(
            Franchise
        ).filter(
            Franchise.league_id == league_id,
            RosterPlayer.is_active == True
        ).subquery()
        
        available_players = self.db.query(Player).filter(
            not_(Player.id.in_(rostered_player_ids))
        ).all()
        
        free_agents = []
        
        for player in available_players:
            # Get aggregated projection
            aggregated_projection = self.projections_aggregator.aggregate_projections(
                player_id=player.id,
                week=None,  # Season-long projection
                season=season
            )
            
            if not aggregated_projection or aggregated_projection.projected_points < min_projected_points:
                continue
            
            # Get recent performance (last 4 weeks)
            recent_performance = self._get_recent_performance(player.id, season)
            
            # Get upcoming matchups
            upcoming_matchups = self._get_upcoming_matchups(player.team, season)
            
            # Determine target type based on player characteristics
            target_type = self._classify_free_agent_type(player, aggregated_projection)
            
            free_agent = FreeAgent(
                player_id=player.id,
                player_name=player.name,
                position=player.position,
                team=player.team,
                projected_points=aggregated_projection.projected_points,
                points_over_replacement=Decimal('0'),  # Will be calculated later
                ownership_percentage=None,  # Would need external data
                recent_performance=recent_performance,
                upcoming_matchups=upcoming_matchups,
                bye_week=player.bye_week,
                injury_status=player.injury_status.value,
                target_type=target_type,
                metadata={
                    'projection_confidence': aggregated_projection.variance,
                    'source_count': aggregated_projection.source_count,
                    'ceiling': aggregated_projection.ceiling,
                    'floor': aggregated_projection.floor
                }
            )
            
            free_agents.append(free_agent)
        
        return free_agents
    
    def calculate_replacement_levels(
        self,
        league_id: str,
        season: int = 2024
    ) -> Dict[PlayerPosition, Decimal]:
        """
        Calculate replacement level baselines for each position.
        
        Args:
            league_id: League identifier
            season: Season year
            
        Returns:
            Dictionary mapping positions to replacement level points
        """
        cache_key = f"{league_id}_{season}"
        if cache_key in self._replacement_levels_cache:
            return self._replacement_levels_cache[cache_key]
        
        league = self.db.query(League).filter(League.id == league_id).first()
        if not league:
            raise ValueError(f"League {league_id} not found")
        
        replacement_levels = {}
        
        # Get all aggregated projections for the season
        all_projections = self.projections_aggregator.aggregate_all_projections(
            week=None,  # Season-long
            season=season,
            min_sources=1
        )
        
        # Group projections by position
        position_projections = {}
        for proj in all_projections:
            player = self.db.query(Player).filter(Player.id == proj.player_id).first()
            if player:
                position = player.position
                if position not in position_projections:
                    position_projections[position] = []
                position_projections[position].append(proj.projected_points)
        
        # Calculate replacement level for each position
        for position, projections in position_projections.items():
            if not projections:
                replacement_levels[position] = Decimal('0')
                continue
            
            # Sort projections in descending order
            sorted_projections = sorted(projections, reverse=True)
            
            # Get roster requirements for this position
            roster_slots = self._get_position_roster_slots(league, position)
            total_teams = len(league.franchises)
            
            # Calculate replacement level index
            # For waiver wire, replacement level is deeper than for keepers
            bench_depth = 3  # Assume 3 bench players per position
            replacement_index = (roster_slots + bench_depth) * total_teams
            
            # Ensure we don't exceed available players
            replacement_index = min(replacement_index, len(sorted_projections) - 1)
            
            if replacement_index >= 0:
                replacement_levels[position] = sorted_projections[replacement_index]
            else:
                replacement_levels[position] = Decimal('0')
        
        # Cache the results
        self._replacement_levels_cache[cache_key] = replacement_levels
        
        logger.info(f"Calculated replacement levels for league {league_id}: {replacement_levels}")
        return replacement_levels
    
    def analyze_team_needs(
        self,
        franchise_id: str,
        week: int,
        season: int = 2024
    ) -> Dict[PlayerPosition, Dict[str, Any]]:
        """
        Analyze team needs by position.
        
        Args:
            franchise_id: Franchise identifier
            week: Current week number
            season: Season year
            
        Returns:
            Dictionary mapping positions to need analysis
        """
        franchise = self.db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise or not franchise.roster:
            return {}
        
        roster = franchise.roster
        league = franchise.league
        
        team_needs = {}
        
        # Analyze each position
        for position in PlayerPosition:
            position_players = roster.get_players_by_position(position.value)
            
            # Get league requirements for this position
            required_starters = self._get_position_roster_slots(league, position)
            
            # Calculate position strength
            if position_players:
                # Get projections for current players
                position_projections = []
                for roster_player in position_players:
                    proj = self.projections_aggregator.aggregate_projections(
                        player_id=roster_player.player_id,
                        week=week,
                        season=season
                    )
                    if proj:
                        position_projections.append(proj.projected_points)
                
                # Sort by projected points
                position_projections.sort(reverse=True)
                
                # Calculate position metrics
                starter_strength = sum(position_projections[:required_starters]) if position_projections else Decimal('0')
                depth_count = len(position_projections)
                avg_starter_points = starter_strength / required_starters if required_starters > 0 else Decimal('0')
                
                # Determine need level
                if depth_count < required_starters:
                    need_level = "critical"
                elif depth_count == required_starters:
                    need_level = "high"
                elif avg_starter_points < 10:  # Arbitrary threshold
                    need_level = "medium"
                else:
                    need_level = "low"
            else:
                # No players at this position
                need_level = "critical"
                starter_strength = Decimal('0')
                depth_count = 0
                avg_starter_points = Decimal('0')
            
            team_needs[position] = {
                'need_level': need_level,
                'starter_strength': starter_strength,
                'depth_count': depth_count,
                'required_starters': required_starters,
                'avg_starter_points': avg_starter_points
            }
        
        return team_needs  
  
    def _determine_target_type_and_priority(
        self,
        free_agent: FreeAgent,
        team_needs: Dict[PlayerPosition, Dict[str, Any]],
        franchise: Franchise
    ) -> Tuple[WaiverTargetType, WaiverPriority]:
        """Determine the target type and priority for a free agent."""
        position_need = team_needs.get(free_agent.position, {})
        need_level = position_need.get('need_level', 'low')
        
        # Check if this is a handcuff situation
        if self._is_handcuff_target(free_agent, franchise):
            return WaiverTargetType.HANDCUFF, WaiverPriority.MEDIUM
        
        # Check if this is a streaming candidate
        if self._is_streaming_candidate(free_agent):
            priority = WaiverPriority.LOW if need_level == 'low' else WaiverPriority.MEDIUM
            return WaiverTargetType.STREAMING, priority
        
        # Check if this is an injury replacement
        if need_level == 'critical':
            return WaiverTargetType.INJURY_REPLACEMENT, WaiverPriority.HIGH
        
        # Check if this is a starter upgrade
        if need_level in ['high', 'medium'] and free_agent.projected_points > 15:
            priority = WaiverPriority.HIGH if need_level == 'high' else WaiverPriority.MEDIUM
            return WaiverTargetType.STARTER_UPGRADE, priority
        
        # Default to stash with low priority
        return WaiverTargetType.STASH, WaiverPriority.LOW
    
    def _calculate_bid_amounts(
        self,
        free_agent: FreeAgent,
        points_over_replacement: Decimal,
        franchise: Franchise,
        priority: WaiverPriority
    ) -> Tuple[Decimal, Decimal]:
        """Calculate recommended and maximum bid amounts."""
        remaining_budget = franchise.get_remaining_faab()
        
        if remaining_budget <= 0:
            return Decimal('0'), Decimal('0')
        
        # Base bid calculation using points over replacement
        base_bid = points_over_replacement * Decimal('0.5')  # $0.50 per point over replacement
        
        # Adjust based on priority
        priority_multipliers = {
            WaiverPriority.HIGH: Decimal('1.5'),
            WaiverPriority.MEDIUM: Decimal('1.0'),
            WaiverPriority.LOW: Decimal('0.6')
        }
        
        adjusted_bid = base_bid * priority_multipliers[priority]
        
        # Apply budget constraints
        max_budget_percentage = {
            WaiverPriority.HIGH: Decimal('0.25'),  # Up to 25% of budget
            WaiverPriority.MEDIUM: Decimal('0.15'),  # Up to 15% of budget
            WaiverPriority.LOW: Decimal('0.08')     # Up to 8% of budget
        }
        
        max_bid = remaining_budget * max_budget_percentage[priority]
        recommended_bid = min(adjusted_bid, max_bid)
        
        # Ensure minimum bid of $1 for viable targets
        if recommended_bid > 0:
            recommended_bid = max(recommended_bid, Decimal('1'))
        
        # Maximum bid is 50% higher than recommended
        max_bid = min(recommended_bid * Decimal('1.5'), remaining_budget)
        
        return recommended_bid, max_bid
    
    def _find_drop_candidates(
        self,
        franchise: Franchise,
        target_position: PlayerPosition,
        target_por: Decimal
    ) -> List[DropCandidate]:
        """Find players that could be dropped for the waiver pickup."""
        if not franchise.roster:
            return []
        
        drop_candidates = []
        
        # Get all bench players (not in starting lineup)
        bench_players = [
            rp for rp in franchise.roster.get_active_players()
            if not rp.is_starting()
        ]
        
        for roster_player in bench_players:
            player = roster_player.player
            
            # Get player projection
            proj = self.projections_aggregator.aggregate_projections(
                player_id=player.id,
                week=None,
                season=2024
            )
            
            if not proj:
                continue
            
            # Calculate drop priority (lower = more likely to drop)
            drop_priority = self._calculate_drop_priority(
                roster_player, proj.projected_points, target_por
            )
            
            # Generate drop reason
            reason = self._generate_drop_reason(roster_player, proj.projected_points, target_por)
            
            candidate = DropCandidate(
                player_id=player.id,
                player_name=player.name,
                position=player.position,
                projected_points=proj.projected_points,
                drop_priority=drop_priority,
                reason=reason
            )
            
            drop_candidates.append(candidate)
        
        # Sort by drop priority (ascending - lower priority = more likely to drop)
        drop_candidates.sort(key=lambda c: c.drop_priority)
        
        return drop_candidates[:5]  # Return top 5 drop candidates
    
    def _calculate_weekly_upside(
        self,
        free_agent: FreeAgent,
        week: int,
        season: int
    ) -> Decimal:
        """Calculate the weekly upside potential for a free agent."""
        # Get weekly projection if available
        weekly_proj = self.projections_aggregator.aggregate_projections(
            player_id=free_agent.player_id,
            week=week,
            season=season
        )
        
        if weekly_proj and weekly_proj.ceiling:
            return weekly_proj.ceiling
        
        # Estimate upside from season projection
        weekly_estimate = free_agent.projected_points / Decimal('17')  # 17 weeks in season
        upside_multiplier = Decimal('1.5')  # 50% upside
        
        return weekly_estimate * upside_multiplier
    
    def _identify_streaming_weeks(
        self,
        free_agent: FreeAgent,
        current_week: int,
        season: int,
        weeks_ahead: int = 4
    ) -> List[int]:
        """Identify which weeks this player would be good for streaming."""
        streaming_weeks = []
        
        # For now, use a simple heuristic based on matchups and bye weeks
        for week in range(current_week, min(current_week + weeks_ahead, 18)):
            if free_agent.bye_week and week == free_agent.bye_week:
                continue  # Skip bye week
            
            # Add week if player has favorable matchup (simplified)
            # In a real implementation, this would analyze opponent strength
            streaming_weeks.append(week)
        
        return streaming_weeks
    
    def _generate_waiver_rationale(
        self,
        free_agent: FreeAgent,
        target_type: WaiverTargetType,
        por: Decimal,
        weekly_upside: Decimal
    ) -> str:
        """Generate rationale text for a waiver recommendation."""
        rationale_parts = []
        
        # Value analysis
        if por > 30:
            rationale_parts.append(f"Exceptional value with {por:.1f} points over replacement")
        elif por > 15:
            rationale_parts.append(f"Strong value with {por:.1f} points over replacement")
        else:
            rationale_parts.append(f"Moderate value with {por:.1f} points over replacement")
        
        # Target type specific rationale
        if target_type == WaiverTargetType.STARTER_UPGRADE:
            rationale_parts.append("immediate starter potential")
        elif target_type == WaiverTargetType.HANDCUFF:
            rationale_parts.append("valuable handcuff insurance")
        elif target_type == WaiverTargetType.STREAMING:
            rationale_parts.append("good streaming option for favorable matchups")
        elif target_type == WaiverTargetType.INJURY_REPLACEMENT:
            rationale_parts.append("critical injury replacement need")
        elif target_type == WaiverTargetType.STASH:
            rationale_parts.append("potential breakout candidate")
        
        # Weekly upside
        if weekly_upside > 20:
            rationale_parts.append(f"high weekly ceiling of {weekly_upside:.1f} points")
        
        # Injury status
        if free_agent.injury_status != 'HEALTHY':
            rationale_parts.append(f"monitor {free_agent.injury_status.lower()} status")
        
        return ". ".join(rationale_parts).capitalize() + "."
    
    def _calculate_waiver_confidence(
        self,
        free_agent: FreeAgent,
        target_type: WaiverTargetType
    ) -> float:
        """Calculate confidence score for a waiver recommendation."""
        base_confidence = 0.6
        
        # Adjust based on projection variance
        variance = float(free_agent.metadata.get('projection_confidence', 0.1))
        confidence_adjustment = max(0, 0.3 - variance)
        
        # Adjust based on target type
        type_adjustments = {
            WaiverTargetType.STARTER_UPGRADE: 0.1,
            WaiverTargetType.INJURY_REPLACEMENT: 0.15,
            WaiverTargetType.HANDCUFF: 0.05,
            WaiverTargetType.STREAMING: -0.05,
            WaiverTargetType.STASH: -0.1
        }
        
        type_adjustment = type_adjustments.get(target_type, 0)
        
        # Adjust based on injury status
        injury_adjustment = 0 if free_agent.injury_status == 'HEALTHY' else -0.15
        
        confidence = base_confidence + confidence_adjustment + type_adjustment + injury_adjustment
        return max(0.1, min(1.0, confidence))
    
    def _priority_sort_key(self, priority: WaiverPriority) -> int:
        """Convert priority to sort key (lower = higher priority)."""
        priority_values = {
            WaiverPriority.HIGH: 1,
            WaiverPriority.MEDIUM: 2,
            WaiverPriority.LOW: 3
        }
        return priority_values[priority]
    
    def _create_empty_strategy(self, available_budget: Decimal) -> WaiverStrategy:
        """Create an empty waiver strategy when no targets are available."""
        return WaiverStrategy(
            strategy_name="No Targets Available",
            targets=[],
            total_faab_allocation=Decimal('0'),
            remaining_budget=available_budget,
            expected_value=Decimal('0'),
            risk_level="low",
            streaming_opportunities=[],
            trade_offs=["No viable waiver targets within budget constraints"],
            metadata={
                'generation_timestamp': datetime.utcnow().isoformat(),
                'reason': 'no_affordable_targets'
            }
        )
    
    def _generate_conservative_strategy(
        self,
        targets: List[WaiverTarget],
        budget: Decimal
    ) -> WaiverStrategy:
        """Generate a conservative waiver strategy focusing on high-priority targets."""
        selected_targets = []
        allocated_budget = Decimal('0')
        
        # Sort by priority and select high-priority targets first
        high_priority_targets = [t for t in targets if t.priority == WaiverPriority.HIGH]
        high_priority_targets.sort(key=lambda t: t.points_over_replacement, reverse=True)
        
        for target in high_priority_targets:
            if allocated_budget + target.recommended_bid <= budget:
                selected_targets.append(target)
                allocated_budget += target.recommended_bid
        
        # Add medium priority targets if budget allows
        if allocated_budget < budget * Decimal('0.8'):  # Use up to 80% of budget
            medium_priority_targets = [t for t in targets if t.priority == WaiverPriority.MEDIUM]
            medium_priority_targets.sort(key=lambda t: t.points_over_replacement, reverse=True)
            
            for target in medium_priority_targets:
                if allocated_budget + target.recommended_bid <= budget * Decimal('0.8'):
                    selected_targets.append(target)
                    allocated_budget += target.recommended_bid
        
        expected_value = sum(t.points_over_replacement for t in selected_targets)
        
        return WaiverStrategy(
            strategy_name="Conservative",
            targets=selected_targets,
            total_faab_allocation=allocated_budget,
            remaining_budget=budget - allocated_budget,
            expected_value=expected_value,
            risk_level="low",
            streaming_opportunities=[],
            trade_offs=self._analyze_strategy_trade_offs(selected_targets, targets),
            metadata={
                'strategy_type': 'conservative',
                'budget_utilization': float(allocated_budget / budget),
                'generation_timestamp': datetime.utcnow().isoformat()
            }
        )
    
    def _generate_aggressive_strategy(
        self,
        targets: List[WaiverTarget],
        budget: Decimal
    ) -> WaiverStrategy:
        """Generate an aggressive waiver strategy maximizing total value."""
        # Sort all targets by points over replacement
        sorted_targets = sorted(targets, key=lambda t: t.points_over_replacement, reverse=True)
        
        selected_targets = []
        allocated_budget = Decimal('0')
        
        for target in sorted_targets:
            if allocated_budget + target.max_bid <= budget:
                # Use max bid for aggressive strategy
                target_copy = target
                target_copy.recommended_bid = target.max_bid
                selected_targets.append(target_copy)
                allocated_budget += target.max_bid
        
        expected_value = sum(t.points_over_replacement for t in selected_targets)
        
        return WaiverStrategy(
            strategy_name="Aggressive",
            targets=selected_targets,
            total_faab_allocation=allocated_budget,
            remaining_budget=budget - allocated_budget,
            expected_value=expected_value,
            risk_level="high",
            streaming_opportunities=[],
            trade_offs=self._analyze_strategy_trade_offs(selected_targets, targets),
            metadata={
                'strategy_type': 'aggressive',
                'budget_utilization': float(allocated_budget / budget),
                'generation_timestamp': datetime.utcnow().isoformat()
            }
        )
    
    def _generate_balanced_strategy(
        self,
        targets: List[WaiverTarget],
        budget: Decimal
    ) -> WaiverStrategy:
        """Generate a balanced waiver strategy mixing priorities and value."""
        selected_targets = []
        allocated_budget = Decimal('0')
        
        # Allocate budget by priority tiers
        high_priority_budget = budget * Decimal('0.5')
        medium_priority_budget = budget * Decimal('0.3')
        low_priority_budget = budget * Decimal('0.2')
        
        # Select high priority targets
        high_targets = [t for t in targets if t.priority == WaiverPriority.HIGH]
        high_targets.sort(key=lambda t: t.points_over_replacement, reverse=True)
        
        for target in high_targets:
            if allocated_budget + target.recommended_bid <= high_priority_budget:
                selected_targets.append(target)
                allocated_budget += target.recommended_bid
        
        # Select medium priority targets
        medium_targets = [t for t in targets if t.priority == WaiverPriority.MEDIUM]
        medium_targets.sort(key=lambda t: t.points_over_replacement, reverse=True)
        
        for target in medium_targets:
            if allocated_budget + target.recommended_bid <= high_priority_budget + medium_priority_budget:
                selected_targets.append(target)
                allocated_budget += target.recommended_bid
        
        # Select low priority targets
        low_targets = [t for t in targets if t.priority == WaiverPriority.LOW]
        low_targets.sort(key=lambda t: t.points_over_replacement, reverse=True)
        
        for target in low_targets:
            if allocated_budget + target.recommended_bid <= budget:
                selected_targets.append(target)
                allocated_budget += target.recommended_bid
        
        expected_value = sum(t.points_over_replacement for t in selected_targets)
        
        return WaiverStrategy(
            strategy_name="Balanced",
            targets=selected_targets,
            total_faab_allocation=allocated_budget,
            remaining_budget=budget - allocated_budget,
            expected_value=expected_value,
            risk_level="medium",
            streaming_opportunities=[],
            trade_offs=self._analyze_strategy_trade_offs(selected_targets, targets),
            metadata={
                'strategy_type': 'balanced',
                'budget_utilization': float(allocated_budget / budget),
                'generation_timestamp': datetime.utcnow().isoformat()
            }
        )
    
    def _analyze_position_streaming(
        self,
        franchise: Franchise,
        position: PlayerPosition,
        current_week: int,
        weeks_ahead: int,
        season: int
    ) -> Optional[StreamingOpportunity]:
        """Analyze streaming opportunities for a specific position."""
        # Get available free agents at this position
        league_id = franchise.league_id
        free_agents = self.get_available_free_agents(league_id, season)
        position_agents = [fa for fa in free_agents if fa.position == position]
        
        if not position_agents:
            return None
        
        # Analyze current team strength at this position
        team_needs = self.analyze_team_needs(franchise.id, current_week, season)
        position_need = team_needs.get(position, {})
        
        # Only consider streaming if position need is low or medium
        if position_need.get('need_level') == 'critical':
            return None
        
        # Find streaming targets for upcoming weeks
        streaming_weeks = list(range(current_week, min(current_week + weeks_ahead, 18)))
        streaming_targets = []
        
        for agent in position_agents[:5]:  # Top 5 candidates
            if agent.projected_points > 8:  # Minimum threshold for streaming
                # Create waiver target for streaming
                target = WaiverTarget(
                    player_id=agent.player_id,
                    player_name=agent.player_name,
                    position=agent.position,
                    target_type=WaiverTargetType.STREAMING,
                    priority=WaiverPriority.LOW,
                    recommended_bid=Decimal('1'),  # Low bid for streaming
                    max_bid=Decimal('3'),
                    points_over_replacement=agent.points_over_replacement,
                    weekly_upside=self._calculate_weekly_upside(agent, current_week, season),
                    rationale=f"Streaming option for {position.value}",
                    drop_candidates=[],
                    streaming_weeks=streaming_weeks,
                    confidence=0.6,
                    metadata=agent.metadata
                )
                streaming_targets.append(target)
        
        if not streaming_targets:
            return None
        
        total_value = sum(t.points_over_replacement for t in streaming_targets)
        
        return StreamingOpportunity(
            position=position,
            weeks=streaming_weeks,
            targets=streaming_targets,
            total_value=total_value,
            strategy=f"Stream {position.value} based on matchups",
            confidence=0.7
        )
    
    def _analyze_strategy_trade_offs(
        self,
        selected_targets: List[WaiverTarget],
        all_targets: List[WaiverTarget]
    ) -> List[str]:
        """Analyze trade-offs made in strategy selection."""
        trade_offs = []
        
        selected_ids = {t.player_id for t in selected_targets}
        not_selected = [t for t in all_targets if t.player_id not in selected_ids]
        
        # Find high-value targets not selected
        high_value_not_selected = [
            t for t in not_selected 
            if t.points_over_replacement > 20
        ]
        
        for target in high_value_not_selected[:3]:  # Top 3 trade-offs
            trade_offs.append(
                f"Passed on {target.player_name} ({target.points_over_replacement:.1f} POR) "
                f"due to budget/priority constraints"
            )
        
        return trade_offs
    
    def _get_recent_performance(self, player_id: str, season: int) -> List[Decimal]:
        """Get recent weekly performance for a player."""
        # Get last 4 weeks of projections as proxy for performance
        recent_projections = []
        
        for week in range(max(1, 17 - 4), 17):  # Last 4 weeks
            proj = self.projections_aggregator.aggregate_projections(
                player_id=player_id,
                week=week,
                season=season
            )
            if proj:
                recent_projections.append(proj.projected_points)
        
        return recent_projections
    
    def _get_upcoming_matchups(self, team: str, season: int) -> List[str]:
        """Get upcoming matchups for a team."""
        # Simplified - would need actual schedule data
        return [f"vs TEAM{i}" for i in range(1, 5)]
    
    def _classify_free_agent_type(
        self,
        player: Player,
        projection: Any
    ) -> WaiverTargetType:
        """Classify the type of free agent based on characteristics."""
        # Simple classification based on projected points
        if projection.projected_points > 150:
            return WaiverTargetType.STARTER_UPGRADE
        elif projection.projected_points > 100:
            return WaiverTargetType.STREAMING
        else:
            return WaiverTargetType.STASH
    
    def _is_handcuff_target(self, free_agent: FreeAgent, franchise: Franchise) -> bool:
        """Check if free agent is a handcuff for current roster players."""
        # Simplified - would need actual depth chart data
        # For now, assume RBs with low projections might be handcuffs
        return (free_agent.position == PlayerPosition.RB and 
                free_agent.projected_points < 80)
    
    def _is_streaming_candidate(self, free_agent: FreeAgent) -> bool:
        """Check if free agent is a good streaming candidate."""
        streaming_positions = [PlayerPosition.QB, PlayerPosition.TE, PlayerPosition.DEF, PlayerPosition.K]
        return free_agent.position in streaming_positions
    
    def _calculate_drop_priority(
        self,
        roster_player: RosterPlayer,
        projected_points: Decimal,
        target_por: Decimal
    ) -> int:
        """Calculate drop priority (lower = more likely to drop)."""
        base_priority = 100
        
        # Adjust based on projected points (lower points = higher drop priority)
        points_adjustment = int(projected_points * -2)
        
        # Adjust based on position (some positions are harder to replace)
        position_adjustments = {
            PlayerPosition.QB: 10,
            PlayerPosition.RB: -5,
            PlayerPosition.WR: -5,
            PlayerPosition.TE: 5,
            PlayerPosition.K: -10,
            PlayerPosition.DEF: -10
        }
        
        position_adjustment = position_adjustments.get(roster_player.player.position, 0)
        
        # Adjust based on injury status
        injury_adjustment = 0
        if roster_player.player.injury_status.value in ['OUT', 'IR']:
            injury_adjustment = -20  # More likely to drop injured players
        
        priority = base_priority + points_adjustment + position_adjustment + injury_adjustment
        return max(1, priority)
    
    def _generate_drop_reason(
        self,
        roster_player: RosterPlayer,
        projected_points: Decimal,
        target_por: Decimal
    ) -> str:
        """Generate reason for dropping a player."""
        player = roster_player.player
        
        if projected_points < 50:
            return f"Low projected points ({projected_points:.1f})"
        elif player.injury_status.value in ['OUT', 'IR']:
            return f"Injured ({player.injury_status.value})"
        elif target_por > projected_points:
            return f"Target has higher upside ({target_por:.1f} vs {projected_points:.1f})"
        else:
            return "Roster optimization"
    
    def _get_position_roster_slots(self, league: League, position: PlayerPosition) -> int:
        """Get the number of starting roster slots for a position."""
        total_slots = 0
        
        for slot in league.roster_slots:
            slot_positions = slot.get('positions', [])
            if position.value in slot_positions:
                total_slots += slot.get('count', 1)
        
        return total_slots
    
    def refresh_replacement_levels_cache(self) -> None:
        """Clear the replacement levels cache to force recalculation."""
        self._replacement_levels_cache.clear()
        logger.info("Replacement levels cache cleared")