"""
MFL Data Ingestion Service

Handles integration with MyFantasyLeague (MFL) API using pymfl wrapper.
Provides data normalization and ETL pipeline for league data ingestion.
"""
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
from decimal import Decimal
import uuid

from pymfl import MFL
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from ..models.league import League
from ..models.player import Player, PlayerPosition, InjuryStatus
from ..models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RosterPlayer
from ..models.projection import Projection
from ..core.database import get_db

logger = logging.getLogger(__name__)


class MFLIngestionError(Exception):
    """Custom exception for MFL ingestion errors."""
    pass


class DataProvenance:
    """Tracks data provenance for ingested data."""
    
    def __init__(self, source: str, timestamp: datetime, metadata: Dict[str, Any] = None):
        self.source = source
        self.timestamp = timestamp
        self.metadata = metadata or {}
        self.id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "provenance_id": self.id,
            "source": self.source,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata
        }


class MFLDataNormalizer:
    """Normalizes MFL data to canonical schema."""
    
    # MFL position mapping to canonical positions
    POSITION_MAPPING = {
        "QB": PlayerPosition.QB,
        "RB": PlayerPosition.RB,
        "WR": PlayerPosition.WR,
        "TE": PlayerPosition.TE,
        "K": PlayerPosition.K,
        "Def": PlayerPosition.DEF,
        "DL": PlayerPosition.DL,
        "LB": PlayerPosition.LB,
        "DB": PlayerPosition.DB,
    }
    
    # MFL injury status mapping
    INJURY_MAPPING = {
        "": InjuryStatus.HEALTHY,
        "Q": InjuryStatus.QUESTIONABLE,
        "D": InjuryStatus.DOUBTFUL,
        "O": InjuryStatus.OUT,
        "IR": InjuryStatus.IR,
        "PUP": InjuryStatus.PUP,
        "SUSP": InjuryStatus.SUSPENDED,
    }
    
    @classmethod
    def normalize_league_data(cls, mfl_league: Dict[str, Any], provenance: DataProvenance) -> Dict[str, Any]:
        """Normalize MFL league data to canonical schema."""
        try:
            # Extract basic league information
            league_data = {
                "id": f"mfl_{mfl_league.get('id')}",
                "name": mfl_league.get("name", "Unknown League"),
                "season": int(mfl_league.get("season", datetime.now().year)),
                "mfl_league_id": mfl_league.get("id"),
                "description": mfl_league.get("description"),
                "is_active": True,
            }
            
            # Normalize scoring rules
            scoring_rules = cls._normalize_scoring_rules(mfl_league.get("scoring", {}))
            league_data["scoring_rules"] = scoring_rules
            
            # Normalize roster slots
            roster_slots = cls._normalize_roster_slots(mfl_league.get("rosterSize", {}))
            league_data["roster_slots"] = roster_slots
            
            # Normalize keeper rules if present
            if "keeperRules" in mfl_league:
                league_data["keeper_rules"] = cls._normalize_keeper_rules(mfl_league["keeperRules"])
            
            # Add provenance tracking
            league_data["metadata"] = {"provenance": provenance.to_dict()}
            
            return league_data
            
        except Exception as e:
            logger.error(f"Error normalizing league data: {e}")
            raise MFLIngestionError(f"Failed to normalize league data: {e}")
    
    @classmethod
    def normalize_player_data(cls, mfl_player: Dict[str, Any], provenance: DataProvenance) -> Dict[str, Any]:
        """Normalize MFL player data to canonical schema."""
        try:
            # Extract basic player information
            player_data = {
                "id": f"mfl_{mfl_player.get('id')}",
                "name": mfl_player.get("name", "Unknown Player"),
                "team": mfl_player.get("team", "FA"),
                "mfl_id": mfl_player.get("id"),
            }
            
            # Normalize position
            mfl_position = mfl_player.get("position", "")
            player_data["position"] = cls.POSITION_MAPPING.get(mfl_position, PlayerPosition.RB)
            
            # Normalize injury status
            injury_code = mfl_player.get("injury_status", "")
            player_data["injury_status"] = cls.INJURY_MAPPING.get(injury_code, InjuryStatus.HEALTHY)
            
            # Add bye week if available
            if "bye_week" in mfl_player:
                player_data["bye_week"] = int(mfl_player["bye_week"])
            
            # Add provenance and additional metadata
            metadata = {"provenance": provenance.to_dict()}
            if "jersey_number" in mfl_player:
                metadata["jersey_number"] = mfl_player["jersey_number"]
            if "height" in mfl_player:
                metadata["height"] = mfl_player["height"]
            if "weight" in mfl_player:
                metadata["weight"] = mfl_player["weight"]
            
            player_data["metadata"] = metadata
            
            return player_data
            
        except Exception as e:
            logger.error(f"Error normalizing player data: {e}")
            raise MFLIngestionError(f"Failed to normalize player data: {e}")
    
    @classmethod
    def normalize_franchise_data(cls, mfl_franchise: Dict[str, Any], league_id: str, provenance: DataProvenance) -> Dict[str, Any]:
        """Normalize MFL franchise data to canonical schema."""
        try:
            franchise_data = {
                "id": f"mfl_{league_id}_{mfl_franchise.get('id')}",
                "name": mfl_franchise.get("name", "Unknown Team"),
                "owner_name": mfl_franchise.get("owner_name", "Unknown Owner"),
                "league_id": league_id,
                "mfl_franchise_id": mfl_franchise.get("id"),
                "is_active": True,
            }
            
            # Add financial information if available
            if "salary_cap" in mfl_franchise:
                franchise_data["salary_cap"] = Decimal(str(mfl_franchise["salary_cap"]))
            
            if "faab_budget" in mfl_franchise:
                franchise_data["faab_budget"] = Decimal(str(mfl_franchise["faab_budget"]))
                franchise_data["faab_spent"] = Decimal(str(mfl_franchise.get("faab_spent", 0)))
            
            # Add provenance tracking
            franchise_data["metadata"] = {"provenance": provenance.to_dict()}
            
            return franchise_data
            
        except Exception as e:
            logger.error(f"Error normalizing franchise data: {e}")
            raise MFLIngestionError(f"Failed to normalize franchise data: {e}")
    
    @classmethod
    def normalize_roster_data(cls, mfl_roster: Dict[str, Any], franchise_id: str, provenance: DataProvenance) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """Normalize MFL roster data to canonical schema."""
        try:
            # Create roster record
            roster_data = {
                "id": f"roster_{franchise_id}",
                "franchise_id": franchise_id,
                "is_valid": True,
            }
            
            # Process roster players
            roster_players = []
            for mfl_player in mfl_roster.get("players", []):
                player_data = {
                    "id": f"rp_{franchise_id}_{mfl_player.get('id')}",
                    "roster_id": roster_data["id"],
                    "player_id": f"mfl_{mfl_player.get('id')}",
                    "roster_slot": mfl_player.get("status", "BENCH"),
                    "is_active": True,
                }
                
                # Add salary information if available
                if "salary" in mfl_player:
                    player_data["salary"] = Decimal(str(mfl_player["salary"]))
                
                # Add keeper information
                if mfl_player.get("is_keeper"):
                    player_data["is_keeper"] = True
                    if "keeper_cost" in mfl_player:
                        player_data["keeper_cost"] = int(mfl_player["keeper_cost"])
                
                # Add provenance tracking
                player_data["metadata"] = {"provenance": provenance.to_dict()}
                
                roster_players.append(player_data)
            
            return roster_data, roster_players
            
        except Exception as e:
            logger.error(f"Error normalizing roster data: {e}")
            raise MFLIngestionError(f"Failed to normalize roster data: {e}")
    
    @classmethod
    def _normalize_scoring_rules(cls, mfl_scoring: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize MFL scoring rules to canonical format."""
        # Default scoring rules
        scoring_rules = {
            "passing_yards": 0.04,  # 1 point per 25 yards
            "passing_touchdowns": 4.0,
            "passing_interceptions": -2.0,
            "rushing_yards": 0.1,  # 1 point per 10 yards
            "rushing_touchdowns": 6.0,
            "receiving_yards": 0.1,  # 1 point per 10 yards
            "receiving_touchdowns": 6.0,
            "receptions": 0.0,  # Standard scoring, not PPR
            "fumbles_lost": -2.0,
            "kicking_extra_points": 1.0,
            "kicking_field_goals": 3.0,
            "defense_touchdowns": 6.0,
            "defense_interceptions": 2.0,
            "defense_fumble_recoveries": 2.0,
            "defense_sacks": 1.0,
            "defense_safeties": 2.0,
        }
        
        # Override with MFL-specific rules if available
        if mfl_scoring:
            # Map MFL scoring categories to canonical names
            mfl_mapping = {
                "pass_yd": "passing_yards",
                "pass_td": "passing_touchdowns",
                "pass_int": "passing_interceptions",
                "rush_yd": "rushing_yards",
                "rush_td": "rushing_touchdowns",
                "rec_yd": "receiving_yards",
                "rec_td": "receiving_touchdowns",
                "rec": "receptions",
                "fumble": "fumbles_lost",
            }
            
            for mfl_key, canonical_key in mfl_mapping.items():
                if mfl_key in mfl_scoring:
                    scoring_rules[canonical_key] = float(mfl_scoring[mfl_key])
        
        return scoring_rules
    
    @classmethod
    def _normalize_roster_slots(cls, mfl_roster_size: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Normalize MFL roster slots to canonical format."""
        # Default roster configuration
        default_slots = [
            {"position": "QB", "count": 1, "is_starting": True},
            {"position": "RB", "count": 2, "is_starting": True},
            {"position": "WR", "count": 2, "is_starting": True},
            {"position": "TE", "count": 1, "is_starting": True},
            {"position": "FLEX", "count": 1, "is_starting": True, "eligible_positions": ["RB", "WR", "TE"]},
            {"position": "K", "count": 1, "is_starting": True},
            {"position": "DEF", "count": 1, "is_starting": True},
            {"position": "BENCH", "count": 6, "is_starting": False},
        ]
        
        # Override with MFL configuration if available
        if mfl_roster_size:
            slots = []
            for position, count in mfl_roster_size.items():
                slots.append({
                    "position": position.upper(),
                    "count": int(count),
                    "is_starting": position.upper() != "BENCH"
                })
            return slots
        
        return default_slots
    
    @classmethod
    def _normalize_keeper_rules(cls, mfl_keeper_rules: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize MFL keeper rules to canonical format."""
        return {
            "max_keepers": int(mfl_keeper_rules.get("max_keepers", 0)),
            "keeper_deadline": mfl_keeper_rules.get("deadline"),
            "round_escalation": mfl_keeper_rules.get("round_escalation", False),
            "franchise_tags": int(mfl_keeper_rules.get("franchise_tags", 0)),
            "rules": mfl_keeper_rules
        }


class MFLIngestionService:
    """Main service for ingesting data from MFL API."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.normalizer = MFLDataNormalizer()
    
    def ingest_league_data(self, league_id: str, api_key: str) -> Dict[str, Any]:
        """
        Ingest complete league data from MFL API.
        
        Args:
            league_id: MFL league identifier
            api_key: MFL API key for authentication
            
        Returns:
            Dictionary with ingestion results and statistics
        """
        try:
            logger.info(f"Starting MFL data ingestion for league {league_id}")
            
            # Initialize MFL client
            mfl_client = MFL(league_id=league_id, api_key=api_key)
            
            # Create provenance record
            provenance = DataProvenance(
                source=f"mfl_api_{league_id}",
                timestamp=datetime.now(timezone.utc),
                metadata={"league_id": league_id, "api_version": "2024"}
            )
            
            results = {
                "league_id": league_id,
                "ingestion_timestamp": provenance.timestamp,
                "leagues_processed": 0,
                "players_processed": 0,
                "franchises_processed": 0,
                "rosters_processed": 0,
                "errors": []
            }
            
            # Ingest league configuration
            try:
                league_data = self._ingest_league_config(mfl_client, provenance)
                results["leagues_processed"] = 1
                logger.info(f"Successfully ingested league configuration")
            except Exception as e:
                error_msg = f"Failed to ingest league configuration: {e}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
                return results
            
            # Ingest player data
            try:
                player_count = self._ingest_players(mfl_client, provenance)
                results["players_processed"] = player_count
                logger.info(f"Successfully ingested {player_count} players")
            except Exception as e:
                error_msg = f"Failed to ingest players: {e}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
            
            # Ingest franchise and roster data
            try:
                franchise_count, roster_count = self._ingest_franchises_and_rosters(
                    mfl_client, league_data["id"], provenance
                )
                results["franchises_processed"] = franchise_count
                results["rosters_processed"] = roster_count
                logger.info(f"Successfully ingested {franchise_count} franchises and {roster_count} rosters")
            except Exception as e:
                error_msg = f"Failed to ingest franchises/rosters: {e}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
            
            # Commit all changes
            self.db.commit()
            logger.info(f"MFL data ingestion completed for league {league_id}")
            
            return results
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"MFL ingestion failed for league {league_id}: {e}")
            raise MFLIngestionError(f"MFL ingestion failed: {e}")
    
    def _ingest_league_config(self, mfl_client: MFL, provenance: DataProvenance) -> Dict[str, Any]:
        """Ingest league configuration data."""
        # Fetch league data from MFL API
        mfl_league_data = mfl_client.get_league()
        
        # Normalize to canonical schema
        league_data = self.normalizer.normalize_league_data(mfl_league_data, provenance)
        
        # Create or update league record
        league = self.db.query(League).filter(League.id == league_data["id"]).first()
        
        if league:
            # Update existing league
            for key, value in league_data.items():
                if key != "id":  # Don't update primary key
                    setattr(league, key, value)
            logger.info(f"Updated existing league: {league.id}")
        else:
            # Create new league
            league = League(**league_data)
            self.db.add(league)
            logger.info(f"Created new league: {league.id}")
        
        return league_data
    
    def _ingest_players(self, mfl_client: MFL, provenance: DataProvenance) -> int:
        """Ingest player data from MFL API."""
        # Fetch players from MFL API
        mfl_players = mfl_client.get_players()
        
        player_count = 0
        for mfl_player in mfl_players:
            try:
                # Normalize player data
                player_data = self.normalizer.normalize_player_data(mfl_player, provenance)
                
                # Create or update player record
                player = self.db.query(Player).filter(Player.id == player_data["id"]).first()
                
                if player:
                    # Update existing player
                    for key, value in player_data.items():
                        if key != "id":
                            setattr(player, key, value)
                else:
                    # Create new player
                    player = Player(**player_data)
                    self.db.add(player)
                
                player_count += 1
                
            except Exception as e:
                logger.warning(f"Failed to process player {mfl_player.get('id', 'unknown')}: {e}")
                continue
        
        return player_count
    
    def _ingest_franchises_and_rosters(self, mfl_client: MFL, league_id: str, provenance: DataProvenance) -> Tuple[int, int]:
        """Ingest franchise and roster data from MFL API."""
        # Fetch franchises from MFL API
        mfl_franchises = mfl_client.get_franchises()
        
        franchise_count = 0
        roster_count = 0
        
        for mfl_franchise in mfl_franchises:
            try:
                # Normalize franchise data
                franchise_data = self.normalizer.normalize_franchise_data(mfl_franchise, league_id, provenance)
                
                # Create or update franchise record
                franchise = self.db.query(Franchise).filter(Franchise.id == franchise_data["id"]).first()
                
                if franchise:
                    # Update existing franchise
                    for key, value in franchise_data.items():
                        if key != "id":
                            setattr(franchise, key, value)
                else:
                    # Create new franchise
                    franchise = Franchise(**franchise_data)
                    self.db.add(franchise)
                
                franchise_count += 1
                
                # Ingest roster for this franchise
                try:
                    mfl_roster = mfl_client.get_roster(mfl_franchise["id"])
                    if mfl_roster:
                        roster_data, roster_players = self.normalizer.normalize_roster_data(
                            mfl_roster, franchise_data["id"], provenance
                        )
                        
                        # Create or update roster
                        roster = self.db.query(Roster).filter(Roster.id == roster_data["id"]).first()
                        
                        if roster:
                            # Update existing roster
                            for key, value in roster_data.items():
                                if key != "id":
                                    setattr(roster, key, value)
                            
                            # Clear existing roster players
                            for rp in roster.roster_players:
                                self.db.delete(rp)
                        else:
                            # Create new roster
                            roster = Roster(**roster_data)
                            self.db.add(roster)
                        
                        # Add roster players
                        for rp_data in roster_players:
                            roster_player = RosterPlayer(**rp_data)
                            self.db.add(roster_player)
                        
                        roster_count += 1
                        
                except Exception as e:
                    logger.warning(f"Failed to process roster for franchise {mfl_franchise.get('id', 'unknown')}: {e}")
                    continue
                
            except Exception as e:
                logger.warning(f"Failed to process franchise {mfl_franchise.get('id', 'unknown')}: {e}")
                continue
        
        return franchise_count, roster_count
    
    def validate_ingested_data(self, league_id: str) -> Dict[str, Any]:
        """Validate the integrity of ingested data."""
        try:
            validation_results = {
                "league_id": league_id,
                "validation_timestamp": datetime.now(timezone.utc),
                "is_valid": True,
                "issues": []
            }
            
            # Check if league exists
            league = self.db.query(League).filter(League.id == league_id).first()
            if not league:
                validation_results["is_valid"] = False
                validation_results["issues"].append("League not found")
                return validation_results
            
            # Check franchises
            franchises = self.db.query(Franchise).filter(Franchise.league_id == league_id).all()
            if not franchises:
                validation_results["issues"].append("No franchises found")
            
            # Check players
            player_count = self.db.query(Player).count()
            if player_count == 0:
                validation_results["issues"].append("No players found")
            
            # Check rosters
            for franchise in franchises:
                if not franchise.roster:
                    validation_results["issues"].append(f"No roster found for franchise {franchise.id}")
                elif not franchise.roster.roster_players:
                    validation_results["issues"].append(f"Empty roster for franchise {franchise.id}")
            
            # Check data consistency
            orphaned_roster_players = self.db.query(RosterPlayer).join(Player, isouter=True).filter(Player.id.is_(None)).count()
            if orphaned_roster_players > 0:
                validation_results["issues"].append(f"{orphaned_roster_players} roster players reference non-existent players")
            
            if validation_results["issues"]:
                validation_results["is_valid"] = False
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Data validation failed: {e}")
            return {
                "league_id": league_id,
                "validation_timestamp": datetime.now(timezone.utc),
                "is_valid": False,
                "issues": [f"Validation error: {e}"]
            }


def create_mfl_ingestion_service(db_session: Session = None) -> MFLIngestionService:
    """Factory function to create MFL ingestion service."""
    if db_session is None:
        db_session = next(get_db())
    return MFLIngestionService(db_session)