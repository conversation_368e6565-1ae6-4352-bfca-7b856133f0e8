"""
MFL Data Ingestion Service

Handles integration with MyFantasyLeague (MFL) API using pymfl wrapper.
Provides data normalization and ETL pipeline for league data ingestion.
"""
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
import os
from decimal import Decimal
import uuid

try:
    # pymfl 1.x exposes client classes under pymfl.api
    from pymfl.api import MFLAPIClient as MFL  # type: ignore
except Exception:
    MFL = None  # Will be checked at runtime to avoid hard dependency at import time
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from ..models.league import League
from ..models.player import Player, PlayerPosition, InjuryStatus
from ..models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RosterPlayer
from ..models.projection import Projection
from ..core.database import get_db
import httpx

logger = logging.getLogger(__name__)


class MFLIngestionError(Exception):
    """Custom exception for MFL ingestion errors."""
    pass


class DataProvenance:
    """Tracks data provenance for ingested data."""
    
    def __init__(self, source: str, timestamp: datetime, metadata: Dict[str, Any] = None):
        self.source = source
        self.timestamp = timestamp
        self.metadata = metadata or {}
        self.id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "provenance_id": self.id,
            "source": self.source,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata
        }


class MFLDataNormalizer:
    """Normalizes MFL data to canonical schema."""
    
    # MFL position mapping to canonical positions
    POSITION_MAPPING = {
        "QB": PlayerPosition.QB,
        "RB": PlayerPosition.RB,
        "WR": PlayerPosition.WR,
        "TE": PlayerPosition.TE,
        "K": PlayerPosition.K,
        "Def": PlayerPosition.DEF,
        "DL": PlayerPosition.DL,
        "LB": PlayerPosition.LB,
        "DB": PlayerPosition.DB,
    }
    
    # MFL injury status mapping
    INJURY_MAPPING = {
        "": InjuryStatus.HEALTHY,
        "Q": InjuryStatus.QUESTIONABLE,
        "D": InjuryStatus.DOUBTFUL,
        "O": InjuryStatus.OUT,
        "IR": InjuryStatus.IR,
        "PUP": InjuryStatus.PUP,
        "SUSP": InjuryStatus.SUSPENDED,
    }
    
    @classmethod
    def normalize_league_data(cls, mfl_league: Dict[str, Any], provenance: DataProvenance) -> Dict[str, Any]:
        """Normalize MFL league data to canonical schema."""
        try:
            # Some pymfl responses wrap payload under 'league'
            payload = mfl_league.get('league') if isinstance(mfl_league, dict) and 'league' in mfl_league else mfl_league
            # Extract basic league information
            league_id_raw = payload.get('id') if isinstance(payload, dict) else None
            season_val = payload.get('season') or payload.get('year') if isinstance(payload, dict) else None
            try:
                season_int = int(season_val) if season_val is not None else datetime.now().year
            except Exception:
                season_int = datetime.now().year
            league_data = {
                "id": f"mfl_{league_id_raw}",
                "name": (payload.get("name") if isinstance(payload, dict) else None) or "Unknown League",
                "season": season_int,
                "mfl_league_id": league_id_raw,
                "description": (payload.get("description") if isinstance(payload, dict) else None),
                "is_active": True,
            }
            
            # Normalize scoring rules
            scoring_rules = cls._normalize_scoring_rules(mfl_league.get("scoring", {}))
            league_data["scoring_rules"] = scoring_rules
            
            # Normalize roster slots
            roster_slots = cls._normalize_roster_slots(mfl_league.get("rosterSize", {}))
            league_data["roster_slots"] = roster_slots
            
            # Normalize keeper rules if present
            if "keeperRules" in mfl_league:
                league_data["keeper_rules"] = cls._normalize_keeper_rules(mfl_league["keeperRules"])
            
            # Add provenance tracking
            league_data["metadata"] = {"provenance": provenance.to_dict()}
            
            return league_data
            
        except Exception as e:
            logger.error(f"Error normalizing league data: {e}")
            raise MFLIngestionError(f"Failed to normalize league data: {e}")
    
    @classmethod
    def normalize_player_data(cls, mfl_player: Dict[str, Any], provenance: DataProvenance) -> Dict[str, Any]:
        """Normalize MFL player data to canonical schema."""
        try:
            # Extract basic player information
            player_data = {
                "id": f"mfl_{mfl_player.get('id')}",
                "name": mfl_player.get("name", "Unknown Player"),
                "team": mfl_player.get("team", "FA"),
                "mfl_id": mfl_player.get("id"),
            }
            
            # Normalize position
            mfl_position = mfl_player.get("position", "")
            player_data["position"] = cls.POSITION_MAPPING.get(mfl_position, PlayerPosition.RB)
            
            # Normalize injury status
            injury_code = mfl_player.get("injury_status", "")
            player_data["injury_status"] = cls.INJURY_MAPPING.get(injury_code, InjuryStatus.HEALTHY)
            
            # Add bye week if available
            if "bye_week" in mfl_player:
                player_data["bye_week"] = int(mfl_player["bye_week"])
            
            # Add provenance and additional metadata
            metadata = {"provenance": provenance.to_dict()}
            if "jersey_number" in mfl_player:
                metadata["jersey_number"] = mfl_player["jersey_number"]
            if "height" in mfl_player:
                metadata["height"] = mfl_player["height"]
            if "weight" in mfl_player:
                metadata["weight"] = mfl_player["weight"]
            
            player_data["metadata"] = metadata
            
            return player_data
            
        except Exception as e:
            logger.error(f"Error normalizing player data: {e}")
            raise MFLIngestionError(f"Failed to normalize player data: {e}")
    
    @classmethod
    def normalize_franchise_data(cls, mfl_franchise: Dict[str, Any], league_id: str, provenance: DataProvenance) -> Dict[str, Any]:
        """Normalize MFL franchise data to canonical schema."""
        try:
            franchise_data = {
                "id": f"mfl_{league_id}_{mfl_franchise.get('id')}",
                "name": mfl_franchise.get("name", "Unknown Team"),
                "owner_name": mfl_franchise.get("owner_name", "Unknown Owner"),
                "league_id": league_id,
                "mfl_franchise_id": mfl_franchise.get("id"),
                "is_active": True,
            }
            
            # Add financial information if available
            if "salary_cap" in mfl_franchise:
                franchise_data["salary_cap"] = Decimal(str(mfl_franchise["salary_cap"]))
            
            if "faab_budget" in mfl_franchise:
                franchise_data["faab_budget"] = Decimal(str(mfl_franchise["faab_budget"]))
                franchise_data["faab_spent"] = Decimal(str(mfl_franchise.get("faab_spent", 0)))
            
            # Add provenance tracking
            franchise_data["metadata"] = {"provenance": provenance.to_dict()}
            
            return franchise_data
            
        except Exception as e:
            logger.error(f"Error normalizing franchise data: {e}")
            raise MFLIngestionError(f"Failed to normalize franchise data: {e}")
    
    @classmethod
    def normalize_roster_data(cls, mfl_roster: Dict[str, Any], franchise_id: str, provenance: DataProvenance) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """Normalize MFL roster data to canonical schema."""
        try:
            # Create roster record
            roster_data = {
                "id": f"roster_{franchise_id}",
                "franchise_id": franchise_id,
                "is_valid": True,
            }
            
            # Helpers for safe parsing
            def _to_decimal(val):
                try:
                    if val is None:
                        return None
                    s = str(val).strip()
                    if not s or s.upper() == "NA":
                        return None
                    return Decimal(s)
                except Exception:
                    return None
            def _to_int(val):
                try:
                    if val is None:
                        return None
                    s = str(val).strip()
                    # Extract leading digits only
                    digits = "".join(ch for ch in s if ch.isdigit())
                    if not digits:
                        return None
                    return int(digits)
                except Exception:
                    return None
            
            # Process roster players
            roster_players = []
            for mfl_player in mfl_roster.get("players", []):
                player_data = {
                    "id": f"rp_{franchise_id}_{mfl_player.get('id')}",
                    "roster_id": roster_data["id"],
                    "player_id": f"mfl_{mfl_player.get('id')}",
                    "roster_slot": mfl_player.get("status", "BENCH"),
                    "is_active": True,
                }
                
                # Add salary information if available
                salary_val = mfl_player.get("salary")
                parsed_salary = _to_decimal(salary_val)
                if parsed_salary is not None:
                    player_data["salary"] = parsed_salary
                
                # Add keeper information
                if mfl_player.get("is_keeper"):
                    player_data["is_keeper"] = True
                    keeper_cost_val = _to_int(mfl_player.get("keeper_cost"))
                    if keeper_cost_val is not None:
                        player_data["keeper_cost"] = keeper_cost_val
                
                # Add provenance tracking
                player_data["metadata"] = {"provenance": provenance.to_dict()}
                
                roster_players.append(player_data)
            
            return roster_data, roster_players
            
        except Exception as e:
            logger.error(f"Error normalizing roster data: {e}")
            raise MFLIngestionError(f"Failed to normalize roster data: {e}")
    
    @classmethod
    def _normalize_scoring_rules(cls, mfl_scoring: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize MFL scoring rules to canonical format."""
        # Default scoring rules
        scoring_rules = {
            "passing_yards": 0.04,  # 1 point per 25 yards
            "passing_touchdowns": 4.0,
            "passing_interceptions": -2.0,
            "rushing_yards": 0.1,  # 1 point per 10 yards
            "rushing_touchdowns": 6.0,
            "receiving_yards": 0.1,  # 1 point per 10 yards
            "receiving_touchdowns": 6.0,
            "receptions": 0.0,  # Standard scoring, not PPR
            "fumbles_lost": -2.0,
            "kicking_extra_points": 1.0,
            "kicking_field_goals": 3.0,
            "defense_touchdowns": 6.0,
            "defense_interceptions": 2.0,
            "defense_fumble_recoveries": 2.0,
            "defense_sacks": 1.0,
            "defense_safeties": 2.0,
        }
        
        # Override with MFL-specific rules if available
        if mfl_scoring:
            # Map MFL scoring categories to canonical names
            mfl_mapping = {
                "pass_yd": "passing_yards",
                "pass_td": "passing_touchdowns",
                "pass_int": "passing_interceptions",
                "rush_yd": "rushing_yards",
                "rush_td": "rushing_touchdowns",
                "rec_yd": "receiving_yards",
                "rec_td": "receiving_touchdowns",
                "rec": "receptions",
                "fumble": "fumbles_lost",
            }
            
            for mfl_key, canonical_key in mfl_mapping.items():
                if mfl_key in mfl_scoring:
                    scoring_rules[canonical_key] = float(mfl_scoring[mfl_key])
        
        return scoring_rules
    
    @classmethod
    def _normalize_roster_slots(cls, mfl_roster_size: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Normalize MFL roster slots to canonical format."""
        # Default roster configuration
        default_slots = [
            {"position": "QB", "count": 1, "is_starting": True},
            {"position": "RB", "count": 2, "is_starting": True},
            {"position": "WR", "count": 2, "is_starting": True},
            {"position": "TE", "count": 1, "is_starting": True},
            {"position": "FLEX", "count": 1, "is_starting": True, "eligible_positions": ["RB", "WR", "TE"]},
            {"position": "K", "count": 1, "is_starting": True},
            {"position": "DEF", "count": 1, "is_starting": True},
            {"position": "BENCH", "count": 6, "is_starting": False},
        ]
        
        # Override with MFL configuration if available
        if mfl_roster_size:
            slots = []
            for position, count in mfl_roster_size.items():
                slots.append({
                    "position": position.upper(),
                    "count": int(count),
                    "is_starting": position.upper() != "BENCH"
                })
            return slots
        
        return default_slots
    
    @classmethod
    def _normalize_keeper_rules(cls, mfl_keeper_rules: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize MFL keeper rules to canonical format."""
        return {
            "max_keepers": int(mfl_keeper_rules.get("max_keepers", 0)),
            "keeper_deadline": mfl_keeper_rules.get("deadline"),
            "round_escalation": mfl_keeper_rules.get("round_escalation", False),
            "franchise_tags": int(mfl_keeper_rules.get("franchise_tags", 0)),
            "rules": mfl_keeper_rules
        }


class MFLIngestionService:
    """Main service for ingesting data from MFL API."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.normalizer = MFLDataNormalizer()
    
    def ingest_league_data(self, league_id: str, api_key: str, username: Optional[str] = None, password: Optional[str] = None, user_agent: Optional[str] = None) -> Dict[str, Any]:
        """
        Ingest complete league data from MFL API.
        
        Args:
            league_id: MFL league identifier
            api_key: MFL API key for authentication
            
        Returns:
            Dictionary with ingestion results and statistics
        """
        try:
            logger.info(f"Starting MFL data ingestion for league {league_id}")
            
            # Initialize MFL client
            if MFL is None:
                raise MFLIngestionError("pymfl is not installed on the backend. Please add 'pymfl' to backend/requirements.txt and rebuild, or run via the jobs trigger if available.")
            # pymfl 1.x MFLAPIClient acts as a namespace; use specific API clients
            # Session/API key management varies by version, so we pass league_id/api_key to fetchers directly where needed
            from pymfl.api import FantasyContentAPIClient, CommonLeagueInfoAPIClient, OtherLeagueInfoAPIClient
            from pymfl.api.config.APIConfig import APIConfig
            league_info = CommonLeagueInfoAPIClient()
            fantasy_api = FantasyContentAPIClient()
            other_api = OtherLeagueInfoAPIClient()
            # Determine year to query (prefer current UTC year)
            year = datetime.now(timezone.utc).year

            # Configure pymfl APIConfig (requires MFL credentials to obtain mfl_user_id)
            cfg_username = username or os.getenv("MFL_USERNAME") or os.getenv("MFL_USER")
            cfg_password = password or os.getenv("MFL_PASSWORD")
            cfg_user_agent = user_agent or os.getenv("MFL_USER_AGENT") or "ai-fantasy-assistant/1.0"
            if not cfg_username or not cfg_password:
                raise MFLIngestionError("Missing MFL credentials. Provide username/password in request or set MFL_USERNAME and MFL_PASSWORD env vars.")
            try:
                APIConfig.add_config_for_year_and_league_id(year=year, league_id=league_id, username=cfg_username, password=cfg_password, user_agent_name=cfg_user_agent)
            except Exception as e:
                raise MFLIngestionError(f"Failed to configure pymfl API: {e}")
            
            # Create provenance record
            provenance = DataProvenance(
                source=f"mfl_api_{league_id}",
                timestamp=datetime.now(timezone.utc),
                metadata={"league_id": league_id, "api_version": "2024"}
            )
            
            results = {
                "league_id": league_id,
                "ingestion_timestamp": provenance.timestamp.isoformat(),
                "leagues_processed": 0,
                "players_processed": 0,
                "franchises_processed": 0,
                "rosters_processed": 0,
                "errors": []
            }
            
            # Ingest league configuration
            try:
                league_data = self._ingest_league_config(league_info, league_id, year, provenance)
                results["leagues_processed"] = 1
                logger.info(f"Successfully ingested league configuration")
            except Exception as e:
                error_msg = f"Failed to ingest league configuration: {e}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
                return results
            
            # Ingest player data
            try:
                player_count = self._ingest_players(fantasy_api, league_id, year, provenance)
                results["players_processed"] = player_count
                logger.info(f"Successfully ingested {player_count} players")
            except Exception as e:
                error_msg = f"Failed to ingest players: {e}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
            
            # Ingest franchise and roster data
            try:
                franchise_count, roster_count = self._ingest_franchises_and_rosters(
                    other_api, league_data["id"], league_id, year, provenance
                )
                results["franchises_processed"] = franchise_count
                results["rosters_processed"] = roster_count
                logger.info(f"Successfully ingested {franchise_count} franchises and {roster_count} rosters")
            except Exception as e:
                error_msg = f"Failed to ingest franchises/rosters: {e}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
            
            # Commit all changes
            self.db.commit()
            logger.info(f"MFL data ingestion completed for league {league_id}")
            
            return results
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"MFL ingestion failed for league {league_id}: {e}")
            raise MFLIngestionError(f"MFL ingestion failed: {e}")
    
    def _ingest_league_config(self, league_info_client, league_id: str, year: int, provenance: DataProvenance) -> Dict[str, Any]:
        """Ingest league configuration data using pymfl CommonLeagueInfoAPIClient."""
        # Fetch league data from MFL API via pymfl
        try:
            mfl_league_data = league_info_client.get_league(year=year, league_id=league_id)
        except Exception as e:
            logger.warning(f"pymfl get_league failed, falling back to export proxy: {e}")
            mfl_league_data = None
        # Fallback to internal MFL proxy export if needed
        if not isinstance(mfl_league_data, dict) or (isinstance(mfl_league_data, dict) and not mfl_league_data):
            export = self._mfl_export("league", league_id=league_id, year=year)
            mfl_league_data = export
        
        # Normalize to canonical schema
        league_data = self.normalizer.normalize_league_data(mfl_league_data, provenance)
        # Ensure league identifiers are populated
        if not league_data.get("mfl_league_id"):
            league_data["mfl_league_id"] = league_id
        if not league_data.get("id") or league_data.get("id") == "mfl_None":
            league_data["id"] = f"mfl_{league_id}"
        
        # Create or update league record
        league = self.db.query(League).filter(League.id == league_data["id"]).first()
        
        if league:
            # Update existing league
            for key, value in league_data.items():
                if key != "id":  # Don't update primary key
                    setattr(league, key, value)
            logger.info(f"Updated existing league: {league.id}")
        else:
            # Create new league
            league = League(**league_data)
            self.db.add(league)
            logger.info(f"Created new league: {league.id}")
        
        return league_data
    
    def _ingest_players(self, fantasy_api_client, league_id: str, year: int, provenance: DataProvenance) -> int:
        """Ingest player data from MFL API."""
        # Fetch players from MFL API
        try:
            players_resp = fantasy_api_client.get_players(year=year, league_id=league_id) or {}
        except Exception as e:
            logger.warning(f"pymfl get_players failed, falling back to export proxy: {e}")
            players_resp = {}
        mfl_players: List[Dict[str, Any]] = []
        if isinstance(players_resp, dict):
            # Try common shapes: {'players': {'player': [...]}} or {'league': {'players': {'player': [...]}}}
            container = players_resp.get('players') or (players_resp.get('league', {}).get('players') if isinstance(players_resp.get('league'), dict) else None)
            if isinstance(container, dict) and isinstance(container.get('player'), list):
                mfl_players = container['player']
            elif isinstance(container, list):
                mfl_players = container
        if not mfl_players:
            export = self._mfl_export("players", league_id=league_id, year=year)
            if isinstance(export, dict):
                players_wrap = export.get('players')
                if isinstance(players_wrap, dict) and isinstance(players_wrap.get('player'), list):
                    mfl_players = players_wrap['player']
        # Filter to only relevant player positions and dict entries
        allowed_positions = {"QB", "RB", "WR", "TE", "K", "Def", "DEF"}
        mfl_players = [p for p in mfl_players if isinstance(p, dict) and str(p.get('position', '')).upper() in {pos.upper() for pos in allowed_positions}]
        
        player_count = 0
        for mfl_player in mfl_players:
            try:
                # Normalize player data
                player_data = self.normalizer.normalize_player_data(mfl_player, provenance)
                
                # Create or update player record
                player = self.db.query(Player).filter(Player.id == player_data["id"]).first()
                
                if player:
                    # Update existing player
                    for key, value in player_data.items():
                        if key != "id":
                            setattr(player, key, value)
                else:
                    # Create new player
                    player = Player(**player_data)
                    self.db.add(player)
                
                player_count += 1
                
            except Exception as e:
                logger.warning(f"Failed to process player {mfl_player.get('id', 'unknown')}: {e}")
                continue
        
        return player_count
    
    def _ingest_franchises_and_rosters(self, other_api_client, league_canonical_id: str, league_mfl_id: str, year: int, provenance: DataProvenance) -> Tuple[int, int]:
        """Ingest franchise and roster data from MFL API."""
        # Fetch franchises from MFL API
        # Franchises list isnt exposed in OtherLeagueInfoAPIClient 1.0.2; fetch via league and pull franchises
        try:
            league_data = other_api_client.get_appearance(year=year, league_id=league_mfl_id) if hasattr(other_api_client, 'get_appearance') else {}
        except Exception as e:
            logger.warning(f"pymfl get_appearance failed: {e}")
            league_data = {}
        # Fallback: use CommonLeagueInfoAPIClient.get_league for structured league metadata, or proxy export
        try:
            from pymfl.api import CommonLeagueInfoAPIClient as _CL
            _cli = _CL()
            league_info = _cli.get_league(year=year, league_id=league_mfl_id) or {}
        except Exception:
            league_info = {}
        if not isinstance(league_info, dict) or not league_info:
            export = self._mfl_export("league", league_id=league_mfl_id, year=year)
            league_info = export
        # Normalize franchises from league_info if available
        mfl_franchises = []
        if isinstance(league_info, dict):
            # Typical structure: { 'league': { 'franchises': { 'franchise': [ { 'id': '0001', 'name': 'Team' }, ... ] } } }
            l = league_info.get('league') or league_info
            fr_wrap = l.get('franchises') if isinstance(l, dict) else None
            fr_list = (fr_wrap or {}).get('franchise') if isinstance(fr_wrap, dict) else None
            if isinstance(fr_list, list):
                mfl_franchises = fr_list
        
        
        franchise_count = 0
        roster_count = 0
        
        for mfl_franchise in mfl_franchises:
            try:
                # Normalize franchise data
                franchise_data = self.normalizer.normalize_franchise_data(mfl_franchise, league_canonical_id, provenance)
                
                # Create or update franchise record
                franchise = self.db.query(Franchise).filter(Franchise.id == franchise_data["id"]).first()
                
                if franchise:
                    # Update existing franchise
                    for key, value in franchise_data.items():
                        if key != "id":
                            setattr(franchise, key, value)
                else:
                    # Create new franchise
                    franchise = Franchise(**franchise_data)
                    self.db.add(franchise)
                
                franchise_count += 1
                
                # Ingest roster for this franchise
                try:
                    # Roster fetch, API provides franchise roster under Common/Other endpoints depending on version
                    try:
                        # Try via pymfl first
                        try:
                            from pymfl.api import CommonLeagueInfoAPIClient as _CL
                            _cli = _CL()
                            rosters_resp = _cli.get_rosters(year=year, league_id=league_mfl_id)
                            # Expected: { 'league': { 'franchises': { 'franchise': [ { 'id': '0001', 'players': { 'player': [...] } }, ... ] } } }
                            mfl_roster = {}
                            if isinstance(rosters_resp, dict):
                                l = rosters_resp.get('league') or rosters_resp
                                fr_wrap = l.get('franchises') if isinstance(l, dict) else None
                                fr_list = (fr_wrap or {}).get('franchise') if isinstance(fr_wrap, dict) else None
                                if isinstance(fr_list, list):
                                    fr = next((x for x in fr_list if str(x.get('id')) == str(mfl_franchise.get('id'))), None)
                                    if fr:
                                        players_list = fr.get('players', {}).get('player', [])
                                        mfl_roster = {'players': players_list}
                        except Exception:
                            mfl_roster = {}
                        # Fallback via proxy export if pymfl fails
                        if not mfl_roster:
                            export_rosters = self._mfl_export("rosters", league_id=league_mfl_id, year=year)
                            # export shape: {'rosters': {'franchise': [ {'id': '0001', 'player': [ {...}, ... ]}, ... ] } }
                            if isinstance(export_rosters, dict):
                                rosters_wrap = export_rosters.get('rosters', {})
                                fr_list = rosters_wrap.get('franchise') if isinstance(rosters_wrap, dict) else None
                                if isinstance(fr_list, list):
                                    fr = next((x for x in fr_list if str(x.get('id')) == str(mfl_franchise.get('id'))), None)
                                    if fr and isinstance(fr.get('player'), list):
                                        # Map export player entries to expected minimal shape
                                        mapped_players = []
                                        for rp in fr['player']:
                                            if not isinstance(rp, dict):
                                                continue
                                            mapped_players.append({
                                                'id': rp.get('id'),
                                                'status': rp.get('status'),
                                                'salary': rp.get('salary'),
                                                'is_keeper': rp.get('drafted') == 'Keeper',
                                                'keeper_cost': rp.get('contractInfo')
                                            })
                                        mfl_roster = {'players': mapped_players}
                    except Exception:
                        mfl_roster = {}
                    if mfl_roster:
                        roster_data, roster_players = self.normalizer.normalize_roster_data(
                            mfl_roster, franchise_data["id"], provenance
                        )
                        
                        # Create or update roster
                        roster = self.db.query(Roster).filter(Roster.id == roster_data["id"]).first()
                        
                        if roster:
                            # Update existing roster
                            for key, value in roster_data.items():
                                if key != "id":
                                    setattr(roster, key, value)
                            
                            # Clear existing roster players
                            for rp in roster.roster_players:
                                self.db.delete(rp)
                        else:
                            # Create new roster
                            roster = Roster(**roster_data)
                            self.db.add(roster)
                        
                        # Add roster players
                        for rp_data in roster_players:
                            roster_player = RosterPlayer(**rp_data)
                            self.db.add(roster_player)
                        
                        roster_count += 1
                        
                except Exception as e:
                    logger.warning(f"Failed to process roster for franchise {mfl_franchise.get('id', 'unknown')}: {e}")
                    continue
                
            except Exception as e:
                logger.warning(f"Failed to process franchise {mfl_franchise.get('id', 'unknown')}: {e}")
                continue
        
        return franchise_count, roster_count
    
    def refresh_rosters_via_export(self, league_mfl_id: str, year: int, provenance: DataProvenance) -> Tuple[int, int]:
        """Refresh all rosters for a league using a single export call.
        Returns (franchises_touched, rosters_updated).
        """
        # Determine canonical league id
        league_canonical_id = f"mfl_{league_mfl_id}"
        # Fetch export once
        export = self._mfl_export("rosters", league_id=league_mfl_id, year=year)
        if not isinstance(export, dict) or not export:
            logger.warning("Rosters export returned empty response")
            return (0, 0)
        rosters_wrap = export.get('rosters', {})
        franchises_list = rosters_wrap.get('franchise') if isinstance(rosters_wrap, dict) else None
        if not isinstance(franchises_list, list):
            logger.warning("Rosters export missing franchise list")
            return (0, 0)
        # Build map by franchise id
        by_id = {}
        for fr in franchises_list:
            if isinstance(fr, dict) and fr.get('id') is not None:
                raw = fr.get('id')
                s = str(raw)
                z = s.zfill(4)
                by_id[z] = fr
                by_id[s] = fr
                # also map int form without leading zeros when numeric
                try:
                    n = str(int(s))
                    by_id[n] = fr
                except Exception:
                    pass
        # Load franchises from DB for this league
        db_franchises: List[Franchise] = self.db.query(Franchise).filter(Franchise.league_id == league_canonical_id).all()
        touched = 0
        updated_rosters = 0
        for fr in db_franchises:
            mfl_fid = fr.mfl_franchise_id
            # try multiple key forms: zero-padded, raw string, and int string
            key_z = str(mfl_fid).zfill(4)
            key_s = str(mfl_fid)
            try:
                key_n = str(int(key_s))
            except Exception:
                key_n = key_s
            fr_export = by_id.get(key_z) or by_id.get(key_s) or by_id.get(key_n)
            if not fr_export:
                logger.debug(f"No export roster found for franchise {fr.id} using keys {key_z},{key_s},{key_n}")
                continue
            # Map players
            mapped_players: List[Dict[str, Any]] = []
            players = fr_export.get('player')
            if isinstance(players, list):
                for rp in players:
                    if not isinstance(rp, dict):
                        continue
                    mapped_players.append({
                        'id': rp.get('id'),
                        'status': rp.get('status'),
                        'salary': rp.get('salary'),
                        'is_keeper': rp.get('drafted') == 'Keeper',
                        'keeper_cost': rp.get('contractInfo')
                    })
            roster_payload = {'players': mapped_players}
            # Normalize and upsert
            roster_data, roster_players = self.normalizer.normalize_roster_data(roster_payload, fr.id, provenance)
            roster = self.db.query(Roster).filter(Roster.id == roster_data['id']).first()
            if roster:
                for key, value in roster_data.items():
                    if key != 'id':
                        setattr(roster, key, value)
                # Clear existing players
                for rp in roster.roster_players:
                    self.db.delete(rp)
            else:
                roster = Roster(**roster_data)
                self.db.add(roster)
            for rp_data in roster_players:
                self.db.add(RosterPlayer(**rp_data))
            touched += 1
            updated_rosters += 1
        # Commit changes so rosters persist
        try:
            self.db.commit()
        except Exception:
            self.db.rollback()
            raise
        return (touched, updated_rosters)

    def _mfl_export(self, type_: str, *, league_id: str, year: int) -> Dict[str, Any]:
        """Fetch data from MFL export API. Prefer direct MFL endpoint to avoid self-call deadlocks.
        Falls back to internal proxy if direct fetch fails.
        """
        apikey = os.getenv("MFL_API_KEY", "")
        # Try direct MFL endpoint first (follow redirects to wwwXX host) and include YEAR
        direct_url = f"https://api.myfantasyleague.com/{year}/export"
        params = {
            "TYPE": type_,
            "L": league_id,
            "JSON": 1,
            "YEAR": year,
            "APIKEY": apikey,
        }
        headers = {"User-Agent": os.getenv("MFL_USER_AGENT", "ai-fantasy-assistant/1.0")}
        try:
            with httpx.Client(timeout=30.0, headers=headers, follow_redirects=True) as client:
                r = client.get(direct_url, params=params)
                r.raise_for_status()
                return r.json()
        except Exception as e:
            logger.warning(f"Direct MFL export fetch failed for {type_}: {e}")
        # Fallback to internal proxy route if configured
        base = os.getenv("INTERNAL_API_BASE")
        if base:
            try:
                with httpx.Client(timeout=30.0, headers=headers, follow_redirects=True) as client:
                    r = client.get(f"{base}/api/v1/mfl/export", params=params)
                    r.raise_for_status()
                    return r.json()
            except Exception as e2:
                logger.warning(f"MFL export proxy fetch failed for {type_}: {e2}")
        return {}

    def validate_ingested_data(self, league_id: str) -> Dict[str, Any]:
        """Validate the integrity of ingested data."""
        try:
            validation_results = {
                "league_id": league_id,
                "validation_timestamp": datetime.now(timezone.utc),
                "is_valid": True,
                "issues": []
            }
            
            # Check if league exists
            league = self.db.query(League).filter(League.id == league_id).first()
            if not league:
                validation_results["is_valid"] = False
                validation_results["issues"].append("League not found")
                return validation_results
            
            # Check franchises
            franchises = self.db.query(Franchise).filter(Franchise.league_id == league_id).all()
            if not franchises:
                validation_results["issues"].append("No franchises found")
            
            # Check players
            player_count = self.db.query(Player).count()
            if player_count == 0:
                validation_results["issues"].append("No players found")
            
            # Check rosters
            for franchise in franchises:
                if not franchise.roster:
                    validation_results["issues"].append(f"No roster found for franchise {franchise.id}")
                elif not franchise.roster.roster_players:
                    validation_results["issues"].append(f"Empty roster for franchise {franchise.id}")
            
            # Check data consistency
            orphaned_roster_players = self.db.query(RosterPlayer).join(Player, isouter=True).filter(Player.id.is_(None)).count()
            if orphaned_roster_players > 0:
                validation_results["issues"].append(f"{orphaned_roster_players} roster players reference non-existent players")
            
            if validation_results["issues"]:
                validation_results["is_valid"] = False
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Data validation failed: {e}")
            return {
                "league_id": league_id,
                "validation_timestamp": datetime.now(timezone.utc),
                "is_valid": False,
                "issues": [f"Validation error: {e}"]
            }


def create_mfl_ingestion_service(db_session: Session = None) -> MFLIngestionService:
    """Factory function to create MFL ingestion service."""
    if db_session is None:
        db_session = next(get_db())
    return MFLIngestionService(db_session)