"""
Trade analysis engine for fantasy football trade suggestions and evaluation.

This service implements team need and surplus analysis, Pareto-optimal trade suggestions,
trade fairness calculations, and win probability impact analysis.
"""
from typing import Dict, List, Optional, Tuple, Any, Set
from decimal import Decimal
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum
import logging
import math
from itertools import combinations

from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from ..models.league import League
from ..models.roster import <PERSON><PERSON><PERSON>, RosterPlayer, <PERSON>an<PERSON><PERSON>
from ..models.player import Player, PlayerPosition
from ..models.projection import Projection
from ..services.projections_aggregator import ProjectionsAggregator
from ..core.database import get_db

logger = logging.getLogger(__name__)


class TradeType(str, Enum):
    """Types of trade suggestions."""
    NEED_BASED = "need_based"
    SURPLUS_BASED = "surplus_based"
    VALUE_ARBITRAGE = "value_arbitrage"
    WIN_NOW = "win_now"
    FUTURE_FOCUSED = "future_focused"


class TradeFairness(str, Enum):
    """Trade fairness classifications."""
    VERY_FAIR = "very_fair"
    FAIR = "fair"
    SLIGHTLY_UNFAIR = "slightly_unfair"
    UNFAIR = "unfair"
    VERY_UNFAIR = "very_unfair"


@dataclass
class PositionNeed:
    """Represents a team's need at a specific position."""
    position: PlayerPosition
    need_level: float  # 0.0 (no need) to 1.0 (critical need)
    current_strength: Decimal  # Total projected points at position
    replacement_level: Decimal  # Replacement level for position
    depth_score: float  # Quality of depth (0.0 to 1.0)
    injury_risk: float  # Injury risk factor (0.0 to 1.0)
    bye_week_coverage: float  # Bye week coverage (0.0 to 1.0)


@dataclass
class PositionSurplus:
    """Represents a team's surplus at a specific position."""
    position: PlayerPosition
    surplus_level: float  # 0.0 (no surplus) to 1.0 (major surplus)
    tradeable_players: List[str]  # Player IDs that could be traded
    surplus_value: Decimal  # Total surplus value over replacement
    depth_quality: float  # Quality of remaining depth after trade


@dataclass
class TeamAnalysis:
    """Complete analysis of a team's needs and surpluses."""
    franchise_id: str
    franchise_name: str
    needs: Dict[PlayerPosition, PositionNeed]
    surpluses: Dict[PlayerPosition, PositionSurplus]
    overall_strength: Decimal
    win_probability: float
    trade_urgency: float  # How urgently team needs to make trades
    metadata: Dict[str, Any]


@dataclass
class TradeProposal:
    """Represents a specific trade proposal between two teams."""
    trade_id: str
    team_a_id: str
    team_b_id: str
    team_a_gives: List[str]  # Player IDs
    team_a_receives: List[str]  # Player IDs
    team_b_gives: List[str]  # Player IDs
    team_b_receives: List[str]  # Player IDs
    trade_type: TradeType
    fairness: TradeFairness
    fairness_score: float  # -1.0 (very unfair to A) to 1.0 (very unfair to B)
    acceptance_probability: float  # 0.0 to 1.0
    win_probability_impact_a: float  # Change in win probability for team A
    win_probability_impact_b: float  # Change in win probability for team B
    rationale: str
    metadata: Dict[str, Any]


@dataclass
class TradeImpactAnalysis:
    """Analysis of trade impact on team composition and performance."""
    franchise_id: str
    pre_trade_strength: Dict[PlayerPosition, Decimal]
    post_trade_strength: Dict[PlayerPosition, Decimal]
    position_changes: Dict[PlayerPosition, Decimal]
    overall_impact: Decimal
    win_probability_change: float
    risk_change: float  # Change in roster risk/variance
    depth_impact: Dict[PlayerPosition, float]
    bye_week_impact: Dict[int, float]  # Impact by bye week


class TradeAnalyzer:
    """
    Service for analyzing trades and generating trade suggestions.
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.projections_aggregator = ProjectionsAggregator(db)
        self._replacement_levels_cache: Dict[str, Dict[PlayerPosition, Decimal]] = {}
    
    def analyze_all_teams(
        self,
        league_id: str,
        season: int = 2024
    ) -> Dict[str, TeamAnalysis]:
        """
        Analyze needs and surpluses for all teams in a league.
        
        Args:
            league_id: League identifier
            season: Season year
            
        Returns:
            Dictionary mapping franchise IDs to TeamAnalysis objects
        """
        league = self.db.query(League).filter(League.id == league_id).first()
        if not league:
            raise ValueError(f"League {league_id} not found")
        
        # Get replacement levels for all positions
        replacement_levels = self._calculate_replacement_levels(league_id, season)
        
        team_analyses = {}
        
        for franchise in league.franchises:
            if not franchise.is_active or not franchise.roster:
                continue
            
            analysis = self._analyze_single_team(
                franchise, replacement_levels, season
            )
            team_analyses[franchise.id] = analysis
        
        return team_analyses
    
    def suggest_trades(
        self,
        league_id: str,
        franchise_id: str,
        season: int = 2024,
        max_suggestions: int = 10
    ) -> List[TradeProposal]:
        """
        Generate Pareto-optimal trade suggestions for a specific team.
        
        Args:
            league_id: League identifier
            franchise_id: Target franchise identifier
            season: Season year
            max_suggestions: Maximum number of suggestions to return
            
        Returns:
            List of TradeProposal objects ordered by acceptance probability
        """
        # Analyze all teams in the league
        team_analyses = self.analyze_all_teams(league_id, season)
        
        if franchise_id not in team_analyses:
            raise ValueError(f"Franchise {franchise_id} not found or inactive")
        
        target_team = team_analyses[franchise_id]
        trade_proposals = []
        
        # Generate trade suggestions with each other team
        for other_franchise_id, other_team in team_analyses.items():
            if other_franchise_id == franchise_id:
                continue
            
            # Find mutually beneficial trades
            proposals = self._find_mutually_beneficial_trades(
                target_team, other_team, season
            )
            trade_proposals.extend(proposals)
        
        # Sort by acceptance probability and fairness
        trade_proposals.sort(
            key=lambda t: (t.acceptance_probability, -abs(t.fairness_score)),
            reverse=True
        )
        
        return trade_proposals[:max_suggestions]
    
    def evaluate_trade(
        self,
        team_a_id: str,
        team_b_id: str,
        team_a_gives: List[str],
        team_b_gives: List[str],
        season: int = 2024
    ) -> TradeProposal:
        """
        Evaluate a specific trade proposal between two teams.
        
        Args:
            team_a_id: First team's franchise ID
            team_b_id: Second team's franchise ID
            team_a_gives: List of player IDs team A is trading away
            team_b_gives: List of player IDs team B is trading away
            season: Season year
            
        Returns:
            TradeProposal with evaluation metrics
        """
        # Get team analyses
        team_a_analysis = self._analyze_single_team_by_id(team_a_id, season)
        team_b_analysis = self._analyze_single_team_by_id(team_b_id, season)
        
        # Calculate trade impact for both teams
        impact_a = self._calculate_trade_impact(
            team_a_analysis, team_a_gives, team_b_gives, season
        )
        impact_b = self._calculate_trade_impact(
            team_b_analysis, team_b_gives, team_a_gives, season
        )
        
        # Calculate fairness metrics
        fairness_score = self._calculate_trade_fairness(
            impact_a.overall_impact, impact_b.overall_impact
        )
        fairness = self._classify_fairness(fairness_score)
        
        # Calculate acceptance probability
        acceptance_prob = self._calculate_acceptance_probability(
            impact_a, impact_b, fairness_score
        )
        
        # Generate trade ID and rationale
        trade_id = f"trade_{team_a_id}_{team_b_id}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        rationale = self._generate_trade_rationale(impact_a, impact_b, fairness)
        
        return TradeProposal(
            trade_id=trade_id,
            team_a_id=team_a_id,
            team_b_id=team_b_id,
            team_a_gives=team_a_gives,
            team_a_receives=team_b_gives,
            team_b_gives=team_b_gives,
            team_b_receives=team_a_gives,
            trade_type=self._determine_trade_type(impact_a, impact_b),
            fairness=fairness,
            fairness_score=fairness_score,
            acceptance_probability=acceptance_prob,
            win_probability_impact_a=impact_a.win_probability_change,
            win_probability_impact_b=impact_b.win_probability_change,
            rationale=rationale,
            metadata={
                'evaluation_timestamp': datetime.now(timezone.utc).isoformat(),
                'season': season,
                'team_a_impact': impact_a,
                'team_b_impact': impact_b
            }
        )
    
    def _analyze_single_team(
        self,
        franchise: Franchise,
        replacement_levels: Dict[PlayerPosition, Decimal],
        season: int
    ) -> TeamAnalysis:
        """Analyze a single team's needs and surpluses."""
        if not franchise.roster:
            raise ValueError(f"Franchise {franchise.id} has no roster")
        
        roster = franchise.roster
        needs = {}
        surpluses = {}
        position_strengths = {}
        
        # Analyze each position
        for position in PlayerPosition:
            position_analysis = self._analyze_position(
                roster, position, replacement_levels.get(position, Decimal('0')), season
            )
            
            position_strengths[position] = position_analysis['strength']
            
            if position_analysis['need_level'] > 0.3:  # Significant need
                needs[position] = PositionNeed(
                    position=position,
                    need_level=position_analysis['need_level'],
                    current_strength=position_analysis['strength'],
                    replacement_level=replacement_levels.get(position, Decimal('0')),
                    depth_score=position_analysis['depth_score'],
                    injury_risk=position_analysis['injury_risk'],
                    bye_week_coverage=position_analysis['bye_week_coverage']
                )
            
            if position_analysis['surplus_level'] > 0.3:  # Significant surplus
                surpluses[position] = PositionSurplus(
                    position=position,
                    surplus_level=position_analysis['surplus_level'],
                    tradeable_players=position_analysis['tradeable_players'],
                    surplus_value=position_analysis['surplus_value'],
                    depth_quality=position_analysis['depth_quality']
                )
        
        # Calculate overall team metrics
        overall_strength = sum(position_strengths.values())
        win_probability = self._estimate_win_probability(position_strengths)
        trade_urgency = self._calculate_trade_urgency(needs, surpluses)
        
        return TeamAnalysis(
            franchise_id=franchise.id,
            franchise_name=franchise.name,
            needs=needs,
            surpluses=surpluses,
            overall_strength=overall_strength,
            win_probability=win_probability,
            trade_urgency=trade_urgency,
            metadata={
                'analysis_timestamp': datetime.now(timezone.utc).isoformat(),
                'season': season,
                'position_strengths': {pos.value: float(strength) for pos, strength in position_strengths.items()}
            }
        )    

    def _analyze_position(
        self,
        roster: Roster,
        position: PlayerPosition,
        replacement_level: Decimal,
        season: int
    ) -> Dict[str, Any]:
        """Analyze a team's situation at a specific position."""
        position_players = roster.get_players_by_position(position.value)
        
        if not position_players:
            return {
                'strength': Decimal('0'),
                'need_level': 1.0,
                'surplus_level': 0.0,
                'depth_score': 0.0,
                'injury_risk': 1.0,
                'bye_week_coverage': 0.0,
                'tradeable_players': [],
                'surplus_value': Decimal('0'),
                'depth_quality': 0.0
            }
        
        # Get projections for all players at this position
        player_projections = []
        for roster_player in position_players:
            projection = self.projections_aggregator.aggregate_projections(
                player_id=roster_player.player_id,
                week=None,  # Season-long
                season=season
            )
            if projection:
                player_projections.append({
                    'player_id': roster_player.player_id,
                    'player': roster_player.player,
                    'projection': projection,
                    'is_starter': roster_player.is_starting()
                })
        
        if not player_projections:
            return {
                'strength': Decimal('0'),
                'need_level': 1.0,
                'surplus_level': 0.0,
                'depth_score': 0.0,
                'injury_risk': 1.0,
                'bye_week_coverage': 0.0,
                'tradeable_players': [],
                'surplus_value': Decimal('0'),
                'depth_quality': 0.0
            }
        
        # Sort by projected points
        player_projections.sort(
            key=lambda p: p['projection'].projected_points, reverse=True
        )
        
        # Calculate position strength (sum of starter projections)
        league = roster.franchise.league
        starter_slots = self._get_position_starter_slots(league, position)
        
        starters = player_projections[:starter_slots]
        bench = player_projections[starter_slots:]
        
        total_strength = sum(p['projection'].projected_points for p in starters)
        
        # Calculate need level
        need_level = self._calculate_need_level(
            starters, replacement_level, starter_slots
        )
        
        # Calculate surplus level
        surplus_level = self._calculate_surplus_level(
            starters, bench, replacement_level
        )
        
        # Calculate depth metrics
        depth_score = self._calculate_depth_score(bench, replacement_level)
        
        # Calculate injury risk
        injury_risk = self._calculate_injury_risk(player_projections)
        
        # Calculate bye week coverage
        bye_week_coverage = self._calculate_bye_week_coverage(player_projections)
        
        # Identify tradeable players (bench players above replacement)
        tradeable_players = [
            p['player_id'] for p in bench
            if p['projection'].projected_points > replacement_level * Decimal('1.1')
        ]
        
        # Calculate surplus value
        surplus_value = sum(
            p['projection'].projected_points - replacement_level
            for p in bench
            if p['projection'].projected_points > replacement_level
        )
        
        # Calculate depth quality after potential trades
        depth_quality = self._calculate_post_trade_depth_quality(bench)
        
        return {
            'strength': total_strength,
            'need_level': need_level,
            'surplus_level': surplus_level,
            'depth_score': depth_score,
            'injury_risk': injury_risk,
            'bye_week_coverage': bye_week_coverage,
            'tradeable_players': tradeable_players,
            'surplus_value': surplus_value,
            'depth_quality': depth_quality
        }
    
    def _calculate_need_level(
        self,
        starters: List[Dict],
        replacement_level: Decimal,
        required_starters: int
    ) -> float:
        """Calculate how much a team needs help at a position."""
        if not starters or required_starters == 0:
            return 1.0
        
        # If we don't have enough starters, high need
        if len(starters) < required_starters:
            return 1.0
        
        # Calculate average starter strength vs replacement
        avg_starter_strength = sum(
            p['projection'].projected_points for p in starters
        ) / len(starters)
        
        if replacement_level <= 0:
            return 0.0
        
        # Need level based on how close starters are to replacement level
        strength_ratio = float(avg_starter_strength / replacement_level)
        
        if strength_ratio < 1.1:  # Very close to replacement
            return 0.9
        elif strength_ratio < 1.3:  # Moderate strength
            return 0.6
        elif strength_ratio < 1.5:  # Good strength
            return 0.3
        else:  # Strong position
            return 0.1
    
    def _calculate_surplus_level(
        self,
        starters: List[Dict],
        bench: List[Dict],
        replacement_level: Decimal
    ) -> float:
        """Calculate how much surplus a team has at a position."""
        if not bench:
            return 0.0
        
        # Count bench players significantly above replacement
        quality_bench = [
            p for p in bench
            if p['projection'].projected_points > replacement_level * Decimal('1.2')
        ]
        
        if not quality_bench:
            return 0.0
        
        # Surplus level based on number and quality of bench players
        surplus_count = len(quality_bench)
        avg_surplus_quality = sum(
            float(p['projection'].projected_points / replacement_level)
            for p in quality_bench
        ) / len(quality_bench)
        
        # Normalize surplus level
        surplus_level = min(1.0, (surplus_count * 0.3) + ((avg_surplus_quality - 1.0) * 0.5))
        
        return max(0.0, surplus_level)
    
    def _calculate_depth_score(
        self,
        bench_players: List[Dict],
        replacement_level: Decimal
    ) -> float:
        """Calculate the quality of depth at a position."""
        if not bench_players:
            return 0.0
        
        # Score based on how many bench players are above replacement
        above_replacement = [
            p for p in bench_players
            if p['projection'].projected_points > replacement_level
        ]
        
        if not above_replacement:
            return 0.0
        
        # Average quality of bench depth
        avg_quality = sum(
            float(p['projection'].projected_points / replacement_level)
            for p in above_replacement
        ) / len(above_replacement)
        
        # Depth score considers both quantity and quality
        quantity_score = min(1.0, len(above_replacement) / 3.0)  # Normalize to 3 bench players
        quality_score = min(1.0, (avg_quality - 1.0) / 0.5)  # Normalize quality above replacement
        
        return (quantity_score + quality_score) / 2.0
    
    def _calculate_injury_risk(self, player_projections: List[Dict]) -> float:
        """Calculate injury risk for a position group."""
        if not player_projections:
            return 1.0
        
        injury_count = 0
        total_players = len(player_projections)
        
        for p in player_projections:
            injury_status = p['player'].injury_status.value
            if injury_status in ['QUESTIONABLE', 'DOUBTFUL']:
                injury_count += 0.5
            elif injury_status in ['OUT', 'IR', 'PUP']:
                injury_count += 1.0
        
        # Risk is higher with more injured players and fewer total players
        base_risk = injury_count / total_players
        depth_adjustment = max(0.0, (4 - total_players) * 0.1)  # Higher risk with less depth
        
        return min(1.0, base_risk + depth_adjustment)
    
    def _calculate_bye_week_coverage(self, player_projections: List[Dict]) -> float:
        """Calculate bye week coverage for a position."""
        if not player_projections:
            return 0.0
        
        bye_weeks = set()
        for p in player_projections:
            if p['player'].bye_week:
                bye_weeks.add(p['player'].bye_week)
        
        # Good coverage means players have different bye weeks
        unique_bye_weeks = len(bye_weeks)
        total_players = len(player_projections)
        
        if total_players <= 1:
            return 0.0
        
        # Coverage score based on bye week diversity
        coverage = min(1.0, unique_bye_weeks / min(total_players, 4))
        
        return coverage
    
    def _calculate_post_trade_depth_quality(self, bench_players: List[Dict]) -> float:
        """Calculate remaining depth quality after trading away surplus."""
        if len(bench_players) <= 1:
            return 0.0
        
        # Assume we trade away the best bench player
        remaining_bench = bench_players[1:]  # Skip the best bench player
        
        if not remaining_bench:
            return 0.0
        
        # Quality of remaining depth
        avg_projection = sum(
            p['projection'].projected_points for p in remaining_bench
        ) / len(remaining_bench)
        
        # Normalize to a 0-1 scale (assuming 10-20 points is good depth)
        quality = min(1.0, max(0.0, (float(avg_projection) - 5.0) / 15.0))
        
        return quality
    
    def _find_mutually_beneficial_trades(
        self,
        team_a: TeamAnalysis,
        team_b: TeamAnalysis,
        season: int
    ) -> List[TradeProposal]:
        """Find trades that benefit both teams."""
        proposals = []
        
        # Find positions where team A has surplus and team B has need
        for pos_a, surplus in team_a.surpluses.items():
            if pos_a in team_b.needs:
                need = team_b.needs[pos_a]
                
                # Look for reciprocal needs/surpluses
                for pos_b, surplus_b in team_b.surpluses.items():
                    if pos_b in team_a.needs:
                        need_a = team_a.needs[pos_b]
                        
                        # Generate specific trade proposals
                        trade_proposals = self._generate_position_trades(
                            team_a, team_b, pos_a, pos_b, surplus, need, surplus_b, need_a, season
                        )
                        proposals.extend(trade_proposals)
        
        return proposals
    
    def _generate_position_trades(
        self,
        team_a: TeamAnalysis,
        team_b: TeamAnalysis,
        pos_a: PlayerPosition,
        pos_b: PlayerPosition,
        surplus_a: PositionSurplus,
        need_b: PositionNeed,
        surplus_b: PositionSurplus,
        need_a: PositionNeed,
        season: int
    ) -> List[TradeProposal]:
        """Generate specific trade proposals between two positions."""
        proposals = []
        
        # Get tradeable players from each team
        tradeable_a = surplus_a.tradeable_players[:3]  # Top 3 tradeable
        tradeable_b = surplus_b.tradeable_players[:3]  # Top 3 tradeable
        
        # Generate 1-for-1 trades
        for player_a in tradeable_a:
            for player_b in tradeable_b:
                proposal = self.evaluate_trade(
                    team_a.franchise_id,
                    team_b.franchise_id,
                    [player_a],
                    [player_b],
                    season
                )
                
                # Only include if trade is beneficial for both teams
                if (proposal.win_probability_impact_a > 0 and 
                    proposal.win_probability_impact_b > 0 and
                    proposal.fairness in [TradeFairness.VERY_FAIR, TradeFairness.FAIR]):
                    proposals.append(proposal)
        
        # Generate 2-for-1 trades if there's significant value imbalance
        if len(tradeable_a) >= 2:
            for player_b in tradeable_b:
                for combo in combinations(tradeable_a, 2):
                    proposal = self.evaluate_trade(
                        team_a.franchise_id,
                        team_b.franchise_id,
                        list(combo),
                        [player_b],
                        season
                    )
                    
                    if (proposal.win_probability_impact_a > 0 and 
                        proposal.win_probability_impact_b > 0 and
                        proposal.fairness in [TradeFairness.VERY_FAIR, TradeFairness.FAIR]):
                        proposals.append(proposal)
        
        if len(tradeable_b) >= 2:
            for player_a in tradeable_a:
                for combo in combinations(tradeable_b, 2):
                    proposal = self.evaluate_trade(
                        team_a.franchise_id,
                        team_b.franchise_id,
                        [player_a],
                        list(combo),
                        season
                    )
                    
                    if (proposal.win_probability_impact_a > 0 and 
                        proposal.win_probability_impact_b > 0 and
                        proposal.fairness in [TradeFairness.VERY_FAIR, TradeFairness.FAIR]):
                        proposals.append(proposal)
        
        return proposals
    
    def _calculate_trade_impact(
        self,
        team_analysis: TeamAnalysis,
        players_out: List[str],
        players_in: List[str],
        season: int
    ) -> TradeImpactAnalysis:
        """Calculate the impact of a trade on a team."""
        # Get current position strengths
        pre_trade_strength = {}
        for position in PlayerPosition:
            if position in team_analysis.needs:
                pre_trade_strength[position] = team_analysis.needs[position].current_strength
            elif position in team_analysis.surpluses:
                # Estimate strength from surplus data
                pre_trade_strength[position] = team_analysis.surpluses[position].surplus_value + Decimal('50')  # Rough estimate
            else:
                pre_trade_strength[position] = Decimal('0')
        
        # Calculate post-trade strengths
        post_trade_strength = pre_trade_strength.copy()
        position_changes = {}
        
        # Subtract players going out
        for player_id in players_out:
            player = self.db.query(Player).filter(Player.id == player_id).first()
            if player:
                projection = self.projections_aggregator.aggregate_projections(
                    player_id=player_id, week=None, season=season
                )
                if projection:
                    post_trade_strength[player.position] -= projection.projected_points
        
        # Add players coming in
        for player_id in players_in:
            player = self.db.query(Player).filter(Player.id == player_id).first()
            if player:
                projection = self.projections_aggregator.aggregate_projections(
                    player_id=player_id, week=None, season=season
                )
                if projection:
                    post_trade_strength[player.position] += projection.projected_points
        
        # Calculate position changes
        for position in PlayerPosition:
            position_changes[position] = post_trade_strength[position] - pre_trade_strength[position]
        
        # Calculate overall impact
        overall_impact = sum(position_changes.values())
        
        # Estimate win probability change (simplified)
        win_prob_change = float(overall_impact) * 0.001  # Rough conversion
        
        # Calculate risk change (simplified - based on player variance)
        risk_change = 0.0  # Placeholder for now
        
        # Calculate depth impact (simplified)
        depth_impact = {pos: 0.0 for pos in PlayerPosition}  # Placeholder
        
        # Calculate bye week impact (simplified)
        bye_week_impact = {}  # Placeholder
        
        return TradeImpactAnalysis(
            franchise_id=team_analysis.franchise_id,
            pre_trade_strength=pre_trade_strength,
            post_trade_strength=post_trade_strength,
            position_changes=position_changes,
            overall_impact=overall_impact,
            win_probability_change=win_prob_change,
            risk_change=risk_change,
            depth_impact=depth_impact,
            bye_week_impact=bye_week_impact
        )
    
    def _calculate_trade_fairness(
        self,
        impact_a: Decimal,
        impact_b: Decimal
    ) -> float:
        """Calculate trade fairness score."""
        if impact_a == 0 and impact_b == 0:
            return 0.0
        
        total_impact = abs(impact_a) + abs(impact_b)
        if total_impact == 0:
            return 0.0
        
        # Fairness score: positive means favors team B, negative favors team A
        fairness = float((impact_b - impact_a) / total_impact)
        
        return max(-1.0, min(1.0, fairness))
    
    def _classify_fairness(self, fairness_score: float) -> TradeFairness:
        """Classify trade fairness based on score."""
        abs_score = abs(fairness_score)
        
        if abs_score <= 0.1:
            return TradeFairness.VERY_FAIR
        elif abs_score <= 0.25:
            return TradeFairness.FAIR
        elif abs_score <= 0.4:
            return TradeFairness.SLIGHTLY_UNFAIR
        elif abs_score <= 0.6:
            return TradeFairness.UNFAIR
        else:
            return TradeFairness.VERY_UNFAIR
    
    def _calculate_acceptance_probability(
        self,
        impact_a: TradeImpactAnalysis,
        impact_b: TradeImpactAnalysis,
        fairness_score: float
    ) -> float:
        """Calculate the probability that both teams would accept the trade."""
        # Base probability based on positive impact for both teams
        base_prob = 0.5
        
        if impact_a.win_probability_change > 0:
            base_prob += 0.2
        if impact_b.win_probability_change > 0:
            base_prob += 0.2
        
        # Adjust for fairness
        fairness_adjustment = max(-0.3, min(0.3, -abs(fairness_score) * 0.5))
        
        # Adjust for magnitude of impact
        impact_magnitude = (abs(impact_a.win_probability_change) + abs(impact_b.win_probability_change)) / 2
        magnitude_adjustment = min(0.2, impact_magnitude * 10)  # Scale impact
        
        final_prob = base_prob + fairness_adjustment + magnitude_adjustment
        
        return max(0.0, min(1.0, final_prob))
    
    def _determine_trade_type(
        self,
        impact_a: TradeImpactAnalysis,
        impact_b: TradeImpactAnalysis
    ) -> TradeType:
        """Determine the type of trade based on impacts."""
        # Simplified logic for trade type classification
        if (impact_a.win_probability_change > 0.02 and 
            impact_b.win_probability_change > 0.02):
            return TradeType.WIN_NOW
        elif abs(impact_a.overall_impact - impact_b.overall_impact) < 5:
            return TradeType.NEED_BASED
        else:
            return TradeType.VALUE_ARBITRAGE
    
    def _generate_trade_rationale(
        self,
        impact_a: TradeImpactAnalysis,
        impact_b: TradeImpactAnalysis,
        fairness: TradeFairness
    ) -> str:
        """Generate human-readable rationale for the trade."""
        rationale_parts = []
        
        # Describe the trade type and fairness
        if fairness == TradeFairness.VERY_FAIR:
            rationale_parts.append("This is a very fair trade that benefits both teams")
        elif fairness == TradeFairness.FAIR:
            rationale_parts.append("This is a fair trade with mutual benefits")
        else:
            rationale_parts.append("This trade has some imbalance but could still work")
        
        # Describe impact for team A
        if impact_a.win_probability_change > 0:
            rationale_parts.append(f"improves Team A's win probability by {impact_a.win_probability_change:.1%}")
        
        # Describe impact for team B
        if impact_b.win_probability_change > 0:
            rationale_parts.append(f"improves Team B's win probability by {impact_b.win_probability_change:.1%}")
        
        # Describe position impacts
        significant_changes_a = [
            pos for pos, change in impact_a.position_changes.items()
            if abs(change) > 5
        ]
        
        if significant_changes_a:
            pos_names = [pos.value for pos in significant_changes_a[:2]]
            rationale_parts.append(f"addresses Team A's needs at {', '.join(pos_names)}")
        
        return ". ".join(rationale_parts).capitalize() + "."
    
    def _analyze_single_team_by_id(
        self,
        franchise_id: str,
        season: int
    ) -> TeamAnalysis:
        """Analyze a single team by franchise ID."""
        franchise = self.db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise:
            raise ValueError(f"Franchise {franchise_id} not found")
        
        league = franchise.league
        replacement_levels = self._calculate_replacement_levels(league.id, season)
        
        return self._analyze_single_team(franchise, replacement_levels, season)
    
    def _calculate_replacement_levels(
        self,
        league_id: str,
        season: int
    ) -> Dict[PlayerPosition, Decimal]:
        """Calculate replacement level baselines for each position."""
        cache_key = f"{league_id}_{season}"
        if cache_key in self._replacement_levels_cache:
            return self._replacement_levels_cache[cache_key]
        
        league = self.db.query(League).filter(League.id == league_id).first()
        if not league:
            raise ValueError(f"League {league_id} not found")
        
        replacement_levels = {}
        
        # Get all aggregated projections for the season
        all_projections = self.projections_aggregator.aggregate_all_projections(
            week=None,  # Season-long
            season=season,
            min_sources=1
        )
        
        # Group projections by position
        position_projections = {}
        for proj in all_projections:
            player = self.db.query(Player).filter(Player.id == proj.player_id).first()
            if player:
                position = player.position
                if position not in position_projections:
                    position_projections[position] = []
                position_projections[position].append(proj.projected_points)
        
        # Calculate replacement level for each position
        for position, projections in position_projections.items():
            if not projections:
                replacement_levels[position] = Decimal('0')
                continue
            
            # Sort projections in descending order
            sorted_projections = sorted(projections, reverse=True)
            
            # Get roster requirements for this position
            roster_slots = self._get_position_starter_slots(league, position)
            total_teams = len(league.franchises)
            
            # Calculate replacement level index
            bench_depth = 2  # Assume 2 bench players per position
            replacement_index = (roster_slots + bench_depth) * total_teams
            
            # Ensure we don't exceed available players
            replacement_index = min(replacement_index, len(sorted_projections) - 1)
            
            if replacement_index >= 0:
                replacement_levels[position] = sorted_projections[replacement_index]
            else:
                replacement_levels[position] = Decimal('0')
        
        # Cache the results
        self._replacement_levels_cache[cache_key] = replacement_levels
        
        return replacement_levels
    
    def _get_position_starter_slots(self, league: League, position: PlayerPosition) -> int:
        """Get the number of starting roster slots for a position."""
        total_slots = 0
        
        for slot in league.roster_slots:
            # Check if this slot is for a single position
            if slot.get('position') == position.value:
                total_slots += slot.get('count', 1)
            # Check if this slot allows multiple positions (like FLEX)
            elif 'positions' in slot:
                slot_positions = slot.get('positions', [])
                if position.value in slot_positions:
                    total_slots += slot.get('count', 1)
        
        return total_slots
    
    def _estimate_win_probability(
        self,
        position_strengths: Dict[PlayerPosition, Decimal]
    ) -> float:
        """Estimate win probability based on position strengths."""
        # Simplified win probability estimation
        total_strength = sum(position_strengths.values())
        
        # Normalize to a probability (this is a rough approximation)
        # In reality, this would use historical data and more sophisticated modeling
        normalized_strength = float(total_strength) / 200.0  # Assume 200 is average total
        
        # Convert to probability using sigmoid function
        win_prob = 1.0 / (1.0 + math.exp(-normalized_strength))
        
        return max(0.1, min(0.9, win_prob))
    
    def _calculate_trade_urgency(
        self,
        needs: Dict[PlayerPosition, PositionNeed],
        surpluses: Dict[PlayerPosition, PositionSurplus]
    ) -> float:
        """Calculate how urgently a team needs to make trades."""
        if not needs and not surpluses:
            return 0.0
        
        # Urgency based on severity of needs
        need_urgency = 0.0
        if needs:
            avg_need_level = sum(need.need_level for need in needs.values()) / len(needs)
            need_urgency = avg_need_level * 0.7
        
        # Urgency based on available surpluses to trade
        surplus_opportunity = 0.0
        if surpluses:
            avg_surplus_level = sum(surplus.surplus_level for surplus in surpluses.values()) / len(surpluses)
            surplus_opportunity = avg_surplus_level * 0.3
        
        total_urgency = need_urgency + surplus_opportunity
        
        return min(1.0, total_urgency)