"""
Fantasy Football Calculator (FFC) ADP client.
"""
from __future__ import annotations

from typing import Any, Dict, List, Optional
import httpx

class FFCClient:
    def __init__(self, timeout: float = 15.0, base_url: str = "https://fantasyfootballcalculator.com/api/v1/adp") -> None:
        self.timeout = timeout
        self.base_url = base_url.rstrip("/")

    async def get_adp(self, *, scoring: str = "ppr", teams: int = 12, year: int = 2025, position: Optional[str] = None) -> Dict[str, Any]:
        """
        Fetch ADP data.
        scoring: ppr|half|standard
        position: None for all, or one of QB/RB/WR/TE
        """
        path = scoring.lower()
        url = f"{self.base_url}/{path}"
        params = {"teams": teams, "year": year}
        if position:
            params["position"] = position.upper()
        async with httpx.AsyncClient(timeout=self.timeout, follow_redirects=True) as client:
            resp = await client.get(url, params=params, headers={
                "User-Agent": "AI Fantasy Assistant/1.0",
                "Accept": "application/json",
            })
            resp.raise_for_status()
            try:
                data = resp.json()
            except ValueError:
                raise httpx.HTTPError("FFC returned non-JSON ADP response")
            return data

