"""
League Rules Engine for fantasy football league configuration and validation.

This module provides comprehensive rule management including:
- JSON schema validation for league configurations
- Scoring calculation based on league rules
- Roster constraint validation
- Rule parsing and normalization
"""
from typing import Dict, Any, List, Optional, Union, Tuple
from decimal import Decimal
from pydantic import BaseModel, Field, field_validator
from enum import Enum
import json
from datetime import datetime

from ..models.player import PlayerPosition
from ..models.roster import Roster, RosterPlayer


class ScoringType(str, Enum):
    """Types of scoring systems."""
    STANDARD = "standard"
    PPR = "ppr"
    HALF_PPR = "half_ppr"
    CUSTOM = "custom"


class RosterSlotType(str, Enum):
    """Types of roster slots."""
    STARTING = "starting"
    BENCH = "bench"
    IR = "ir"
    TAXI = "taxi"


class LeagueRulesSchema(BaseModel):
    """Pydantic schema for league rules validation."""
    
    # Basic league info
    league_type: str = Field(default="redraft", description="League type: redraft, keeper, dynasty")
    scoring_type: ScoringType = Field(default=ScoringType.STANDARD, description="Base scoring system")
    
    # Scoring rules
    scoring_rules: Dict[str, float] = Field(
        default_factory=lambda: {
            "passing_yards": 0.04,
            "passing_touchdowns": 4.0,
            "passing_interceptions": -2.0,
            "rushing_yards": 0.1,
            "rushing_touchdowns": 6.0,
            "receiving_yards": 0.1,
            "receiving_touchdowns": 6.0,
            "receptions": 0.0,  # PPR value
            "fumbles_lost": -2.0,
            "two_point_conversions": 2.0,
            "field_goals_made": 3.0,
            "field_goals_missed": -1.0,
            "extra_points_made": 1.0,
            "extra_points_missed": -1.0,
            "defense_touchdowns": 6.0,
            "defense_interceptions": 2.0,
            "defense_fumble_recoveries": 2.0,
            "defense_sacks": 1.0,
            "defense_safeties": 2.0,
            "defense_points_allowed_0": 10.0,
            "defense_points_allowed_1_6": 7.0,
            "defense_points_allowed_7_13": 4.0,
            "defense_points_allowed_14_20": 1.0,
            "defense_points_allowed_21_27": 0.0,
            "defense_points_allowed_28_34": -1.0,
            "defense_points_allowed_35_plus": -4.0,
        },
        description="Scoring rules mapping stat names to point values"
    )
    
    # Roster configuration
    roster_slots: List[Dict[str, Any]] = Field(
        default_factory=lambda: [
            {"position": "QB", "count": 1, "type": "starting"},
            {"position": "RB", "count": 2, "type": "starting"},
            {"position": "WR", "count": 2, "type": "starting"},
            {"position": "TE", "count": 1, "type": "starting"},
            {"position": "FLEX", "count": 1, "type": "starting", "eligible_positions": ["RB", "WR", "TE"]},
            {"position": "K", "count": 1, "type": "starting"},
            {"position": "DEF", "count": 1, "type": "starting"},
            {"position": "BENCH", "count": 6, "type": "bench"},
        ],
        description="Roster slot configuration"
    )
    
    # Keeper rules (optional)
    keeper_rules: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Keeper league rules if applicable"
    )
    
    # Waiver and FAAB rules
    waiver_rules: Dict[str, Any] = Field(
        default_factory=lambda: {
            "waiver_type": "faab",  # faab, rolling, reverse_standings
            "faab_budget": 100,
            "minimum_bid": 0,
            "waiver_period_hours": 24,
            "waiver_days": ["Wednesday", "Saturday"],
        },
        description="Waiver wire and FAAB configuration"
    )
    
    # Trade rules
    trade_rules: Dict[str, Any] = Field(
        default_factory=lambda: {
            "trade_deadline_week": 10,
            "review_period_hours": 24,
            "veto_votes_required": 4,
            "commissioner_approval": False,
        },
        description="Trade configuration and deadlines"
    )
    
    @field_validator('scoring_rules')
    @classmethod
    def validate_scoring_rules(cls, v):
        """Validate scoring rules contain required fields."""
        required_fields = [
            "passing_yards", "passing_touchdowns", "rushing_yards", 
            "rushing_touchdowns", "receiving_yards", "receiving_touchdowns"
        ]
        missing_fields = [field for field in required_fields if field not in v]
        if missing_fields:
            raise ValueError(f"Missing required scoring fields: {missing_fields}")
        return v
    
    @field_validator('roster_slots')
    @classmethod
    def validate_roster_slots(cls, v):
        """Validate roster slot configuration."""
        if not v:
            raise ValueError("Roster slots cannot be empty")
        
        # Check for required starting positions
        starting_slots = [slot for slot in v if slot.get("type") == "starting"]
        positions = [slot.get("position") for slot in starting_slots]
        
        if "QB" not in positions:
            raise ValueError("Must have at least one QB starting slot")
        
        return v


class KeeperRulesSchema(BaseModel):
    """Schema for keeper league rules."""
    
    max_keepers: int = Field(ge=0, le=20, description="Maximum number of keepers allowed")
    keeper_deadline: Optional[str] = Field(default=None, description="Keeper deadline date (ISO format)")
    round_escalation: bool = Field(default=False, description="Whether keeper costs escalate each year")
    escalation_rounds: int = Field(default=1, description="Number of rounds to escalate each year")
    franchise_tags: int = Field(default=0, description="Number of franchise tags allowed")
    max_years_kept: Optional[int] = Field(default=None, description="Maximum years a player can be kept")
    
    # Keeper cost rules
    cost_rules: Dict[str, Any] = Field(
        default_factory=lambda: {
            "waiver_pickup_cost": "last_round",  # last_round, fixed_round, faab_spent
            "free_agent_cost": "last_round",
            "drafted_player_cost": "draft_round",
            "minimum_cost": 1,
        },
        description="Rules for calculating keeper costs"
    )


class RulesEngine:
    """
    Core rules engine for fantasy football league management.
    
    Handles rule validation, scoring calculations, and roster constraints.
    """
    
    def __init__(self):
        self._cached_schemas: Dict[str, LeagueRulesSchema] = {}
    
    def validate_league_rules(self, rules_dict: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate league rules against the schema.
        
        Args:
            rules_dict: Dictionary containing league rules
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        try:
            LeagueRulesSchema(**rules_dict)
            return True, []
        except Exception as e:
            return False, [str(e)]
    
    def parse_league_rules(self, rules_dict: Dict[str, Any]) -> LeagueRulesSchema:
        """
        Parse and validate league rules, returning a structured schema.
        
        Args:
            rules_dict: Raw league rules dictionary
            
        Returns:
            Validated LeagueRulesSchema instance
            
        Raises:
            ValueError: If rules are invalid
        """
        try:
            return LeagueRulesSchema(**rules_dict)
        except Exception as e:
            raise ValueError(f"Invalid league rules: {e}")
    
    def calculate_player_score(
        self, 
        player_stats: Dict[str, Union[int, float]], 
        scoring_rules: Dict[str, float]
    ) -> float:
        """
        Calculate fantasy points for a player based on stats and scoring rules.
        
        Args:
            player_stats: Dictionary of player statistics
            scoring_rules: League scoring rules
            
        Returns:
            Total fantasy points scored
        """
        total_points = 0.0
        
        for stat_name, stat_value in player_stats.items():
            if stat_name in scoring_rules:
                points = float(stat_value) * scoring_rules[stat_name]
                total_points += points
        
        return round(total_points, 2)
    
    def validate_roster_constraints(
        self, 
        roster: Roster, 
        roster_slots: List[Dict[str, Any]]
    ) -> Tuple[bool, List[str]]:
        """
        Validate that a roster meets league constraints.
        
        Args:
            roster: Roster instance to validate
            roster_slots: League roster slot configuration
            
        Returns:
            Tuple of (is_valid, constraint_violations)
        """
        violations = []
        active_players = roster.get_active_players()
        
        # Count players by position
        position_counts = {}
        starting_counts = {}
        flex_counts = {}
        
        for roster_player in active_players:
            position = roster_player.player.position.value
            position_counts[position] = position_counts.get(position, 0) + 1
            
            if roster_player.is_starting():
                if roster_player.roster_slot == "FLEX":
                    # Count FLEX players separately
                    flex_counts[position] = flex_counts.get(position, 0) + 1
                else:
                    # Count regular starting positions
                    starting_counts[position] = starting_counts.get(position, 0) + 1
        
        # Validate against roster slot requirements
        for slot_config in roster_slots:
            position = slot_config.get("position")
            required_count = slot_config.get("count", 1)
            slot_type = slot_config.get("type", "starting")
            
            if slot_type == "starting" and position != "FLEX":
                actual_count = starting_counts.get(position, 0)
                if actual_count < required_count:
                    violations.append(
                        f"Not enough starting {position} players: {actual_count}/{required_count}"
                    )
                elif actual_count > required_count:
                    violations.append(
                        f"Too many starting {position} players: {actual_count}/{required_count}"
                    )
        
        # Validate FLEX positions
        flex_slots = [slot for slot in roster_slots if slot.get("position") == "FLEX"]
        for flex_slot in flex_slots:
            eligible_positions = flex_slot.get("eligible_positions", ["RB", "WR", "TE"])
            required_flex = flex_slot.get("count", 1)
            
            # Count FLEX players (players in FLEX slots)
            flex_players = [
                rp for rp in active_players 
                if rp.roster_slot == "FLEX" and rp.player.position.value in eligible_positions
            ]
            
            if len(flex_players) < required_flex:
                violations.append(f"Not enough FLEX players: {len(flex_players)}/{required_flex}")
            elif len(flex_players) > required_flex:
                violations.append(f"Too many FLEX players: {len(flex_players)}/{required_flex}")
        
        return len(violations) == 0, violations
    
    def get_roster_slot_requirements(self, roster_slots: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        Get the minimum and maximum roster requirements by position.
        
        Args:
            roster_slots: League roster slot configuration
            
        Returns:
            Dictionary mapping positions to required counts
        """
        requirements = {}
        
        for slot_config in roster_slots:
            position = slot_config.get("position")
            count = slot_config.get("count", 1)
            slot_type = slot_config.get("type", "starting")
            
            if position and position != "FLEX":
                if position not in requirements:
                    requirements[position] = {"starting": 0, "total": 0}
                
                if slot_type == "starting":
                    requirements[position]["starting"] += count
                
                requirements[position]["total"] += count
        
        return requirements
    
    def validate_keeper_selection(
        self, 
        keeper_players: List[RosterPlayer], 
        keeper_rules: Dict[str, Any]
    ) -> Tuple[bool, List[str]]:
        """
        Validate keeper selections against league keeper rules.
        
        Args:
            keeper_players: List of players selected as keepers
            keeper_rules: League keeper rules
            
        Returns:
            Tuple of (is_valid, rule_violations)
        """
        violations = []
        
        if not keeper_rules:
            if keeper_players:
                violations.append("No keepers allowed in this league")
            return len(violations) == 0, violations
        
        try:
            rules_schema = KeeperRulesSchema(**keeper_rules)
        except Exception as e:
            violations.append(f"Invalid keeper rules: {e}")
            return False, violations
        
        # Check maximum keeper count
        if len(keeper_players) > rules_schema.max_keepers:
            violations.append(
                f"Too many keepers selected: {len(keeper_players)}/{rules_schema.max_keepers}"
            )
        
        # Validate keeper costs if specified
        for keeper in keeper_players:
            if keeper.keeper_cost is None:
                violations.append(f"Keeper cost not specified for {keeper.player.name}")
            elif keeper.keeper_cost < rules_schema.cost_rules.get("minimum_cost", 1):
                violations.append(
                    f"Keeper cost too low for {keeper.player.name}: "
                    f"{keeper.keeper_cost} < {rules_schema.cost_rules.get('minimum_cost', 1)}"
                )
        
        return len(violations) == 0, violations
    
    def calculate_keeper_cost(
        self, 
        roster_player: RosterPlayer, 
        keeper_rules: Dict[str, Any]
    ) -> int:
        """
        Calculate the draft round cost for keeping a player.
        
        Args:
            roster_player: Player being considered for keeper
            keeper_rules: League keeper rules
            
        Returns:
            Draft round cost for keeping the player
        """
        if not keeper_rules:
            return 1
        
        try:
            rules_schema = KeeperRulesSchema(**keeper_rules)
        except Exception:
            return 1
        
        cost_rules = rules_schema.cost_rules
        base_cost = roster_player.keeper_cost or 16  # Default to last round
        
        # Apply escalation if enabled
        if rules_schema.round_escalation:
            # This would need additional logic to track years kept
            # For now, just apply the escalation rounds
            escalated_cost = base_cost - rules_schema.escalation_rounds
            base_cost = max(1, escalated_cost)
        
        # Apply minimum cost
        minimum_cost = cost_rules.get("minimum_cost", 1)
        return max(minimum_cost, base_cost)
    
    def get_league_rules_json_schema(self) -> Dict[str, Any]:
        """
        Get the JSON schema for league rules validation.
        
        Returns:
            JSON schema dictionary
        """
        return LeagueRulesSchema.model_json_schema()
    
    def normalize_mfl_rules(self, mfl_rules: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalize MFL-specific rules to canonical format.
        
        Args:
            mfl_rules: Raw MFL rules dictionary
            
        Returns:
            Normalized rules dictionary
        """
        # This would contain MFL-specific normalization logic
        # For now, return a basic structure
        normalized = {
            "league_type": "redraft",
            "scoring_type": "standard",
            "scoring_rules": {},
            "roster_slots": [],
            "waiver_rules": {},
            "trade_rules": {},
        }
        
        # Add MFL-specific parsing logic here
        if "scoring" in mfl_rules:
            normalized["scoring_rules"] = self._normalize_mfl_scoring(mfl_rules["scoring"])
        
        if "roster" in mfl_rules:
            normalized["roster_slots"] = self._normalize_mfl_roster(mfl_rules["roster"])
        
        return normalized
    
    def _normalize_mfl_scoring(self, mfl_scoring: Dict[str, Any]) -> Dict[str, float]:
        """Normalize MFL scoring rules."""
        # MFL scoring normalization logic
        return {
            "passing_yards": float(mfl_scoring.get("passYds", 0.04)),
            "passing_touchdowns": float(mfl_scoring.get("passTD", 4.0)),
            "rushing_yards": float(mfl_scoring.get("rushYds", 0.1)),
            "rushing_touchdowns": float(mfl_scoring.get("rushTD", 6.0)),
            "receiving_yards": float(mfl_scoring.get("recYds", 0.1)),
            "receiving_touchdowns": float(mfl_scoring.get("recTD", 6.0)),
            "receptions": float(mfl_scoring.get("rec", 0.0)),
        }
    
    def _normalize_mfl_roster(self, mfl_roster: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Normalize MFL roster configuration."""
        # MFL roster normalization logic
        slots = []
        
        # Add basic starting lineup
        slots.extend([
            {"position": "QB", "count": 1, "type": "starting"},
            {"position": "RB", "count": 2, "type": "starting"},
            {"position": "WR", "count": 2, "type": "starting"},
            {"position": "TE", "count": 1, "type": "starting"},
            {"position": "FLEX", "count": 1, "type": "starting", "eligible_positions": ["RB", "WR", "TE"]},
            {"position": "K", "count": 1, "type": "starting"},
            {"position": "DEF", "count": 1, "type": "starting"},
            {"position": "BENCH", "count": 6, "type": "bench"},
        ])
        
        return slots