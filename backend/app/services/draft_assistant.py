"""
Draft assistance service for fantasy football draft recommendations.

This service implements tiered player boards, Monte Carlo simulations,
real-time draft recommendations, and contingency planning.
"""
from typing import Dict, List, Optional, Tuple, Any, Set
from decimal import Decimal
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass, field
from collections import defaultdict
import statistics
import math
import random
from enum import Enum

from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc

from ..models.player import Player, PlayerPosition
from ..models.projection import Projection
from ..models.ranking import Ranking
from ..models.league import League
from ..models.roster import Fr<PERSON><PERSON><PERSON>, Roster, RosterPlayer
from ..models.recommendation import Recommendation, RecommendationType, RecommendationPriority
from ..services.projections_aggregator import ProjectionsAggregator, AggregatedProjection
from ..core.database import get_db

logger = logging.getLogger(__name__)


class DraftStrategy(str, Enum):
    """Draft strategy types."""
    VALUE_BASED = "VALUE_BASED"
    POSITIONAL_NEED = "POSITIONAL_NEED"
    BEST_AVAILABLE = "BEST_AVAILABLE"
    ZERO_RB = "ZERO_RB"
    ROBUST_RB = "ROBUST_RB"


@dataclass
class PlayerTier:
    """Represents a tier of players with similar value."""
    tier_number: int
    position: PlayerPosition
    players: List[str] = field(default_factory=list)
    min_value: Decimal = Decimal('0')
    max_value: Decimal = Decimal('0')
    avg_value: Decimal = Decimal('0')
    tier_break_threshold: Decimal = Decimal('0.5')  # Value drop to next tier


@dataclass
class DraftBoard:
    """Represents a tiered draft board with player rankings."""
    tiers: List[PlayerTier]
    overall_rankings: List[str]  # Player IDs in draft order
    position_rankings: Dict[PlayerPosition, List[str]]
    value_over_replacement: Dict[str, Decimal]
    last_updated: datetime
    strategy: DraftStrategy


@dataclass
class DraftPick:
    """Represents a draft pick in a simulation or actual draft."""
    round_number: int
    pick_number: int
    overall_pick: int
    franchise_id: str
    player_id: Optional[str] = None
    timestamp: Optional[datetime] = None


@dataclass
class DraftScenario:
    """Represents a draft scenario from Monte Carlo simulation."""
    scenario_id: str
    picks: List[DraftPick]
    final_roster: Dict[PlayerPosition, List[str]]
    projected_points: Decimal
    win_probability: Decimal
    strategy_score: Decimal


@dataclass
class DraftRecommendation:
    """Represents a draft pick recommendation."""
    player_id: str
    player_name: str
    position: PlayerPosition
    team: str
    projected_points: Decimal
    value_over_replacement: Decimal
    tier: int
    confidence: Decimal
    rationale: str
    alternatives: List[str]  # Alternative player IDs
    positional_need_score: Decimal
    opportunity_cost: Decimal


class DraftAssistant:
    """
    Service for providing draft assistance including tiered boards,
    Monte Carlo simulations, and real-time recommendations.
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.projections_aggregator = ProjectionsAggregator(db)
        self._replacement_levels_cache: Dict[str, Dict[PlayerPosition, Decimal]] = {}
        self._draft_boards_cache: Dict[str, DraftBoard] = {}
        self._cache_expiry: Optional[datetime] = None
        self._cache_duration = timedelta(hours=2)
    
    def generate_draft_board(
        self,
        league_id: str,
        season: int = 2024,
        strategy: DraftStrategy = DraftStrategy.VALUE_BASED,
        force_refresh: bool = False
    ) -> DraftBoard:
        """
        Generate a tiered draft board based on value over replacement.
        
        Args:
            league_id: League identifier
            season: Season year
            strategy: Draft strategy to use
            force_refresh: Force regeneration of cached board
            
        Returns:
            DraftBoard with tiered player rankings
        """
        cache_key = f"{league_id}_{season}_{strategy.value}"
        
        # Check cache
        if (not force_refresh and 
            cache_key in self._draft_boards_cache and
            self._cache_expiry and 
            datetime.utcnow() < self._cache_expiry):
            return self._draft_boards_cache[cache_key]
        
        logger.info(f"Generating draft board for league {league_id}, strategy {strategy}")
        
        # Get league configuration
        league = self.db.query(League).filter(League.id == league_id).first()
        if not league:
            raise ValueError(f"League {league_id} not found")
        
        # Get all available players with projections
        available_players = self._get_available_players(league_id, season)
        
        # Calculate replacement levels for each position
        replacement_levels = self._calculate_replacement_levels(league, available_players, season)
        
        # Calculate value over replacement for all players
        vor_values = self._calculate_value_over_replacement(
            available_players, replacement_levels, season
        )
        
        # Generate tiers based on value drops
        tiers = self._generate_player_tiers(vor_values, available_players)
        
        # Create overall and positional rankings
        overall_rankings = self._create_overall_rankings(vor_values, strategy)
        position_rankings = self._create_position_rankings(vor_values, available_players)
        
        # Create draft board
        draft_board = DraftBoard(
            tiers=tiers,
            overall_rankings=overall_rankings,
            position_rankings=position_rankings,
            value_over_replacement=vor_values,
            last_updated=datetime.utcnow(),
            strategy=strategy
        )
        
        # Cache the result
        self._draft_boards_cache[cache_key] = draft_board
        self._cache_expiry = datetime.utcnow() + self._cache_duration
        
        return draft_board
    
    def get_draft_recommendation(
        self,
        league_id: str,
        franchise_id: str,
        available_players: List[str],
        current_pick: int,
        season: int = 2024,
        strategy: DraftStrategy = DraftStrategy.VALUE_BASED
    ) -> DraftRecommendation:
        """
        Get real-time draft recommendation for a specific pick.
        
        Args:
            league_id: League identifier
            franchise_id: Franchise making the pick
            available_players: List of available player IDs
            current_pick: Current overall pick number
            season: Season year
            strategy: Draft strategy to use
            
        Returns:
            DraftRecommendation for the current pick
        """
        logger.info(f"Getting draft recommendation for franchise {franchise_id}, pick {current_pick}")
        
        # Get current draft board
        draft_board = self.generate_draft_board(league_id, season, strategy)
        
        # Get current roster composition
        current_roster = self._get_current_roster_composition(franchise_id)
        
        # Filter available players from draft board
        available_from_board = [
            player_id for player_id in draft_board.overall_rankings
            if player_id in available_players
        ]
        
        if not available_from_board:
            raise ValueError("No available players found in draft board")
        
        # Calculate positional needs
        positional_needs = self._calculate_positional_needs(league_id, current_roster)
        
        # Get top recommendation
        top_player_id = available_from_board[0]
        player = self.db.query(Player).filter(Player.id == top_player_id).first()
        
        if not player:
            raise ValueError(f"Player {top_player_id} not found")
        
        # Calculate recommendation metrics
        vor_value = draft_board.value_over_replacement.get(top_player_id, Decimal('0'))
        tier = self._get_player_tier(top_player_id, draft_board.tiers)
        
        # Get aggregated projection
        projection = self.projections_aggregator.aggregate_projections(
            player_id=top_player_id,
            season=season,
            min_sources=1  # Allow single source for testing
        )
        projected_points = projection.projected_points if projection else Decimal('0')
        
        # Calculate positional need score
        positional_need_score = positional_needs.get(player.position, Decimal('0'))
        
        # Calculate opportunity cost (value drop to next best player)
        opportunity_cost = self._calculate_opportunity_cost(
            available_from_board, draft_board.value_over_replacement
        )
        
        # Generate rationale
        rationale = self._generate_recommendation_rationale(
            player, vor_value, tier, positional_need_score, opportunity_cost
        )
        
        # Get alternatives (next 3 best available players)
        alternatives = available_from_board[1:4]
        
        # Calculate confidence based on tier gap and positional need
        confidence = self._calculate_recommendation_confidence(
            vor_value, tier, positional_need_score, opportunity_cost
        )
        
        return DraftRecommendation(
            player_id=top_player_id,
            player_name=player.name,
            position=player.position,
            team=player.team,
            projected_points=projected_points,
            value_over_replacement=vor_value,
            tier=tier,
            confidence=confidence,
            rationale=rationale,
            alternatives=alternatives,
            positional_need_score=positional_need_score,
            opportunity_cost=opportunity_cost
        )
    
    def run_monte_carlo_simulation(
        self,
        league_id: str,
        franchise_id: str,
        num_simulations: int = 1000,
        season: int = 2024,
        strategy: DraftStrategy = DraftStrategy.VALUE_BASED
    ) -> List[DraftScenario]:
        """
        Run Monte Carlo simulation for draft scenarios.
        
        Args:
            league_id: League identifier
            franchise_id: Franchise to simulate for
            num_simulations: Number of simulations to run
            season: Season year
            strategy: Draft strategy to use
            
        Returns:
            List of DraftScenario objects representing possible outcomes
        """
        logger.info(f"Running {num_simulations} Monte Carlo simulations for franchise {franchise_id}")
        
        # Get league and franchise information
        league = self.db.query(League).filter(League.id == league_id).first()
        franchise = self.db.query(Franchise).filter(Franchise.id == franchise_id).first()
        
        if not league or not franchise:
            raise ValueError("League or franchise not found")
        
        # Get draft order and available players
        draft_order = self._get_draft_order(league_id)
        available_players = self._get_available_players(league_id, season)
        draft_board = self.generate_draft_board(league_id, season, strategy)
        
        scenarios = []
        
        for sim_id in range(num_simulations):
            scenario = self._simulate_draft_scenario(
                f"sim_{sim_id}",
                league,
                franchise,
                draft_order,
                available_players.copy(),
                draft_board,
                strategy
            )
            scenarios.append(scenario)
        
        # Sort scenarios by projected value
        scenarios.sort(key=lambda s: s.strategy_score, reverse=True)
        
        return scenarios
    
    def generate_contingency_plans(
        self,
        league_id: str,
        franchise_id: str,
        target_players: List[str],
        season: int = 2024
    ) -> Dict[str, List[DraftRecommendation]]:
        """
        Generate contingency plans for different draft outcomes.
        
        Args:
            league_id: League identifier
            franchise_id: Franchise identifier
            target_players: List of target player IDs
            season: Season year
            
        Returns:
            Dictionary mapping scenarios to recommendation lists
        """
        logger.info(f"Generating contingency plans for franchise {franchise_id}")
        
        contingency_plans = {}
        
        # Get current available players
        available_players = self._get_available_players(league_id, season)
        available_ids = [p.id for p in available_players]
        
        # Generate plans for each target player being available/unavailable
        for target_player_id in target_players:
            # Scenario 1: Target player is available
            if target_player_id in available_ids:
                available_with_target = available_ids.copy()
                plan_with_target = self._generate_draft_plan(
                    league_id, franchise_id, available_with_target, season, target_player_id
                )
                contingency_plans[f"with_{target_player_id}"] = plan_with_target
            
            # Scenario 2: Target player is not available
            available_without_target = [pid for pid in available_ids if pid != target_player_id]
            plan_without_target = self._generate_draft_plan(
                league_id, franchise_id, available_without_target, season
            )
            contingency_plans[f"without_{target_player_id}"] = plan_without_target
        
        return contingency_plans
    
    def update_draft_board_real_time(
        self,
        league_id: str,
        picked_player_id: str,
        season: int = 2024
    ) -> DraftBoard:
        """
        Update draft board in real-time after a pick is made.
        
        Args:
            league_id: League identifier
            picked_player_id: Player ID that was just picked
            season: Season year
            
        Returns:
            Updated DraftBoard
        """
        logger.info(f"Updating draft board after pick: {picked_player_id}")
        
        # Add picked player to a roster to make them unavailable
        # This is a simplified approach for the draft assistant
        # In a real implementation, this would be handled by the draft management system
        
        # Invalidate cache to force refresh
        cache_keys_to_remove = [
            key for key in self._draft_boards_cache.keys()
            if key.startswith(f"{league_id}_{season}")
        ]
        
        for key in cache_keys_to_remove:
            self._draft_boards_cache.pop(key, None)
        
        # Generate fresh draft board
        return self.generate_draft_board(league_id, season, force_refresh=True) 
   
    def _get_available_players(self, league_id: str, season: int) -> List[Player]:
        """Get all available players for drafting."""
        # Get all players not currently on any roster in this league
        rostered_player_ids = self.db.query(RosterPlayer.player_id).join(
            Roster
        ).join(
            Franchise
        ).filter(
            and_(
                Franchise.league_id == league_id,
                RosterPlayer.is_active == True
            )
        ).subquery()
        
        available_players = self.db.query(Player).filter(
            and_(
                ~Player.id.in_(rostered_player_ids),
                Player.position.in_([pos.value for pos in PlayerPosition])
            )
        ).all()
        
        return available_players
    
    def _calculate_replacement_levels(
        self,
        league: League,
        available_players: List[Player],
        season: int
    ) -> Dict[PlayerPosition, Decimal]:
        """Calculate replacement level values for each position."""
        cache_key = f"{league.id}_{season}"
        
        if cache_key in self._replacement_levels_cache:
            return self._replacement_levels_cache[cache_key]
        
        replacement_levels = {}
        
        # Get roster slot requirements from league configuration
        roster_slots = league.roster_slots
        
        for position in PlayerPosition:
            # Calculate how many players at this position will be drafted
            position_slots = sum(
                slot.get('count', 1) 
                for slot in roster_slots 
                if slot.get('position') == position.value or 
                   slot.get('position') == 'FLEX' and position in [PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE]
            )
            
            # Add flex consideration
            flex_slots = sum(
                slot.get('count', 1)
                for slot in roster_slots
                if slot.get('position') == 'FLEX'
            )
            
            # Estimate total players drafted at position (including bench)
            num_franchises = len(league.franchises) if league.franchises else 12  # Default to 12
            bench_multiplier = 1.5  # Assume 50% more players drafted than starting slots
            
            total_drafted = int((position_slots + flex_slots * 0.3) * num_franchises * bench_multiplier)
            
            # Get projections for players at this position
            position_players = [p for p in available_players if p.position == position]
            position_projections = []
            
            for player in position_players:
                projection = self.projections_aggregator.aggregate_projections(
                    player_id=player.id,
                    season=season
                )
                if projection:
                    position_projections.append(projection.projected_points)
            
            if not position_projections:
                replacement_levels[position] = Decimal('0')
                continue
            
            # Sort projections in descending order
            position_projections.sort(reverse=True)
            
            # Replacement level is the projection of the player at the replacement threshold
            replacement_index = min(total_drafted, len(position_projections) - 1)
            replacement_level = position_projections[replacement_index] if replacement_index >= 0 else Decimal('0')
            
            replacement_levels[position] = replacement_level
        
        self._replacement_levels_cache[cache_key] = replacement_levels
        return replacement_levels
    
    def _calculate_value_over_replacement(
        self,
        available_players: List[Player],
        replacement_levels: Dict[PlayerPosition, Decimal],
        season: int
    ) -> Dict[str, Decimal]:
        """Calculate value over replacement for all players."""
        vor_values = {}
        
        for player in available_players:
            projection = self.projections_aggregator.aggregate_projections(
                player_id=player.id,
                season=season,
                min_sources=1  # Allow single source for testing
            )
            
            if not projection:
                vor_values[player.id] = Decimal('0')
                continue
            
            replacement_level = replacement_levels.get(player.position, Decimal('0'))
            vor_value = max(Decimal('0'), projection.projected_points - replacement_level)
            vor_values[player.id] = vor_value
        
        return vor_values
    
    def _generate_player_tiers(
        self,
        vor_values: Dict[str, Decimal],
        available_players: List[Player]
    ) -> List[PlayerTier]:
        """Generate player tiers based on value drops."""
        tiers = []
        
        # Group players by position
        position_groups = defaultdict(list)
        for player in available_players:
            if player.id in vor_values:
                position_groups[player.position].append(player.id)
        
        tier_number = 1
        
        for position, player_ids in position_groups.items():
            # Sort players by VOR value
            sorted_players = sorted(
                player_ids,
                key=lambda pid: vor_values[pid],
                reverse=True
            )
            
            if not sorted_players:
                continue
            
            # Create tiers based on value drops
            current_tier_players = []
            current_tier_start_value = vor_values[sorted_players[0]]
            
            for i, player_id in enumerate(sorted_players):
                player_value = vor_values[player_id]
                
                # Check if we should start a new tier
                if current_tier_players and (
                    current_tier_start_value - player_value > Decimal('2.0') or  # Significant value drop
                    len(current_tier_players) >= 6  # Max tier size
                ):
                    # Create tier for current group
                    tier_values = [vor_values[pid] for pid in current_tier_players]
                    tier = PlayerTier(
                        tier_number=tier_number,
                        position=position,
                        players=current_tier_players.copy(),
                        min_value=min(tier_values),
                        max_value=max(tier_values),
                        avg_value=Decimal(str(statistics.mean(float(v) for v in tier_values))),
                        tier_break_threshold=current_tier_start_value - player_value
                    )
                    tiers.append(tier)
                    
                    # Start new tier
                    tier_number += 1
                    current_tier_players = [player_id]
                    current_tier_start_value = player_value
                else:
                    current_tier_players.append(player_id)
            
            # Add final tier if there are remaining players
            if current_tier_players:
                tier_values = [vor_values[pid] for pid in current_tier_players]
                tier = PlayerTier(
                    tier_number=tier_number,
                    position=position,
                    players=current_tier_players,
                    min_value=min(tier_values),
                    max_value=max(tier_values),
                    avg_value=Decimal(str(statistics.mean(float(v) for v in tier_values)))
                )
                tiers.append(tier)
                tier_number += 1
        
        return tiers
    
    def _create_overall_rankings(
        self,
        vor_values: Dict[str, Decimal],
        strategy: DraftStrategy
    ) -> List[str]:
        """Create overall player rankings based on strategy."""
        if strategy == DraftStrategy.VALUE_BASED:
            # Pure value-based ranking
            return sorted(vor_values.keys(), key=lambda pid: vor_values[pid], reverse=True)
        
        elif strategy == DraftStrategy.BEST_AVAILABLE:
            # Similar to value-based but with slight position adjustments
            return sorted(vor_values.keys(), key=lambda pid: vor_values[pid], reverse=True)
        
        else:
            # For other strategies, use value-based as baseline
            return sorted(vor_values.keys(), key=lambda pid: vor_values[pid], reverse=True)
    
    def _create_position_rankings(
        self,
        vor_values: Dict[str, Decimal],
        available_players: List[Player]
    ) -> Dict[PlayerPosition, List[str]]:
        """Create position-specific rankings."""
        position_rankings = {}
        
        # Group players by position
        position_groups = defaultdict(list)
        for player in available_players:
            if player.id in vor_values:
                position_groups[player.position].append(player.id)
        
        # Sort each position by VOR value
        for position, player_ids in position_groups.items():
            sorted_players = sorted(
                player_ids,
                key=lambda pid: vor_values[pid],
                reverse=True
            )
            position_rankings[position] = sorted_players
        
        return position_rankings
    
    def _get_current_roster_composition(self, franchise_id: str) -> Dict[PlayerPosition, int]:
        """Get current roster composition by position."""
        roster_composition = defaultdict(int)
        
        # Get current roster
        roster = self.db.query(Roster).filter(Roster.franchise_id == franchise_id).first()
        
        if roster:
            for roster_player in roster.get_active_players():
                roster_composition[roster_player.player.position] += 1
        
        return dict(roster_composition)
    
    def _calculate_positional_needs(
        self,
        league_id: str,
        current_roster: Dict[PlayerPosition, int]
    ) -> Dict[PlayerPosition, Decimal]:
        """Calculate positional need scores."""
        league = self.db.query(League).filter(League.id == league_id).first()
        if not league:
            return {}
        
        positional_needs = {}
        
        for position in PlayerPosition:
            # Get required starters for this position
            required_starters = sum(
                slot.get('count', 1)
                for slot in league.roster_slots
                if slot.get('position') == position.value
            )
            
            # Add flex consideration
            if position in [PlayerPosition.RB, PlayerPosition.WR, PlayerPosition.TE]:
                flex_slots = sum(
                    slot.get('count', 1)
                    for slot in league.roster_slots
                    if slot.get('position') == 'FLEX'
                )
                required_starters += flex_slots * 0.5  # Assume 50% chance of using flex
            
            current_count = current_roster.get(position, 0)
            
            # Calculate need score (higher = more needed)
            if current_count < required_starters:
                need_score = Decimal(str(required_starters - current_count))
            else:
                # Still some value in depth, but diminishing returns
                need_score = Decimal('0.1')
            
            positional_needs[position] = need_score
        
        return positional_needs
    
    def _get_player_tier(self, player_id: str, tiers: List[PlayerTier]) -> int:
        """Get the tier number for a specific player."""
        for tier in tiers:
            if player_id in tier.players:
                return tier.tier_number
        return 999  # Default high tier number if not found
    
    def _calculate_opportunity_cost(
        self,
        available_players: List[str],
        vor_values: Dict[str, Decimal]
    ) -> Decimal:
        """Calculate opportunity cost of taking the top player."""
        if len(available_players) < 2:
            return Decimal('0')
        
        top_value = vor_values.get(available_players[0], Decimal('0'))
        second_value = vor_values.get(available_players[1], Decimal('0'))
        
        return max(Decimal('0'), top_value - second_value)
    
    def _generate_recommendation_rationale(
        self,
        player: Player,
        vor_value: Decimal,
        tier: int,
        positional_need_score: Decimal,
        opportunity_cost: Decimal
    ) -> str:
        """Generate human-readable rationale for recommendation."""
        rationale_parts = []
        
        # Player introduction
        rationale_parts.append(f"{player.name} ({player.position.value}, {player.team})")
        
        # Value assessment
        if vor_value > Decimal('10'):
            rationale_parts.append("offers exceptional value over replacement")
        elif vor_value > Decimal('5'):
            rationale_parts.append("provides strong value over replacement")
        else:
            rationale_parts.append("has moderate value over replacement")
        
        # Tier information
        if tier <= 2:
            rationale_parts.append(f"and is in the top tier (Tier {tier}) at {player.position.value}")
        else:
            rationale_parts.append(f"and is in Tier {tier} at {player.position.value}")
        
        # Positional need
        if positional_need_score > Decimal('1'):
            rationale_parts.append(f"This addresses a significant positional need")
        elif positional_need_score > Decimal('0.5'):
            rationale_parts.append(f"This helps fill a positional need")
        
        # Opportunity cost
        if opportunity_cost > Decimal('3'):
            rationale_parts.append("Taking this player now avoids significant value loss")
        
        return ". ".join(rationale_parts) + "."
    
    def _calculate_recommendation_confidence(
        self,
        vor_value: Decimal,
        tier: int,
        positional_need_score: Decimal,
        opportunity_cost: Decimal
    ) -> Decimal:
        """Calculate confidence score for recommendation."""
        confidence = Decimal('0.5')  # Base confidence
        
        # Adjust for value
        if vor_value > Decimal('10'):
            confidence += Decimal('0.3')
        elif vor_value > Decimal('5'):
            confidence += Decimal('0.2')
        elif vor_value > Decimal('2'):
            confidence += Decimal('0.1')
        
        # Adjust for tier
        if tier <= 2:
            confidence += Decimal('0.2')
        elif tier <= 4:
            confidence += Decimal('0.1')
        
        # Adjust for positional need
        if positional_need_score > Decimal('1'):
            confidence += Decimal('0.1')
        
        # Adjust for opportunity cost
        if opportunity_cost > Decimal('3'):
            confidence += Decimal('0.1')
        
        return min(Decimal('1.0'), max(Decimal('0.1'), confidence))
    
    def _get_draft_order(self, league_id: str) -> List[str]:
        """Get draft order for the league."""
        # Get all franchises in the league
        franchises = self.db.query(Franchise).filter(
            Franchise.league_id == league_id
        ).order_by(Franchise.id).all()  # Simple ordering for now
        
        return [f.id for f in franchises]
    
    def _simulate_draft_scenario(
        self,
        scenario_id: str,
        league: League,
        target_franchise: Franchise,
        draft_order: List[str],
        available_players: List[Player],
        draft_board: DraftBoard,
        strategy: DraftStrategy
    ) -> DraftScenario:
        """Simulate a single draft scenario."""
        picks = []
        available_ids = [p.id for p in available_players]
        franchise_rosters = defaultdict(list)
        
        # Simulate draft rounds (assume 16 rounds for now)
        num_rounds = 16
        num_franchises = len(draft_order)
        
        for round_num in range(1, num_rounds + 1):
            # Snake draft: reverse order on even rounds
            current_order = draft_order if round_num % 2 == 1 else draft_order[::-1]
            
            for pick_in_round, franchise_id in enumerate(current_order, 1):
                overall_pick = (round_num - 1) * num_franchises + pick_in_round
                
                if not available_ids:
                    break
                
                # Simulate pick based on strategy and randomness
                if franchise_id == target_franchise.id:
                    # Use our strategy for target franchise
                    picked_player_id = self._simulate_strategic_pick(
                        franchise_id, available_ids, draft_board, strategy, franchise_rosters[franchise_id]
                    )
                else:
                    # Simulate other franchises with some randomness
                    picked_player_id = self._simulate_opponent_pick(
                        available_ids, draft_board
                    )
                
                # Record the pick
                pick = DraftPick(
                    round_number=round_num,
                    pick_number=pick_in_round,
                    overall_pick=overall_pick,
                    franchise_id=franchise_id,
                    player_id=picked_player_id
                )
                picks.append(pick)
                
                # Update available players and rosters
                available_ids.remove(picked_player_id)
                franchise_rosters[franchise_id].append(picked_player_id)
        
        # Calculate final roster composition and projected points
        final_roster = self._organize_roster_by_position(
            franchise_rosters[target_franchise.id], available_players
        )
        
        projected_points = self._calculate_roster_projected_points(
            franchise_rosters[target_franchise.id]
        )
        
        # Calculate win probability (simplified)
        win_probability = min(Decimal('0.95'), projected_points / Decimal('1800'))
        
        # Calculate strategy score
        strategy_score = self._calculate_strategy_score(
            franchise_rosters[target_franchise.id], draft_board, strategy
        )
        
        return DraftScenario(
            scenario_id=scenario_id,
            picks=picks,
            final_roster=final_roster,
            projected_points=projected_points,
            win_probability=win_probability,
            strategy_score=strategy_score
        )
    
    def _simulate_strategic_pick(
        self,
        franchise_id: str,
        available_ids: List[str],
        draft_board: DraftBoard,
        strategy: DraftStrategy,
        current_roster: List[str]
    ) -> str:
        """Simulate a strategic pick for our target franchise."""
        # Get available players from draft board in order
        available_from_board = [
            player_id for player_id in draft_board.overall_rankings
            if player_id in available_ids
        ]
        
        if not available_from_board:
            return available_ids[0]  # Fallback
        
        if strategy == DraftStrategy.VALUE_BASED:
            # Take best available value
            return available_from_board[0]
        
        elif strategy == DraftStrategy.POSITIONAL_NEED:
            # Consider positional needs
            current_composition = self._get_roster_composition_from_ids(current_roster)
            positional_needs = self._calculate_positional_needs_simple(current_composition)
            
            # Find best player that fills a need
            for player_id in available_from_board:
                player = self.db.query(Player).filter(Player.id == player_id).first()
                if player and positional_needs.get(player.position, 0) > 0:
                    return player_id
            
            # Fallback to best available
            return available_from_board[0]
        
        else:
            # Default to best available
            return available_from_board[0]
    
    def _simulate_opponent_pick(
        self,
        available_ids: List[str],
        draft_board: DraftBoard
    ) -> str:
        """Simulate an opponent's pick with some randomness."""
        # Get top available players from draft board
        available_from_board = [
            player_id for player_id in draft_board.overall_rankings
            if player_id in available_ids
        ]
        
        if not available_from_board:
            return random.choice(available_ids)
        
        # Add some randomness - 70% chance of taking top 3, 30% chance of surprise pick
        if random.random() < 0.7:
            top_choices = available_from_board[:3]
            return random.choice(top_choices)
        else:
            # Surprise pick from top 10
            surprise_choices = available_from_board[:10]
            return random.choice(surprise_choices)
    
    def _organize_roster_by_position(
        self,
        player_ids: List[str],
        available_players: List[Player]
    ) -> Dict[PlayerPosition, List[str]]:
        """Organize roster by position."""
        roster_by_position = defaultdict(list)
        
        player_lookup = {p.id: p for p in available_players}
        
        for player_id in player_ids:
            player = player_lookup.get(player_id)
            if player:
                roster_by_position[player.position].append(player_id)
        
        return dict(roster_by_position)
    
    def _calculate_roster_projected_points(self, player_ids: List[str]) -> Decimal:
        """Calculate total projected points for a roster."""
        total_points = Decimal('0')
        
        for player_id in player_ids:
            projection = self.projections_aggregator.aggregate_projections(
                player_id=player_id,
                season=2024,
                min_sources=1  # Allow single source for testing
            )
            if projection:
                total_points += projection.projected_points
        
        return total_points
    
    def _calculate_strategy_score(
        self,
        player_ids: List[str],
        draft_board: DraftBoard,
        strategy: DraftStrategy
    ) -> Decimal:
        """Calculate how well the roster aligns with the strategy."""
        total_vor = sum(
            draft_board.value_over_replacement.get(player_id, Decimal('0'))
            for player_id in player_ids
        )
        
        # Strategy-specific adjustments could be added here
        return total_vor
    
    def _get_roster_composition_from_ids(self, player_ids: List[str]) -> Dict[PlayerPosition, int]:
        """Get roster composition from player IDs."""
        composition = defaultdict(int)
        
        for player_id in player_ids:
            player = self.db.query(Player).filter(Player.id == player_id).first()
            if player:
                composition[player.position] += 1
        
        return dict(composition)
    
    def _calculate_positional_needs_simple(
        self,
        current_composition: Dict[PlayerPosition, int]
    ) -> Dict[PlayerPosition, int]:
        """Calculate simple positional needs."""
        # Basic positional requirements
        requirements = {
            PlayerPosition.QB: 2,
            PlayerPosition.RB: 4,
            PlayerPosition.WR: 4,
            PlayerPosition.TE: 2,
            PlayerPosition.K: 1,
            PlayerPosition.DEF: 1
        }
        
        needs = {}
        for position, required in requirements.items():
            current = current_composition.get(position, 0)
            needs[position] = max(0, required - current)
        
        return needs
    
    def _generate_draft_plan(
        self,
        league_id: str,
        franchise_id: str,
        available_player_ids: List[str],
        season: int,
        priority_player_id: Optional[str] = None
    ) -> List[DraftRecommendation]:
        """Generate a draft plan for contingency scenarios."""
        draft_board = self.generate_draft_board(league_id, season)
        
        # Filter available players from draft board
        available_from_board = [
            player_id for player_id in draft_board.overall_rankings
            if player_id in available_player_ids
        ]
        
        recommendations = []
        
        # If priority player is available, recommend them first
        if priority_player_id and priority_player_id in available_from_board:
            rec = self._create_recommendation_for_player(
                priority_player_id, draft_board, league_id, franchise_id, season
            )
            if rec:
                recommendations.append(rec)
        
        # Add next best available players
        for player_id in available_from_board[:5]:  # Top 5 recommendations
            if player_id != priority_player_id:
                rec = self._create_recommendation_for_player(
                    player_id, draft_board, league_id, franchise_id, season
                )
                if rec:
                    recommendations.append(rec)
        
        return recommendations
    
    def _create_recommendation_for_player(
        self,
        player_id: str,
        draft_board: DraftBoard,
        league_id: str,
        franchise_id: str,
        season: int
    ) -> Optional[DraftRecommendation]:
        """Create a draft recommendation for a specific player."""
        player = self.db.query(Player).filter(Player.id == player_id).first()
        if not player:
            return None
        
        vor_value = draft_board.value_over_replacement.get(player_id, Decimal('0'))
        tier = self._get_player_tier(player_id, draft_board.tiers)
        
        projection = self.projections_aggregator.aggregate_projections(
            player_id=player_id,
            season=season,
            min_sources=1  # Allow single source for testing
        )
        projected_points = projection.projected_points if projection else Decimal('0')
        
        current_roster = self._get_current_roster_composition(franchise_id)
        positional_needs = self._calculate_positional_needs(league_id, current_roster)
        positional_need_score = positional_needs.get(player.position, Decimal('0'))
        
        # Get alternatives from same tier or next tier
        alternatives = []
        for tier_obj in draft_board.tiers:
            if tier_obj.tier_number == tier or tier_obj.tier_number == tier + 1:
                alternatives.extend([pid for pid in tier_obj.players if pid != player_id][:3])
        
        rationale = self._generate_recommendation_rationale(
            player, vor_value, tier, positional_need_score, Decimal('0')
        )
        
        confidence = self._calculate_recommendation_confidence(
            vor_value, tier, positional_need_score, Decimal('0')
        )
        
        return DraftRecommendation(
            player_id=player_id,
            player_name=player.name,
            position=player.position,
            team=player.team,
            projected_points=projected_points,
            value_over_replacement=vor_value,
            tier=tier,
            confidence=confidence,
            rationale=rationale,
            alternatives=alternatives,
            positional_need_score=positional_need_score,
            opportunity_cost=Decimal('0')
        )