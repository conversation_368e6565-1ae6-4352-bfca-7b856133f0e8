"""
Sleeper client for ADP in PPR format.
"""
from __future__ import annotations

import httpx
from typing import Any, Dict, List

class SleeperClient:
    def __init__(self, timeout: float = 15.0) -> None:
        self.timeout = timeout
        self.base_url = "https://api.sleeper.app"

    async def get_adp_ppr(self, season: int) -> List[Dict[str, Any]]:
        # Sleeper ADP endpoints can vary; use a stable v1 ADP endpoint where possible.
        # Example: /v1/players/nfl/adp?season=2025&season_type=regular&scoring=ppr
        # Fallback to known community endpoints if needed.
        params = {
            "season": season,
            "season_type": "regular",
            "scoring": "ppr",
        }
        url = f"{self.base_url}/v1/players/nfl/adp"
        async with httpx.AsyncClient(timeout=self.timeout, follow_redirects=True) as client:
            resp = await client.get(url, params=params)
            resp.raise_for_status()
            try:
                data = resp.json()
            except ValueError:
                raise httpx.HTTPError("Sleep<PERSON> returned non-JSON for ADP")
        # Expect a list of dicts with at least { player_id, adp, position, team }
        if not isinstance(data, list):
            return []
        return data

