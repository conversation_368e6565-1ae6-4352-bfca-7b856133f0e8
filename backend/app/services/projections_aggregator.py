"""
Projections aggregation service for combining multiple projection sources.

This service implements weighted ensemble algorithms, backtesting frameworks,
and confidence interval calculations for projection uncertainty.
"""
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from collections import defaultdict
import statistics
import math

from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from ..models.projection import Projection
from ..models.player import Player
from ..core.database import get_db

logger = logging.getLogger(__name__)


@dataclass
class AggregatedProjection:
    """Represents an aggregated projection from multiple sources."""
    player_id: str
    week: Optional[int]
    season: int
    projected_points: Decimal
    confidence_interval: Tuple[Decimal, Decimal]
    variance: Decimal
    source_count: int
    source_weights: Dict[str, Decimal]
    metadata: Dict[str, Any]


@dataclass
class BacktestResult:
    """Results from backtesting projection accuracy."""
    source: str
    mae: float  # Mean Absolute Error
    rmse: float  # Root Mean Square Error
    accuracy_score: float  # Custom accuracy metric
    sample_size: int
    optimal_weight: float


class ProjectionsAggregator:
    """
    Service for aggregating projections from multiple sources using weighted ensembles.
    """
    
    def __init__(self, db: Session):
        self.db = db
        self._source_weights_cache: Dict[str, Dict[str, float]] = {}
        self._cache_expiry: Optional[datetime] = None
        self._cache_duration = timedelta(hours=6)  # Cache weights for 6 hours
    
    def aggregate_projections(
        self,
        player_id: str,
        week: Optional[int] = None,
        season: int = 2024,
        min_sources: int = 2
    ) -> Optional[AggregatedProjection]:
        """
        Aggregate projections for a specific player and time period.
        
        Args:
            player_id: Player identifier
            week: Week number (None for season-long)
            season: Season year
            min_sources: Minimum number of sources required for aggregation
            
        Returns:
            AggregatedProjection or None if insufficient sources
        """
        # Get all active projections for the player/week/season
        query = self.db.query(Projection).filter(
            and_(
                Projection.player_id == player_id,
                Projection.week == week,
                Projection.season == season,
                Projection.is_active == True
            )
        )
        
        projections = query.all()
        
        if len(projections) < min_sources:
            logger.warning(
                f"Insufficient projections for player {player_id}, week {week}: "
                f"found {len(projections)}, need {min_sources}"
            )
            return None
        
        # Get optimized weights for sources
        source_weights = self._get_optimized_weights(season)
        
        # Calculate weighted ensemble
        return self._calculate_weighted_ensemble(projections, source_weights)
    
    def aggregate_all_projections(
        self,
        week: Optional[int] = None,
        season: int = 2024,
        min_sources: int = 2
    ) -> List[AggregatedProjection]:
        """
        Aggregate projections for all players in a given week/season.
        
        Args:
            week: Week number (None for season-long)
            season: Season year
            min_sources: Minimum number of sources required for aggregation
            
        Returns:
            List of AggregatedProjection objects
        """
        # Get all players with projections for the specified period
        query = self.db.query(Projection.player_id).filter(
            and_(
                Projection.week == week,
                Projection.season == season,
                Projection.is_active == True
            )
        ).group_by(Projection.player_id).having(
            func.count(Projection.id) >= min_sources
        )
        
        player_ids = [row[0] for row in query.all()]
        
        aggregated_projections = []
        for player_id in player_ids:
            aggregated = self.aggregate_projections(
                player_id=player_id,
                week=week,
                season=season,
                min_sources=min_sources
            )
            if aggregated:
                aggregated_projections.append(aggregated)
        
        return aggregated_projections
    
    def _calculate_weighted_ensemble(
        self,
        projections: List[Projection],
        source_weights: Dict[str, float]
    ) -> AggregatedProjection:
        """
        Calculate weighted ensemble from multiple projections.
        
        Args:
            projections: List of Projection objects
            source_weights: Dictionary mapping source names to weights
            
        Returns:
            AggregatedProjection object
        """
        if not projections:
            raise ValueError("Cannot aggregate empty projection list")
        
        # Normalize weights for available sources
        available_sources = {p.source for p in projections}
        normalized_weights = self._normalize_weights(source_weights, available_sources)
        
        # Calculate weighted average
        weighted_sum = Decimal('0')
        total_weight = Decimal('0')
        
        # Collect values for confidence interval calculation
        values = []
        weights = []
        
        for projection in projections:
            weight = Decimal(str(normalized_weights.get(projection.source, 0.1)))
            weighted_sum += projection.projected_points * weight
            total_weight += weight
            
            values.append(float(projection.projected_points))
            weights.append(float(weight))
        
        if total_weight == 0:
            total_weight = Decimal('1')
        
        aggregated_points = weighted_sum / total_weight
        
        # Calculate confidence interval and variance
        confidence_interval, variance = self._calculate_confidence_interval(
            values, weights, float(aggregated_points)
        )
        
        # Create metadata
        metadata = {
            'aggregation_method': 'weighted_ensemble',
            'source_count': len(projections),
            'total_weight': float(total_weight),
            'aggregation_timestamp': datetime.utcnow().isoformat()
        }
        
        return AggregatedProjection(
            player_id=projections[0].player_id,
            week=projections[0].week,
            season=projections[0].season,
            projected_points=aggregated_points,
            confidence_interval=confidence_interval,
            variance=variance,
            source_count=len(projections),
            source_weights=normalized_weights,
            metadata=metadata
        )
    
    def _calculate_confidence_interval(
        self,
        values: List[float],
        weights: List[float],
        mean: float,
        confidence_level: float = 0.95
    ) -> Tuple[Tuple[Decimal, Decimal], Decimal]:
        """
        Calculate confidence interval and variance for aggregated projection.
        
        Args:
            values: List of projection values
            weights: List of corresponding weights
            mean: Weighted mean value
            confidence_level: Confidence level (default 0.95 for 95% CI)
            
        Returns:
            Tuple of (confidence_interval, variance)
        """
        if len(values) < 2:
            # Not enough data for meaningful confidence interval
            std_dev = abs(mean * 0.2)  # Assume 20% standard deviation
            margin = std_dev * 1.96  # 95% CI approximation
            return (
                (Decimal(str(max(0, mean - margin))), Decimal(str(mean + margin))),
                Decimal(str(std_dev ** 2))
            )
        
        # Calculate weighted variance
        total_weight = sum(weights)
        if total_weight == 0:
            weights = [1.0] * len(values)
            total_weight = len(values)
        
        # Normalize weights
        normalized_weights = [w / total_weight for w in weights]
        
        # Calculate weighted variance
        weighted_variance = sum(
            w * (v - mean) ** 2 
            for v, w in zip(values, normalized_weights)
        )
        
        # Adjust for effective sample size
        effective_n = 1 / sum(w ** 2 for w in normalized_weights)
        adjusted_variance = weighted_variance * len(values) / effective_n
        
        std_dev = math.sqrt(adjusted_variance)
        
        # Calculate confidence interval using t-distribution approximation
        # For simplicity, using normal approximation with z-score
        z_score = 1.96 if confidence_level == 0.95 else 2.576  # 99% CI
        margin = z_score * std_dev
        
        lower_bound = max(0, mean - margin)  # Fantasy points can't be negative
        upper_bound = mean + margin
        
        return (
            (Decimal(str(lower_bound)), Decimal(str(upper_bound))),
            Decimal(str(adjusted_variance))
        )
    
    def _normalize_weights(
        self,
        source_weights: Dict[str, float],
        available_sources: set
    ) -> Dict[str, float]:
        """
        Normalize weights for available sources.
        
        Args:
            source_weights: Dictionary of source weights
            available_sources: Set of available source names
            
        Returns:
            Dictionary of normalized weights
        """
        # Get weights for available sources
        available_weights = {
            source: source_weights.get(source, 0.1)
            for source in available_sources
        }
        
        # Normalize to sum to 1.0
        total_weight = sum(available_weights.values())
        if total_weight == 0:
            # Equal weights if no weights specified
            return {source: 1.0 / len(available_sources) for source in available_sources}
        
        return {
            source: weight / total_weight
            for source, weight in available_weights.items()
        }
    
    def _get_optimized_weights(self, season: int) -> Dict[str, float]:
        """
        Get optimized weights for projection sources, using cache if available.
        
        Args:
            season: Season year
            
        Returns:
            Dictionary mapping source names to optimized weights
        """
        cache_key = str(season)
        
        # Check cache
        if (self._cache_expiry and 
            datetime.utcnow() < self._cache_expiry and 
            cache_key in self._source_weights_cache):
            return self._source_weights_cache[cache_key]
        
        # Calculate optimized weights
        optimized_weights = self.optimize_source_weights(season)
        
        # Update cache
        self._source_weights_cache[cache_key] = optimized_weights
        self._cache_expiry = datetime.utcnow() + self._cache_duration
        
        return optimized_weights
    
    def optimize_source_weights(self, season: int) -> Dict[str, float]:
        """
        Optimize source weights using backtesting results.
        
        Args:
            season: Season year for optimization
            
        Returns:
            Dictionary mapping source names to optimized weights
        """
        # Run backtesting for all sources
        backtest_results = self.run_backtesting(season)
        
        if not backtest_results:
            logger.warning(f"No backtesting results for season {season}, using equal weights")
            # Get all unique sources for the season
            sources = self.db.query(Projection.source).filter(
                Projection.season == season
            ).distinct().all()
            source_names = [row[0] for row in sources]
            
            if not source_names:
                return {}
            
            equal_weight = 1.0 / len(source_names)
            return {source: equal_weight for source in source_names}
        
        # Convert accuracy scores to weights
        # Higher accuracy = higher weight
        total_accuracy = sum(result.accuracy_score for result in backtest_results)
        
        if total_accuracy == 0:
            # Fallback to equal weights
            equal_weight = 1.0 / len(backtest_results)
            return {result.source: equal_weight for result in backtest_results}
        
        optimized_weights = {
            result.source: result.accuracy_score / total_accuracy
            for result in backtest_results
        }
        
        logger.info(f"Optimized weights for season {season}: {optimized_weights}")
        return optimized_weights    

    def run_backtesting(
        self,
        season: int,
        weeks_back: int = 8,
        min_samples: int = 10
    ) -> List[BacktestResult]:
        """
        Run backtesting to evaluate projection source accuracy.
        
        Args:
            season: Season year
            weeks_back: Number of weeks to look back for testing
            min_samples: Minimum number of samples required per source
            
        Returns:
            List of BacktestResult objects
        """
        # For backtesting, we need actual results to compare against
        # This is a simplified implementation that compares projections
        # against each other to identify the most consistent sources
        
        results = []
        
        # Get all sources for the season
        sources_query = self.db.query(Projection.source).filter(
            Projection.season == season
        ).distinct()
        sources = [row[0] for row in sources_query.all()]
        
        for source in sources:
            backtest_result = self._backtest_source(
                source, season, weeks_back, min_samples
            )
            if backtest_result:
                results.append(backtest_result)
        
        return results
    
    def _backtest_source(
        self,
        source: str,
        season: int,
        weeks_back: int,
        min_samples: int
    ) -> Optional[BacktestResult]:
        """
        Backtest a specific projection source.
        
        Args:
            source: Source name to backtest
            season: Season year
            weeks_back: Number of weeks to look back
            min_samples: Minimum samples required
            
        Returns:
            BacktestResult or None if insufficient data
        """
        # Get projections from this source
        projections_query = self.db.query(Projection).filter(
            and_(
                Projection.source == source,
                Projection.season == season,
                Projection.week.isnot(None),  # Only weekly projections
                Projection.is_active == True
            )
        ).order_by(Projection.week.desc()).limit(weeks_back * 100)  # Rough limit
        
        projections = projections_query.all()
        
        if len(projections) < min_samples:
            logger.debug(f"Insufficient samples for source {source}: {len(projections)}")
            return None
        
        # For this implementation, we'll use projection consistency as a proxy
        # for accuracy (comparing against ensemble average)
        errors = []
        
        # Group projections by player and week
        projection_groups = defaultdict(list)
        for proj in projections:
            key = (proj.player_id, proj.week)
            projection_groups[key].append(proj)
        
        for (player_id, week), player_projections in projection_groups.items():
            if len(player_projections) < 2:  # Need multiple sources for comparison
                continue
            
            # Find this source's projection
            source_projection = None
            other_projections = []
            
            for proj in player_projections:
                if proj.source == source:
                    source_projection = proj
                else:
                    other_projections.append(proj)
            
            if not source_projection or not other_projections:
                continue
            
            # Calculate ensemble average from other sources
            other_avg = statistics.mean(
                float(proj.projected_points) for proj in other_projections
            )
            
            # Calculate error
            error = abs(float(source_projection.projected_points) - other_avg)
            errors.append(error)
        
        if len(errors) < min_samples:
            return None
        
        # Calculate metrics
        mae = statistics.mean(errors)
        rmse = math.sqrt(statistics.mean(error ** 2 for error in errors))
        
        # Calculate accuracy score (inverse of normalized error)
        # Higher score = better accuracy
        max_error = max(errors) if errors else 1.0
        normalized_mae = mae / max_error if max_error > 0 else 0
        accuracy_score = max(0, 1.0 - normalized_mae)
        
        # Calculate optimal weight based on accuracy
        optimal_weight = accuracy_score
        
        return BacktestResult(
            source=source,
            mae=mae,
            rmse=rmse,
            accuracy_score=accuracy_score,
            sample_size=len(errors),
            optimal_weight=optimal_weight
        )
    
    def refresh_projections_cache(self) -> None:
        """
        Clear the projections cache to force recalculation of weights.
        """
        self._source_weights_cache.clear()
        self._cache_expiry = None
        logger.info("Projections cache cleared")
    
    def get_projection_sources(self, season: int) -> List[str]:
        """
        Get all available projection sources for a season.
        
        Args:
            season: Season year
            
        Returns:
            List of source names
        """
        sources_query = self.db.query(Projection.source).filter(
            Projection.season == season
        ).distinct()
        
        return [row[0] for row in sources_query.all()]
    
    def get_source_statistics(self, source: str, season: int) -> Dict[str, Any]:
        """
        Get statistics for a specific projection source.
        
        Args:
            source: Source name
            season: Season year
            
        Returns:
            Dictionary containing source statistics
        """
        projections_query = self.db.query(Projection).filter(
            and_(
                Projection.source == source,
                Projection.season == season,
                Projection.is_active == True
            )
        )
        
        projections = projections_query.all()
        
        if not projections:
            return {}
        
        points = [float(p.projected_points) for p in projections]
        
        stats = {
            'source': source,
            'season': season,
            'total_projections': len(projections),
            'mean_points': statistics.mean(points),
            'median_points': statistics.median(points),
            'std_dev': statistics.stdev(points) if len(points) > 1 else 0,
            'min_points': min(points),
            'max_points': max(points),
            'weekly_projections': len([p for p in projections if p.week is not None]),
            'season_projections': len([p for p in projections if p.week is None])
        }
        
        return stats


class ProjectionsCacheManager:
    """
    Manages caching and refresh mechanisms for aggregated projections.
    """
    
    def __init__(self, db: Session, aggregator: ProjectionsAggregator):
        self.db = db
        self.aggregator = aggregator
        self._cache: Dict[str, AggregatedProjection] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
        self._default_ttl = timedelta(hours=2)  # 2-hour default TTL
    
    def get_cached_projection(
        self,
        player_id: str,
        week: Optional[int] = None,
        season: int = 2024,
        ttl: Optional[timedelta] = None
    ) -> Optional[AggregatedProjection]:
        """
        Get cached aggregated projection or compute if not cached/expired.
        
        Args:
            player_id: Player identifier
            week: Week number (None for season-long)
            season: Season year
            ttl: Time-to-live for cache entry
            
        Returns:
            AggregatedProjection or None if not available
        """
        cache_key = self._make_cache_key(player_id, week, season)
        ttl = ttl or self._default_ttl
        
        # Check if cached and not expired
        if (cache_key in self._cache and 
            cache_key in self._cache_timestamps and
            datetime.utcnow() - self._cache_timestamps[cache_key] < ttl):
            return self._cache[cache_key]
        
        # Compute new aggregation
        aggregated = self.aggregator.aggregate_projections(
            player_id=player_id,
            week=week,
            season=season
        )
        
        if aggregated:
            self._cache[cache_key] = aggregated
            self._cache_timestamps[cache_key] = datetime.utcnow()
        
        return aggregated
    
    def invalidate_cache(
        self,
        player_id: Optional[str] = None,
        week: Optional[int] = None,
        season: Optional[int] = None
    ) -> None:
        """
        Invalidate cache entries matching the criteria.
        
        Args:
            player_id: Player ID to invalidate (None for all players)
            week: Week to invalidate (None for all weeks)
            season: Season to invalidate (None for all seasons)
        """
        keys_to_remove = []
        
        for cache_key in self._cache.keys():
            key_parts = cache_key.split('|')
            if len(key_parts) != 3:
                continue
            
            key_player_id, key_week, key_season = key_parts
            key_week = None if key_week == 'None' else int(key_week)
            key_season = int(key_season)
            
            should_remove = True
            
            if player_id is not None and key_player_id != player_id:
                should_remove = False
            if week is not None and key_week != week:
                should_remove = False
            if season is not None and key_season != season:
                should_remove = False
            
            if should_remove:
                keys_to_remove.append(cache_key)
        
        for key in keys_to_remove:
            self._cache.pop(key, None)
            self._cache_timestamps.pop(key, None)
        
        logger.info(f"Invalidated {len(keys_to_remove)} cache entries")
    
    def clear_cache(self) -> None:
        """Clear all cached projections."""
        self._cache.clear()
        self._cache_timestamps.clear()
        logger.info("Cleared all projection cache entries")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        now = datetime.utcnow()
        expired_count = sum(
            1 for timestamp in self._cache_timestamps.values()
            if now - timestamp >= self._default_ttl
        )
        
        return {
            'total_entries': len(self._cache),
            'expired_entries': expired_count,
            'active_entries': len(self._cache) - expired_count,
            'cache_hit_ratio': self._calculate_hit_ratio(),
            'oldest_entry': min(self._cache_timestamps.values()) if self._cache_timestamps else None,
            'newest_entry': max(self._cache_timestamps.values()) if self._cache_timestamps else None
        }
    
    def _make_cache_key(self, player_id: str, week: Optional[int], season: int) -> str:
        """Create cache key from parameters."""
        return f"{player_id}|{week}|{season}"
    
    def _calculate_hit_ratio(self) -> float:
        """Calculate cache hit ratio (simplified implementation)."""
        # This would need request tracking in a real implementation
        return 0.0  # Placeholder