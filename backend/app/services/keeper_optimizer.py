"""
Keeper optimization service for fantasy football keeper decisions.

This service implements value over replacement calculations, integer linear programming
optimization using OR-Tools, and handles various keeper rule constraints.
"""
from typing import Dict, List, Optional, Tuple, Any, Set
from decimal import Decimal
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import logging
import math

from ortools.linear_solver import pywraplp
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from ..models.league import League
from ..models.roster import <PERSON><PERSON><PERSON>, RosterPlayer, Franchise
from ..models.player import Player, PlayerPosition
from ..models.projection import Projection
from ..services.projections_aggregator import ProjectionsAggregator
from ..core.database import get_db

logger = logging.getLogger(__name__)


class KeeperConstraintType(str, Enum):
    """Types of keeper constraints."""
    MAX_KEEPERS = "max_keepers"
    ROUND_ESCALATION = "round_escalation"
    FRANCHISE_TAG = "franchise_tag"
    POSITION_LIMITS = "position_limits"
    SALARY_CAP = "salary_cap"


@dataclass
class KeeperCandidate:
    """Represents a potential keeper candidate."""
    player_id: str
    player_name: str
    position: PlayerPosition
    current_cost: int  # Current round cost
    projected_points: Decimal
    replacement_level: Decimal
    value_over_replacement: Decimal
    keeper_cost: int  # Cost if kept (after escalation)
    is_eligible: bool
    constraints_violated: List[str]
    metadata: Dict[str, Any]


@dataclass
class KeeperRecommendation:
    """Represents a keeper recommendation with rationale."""
    player_id: str
    player_name: str
    position: PlayerPosition
    keeper_cost: int
    projected_points: Decimal
    value_over_replacement: Decimal
    confidence: float
    rationale: str
    alternatives: List['KeeperAlternative']
    metadata: Dict[str, Any]


@dataclass
class KeeperAlternative:
    """Represents an alternative keeper option."""
    player_id: str
    player_name: str
    position: PlayerPosition
    keeper_cost: int
    value_over_replacement: Decimal
    reason: str


@dataclass
class KeeperScenario:
    """Represents a complete keeper scenario."""
    scenario_name: str
    selected_keepers: List[KeeperRecommendation]
    total_value: Decimal
    remaining_budget: Optional[Decimal]
    constraints_satisfied: bool
    trade_offs: List[str]
    metadata: Dict[str, Any]


class KeeperOptimizer:
    """
    Service for optimizing keeper selections using integer linear programming.
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.projections_aggregator = ProjectionsAggregator(db)
        self._replacement_levels_cache: Dict[str, Dict[PlayerPosition, Decimal]] = {}
    
    def optimize_keepers(
        self,
        franchise_id: str,
        season: int = 2024,
        max_scenarios: int = 5
    ) -> List[KeeperScenario]:
        """
        Optimize keeper selections for a franchise using integer linear programming.
        
        Args:
            franchise_id: Franchise identifier
            season: Season year
            max_scenarios: Maximum number of scenarios to generate
            
        Returns:
            List of KeeperScenario objects ordered by total value
        """
        # Get franchise and league information
        franchise = self.db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise:
            raise ValueError(f"Franchise {franchise_id} not found")
        
        league = franchise.league
        if not league.keeper_rules:
            raise ValueError(f"League {league.id} has no keeper rules configured")
        
        # Get keeper candidates
        candidates = self.get_keeper_candidates(franchise_id, season)
        if not candidates:
            logger.warning(f"No keeper candidates found for franchise {franchise_id}")
            return []
        
        # Generate optimal scenarios
        scenarios = self._generate_keeper_scenarios(
            candidates, league.keeper_rules, max_scenarios
        )
        
        return scenarios
    
    def get_keeper_candidates(
        self,
        franchise_id: str,
        season: int = 2024
    ) -> List[KeeperCandidate]:
        """
        Get all potential keeper candidates for a franchise.
        
        Args:
            franchise_id: Franchise identifier
            season: Season year
            
        Returns:
            List of KeeperCandidate objects
        """
        # Get franchise roster
        franchise = self.db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise or not franchise.roster:
            return []
        
        roster = franchise.roster
        league = franchise.league
        
        # Get replacement levels for all positions
        replacement_levels = self.calculate_replacement_levels(league.id, season)
        
        candidates = []
        
        for roster_player in roster.get_active_players():
            player = roster_player.player
            
            # Get aggregated projection for the player
            aggregated_projection = self.projections_aggregator.aggregate_projections(
                player_id=player.id,
                week=None,  # Season-long projection
                season=season
            )
            
            if not aggregated_projection:
                logger.debug(f"No projection found for player {player.id}")
                continue
            
            # Calculate keeper cost (with escalation if applicable)
            current_cost = roster_player.keeper_cost or 16  # Default to last round
            keeper_cost = self._calculate_keeper_cost(
                current_cost, league.keeper_rules
            )
            
            # Get replacement level for position
            replacement_level = replacement_levels.get(player.position, Decimal('0'))
            
            # Calculate value over replacement
            vor = aggregated_projection.projected_points - replacement_level
            
            # Check eligibility and constraints
            is_eligible, constraints_violated = self._check_keeper_eligibility(
                roster_player, league.keeper_rules
            )
            
            candidate = KeeperCandidate(
                player_id=player.id,
                player_name=player.name,
                position=player.position,
                current_cost=current_cost,
                projected_points=aggregated_projection.projected_points,
                replacement_level=replacement_level,
                value_over_replacement=vor,
                keeper_cost=keeper_cost,
                is_eligible=is_eligible,
                constraints_violated=constraints_violated,
                metadata={
                    'team': player.team,
                    'bye_week': player.bye_week,
                    'injury_status': player.injury_status.value,
                    'projection_confidence': aggregated_projection.variance,
                    'source_count': aggregated_projection.source_count
                }
            )
            
            candidates.append(candidate)
        
        # Sort by value over replacement (descending)
        candidates.sort(key=lambda c: c.value_over_replacement, reverse=True)
        
        return candidates
    
    def calculate_replacement_levels(
        self,
        league_id: str,
        season: int = 2024
    ) -> Dict[PlayerPosition, Decimal]:
        """
        Calculate replacement level baselines for each position.
        
        Args:
            league_id: League identifier
            season: Season year
            
        Returns:
            Dictionary mapping positions to replacement level points
        """
        cache_key = f"{league_id}_{season}"
        if cache_key in self._replacement_levels_cache:
            return self._replacement_levels_cache[cache_key]
        
        league = self.db.query(League).filter(League.id == league_id).first()
        if not league:
            raise ValueError(f"League {league_id} not found")
        
        replacement_levels = {}
        
        # Get all aggregated projections for the season
        all_projections = self.projections_aggregator.aggregate_all_projections(
            week=None,  # Season-long
            season=season,
            min_sources=1
        )
        
        # Group projections by position
        position_projections = {}
        for proj in all_projections:
            player = self.db.query(Player).filter(Player.id == proj.player_id).first()
            if player:
                position = player.position
                if position not in position_projections:
                    position_projections[position] = []
                position_projections[position].append(proj.projected_points)
        
        # Calculate replacement level for each position
        for position, projections in position_projections.items():
            if not projections:
                replacement_levels[position] = Decimal('0')
                continue
            
            # Sort projections in descending order
            sorted_projections = sorted(projections, reverse=True)
            
            # Get roster requirements for this position
            roster_slots = self._get_position_roster_slots(league, position)
            total_teams = len(league.franchises)
            
            # Calculate replacement level index
            # Replacement level = (starter slots + bench depth) * number of teams
            bench_depth = 2  # Assume 2 bench players per position
            replacement_index = (roster_slots + bench_depth) * total_teams
            
            # Ensure we don't exceed available players
            replacement_index = min(replacement_index, len(sorted_projections) - 1)
            
            if replacement_index >= 0:
                replacement_levels[position] = sorted_projections[replacement_index]
            else:
                replacement_levels[position] = Decimal('0')
        
        # Cache the results
        self._replacement_levels_cache[cache_key] = replacement_levels
        
        logger.info(f"Calculated replacement levels for league {league_id}: {replacement_levels}")
        return replacement_levels
    
    def _generate_keeper_scenarios(
        self,
        candidates: List[KeeperCandidate],
        keeper_rules: Dict[str, Any],
        max_scenarios: int = 5
    ) -> List[KeeperScenario]:
        """
        Generate optimal keeper scenarios using integer linear programming.
        
        Args:
            candidates: List of keeper candidates
            keeper_rules: League keeper rules
            max_scenarios: Maximum number of scenarios to generate
            
        Returns:
            List of KeeperScenario objects
        """
        if not candidates:
            return []
        
        # Filter to eligible candidates only
        eligible_candidates = [c for c in candidates if c.is_eligible]
        
        if not eligible_candidates:
            logger.warning("No eligible keeper candidates found")
            return []
        
        scenarios = []
        
        # Generate primary optimal scenario
        primary_scenario = self._solve_keeper_optimization(
            eligible_candidates, keeper_rules, "Optimal"
        )
        if primary_scenario:
            scenarios.append(primary_scenario)
        
        # Generate alternative scenarios with different constraints
        if max_scenarios > 1:
            # Value-focused scenario (maximize total VOR)
            value_scenario = self._solve_keeper_optimization(
                eligible_candidates, keeper_rules, "Value-Focused",
                objective_weights={'vor': 1.0, 'cost': 0.0}
            )
            if value_scenario and value_scenario.total_value != primary_scenario.total_value:
                scenarios.append(value_scenario)
        
        if max_scenarios > 2:
            # Cost-efficient scenario (minimize total cost)
            cost_scenario = self._solve_keeper_optimization(
                eligible_candidates, keeper_rules, "Cost-Efficient",
                objective_weights={'vor': 0.3, 'cost': 0.7}
            )
            if cost_scenario and not any(s.total_value == cost_scenario.total_value for s in scenarios):
                scenarios.append(cost_scenario)
        
        if max_scenarios > 3:
            # Position-balanced scenario
            balanced_scenario = self._solve_keeper_optimization(
                eligible_candidates, keeper_rules, "Position-Balanced",
                enforce_position_balance=True
            )
            if balanced_scenario and not any(s.total_value == balanced_scenario.total_value for s in scenarios):
                scenarios.append(balanced_scenario)
        
        if max_scenarios > 4:
            # Conservative scenario (lower risk)
            conservative_scenario = self._solve_keeper_optimization(
                eligible_candidates, keeper_rules, "Conservative",
                risk_adjustment=True
            )
            if conservative_scenario and not any(s.total_value == conservative_scenario.total_value for s in scenarios):
                scenarios.append(conservative_scenario)
        
        # Sort scenarios by total value (descending)
        scenarios.sort(key=lambda s: s.total_value, reverse=True)
        
        return scenarios[:max_scenarios]
    
    def _solve_keeper_optimization(
        self,
        candidates: List[KeeperCandidate],
        keeper_rules: Dict[str, Any],
        scenario_name: str,
        objective_weights: Optional[Dict[str, float]] = None,
        enforce_position_balance: bool = False,
        risk_adjustment: bool = False
    ) -> Optional[KeeperScenario]:
        """
        Solve keeper optimization using integer linear programming.
        
        Args:
            candidates: List of eligible keeper candidates
            keeper_rules: League keeper rules
            scenario_name: Name for this scenario
            objective_weights: Weights for objective function components
            enforce_position_balance: Whether to enforce position balance
            risk_adjustment: Whether to adjust for projection risk
            
        Returns:
            KeeperScenario or None if no solution found
        """
        if not candidates:
            return None
        
        # Create the solver
        solver = pywraplp.Solver.CreateSolver('SCIP')
        if not solver:
            logger.error("Could not create OR-Tools solver")
            return None
        
        # Default objective weights
        if objective_weights is None:
            objective_weights = {'vor': 0.8, 'cost': 0.2}
        
        # Create decision variables (binary: 1 if player is kept, 0 otherwise)
        keeper_vars = {}
        for i, candidate in enumerate(candidates):
            keeper_vars[i] = solver.IntVar(0, 1, f'keeper_{candidate.player_id}')
        
        # Constraint: Maximum number of keepers
        max_keepers = keeper_rules.get('max_keepers', 3)
        solver.Add(
            solver.Sum([keeper_vars[i] for i in range(len(candidates))]) <= max_keepers
        )
        
        # Constraint: Position limits (if specified)
        position_limits = keeper_rules.get('position_limits', {})
        if position_limits:
            position_groups = {}
            for i, candidate in enumerate(candidates):
                pos = candidate.position.value
                if pos not in position_groups:
                    position_groups[pos] = []
                position_groups[pos].append(keeper_vars[i])
            
            for position, limit in position_limits.items():
                if position in position_groups:
                    solver.Add(solver.Sum(position_groups[position]) <= limit)
        
        # Constraint: Salary cap (if applicable)
        salary_cap = keeper_rules.get('salary_cap')
        if salary_cap:
            total_cost = solver.Sum([
                keeper_vars[i] * candidates[i].keeper_cost
                for i in range(len(candidates))
            ])
            solver.Add(total_cost <= salary_cap)
        
        # Position balance constraints (if enforced)
        if enforce_position_balance:
            # Ensure at least one player from each major position if available
            major_positions = ['QB', 'RB', 'WR', 'TE']
            for pos in major_positions:
                pos_candidates = [
                    i for i, c in enumerate(candidates) 
                    if c.position.value == pos
                ]
                if pos_candidates:
                    solver.Add(
                        solver.Sum([keeper_vars[i] for i in pos_candidates]) >= 1
                    )
        
        # Objective function: Maximize value while considering cost
        objective_terms = []
        
        for i, candidate in enumerate(candidates):
            # Value component
            value_component = float(candidate.value_over_replacement) * objective_weights.get('vor', 0.8)
            
            # Cost component (negative because we want to minimize cost)
            cost_component = -candidate.keeper_cost * objective_weights.get('cost', 0.2)
            
            # Risk adjustment (if enabled)
            if risk_adjustment:
                confidence_factor = 1.0 - float(candidate.metadata.get('projection_confidence', 0.1))
                value_component *= confidence_factor
            
            total_value = value_component + cost_component
            objective_terms.append(keeper_vars[i] * total_value)
        
        solver.Maximize(solver.Sum(objective_terms))
        
        # Solve the optimization problem
        status = solver.Solve()
        
        if status != pywraplp.Solver.OPTIMAL:
            logger.warning(f"Could not find optimal solution for scenario {scenario_name}")
            return None
        
        # Extract the solution
        selected_keepers = []
        total_value = Decimal('0')
        total_cost = 0
        
        for i, candidate in enumerate(candidates):
            if keeper_vars[i].solution_value() > 0.5:  # Binary variable is 1
                # Create keeper recommendation
                recommendation = KeeperRecommendation(
                    player_id=candidate.player_id,
                    player_name=candidate.player_name,
                    position=candidate.position,
                    keeper_cost=candidate.keeper_cost,
                    projected_points=candidate.projected_points,
                    value_over_replacement=candidate.value_over_replacement,
                    confidence=self._calculate_confidence(candidate),
                    rationale=self._generate_rationale(candidate),
                    alternatives=self._find_alternatives(candidate, candidates),
                    metadata=candidate.metadata
                )
                
                selected_keepers.append(recommendation)
                total_value += candidate.value_over_replacement
                total_cost += candidate.keeper_cost
        
        # Calculate remaining budget
        remaining_budget = None
        if salary_cap:
            remaining_budget = Decimal(str(salary_cap - total_cost))
        
        # Check if all constraints are satisfied
        constraints_satisfied = len(selected_keepers) <= max_keepers
        
        # Generate trade-offs analysis
        trade_offs = self._analyze_trade_offs(selected_keepers, candidates)
        
        scenario = KeeperScenario(
            scenario_name=scenario_name,
            selected_keepers=selected_keepers,
            total_value=total_value,
            remaining_budget=remaining_budget,
            constraints_satisfied=constraints_satisfied,
            trade_offs=trade_offs,
            metadata={
                'total_cost': total_cost,
                'max_keepers': max_keepers,
                'objective_weights': objective_weights,
                'solver_status': 'OPTIMAL',
                'generation_timestamp': datetime.utcnow().isoformat()
            }
        )
        
        return scenario
    
    def _calculate_keeper_cost(
        self,
        current_cost: int,
        keeper_rules: Dict[str, Any]
    ) -> int:
        """
        Calculate the cost of keeping a player based on escalation rules.
        
        Args:
            current_cost: Current round cost of the player
            keeper_rules: League keeper rules
            
        Returns:
            Adjusted keeper cost
        """
        escalation_rounds = keeper_rules.get('round_escalation', 1)
        max_round = keeper_rules.get('max_round', 16)
        
        # Apply escalation
        new_cost = current_cost - escalation_rounds
        
        # Ensure we don't go below round 1
        new_cost = max(1, new_cost)
        
        return new_cost
    
    def _check_keeper_eligibility(
        self,
        roster_player: RosterPlayer,
        keeper_rules: Dict[str, Any]
    ) -> Tuple[bool, List[str]]:
        """
        Check if a player is eligible to be kept based on league rules.
        
        Args:
            roster_player: RosterPlayer object
            keeper_rules: League keeper rules
            
        Returns:
            Tuple of (is_eligible, list_of_constraint_violations)
        """
        violations = []
        
        # Check if player was acquired via trade (if restricted)
        if keeper_rules.get('no_traded_players', False):
            # This would need transaction history to implement properly
            pass
        
        # Check minimum ownership time
        min_ownership_weeks = keeper_rules.get('min_ownership_weeks', 0)
        if min_ownership_weeks > 0:
            # This would need transaction history to implement properly
            pass
        
        # Check if player is injured/suspended
        if keeper_rules.get('no_injured_keepers', False):
            player = roster_player.player
            if player.injury_status.value in ['OUT', 'IR', 'PUP', 'SUSPENDED']:
                violations.append(f"Player is {player.injury_status.value}")
        
        # Check keeper cost limits
        max_keeper_cost = keeper_rules.get('max_keeper_cost')
        if max_keeper_cost and roster_player.keeper_cost:
            keeper_cost = self._calculate_keeper_cost(
                roster_player.keeper_cost, keeper_rules
            )
            if keeper_cost > max_keeper_cost:
                violations.append(f"Keeper cost {keeper_cost} exceeds maximum {max_keeper_cost}")
        
        is_eligible = len(violations) == 0
        return is_eligible, violations
    
    def _get_position_roster_slots(self, league: League, position: PlayerPosition) -> int:
        """Get the number of starting roster slots for a position."""
        total_slots = 0
        
        for slot in league.roster_slots:
            slot_positions = slot.get('positions', [])
            if position.value in slot_positions:
                total_slots += slot.get('count', 1)
        
        return total_slots
    
    def _calculate_confidence(self, candidate: KeeperCandidate) -> float:
        """Calculate confidence score for a keeper recommendation."""
        base_confidence = 0.7
        
        # Adjust based on projection variance
        variance = float(candidate.metadata.get('projection_confidence', 0.1))
        confidence_adjustment = max(0, 0.3 - variance)
        
        # Adjust based on source count
        source_count = candidate.metadata.get('source_count', 1)
        source_adjustment = min(0.2, source_count * 0.05)
        
        # Adjust based on injury status
        injury_status = candidate.metadata.get('injury_status', 'HEALTHY')
        injury_adjustment = 0 if injury_status == 'HEALTHY' else -0.1
        
        confidence = base_confidence + confidence_adjustment + source_adjustment + injury_adjustment
        return max(0.1, min(1.0, confidence))
    
    def _generate_rationale(self, candidate: KeeperCandidate) -> str:
        """Generate rationale text for a keeper recommendation."""
        rationale_parts = []
        
        # Value analysis
        vor = float(candidate.value_over_replacement)
        if vor > 50:
            rationale_parts.append(f"Exceptional value with {vor:.1f} points over replacement")
        elif vor > 20:
            rationale_parts.append(f"Strong value with {vor:.1f} points over replacement")
        else:
            rationale_parts.append(f"Moderate value with {vor:.1f} points over replacement")
        
        # Cost analysis
        if candidate.keeper_cost <= 5:
            rationale_parts.append(f"excellent keeper value at round {candidate.keeper_cost}")
        elif candidate.keeper_cost <= 10:
            rationale_parts.append(f"good keeper value at round {candidate.keeper_cost}")
        else:
            rationale_parts.append(f"reasonable keeper cost at round {candidate.keeper_cost}")
        
        # Position scarcity
        if candidate.position in [PlayerPosition.QB, PlayerPosition.TE]:
            rationale_parts.append("position scarcity adds value")
        
        return ". ".join(rationale_parts).capitalize() + "."
    
    def _find_alternatives(
        self,
        selected_candidate: KeeperCandidate,
        all_candidates: List[KeeperCandidate]
    ) -> List[KeeperAlternative]:
        """Find alternative keeper options for comparison."""
        alternatives = []
        
        # Find similar players by position
        same_position = [
            c for c in all_candidates 
            if c.position == selected_candidate.position 
            and c.player_id != selected_candidate.player_id
            and c.is_eligible
        ]
        
        # Sort by VOR and take top 2
        same_position.sort(key=lambda c: c.value_over_replacement, reverse=True)
        
        for candidate in same_position[:2]:
            alternative = KeeperAlternative(
                player_id=candidate.player_id,
                player_name=candidate.player_name,
                position=candidate.position,
                keeper_cost=candidate.keeper_cost,
                value_over_replacement=candidate.value_over_replacement,
                reason=f"Alternative {candidate.position.value} option"
            )
            alternatives.append(alternative)
        
        return alternatives
    
    def _analyze_trade_offs(
        self,
        selected_keepers: List[KeeperRecommendation],
        all_candidates: List[KeeperCandidate]
    ) -> List[str]:
        """Analyze trade-offs made in keeper selection."""
        trade_offs = []
        
        selected_ids = {k.player_id for k in selected_keepers}
        not_selected = [c for c in all_candidates if c.player_id not in selected_ids and c.is_eligible]
        
        # Find high-value players not selected
        high_value_not_selected = [
            c for c in not_selected 
            if c.value_over_replacement > 30
        ]
        
        for candidate in high_value_not_selected[:3]:  # Top 3 trade-offs
            trade_offs.append(
                f"Passed on {candidate.player_name} ({candidate.value_over_replacement:.1f} VOR) "
                f"due to cost/constraint considerations"
            )
        
        return trade_offs
    
    def refresh_replacement_levels_cache(self) -> None:
        """Clear the replacement levels cache to force recalculation."""
        self._replacement_levels_cache.clear()
        logger.info("Replacement levels cache cleared")