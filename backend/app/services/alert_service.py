"""
Alert and notification service for fantasy football management.

Handles deadline monitoring, player news processing, and notification delivery.
"""
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..models.alert import (
    Alert, AlertSchedule, PlayerNewsAlert, 
    AlertType, AlertPriority, AlertStatus, DeliveryMethod
)
from ..models.league import League
from ..models.roster import Franchise
from ..models.player import Player, InjuryStatus
from ..models.recommendation import Recommendation


class AlertService:
    """Service for managing alerts and notifications."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_alert(
        self,
        alert_type: AlertType,
        title: str,
        message: str,
        priority: AlertPriority = AlertPriority.MEDIUM,
        league_id: Optional[str] = None,
        franchise_id: Optional[str] = None,
        player_id: Optional[str] = None,
        scheduled_for: Optional[datetime] = None,
        expires_at: Optional[datetime] = None,
        delivery_methods: Optional[List[str]] = None,
        alert_data: Optional[Dict[str, Any]] = None
    ) -> Alert:
        """Create a new alert."""
        if delivery_methods is None:
            delivery_methods = [DeliveryMethod.IN_APP.value]
        
        if alert_data is None:
            alert_data = {}
        
        alert = Alert(
            id=str(uuid.uuid4()),
            alert_type=alert_type,
            priority=priority,
            title=title,
            message=message,
            league_id=league_id,
            franchise_id=franchise_id,
            player_id=player_id,
            scheduled_for=scheduled_for,
            expires_at=expires_at,
            delivery_methods=delivery_methods,
            alert_data=alert_data
        )
        
        self.db.add(alert)
        self.db.commit()
        self.db.refresh(alert)
        
        return alert
    
    def process_deadline_monitoring(self) -> List[Alert]:
        """Process all active alert schedules and generate alerts as needed."""
        alerts_created = []
        
        # Get all active schedules that need processing
        schedules = self.db.query(AlertSchedule).filter(
            AlertSchedule.is_active == True
        ).all()
        
        for schedule in schedules:
            if schedule.is_due_for_processing():
                alert = self._create_deadline_alert(schedule)
                if alert:
                    alerts_created.append(alert)
                    
                # Update last processed time
                schedule.last_processed = datetime.now()
                self.db.commit()
        
        return alerts_created
    
    def _create_deadline_alert(self, schedule: AlertSchedule) -> Optional[Alert]:
        """Create an alert for a deadline schedule."""
        now = datetime.now(schedule.target_datetime.tzinfo)
        time_until_deadline = schedule.target_datetime - now
        
        # Determine which advance notice period this is
        hours_until = time_until_deadline.total_seconds() / 3600
        
        # Find the appropriate advance notice period
        advance_notice = None
        for notice_hours in sorted(schedule.advance_notice_hours):
            if hours_until <= notice_hours + 0.1:  # Small buffer for timing
                advance_notice = notice_hours
                break
        
        if advance_notice is None:
            return None
        
        # Create appropriate alert based on type
        if schedule.alert_type == AlertType.KEEPER_DEADLINE:
            return self._create_keeper_deadline_alert(schedule, advance_notice, time_until_deadline)
        elif schedule.alert_type == AlertType.WAIVER_DEADLINE:
            return self._create_waiver_deadline_alert(schedule, advance_notice, time_until_deadline)
        elif schedule.alert_type == AlertType.LINEUP_LOCK:
            return self._create_lineup_lock_alert(schedule, advance_notice, time_until_deadline)
        elif schedule.alert_type == AlertType.TRADE_DEADLINE:
            return self._create_trade_deadline_alert(schedule, advance_notice, time_until_deadline)
        
        return None
    
    def _create_keeper_deadline_alert(
        self, 
        schedule: AlertSchedule, 
        advance_notice: float, 
        time_until_deadline: timedelta
    ) -> Alert:
        """Create a keeper deadline alert."""
        if advance_notice >= 24:
            priority = AlertPriority.MEDIUM
            title = "Keeper Deadline Approaching"
        elif advance_notice >= 2:
            priority = AlertPriority.HIGH
            title = "Keeper Deadline Soon"
        else:
            priority = AlertPriority.URGENT
            title = "Keeper Deadline Imminent"
        
        hours_remaining = int(time_until_deadline.total_seconds() / 3600)
        message = f"Your keeper selections are due in {hours_remaining} hours. Review your keeper recommendations and submit your selections."
        
        return self.create_alert(
            alert_type=AlertType.KEEPER_DEADLINE,
            title=title,
            message=message,
            priority=priority,
            league_id=schedule.league_id,
            franchise_id=schedule.franchise_id,
            expires_at=schedule.target_datetime,
            alert_data={
                "deadline": schedule.target_datetime.isoformat(),
                "advance_notice_hours": advance_notice,
                "schedule_id": schedule.id
            }
        )
    
    def _create_waiver_deadline_alert(
        self, 
        schedule: AlertSchedule, 
        advance_notice: float, 
        time_until_deadline: timedelta
    ) -> Alert:
        """Create a waiver deadline alert."""
        if advance_notice >= 24:
            priority = AlertPriority.MEDIUM
            title = "Waiver Period Ending Soon"
        elif advance_notice >= 2:
            priority = AlertPriority.HIGH
            title = "Waiver Deadline Approaching"
        else:
            priority = AlertPriority.URGENT
            title = "Waiver Deadline Imminent"
        
        hours_remaining = int(time_until_deadline.total_seconds() / 3600)
        message = f"Waiver claims are due in {hours_remaining} hours. Review available players and submit your FAAB bids."
        
        return self.create_alert(
            alert_type=AlertType.WAIVER_DEADLINE,
            title=title,
            message=message,
            priority=priority,
            league_id=schedule.league_id,
            franchise_id=schedule.franchise_id,
            expires_at=schedule.target_datetime,
            alert_data={
                "deadline": schedule.target_datetime.isoformat(),
                "advance_notice_hours": advance_notice,
                "schedule_id": schedule.id
            }
        )
    
    def _create_lineup_lock_alert(
        self, 
        schedule: AlertSchedule, 
        advance_notice: float, 
        time_until_deadline: timedelta
    ) -> Alert:
        """Create a lineup lock alert."""
        if advance_notice >= 2:
            priority = AlertPriority.HIGH
            title = "Lineup Lock Approaching"
        else:
            priority = AlertPriority.URGENT
            title = "Lineup Lock Imminent"
        
        minutes_remaining = int(time_until_deadline.total_seconds() / 60)
        if minutes_remaining >= 60:
            time_str = f"{minutes_remaining // 60} hours"
        else:
            time_str = f"{minutes_remaining} minutes"
        
        message = f"Player lineups lock in {time_str}. Make any final lineup changes now."
        
        return self.create_alert(
            alert_type=AlertType.LINEUP_LOCK,
            title=title,
            message=message,
            priority=priority,
            league_id=schedule.league_id,
            franchise_id=schedule.franchise_id,
            expires_at=schedule.target_datetime,
            alert_data={
                "deadline": schedule.target_datetime.isoformat(),
                "advance_notice_hours": advance_notice,
                "schedule_id": schedule.id
            }
        )
    
    def _create_trade_deadline_alert(
        self, 
        schedule: AlertSchedule, 
        advance_notice: float, 
        time_until_deadline: timedelta
    ) -> Alert:
        """Create a trade deadline alert."""
        if advance_notice >= 24:
            priority = AlertPriority.MEDIUM
            title = "Trade Deadline Approaching"
        elif advance_notice >= 2:
            priority = AlertPriority.HIGH
            title = "Trade Deadline Soon"
        else:
            priority = AlertPriority.URGENT
            title = "Trade Deadline Imminent"
        
        hours_remaining = int(time_until_deadline.total_seconds() / 3600)
        message = f"The trade deadline is in {hours_remaining} hours. Review trade opportunities and finalize any pending trades."
        
        return self.create_alert(
            alert_type=AlertType.TRADE_DEADLINE,
            title=title,
            message=message,
            priority=priority,
            league_id=schedule.league_id,
            franchise_id=schedule.franchise_id,
            expires_at=schedule.target_datetime,
            alert_data={
                "deadline": schedule.target_datetime.isoformat(),
                "advance_notice_hours": advance_notice,
                "schedule_id": schedule.id
            }
        )
    
    def process_player_news(self) -> List[Alert]:
        """Process unprocessed player news and generate alerts."""
        alerts_created = []
        
        # Get unprocessed news that should generate alerts
        news_items = self.db.query(PlayerNewsAlert).filter(
            and_(
                PlayerNewsAlert.is_processed == False,
                PlayerNewsAlert.impact_score >= 5.0
            )
        ).all()
        
        for news in news_items:
            alerts = self._create_player_news_alerts(news)
            alerts_created.extend(alerts)
            
            # Mark as processed
            news.is_processed = True
            news.alerts_generated = len(alerts)
            self.db.commit()
        
        return alerts_created
    
    def _create_player_news_alerts(self, news: PlayerNewsAlert) -> List[Alert]:
        """Create alerts for player news based on roster impact."""
        alerts = []
        
        # Find all franchises that have this player
        franchises_with_player = self.db.query(Franchise).join(
            Franchise.roster
        ).join(
            "roster_players"
        ).filter(
            and_(
                "roster_players.c.player_id" == news.player_id,
                "roster_players.c.is_active" == True
            )
        ).all()
        
        # Create alerts for affected franchises
        for franchise in franchises_with_player:
            alert = self._create_franchise_player_news_alert(news, franchise)
            if alert:
                alerts.append(alert)
        
        return alerts
    
    def _create_franchise_player_news_alert(
        self, 
        news: PlayerNewsAlert, 
        franchise: Franchise
    ) -> Alert:
        """Create a player news alert for a specific franchise."""
        # Determine priority based on impact score
        if news.impact_score >= 8.0:
            priority = AlertPriority.URGENT
        elif news.impact_score >= 6.0:
            priority = AlertPriority.HIGH
        else:
            priority = AlertPriority.MEDIUM
        
        player = self.db.query(Player).filter(Player.id == news.player_id).first()
        player_name = player.name if player else "Unknown Player"
        
        title = f"Player News: {player_name}"
        message = f"{news.headline}\n\n{news.fantasy_impact or news.content[:200]}"
        
        return self.create_alert(
            alert_type=AlertType.PLAYER_NEWS,
            title=title,
            message=message,
            priority=priority,
            league_id=franchise.league_id,
            franchise_id=franchise.id,
            player_id=news.player_id,
            expires_at=datetime.now() + timedelta(days=1),
            alert_data={
                "news_id": news.id,
                "impact_score": news.impact_score,
                "source": news.source,
                "affected_positions": news.affected_positions
            }
        )
    
    def create_injury_alert(
        self, 
        player_id: str, 
        old_status: InjuryStatus, 
        new_status: InjuryStatus
    ) -> List[Alert]:
        """Create alerts for injury status changes."""
        alerts = []
        
        player = self.db.query(Player).filter(Player.id == player_id).first()
        if not player:
            return alerts
        
        # Determine if this is alert-worthy
        if not self._is_injury_change_significant(old_status, new_status):
            return alerts
        
        # Find franchises with this player
        franchises_with_player = self.db.query(Franchise).join(
            Franchise.roster
        ).join(
            "roster_players"
        ).filter(
            and_(
                "roster_players.c.player_id" == player_id,
                "roster_players.c.is_active" == True
            )
        ).all()
        
        for franchise in franchises_with_player:
            alert = self._create_injury_status_alert(player, franchise, old_status, new_status)
            if alert:
                alerts.append(alert)
        
        return alerts
    
    def _is_injury_change_significant(
        self, 
        old_status: InjuryStatus, 
        new_status: InjuryStatus
    ) -> bool:
        """Determine if an injury status change warrants an alert."""
        # Define significant changes
        significant_changes = {
            (InjuryStatus.HEALTHY, InjuryStatus.QUESTIONABLE),
            (InjuryStatus.HEALTHY, InjuryStatus.DOUBTFUL),
            (InjuryStatus.HEALTHY, InjuryStatus.OUT),
            (InjuryStatus.QUESTIONABLE, InjuryStatus.OUT),
            (InjuryStatus.DOUBTFUL, InjuryStatus.OUT),
            (InjuryStatus.OUT, InjuryStatus.HEALTHY),
            (InjuryStatus.OUT, InjuryStatus.QUESTIONABLE),
            (InjuryStatus.DOUBTFUL, InjuryStatus.HEALTHY),
        }
        
        return (old_status, new_status) in significant_changes
    
    def _create_injury_status_alert(
        self, 
        player: Player, 
        franchise: Franchise, 
        old_status: InjuryStatus, 
        new_status: InjuryStatus
    ) -> Alert:
        """Create an injury status change alert."""
        # Determine priority based on status change
        if new_status == InjuryStatus.OUT:
            priority = AlertPriority.URGENT
        elif new_status in [InjuryStatus.DOUBTFUL, InjuryStatus.QUESTIONABLE]:
            priority = AlertPriority.HIGH
        else:
            priority = AlertPriority.MEDIUM
        
        title = f"Injury Update: {player.name}"
        message = f"{player.name} ({player.position.value}) status changed from {old_status.value} to {new_status.value}. Review your lineup and consider alternatives."
        
        return self.create_alert(
            alert_type=AlertType.INJURY_UPDATE,
            title=title,
            message=message,
            priority=priority,
            league_id=franchise.league_id,
            franchise_id=franchise.id,
            player_id=player.id,
            expires_at=datetime.now() + timedelta(hours=12),
            alert_data={
                "old_status": old_status.value,
                "new_status": new_status.value,
                "position": player.position.value,
                "team": player.team
            }
        )
    
    def get_active_alerts(
        self, 
        franchise_id: Optional[str] = None,
        league_id: Optional[str] = None,
        limit: int = 50
    ) -> List[Alert]:
        """Get active alerts for a franchise or league."""
        query = self.db.query(Alert).filter(
            and_(
                Alert.status.in_([AlertStatus.PENDING, AlertStatus.SENT]),
                or_(Alert.expires_at.is_(None), Alert.expires_at > datetime.now())
            )
        )
        
        if franchise_id:
            query = query.filter(Alert.franchise_id == franchise_id)
        elif league_id:
            query = query.filter(Alert.league_id == league_id)
        
        return query.order_by(
            Alert.priority.desc(),
            Alert.created_at.desc()
        ).limit(limit).all()
    
    def mark_alert_delivered(self, alert_id: str) -> bool:
        """Mark an alert as delivered."""
        alert = self.db.query(Alert).filter(Alert.id == alert_id).first()
        if not alert:
            return False
        
        alert.status = AlertStatus.DELIVERED
        alert.sent_at = datetime.now()
        self.db.commit()
        
        return True
    
    def dismiss_alert(self, alert_id: str) -> bool:
        """Dismiss an alert."""
        alert = self.db.query(Alert).filter(Alert.id == alert_id).first()
        if not alert:
            return False
        
        alert.status = AlertStatus.DISMISSED
        self.db.commit()
        
        return True
    
    def create_alert_schedule(
        self,
        name: str,
        alert_type: AlertType,
        target_datetime: datetime,
        advance_notice_hours: List[int],
        league_id: Optional[str] = None,
        franchise_id: Optional[str] = None,
        schedule_data: Optional[Dict[str, Any]] = None
    ) -> AlertSchedule:
        """Create a new alert schedule."""
        if schedule_data is None:
            schedule_data = {}
        
        schedule = AlertSchedule(
            id=str(uuid.uuid4()),
            name=name,
            alert_type=alert_type,
            target_datetime=target_datetime,
            advance_notice_hours=advance_notice_hours,
            league_id=league_id,
            franchise_id=franchise_id,
            schedule_data=schedule_data
        )
        
        self.db.add(schedule)
        self.db.commit()
        self.db.refresh(schedule)
        
        return schedule
    
    def create_player_news_alert(
        self,
        player_id: str,
        headline: str,
        content: str,
        source: str,
        impact_score: Optional[float] = None,
        affected_positions: Optional[List[str]] = None,
        fantasy_impact: Optional[str] = None
    ) -> PlayerNewsAlert:
        """Create a player news alert."""
        if affected_positions is None:
            affected_positions = []
        
        news = PlayerNewsAlert(
            id=str(uuid.uuid4()),
            player_id=player_id,
            headline=headline,
            content=content,
            source=source,
            impact_score=impact_score,
            affected_positions=affected_positions,
            fantasy_impact=fantasy_impact
        )
        
        self.db.add(news)
        self.db.commit()
        self.db.refresh(news)
        
        return news