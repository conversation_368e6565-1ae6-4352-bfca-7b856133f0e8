"""
Lineup optimization service for fantasy football weekly lineup decisions.

Implements win probability maximization using player variance, matchup context,
and late-breaking news integration for optimal lineup recommendations.
"""
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from decimal import Decimal
import logging
from dataclasses import dataclass
from enum import Enum
import numpy as np
from sqlalchemy.orm import Session

from ..models.player import Player, PlayerPosition, InjuryStatus
from ..models.roster import <PERSON><PERSON><PERSON>, RosterPlayer, Franchise
from ..models.projection import Projection
from ..models.league import League
from ..models.recommendation import Recommendation, RecommendationType, RecommendationPriority
from ..core.database import get_db

logger = logging.getLogger(__name__)


class LineupSlot(str, Enum):
    """Standard fantasy football lineup slots."""
    QB = "QB"
    RB1 = "RB1"
    RB2 = "RB2"
    WR1 = "WR1"
    WR2 = "WR2"
    TE = "TE"
    FLEX = "FLEX"
    K = "K"
    DEF = "DEF"
    BENCH = "BENCH"


class WeatherCondition(str, Enum):
    """Weather conditions that affect player performance."""
    CLEAR = "CLEAR"
    RAIN = "RAIN"
    SNOW = "SNOW"
    WIND = "WIND"
    DOME = "DOME"


@dataclass
class MatchupContext:
    """Context information for a player's matchup."""
    opponent_team: str
    opponent_rank_vs_position: Optional[int]  # 1 = best defense, 32 = worst
    weather_condition: WeatherCondition
    temperature: Optional[int]
    wind_speed: Optional[int]
    is_home_game: bool
    game_total: Optional[float]  # Over/under betting total
    spread: Optional[float]  # Point spread
    implied_team_total: Optional[float]


@dataclass
class PlayerNews:
    """Late-breaking news about a player."""
    player_id: str
    news_type: str  # "injury", "inactive", "snap_count", "role_change"
    severity: str  # "low", "medium", "high"
    description: str
    timestamp: datetime
    impact_on_projection: Optional[float]  # Percentage change to projection


@dataclass
class LineupRecommendation:
    """Optimized lineup recommendation."""
    lineup: Dict[LineupSlot, str]  # slot -> player_id
    projected_points: float
    win_probability: float
    confidence: float
    rationale: str
    alternatives: List[Dict[str, Any]]
    risk_level: float


class LineupOptimizer:
    """
    Service for optimizing fantasy football lineups to maximize win probability.
    
    Uses player projections, variance, matchup context, and late-breaking news
    to generate optimal lineup recommendations.
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.weather_adjustments = self._initialize_weather_adjustments()
        self.matchup_adjustments = self._initialize_matchup_adjustments()
    
    def optimize_lineup(
        self,
        franchise_id: str,
        week: int,
        season: int = 2024,
        opponent_projection: Optional[float] = None
    ) -> LineupRecommendation:
        """
        Generate optimal lineup recommendation for a franchise.
        
        Args:
            franchise_id: ID of the franchise to optimize
            week: Week number to optimize for
            season: Season year
            opponent_projection: Expected opponent score for win probability calculation
            
        Returns:
            LineupRecommendation with optimal lineup and analysis
        """
        logger.info(f"Optimizing lineup for franchise {franchise_id}, week {week}")
        
        # Get franchise and roster
        franchise = self.db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise:
            raise ValueError(f"Franchise {franchise_id} not found")
        
        roster = franchise.roster
        if not roster:
            raise ValueError(f"No roster found for franchise {franchise_id}")
        
        # Get available players with projections
        available_players = self._get_available_players_with_projections(
            roster, week, season
        )
        
        if not available_players:
            raise ValueError("No players with projections found for lineup optimization")
        
        # Get league roster slots configuration
        league = franchise.league
        roster_slots = self._parse_roster_slots(league.roster_slots)
        
        # Apply matchup context and news adjustments
        adjusted_players = self._apply_context_adjustments(available_players, week)
        
        # Calculate opponent projection if not provided
        if opponent_projection is None:
            opponent_projection = self._estimate_opponent_projection(franchise_id, week)
        
        # Optimize lineup for win probability
        optimal_lineup = self._optimize_for_win_probability(
            adjusted_players, roster_slots, opponent_projection
        )
        
        # Generate alternatives
        alternatives = self._generate_lineup_alternatives(
            adjusted_players, roster_slots, optimal_lineup, opponent_projection
        )
        
        # Calculate confidence and risk metrics
        confidence = self._calculate_lineup_confidence(optimal_lineup, adjusted_players)
        risk_level = self._calculate_lineup_risk(optimal_lineup, adjusted_players)
        
        # Generate rationale
        rationale = self._generate_lineup_rationale(
            optimal_lineup, adjusted_players, alternatives
        )
        
        return LineupRecommendation(
            lineup=optimal_lineup,
            projected_points=sum(
                adjusted_players[player_id]["adjusted_projection"] 
                for player_id in optimal_lineup.values()
            ),
            win_probability=self._calculate_win_probability(
                optimal_lineup, adjusted_players, opponent_projection
            ),
            confidence=confidence,
            rationale=rationale,
            alternatives=alternatives,
            risk_level=risk_level
        )
    
    def get_start_sit_recommendations(
        self,
        franchise_id: str,
        week: int,
        season: int = 2024
    ) -> List[Dict[str, Any]]:
        """
        Generate start/sit recommendations for borderline players.
        
        Args:
            franchise_id: ID of the franchise
            week: Week number
            season: Season year
            
        Returns:
            List of start/sit recommendations with rationale
        """
        optimal_lineup = self.optimize_lineup(franchise_id, week, season)
        
        # Get current lineup
        franchise = self.db.query(Franchise).filter(Franchise.id == franchise_id).first()
        current_lineup = self._get_current_lineup(franchise.roster)
        
        recommendations = []
        
        # Compare optimal vs current lineup
        for slot, optimal_player_id in optimal_lineup.lineup.items():
            current_player_id = current_lineup.get(slot)
            
            if current_player_id != optimal_player_id:
                recommendations.append({
                    "type": "start_sit",
                    "slot": slot.value,
                    "start_player_id": optimal_player_id,
                    "sit_player_id": current_player_id,
                    "projected_improvement": self._calculate_improvement(
                        optimal_player_id, current_player_id, week, season
                    ),
                    "confidence": optimal_lineup.confidence,
                    "rationale": f"Start {optimal_player_id} over {current_player_id} in {slot.value}"
                })
        
        return recommendations
    
    def check_lineup_locks(
        self,
        franchise_id: str,
        week: int
    ) -> List[Dict[str, Any]]:
        """
        Check for upcoming lineup lock times and generate alerts.
        
        Args:
            franchise_id: ID of the franchise
            week: Week number
            
        Returns:
            List of lineup lock alerts
        """
        alerts = []
        
        # Get current lineup
        franchise = self.db.query(Franchise).filter(Franchise.id == franchise_id).first()
        roster = franchise.roster
        
        # Check each starting player for lock times
        for roster_player in roster.roster_players:
            if roster_player.is_starting() and roster_player.player:
                lock_time = self._get_player_lock_time(roster_player.player, week)
                
                if lock_time:
                    time_until_lock = lock_time - datetime.now()
                    
                    # Alert if less than 2 hours until lock
                    if timedelta(0) < time_until_lock < timedelta(hours=2):
                        alerts.append({
                            "type": "lineup_lock",
                            "player_id": roster_player.player_id,
                            "player_name": roster_player.player.name,
                            "slot": roster_player.roster_slot,
                            "lock_time": lock_time,
                            "time_until_lock": time_until_lock,
                            "urgency": "high" if time_until_lock < timedelta(minutes=30) else "medium"
                        })
        
        return alerts
    
    def apply_late_breaking_news(
        self,
        franchise_id: str,
        week: int,
        news_items: List[PlayerNews]
    ) -> List[Dict[str, Any]]:
        """
        Apply late-breaking news to lineup recommendations.
        
        Args:
            franchise_id: ID of the franchise
            week: Week number
            news_items: List of news items to process
            
        Returns:
            List of lineup adjustment recommendations
        """
        adjustments = []
        
        # Get current optimal lineup
        current_optimal = self.optimize_lineup(franchise_id, week)
        
        # Apply news adjustments to projections
        adjusted_projections = {}
        for news in news_items:
            if news.impact_on_projection:
                adjusted_projections[news.player_id] = news.impact_on_projection
        
        # Re-optimize with news adjustments
        if adjusted_projections:
            new_optimal = self._reoptimize_with_news(
                franchise_id, week, adjusted_projections
            )
            
            # Compare lineups and generate adjustment recommendations
            for slot in current_optimal.lineup:
                current_player = current_optimal.lineup[slot]
                new_player = new_optimal.lineup.get(slot)
                
                if current_player != new_player:
                    relevant_news = [
                        n for n in news_items 
                        if n.player_id in [current_player, new_player]
                    ]
                    
                    adjustments.append({
                        "type": "news_adjustment",
                        "slot": slot.value,
                        "old_player_id": current_player,
                        "new_player_id": new_player,
                        "news_items": [n.description for n in relevant_news],
                        "projected_improvement": new_optimal.projected_points - current_optimal.projected_points,
                        "urgency": max([n.severity for n in relevant_news], default="low")
                    })
        
        return adjustments
    
    def _get_available_players_with_projections(
        self,
        roster: Roster,
        week: int,
        season: int
    ) -> Dict[str, Dict[str, Any]]:
        """Get all roster players with their projections and metadata."""
        players_data = {}
        
        for roster_player in roster.get_active_players():
            player = roster_player.player
            
            # Get player projection for the week
            projection = self.db.query(Projection).filter(
                Projection.player_id == player.id,
                Projection.week == week,
                Projection.season == season,
                Projection.is_active == True
            ).first()
            
            if projection:
                players_data[player.id] = {
                    "player": player,
                    "roster_player": roster_player,
                    "projection": projection,
                    "base_projection": float(projection.projected_points),
                    "variance": float(projection.variance) if projection.variance else 1.0,
                    "floor": float(projection.floor) if projection.floor else 0.0,
                    "ceiling": float(projection.ceiling) if projection.ceiling else float(projection.projected_points) * 1.5,
                    "adjusted_projection": float(projection.projected_points),
                    "matchup_context": self._get_matchup_context(player, week)
                }
        
        return players_data
    
    def _parse_roster_slots(self, roster_slots_config: List[Dict[str, Any]]) -> Dict[LineupSlot, int]:
        """Parse league roster slots configuration into lineup slots."""
        slots = {}
        
        for slot_config in roster_slots_config:
            position = slot_config.get("position")
            count = slot_config.get("count", 1)
            slot_type = slot_config.get("type", "starting")
            
            if slot_type == "starting":
                if position == "QB":
                    slots[LineupSlot.QB] = count
                elif position == "RB":
                    # Assign RB1, RB2 based on count
                    if count >= 1:
                        slots[LineupSlot.RB1] = 1
                    if count >= 2:
                        slots[LineupSlot.RB2] = 1
                elif position == "WR":
                    # Assign WR1, WR2 based on count
                    if count >= 1:
                        slots[LineupSlot.WR1] = 1
                    if count >= 2:
                        slots[LineupSlot.WR2] = 1
                elif position == "TE":
                    slots[LineupSlot.TE] = count
                elif position == "K":
                    slots[LineupSlot.K] = count
                elif position == "DEF":
                    slots[LineupSlot.DEF] = count
                elif position == "FLEX":
                    slots[LineupSlot.FLEX] = count
        
        return slots
    
    def _apply_context_adjustments(
        self,
        players_data: Dict[str, Dict[str, Any]],
        week: int
    ) -> Dict[str, Dict[str, Any]]:
        """Apply matchup context and news adjustments to player projections."""
        for player_id, data in players_data.items():
            player = data["player"]
            context = data["matchup_context"]
            base_projection = data["base_projection"]
            
            # Apply weather adjustments
            weather_multiplier = self._get_weather_adjustment(
                player.position, context.weather_condition
            )
            
            # Apply opponent strength adjustments
            opponent_multiplier = self._get_opponent_adjustment(
                player.position, context.opponent_rank_vs_position
            )
            
            # Apply game script adjustments
            game_script_multiplier = self._get_game_script_adjustment(
                player.position, context.spread, context.game_total
            )
            
            # Apply injury status adjustments
            injury_multiplier = self._get_injury_adjustment(player.injury_status)
            
            # Calculate final adjusted projection
            adjusted_projection = (
                base_projection * 
                weather_multiplier * 
                opponent_multiplier * 
                game_script_multiplier * 
                injury_multiplier
            )
            
            data["adjusted_projection"] = adjusted_projection
            data["adjustment_factors"] = {
                "weather": weather_multiplier,
                "opponent": opponent_multiplier,
                "game_script": game_script_multiplier,
                "injury": injury_multiplier
            }
        
        return players_data
    
    def _optimize_for_win_probability(
        self,
        players_data: Dict[str, Dict[str, Any]],
        roster_slots: Dict[LineupSlot, int],
        opponent_projection: float
    ) -> Dict[LineupSlot, str]:
        """Optimize lineup to maximize win probability using variance."""
        # Get eligible players for each slot
        eligible_players = self._get_eligible_players_by_slot(players_data, roster_slots)
        
        # Use Monte Carlo simulation to find optimal lineup
        best_lineup = None
        best_win_prob = 0.0
        
        # Generate lineup combinations (simplified for performance)
        lineup_combinations = self._generate_lineup_combinations(eligible_players, roster_slots)
        
        for lineup in lineup_combinations[:1000]:  # Limit to top 1000 combinations
            win_prob = self._simulate_win_probability(
                lineup, players_data, opponent_projection
            )
            
            if win_prob > best_win_prob:
                best_win_prob = win_prob
                best_lineup = lineup
        
        return best_lineup or {}
    
    def _simulate_win_probability(
        self,
        lineup: Dict[LineupSlot, str],
        players_data: Dict[str, Dict[str, Any]],
        opponent_projection: float,
        num_simulations: int = 1000
    ) -> float:
        """Simulate win probability for a lineup using Monte Carlo method."""
        wins = 0
        
        for _ in range(num_simulations):
            total_points = 0
            
            for slot, player_id in lineup.items():
                if player_id in players_data:
                    data = players_data[player_id]
                    
                    # Sample from normal distribution using projection and variance
                    mean = data["adjusted_projection"]
                    std = np.sqrt(data["variance"])
                    
                    simulated_points = max(0, np.random.normal(mean, std))
                    total_points += simulated_points
            
            # Sample opponent score from normal distribution
            opponent_std = opponent_projection * 0.15  # Assume 15% coefficient of variation
            opponent_score = max(0, np.random.normal(opponent_projection, opponent_std))
            
            if total_points > opponent_score:
                wins += 1
        
        return wins / num_simulations
    
    def _get_eligible_players_by_slot(
        self,
        players_data: Dict[str, Dict[str, Any]],
        roster_slots: Dict[LineupSlot, int]
    ) -> Dict[LineupSlot, List[str]]:
        """Get eligible players for each lineup slot."""
        eligible = {}
        
        for slot in roster_slots:
            eligible[slot] = []
            
            for player_id, data in players_data.items():
                player = data["player"]
                
                if self._is_player_eligible_for_slot(player.position, slot):
                    eligible[slot].append(player_id)
            
            # Sort by adjusted projection (descending)
            eligible[slot].sort(
                key=lambda pid: players_data[pid]["adjusted_projection"],
                reverse=True
            )
        
        return eligible
    
    def _is_player_eligible_for_slot(self, position: PlayerPosition, slot: LineupSlot) -> bool:
        """Check if a player position is eligible for a lineup slot."""
        position_slot_map = {
            PlayerPosition.QB: [LineupSlot.QB],
            PlayerPosition.RB: [LineupSlot.RB1, LineupSlot.RB2, LineupSlot.FLEX],
            PlayerPosition.WR: [LineupSlot.WR1, LineupSlot.WR2, LineupSlot.FLEX],
            PlayerPosition.TE: [LineupSlot.TE, LineupSlot.FLEX],
            PlayerPosition.K: [LineupSlot.K],
            PlayerPosition.DEF: [LineupSlot.DEF]
        }
        
        return slot in position_slot_map.get(position, [])
    
    def _generate_lineup_combinations(
        self,
        eligible_players: Dict[LineupSlot, List[str]],
        roster_slots: Dict[LineupSlot, int]
    ) -> List[Dict[LineupSlot, str]]:
        """Generate lineup combinations for optimization."""
        # Simplified greedy approach for performance
        # In production, would use more sophisticated optimization
        
        combinations = []
        
        # Start with top player at each position
        base_lineup = {}
        used_players = set()
        
        for slot in roster_slots:
            if slot in eligible_players:
                for player_id in eligible_players[slot]:
                    if player_id not in used_players:
                        base_lineup[slot] = player_id
                        used_players.add(player_id)
                        break
        
        combinations.append(base_lineup)
        
        # Generate variations by swapping players
        for _ in range(min(999, len(eligible_players) * 10)):
            variation = base_lineup.copy()
            
            # Randomly swap one player
            slot_to_change = np.random.choice(list(roster_slots.keys()))
            if slot_to_change in eligible_players:
                available_players = [
                    p for p in eligible_players[slot_to_change]
                    if p not in used_players or p == variation.get(slot_to_change)
                ]
                
                if available_players:
                    old_player = variation.get(slot_to_change)
                    new_player = np.random.choice(available_players)
                    
                    if old_player:
                        used_players.discard(old_player)
                    variation[slot_to_change] = new_player
                    used_players.add(new_player)
                    
                    combinations.append(variation)
        
        return combinations
    
    def _get_matchup_context(self, player: Player, week: int) -> MatchupContext:
        """Get matchup context for a player in a given week."""
        # In a real implementation, this would fetch from external APIs
        # For now, return mock data
        
        return MatchupContext(
            opponent_team="OPP",
            opponent_rank_vs_position=16,  # Average defense
            weather_condition=WeatherCondition.CLEAR,
            temperature=70,
            wind_speed=5,
            is_home_game=True,
            game_total=45.0,
            spread=3.0,
            implied_team_total=24.0
        )
    
    def _get_weather_adjustment(
        self,
        position: PlayerPosition,
        weather: WeatherCondition
    ) -> float:
        """Get weather adjustment multiplier for a position."""
        return self.weather_adjustments.get(position, {}).get(weather, 1.0)
    
    def _get_opponent_adjustment(
        self,
        position: PlayerPosition,
        opponent_rank: Optional[int]
    ) -> float:
        """Get opponent strength adjustment multiplier."""
        if opponent_rank is None:
            return 1.0
        
        # Better defense (lower rank) = lower multiplier
        # Rank 1 (best) = 0.85, Rank 32 (worst) = 1.15
        return 1.0 + (opponent_rank - 16.5) * 0.01
    
    def _get_game_script_adjustment(
        self,
        position: PlayerPosition,
        spread: Optional[float],
        game_total: Optional[float]
    ) -> float:
        """Get game script adjustment based on spread and total."""
        if spread is None or game_total is None:
            return 1.0
        
        # Positive spread = favored team
        # Higher total = more offensive opportunities
        
        base_adjustment = 1.0
        
        if position in [PlayerPosition.QB, PlayerPosition.WR]:
            # Passing positions benefit from being behind (negative spread)
            base_adjustment += max(-0.1, min(0.1, -spread * 0.02))
        elif position == PlayerPosition.RB:
            # Running positions benefit from being ahead (positive spread)
            base_adjustment += max(-0.1, min(0.1, spread * 0.02))
        
        # All positions benefit from higher game totals
        if game_total > 50:
            base_adjustment += 0.05
        elif game_total < 40:
            base_adjustment -= 0.05
        
        return base_adjustment
    
    def _get_injury_adjustment(self, injury_status: InjuryStatus) -> float:
        """Get injury status adjustment multiplier."""
        injury_adjustments = {
            InjuryStatus.HEALTHY: 1.0,
            InjuryStatus.QUESTIONABLE: 0.95,
            InjuryStatus.DOUBTFUL: 0.75,
            InjuryStatus.OUT: 0.0,
            InjuryStatus.IR: 0.0,
            InjuryStatus.PUP: 0.0,
            InjuryStatus.SUSPENDED: 0.0
        }
        
        return injury_adjustments.get(injury_status, 1.0)
    
    def _estimate_opponent_projection(self, franchise_id: str, week: int) -> float:
        """Estimate opponent's projected score for win probability calculation."""
        # In a real implementation, this would analyze historical scoring
        # For now, return league average
        return 100.0
    
    def _generate_lineup_alternatives(
        self,
        players_data: Dict[str, Dict[str, Any]],
        roster_slots: Dict[LineupSlot, int],
        optimal_lineup: Dict[LineupSlot, str],
        opponent_projection: float
    ) -> List[Dict[str, Any]]:
        """Generate alternative lineup options."""
        alternatives = []
        
        # Generate a few alternative lineups by swapping players
        for slot in roster_slots:
            if slot in optimal_lineup:
                current_player = optimal_lineup[slot]
                
                # Find next best player for this slot
                eligible_players = [
                    pid for pid, data in players_data.items()
                    if self._is_player_eligible_for_slot(data["player"].position, slot)
                    and pid != current_player
                ]
                
                if eligible_players:
                    # Sort by projection
                    eligible_players.sort(
                        key=lambda pid: players_data[pid]["adjusted_projection"],
                        reverse=True
                    )
                    
                    alt_player = eligible_players[0]
                    alt_lineup = optimal_lineup.copy()
                    alt_lineup[slot] = alt_player
                    
                    alt_win_prob = self._simulate_win_probability(
                        alt_lineup, players_data, opponent_projection, 100
                    )
                    
                    alternatives.append({
                        "lineup": alt_lineup,
                        "change": f"Start {alt_player} instead of {current_player} at {slot.value}",
                        "win_probability": alt_win_prob,
                        "projected_points": sum(
                            players_data[pid]["adjusted_projection"]
                            for pid in alt_lineup.values()
                        )
                    })
        
        # Sort alternatives by win probability
        alternatives.sort(key=lambda x: x["win_probability"], reverse=True)
        
        return alternatives[:3]  # Return top 3 alternatives
    
    def _calculate_lineup_confidence(
        self,
        lineup: Dict[LineupSlot, str],
        players_data: Dict[str, Dict[str, Any]]
    ) -> float:
        """Calculate confidence level for the lineup."""
        total_variance = sum(
            players_data[player_id]["variance"]
            for player_id in lineup.values()
            if player_id in players_data
        )
        
        # Lower variance = higher confidence
        # Normalize to 0-1 scale
        confidence = max(0.5, min(1.0, 1.0 - (total_variance / 100)))
        
        return confidence
    
    def _calculate_lineup_risk(
        self,
        lineup: Dict[LineupSlot, str],
        players_data: Dict[str, Dict[str, Any]]
    ) -> float:
        """Calculate risk level for the lineup."""
        total_floor = sum(
            players_data[player_id]["floor"]
            for player_id in lineup.values()
            if player_id in players_data
        )
        
        total_projection = sum(
            players_data[player_id]["adjusted_projection"]
            for player_id in lineup.values()
            if player_id in players_data
        )
        
        # Risk = (projection - floor) / projection
        if total_projection > 0:
            risk = (total_projection - total_floor) / total_projection
        else:
            risk = 0.5
        
        return max(0.0, min(1.0, risk))
    
    def _generate_lineup_rationale(
        self,
        lineup: Dict[LineupSlot, str],
        players_data: Dict[str, Dict[str, Any]],
        alternatives: List[Dict[str, Any]]
    ) -> str:
        """Generate rationale for the lineup recommendation."""
        rationale_parts = []
        
        # Highlight key players
        top_players = sorted(
            [(slot, player_id) for slot, player_id in lineup.items()],
            key=lambda x: players_data[x[1]]["adjusted_projection"],
            reverse=True
        )[:3]
        
        for slot, player_id in top_players:
            if player_id in players_data:
                data = players_data[player_id]
                player_name = data["player"].name
                projection = data["adjusted_projection"]
                
                rationale_parts.append(
                    f"{player_name} at {slot.value} ({projection:.1f} projected points)"
                )
        
        # Mention key adjustments
        adjustment_mentions = []
        for player_id in lineup.values():
            if player_id in players_data:
                factors = players_data[player_id].get("adjustment_factors", {})
                
                for factor, multiplier in factors.items():
                    if abs(multiplier - 1.0) > 0.05:  # Significant adjustment
                        if multiplier > 1.0:
                            adjustment_mentions.append(f"positive {factor} matchup")
                        else:
                            adjustment_mentions.append(f"negative {factor} matchup")
        
        rationale = f"Optimal lineup features {', '.join(rationale_parts[:2])}."
        
        if adjustment_mentions:
            rationale += f" Key factors: {', '.join(set(adjustment_mentions[:3]))}."
        
        if alternatives:
            best_alt = alternatives[0]
            rationale += f" Best alternative would be {best_alt['change']}."
        
        return rationale
    
    def _get_current_lineup(self, roster: Roster) -> Dict[LineupSlot, str]:
        """Get the current starting lineup from roster."""
        current_lineup = {}
        
        for roster_player in roster.roster_players:
            if roster_player.is_starting() and roster_player.roster_slot:
                try:
                    slot = LineupSlot(roster_player.roster_slot)
                    current_lineup[slot] = roster_player.player_id
                except ValueError:
                    # Handle non-standard slot names
                    continue
        
        return current_lineup
    
    def _calculate_improvement(
        self,
        start_player_id: str,
        sit_player_id: Optional[str],
        week: int,
        season: int
    ) -> float:
        """Calculate projected improvement from player swap."""
        if not sit_player_id:
            return 0.0
        
        start_projection = self.db.query(Projection).filter(
            Projection.player_id == start_player_id,
            Projection.week == week,
            Projection.season == season
        ).first()
        
        sit_projection = self.db.query(Projection).filter(
            Projection.player_id == sit_player_id,
            Projection.week == week,
            Projection.season == season
        ).first()
        
        if start_projection and sit_projection:
            return float(start_projection.projected_points - sit_projection.projected_points)
        
        return 0.0
    
    def _get_player_lock_time(self, player: Player, week: int) -> Optional[datetime]:
        """Get the lineup lock time for a player."""
        # In a real implementation, this would fetch from NFL schedule API
        # For now, return mock data (Sunday 1 PM ET)
        
        from datetime import datetime, timezone, timedelta
        
        # Assume Sunday games lock at 1 PM ET
        sunday_1pm = datetime.now(timezone.utc).replace(
            hour=18, minute=0, second=0, microsecond=0  # 1 PM ET = 6 PM UTC
        )
        
        # Adjust to next Sunday if needed
        days_until_sunday = (6 - sunday_1pm.weekday()) % 7
        if days_until_sunday == 0 and datetime.now(timezone.utc) > sunday_1pm:
            days_until_sunday = 7
        
        lock_time = sunday_1pm + timedelta(days=days_until_sunday)
        
        return lock_time
    
    def _reoptimize_with_news(
        self,
        franchise_id: str,
        week: int,
        news_adjustments: Dict[str, float]
    ) -> LineupRecommendation:
        """Re-optimize lineup with news adjustments applied."""
        # Get base optimization
        base_recommendation = self.optimize_lineup(franchise_id, week)
        
        # Apply news adjustments (simplified)
        # In a real implementation, would re-run full optimization
        
        return base_recommendation
    
    def _initialize_weather_adjustments(self) -> Dict[PlayerPosition, Dict[WeatherCondition, float]]:
        """Initialize weather adjustment multipliers by position."""
        return {
            PlayerPosition.QB: {
                WeatherCondition.CLEAR: 1.0,
                WeatherCondition.RAIN: 0.95,
                WeatherCondition.SNOW: 0.90,
                WeatherCondition.WIND: 0.85,
                WeatherCondition.DOME: 1.05
            },
            PlayerPosition.RB: {
                WeatherCondition.CLEAR: 1.0,
                WeatherCondition.RAIN: 1.05,
                WeatherCondition.SNOW: 1.10,
                WeatherCondition.WIND: 1.02,
                WeatherCondition.DOME: 0.98
            },
            PlayerPosition.WR: {
                WeatherCondition.CLEAR: 1.0,
                WeatherCondition.RAIN: 0.92,
                WeatherCondition.SNOW: 0.88,
                WeatherCondition.WIND: 0.80,
                WeatherCondition.DOME: 1.05
            },
            PlayerPosition.TE: {
                WeatherCondition.CLEAR: 1.0,
                WeatherCondition.RAIN: 0.95,
                WeatherCondition.SNOW: 0.92,
                WeatherCondition.WIND: 0.90,
                WeatherCondition.DOME: 1.02
            },
            PlayerPosition.K: {
                WeatherCondition.CLEAR: 1.0,
                WeatherCondition.RAIN: 0.90,
                WeatherCondition.SNOW: 0.85,
                WeatherCondition.WIND: 0.75,
                WeatherCondition.DOME: 1.10
            },
            PlayerPosition.DEF: {
                WeatherCondition.CLEAR: 1.0,
                WeatherCondition.RAIN: 1.05,
                WeatherCondition.SNOW: 1.08,
                WeatherCondition.WIND: 1.03,
                WeatherCondition.DOME: 0.98
            }
        }
    
    def _initialize_matchup_adjustments(self) -> Dict[str, Any]:
        """Initialize matchup adjustment parameters."""
        return {
            "opponent_strength_factor": 0.01,
            "game_total_threshold_high": 50,
            "game_total_threshold_low": 40,
            "spread_impact_factor": 0.02
        }