"""
Lightweight MFL API client using httpx.

Builds requests to the public MFL export API and returns JSON.
Falls back to text parse errors if the API returns non-JSON payloads.
"""
from __future__ import annotations

from typing import Any, Dict, Optional
import httpx

from app.core.config import settings


class MFLClient:
    def __init__(self, year: Optional[int] = None, base_url: str = "https://api.myfantasyleague.com") -> None:
        self.year = year
        self.base_url = base_url.rstrip("/")
        # Primary host is the API host; fallback to WWW only for non-ADP endpoints.
        self.fallback_base_url = "https://www.myfantasyleague.com"
        self.api_key = settings.mfl_api_key
        self.default_league_id = settings.mfl_league_id
        self.timeout = 15.0

    def _build_url(self, *, use_api: bool = True) -> str:
        year = self.year
        if not year:
            # default to current year
            import datetime as _dt
            year = _dt.datetime.now().year
        base = self.base_url if use_api else self.fallback_base_url.rstrip("/")
        return f"{base}/{year}/export"

    async def export(self, params: Dict[str, Any]) -> Dict[str, Any]:
        # Assemble query params: add JSON=1 by default, API key if available
        query: Dict[str, Any] = {"JSON": 1, **params}
        endpoint_type = str(query.get("TYPE", "")).lower()
        # For ADP, it is a global endpoint; do not include league id or api key.
        if endpoint_type == "adp":
            query.pop("L", None)
            query.pop("APIKEY", None)
        else:
            # Allow default league id if caller didn't supply L
            if "L" not in query and self.default_league_id:
                query["L"] = self.default_league_id
            # Some MFL installs expect APIKEY param for private data; include if set
            if self.api_key and "APIKEY" not in query:
                query["APIKEY"] = self.api_key
        async with httpx.AsyncClient(timeout=self.timeout, follow_redirects=True) as client:
            # Helper to perform a GET and parse JSON with helpful error on non-JSON
            async def _get_json(url: str) -> Dict[str, Any]:
                resp = await client.get(url, params=query, headers={
                    "User-Agent": "AI Fantasy Assistant/1.0",
                    "Accept": "application/json, text/plain, */*",
                    "Cache-Control": "no-cache",
                })
                resp.raise_for_status()
                try:
                    data = resp.json()
                except ValueError:
                    text = resp.text
                    raise httpx.HTTPError(
                        f"MFL returned non-JSON response (len={len(text)}). First 200 chars: {text[:200]!r}")
                return data

            # Always prefer the API host
            api_url = self._build_url(use_api=True)
            www_url = self._build_url(use_api=False)

            # For ADP, some hosts require the API host explicitly. Avoid fallback to WWW.
            if endpoint_type == "adp":
                data = await _get_json(api_url)
                # If API returns an explicit error instructing to use API host, raise to surface issue
                if isinstance(data, dict) and data.get("error"):
                    # Try to detect error message pattern
                    err_txt = None
                    err = data.get("error")
                    if isinstance(err, dict):
                        err_txt = err.get("$t") or err.get("msg")
                    if isinstance(err, str):
                        err_txt = err
                    if err_txt and "api.myfantasyleague.com" in err_txt:
                        raise httpx.HTTPError(f"MFL ADP request rejected with message: {err_txt}")
                return data

            # Non-ADP: allow fallback to WWW if API host fails
            try:
                return await _get_json(api_url)
            except Exception:
                # Fallback to www host
                return await _get_json(www_url)
