"""
ADP aggregation and ingestion service.

- Fetches MFL ADP (via existing mfl_proxy) and Sleeper ADP (PPR)
- Normalizes to rankings rows (ranking_type='adp')
- Computes composite ADP Index (ranking_type='adp_index') with adjustable weights
"""
from __future__ import annotations

from typing import Any, Dict, List, Tuple
from datetime import datetime, timezone, timedelta
from decimal import Decimal
import uuid

from sqlalchemy.orm import Session

from ..models.ranking import Ranking
from ..models.player import Player
from ..models.league import League
from .mfl_client import MFLClient
from .ffc_client import FFCClient
from .adp_config import load_adp_config

import os
import json
import re

EXCLUDED_POSITIONS = {"K","DL","LB","DB"}

class ADPAggregationService:
    def __init__(self, db: Session, league_id: str, season: int) -> None:
        self.db = db
        self.league_id = league_id
        self.season = season
        self.config = load_adp_config(league_id)
        self.mfl = MFLClient(year=season)
        self.ffc = FFCClient()
        # data directory for unmapped cache
        from .adp_config import BASE_DIR
        self._data_dir = os.path.join(BASE_DIR, "data", "adp")
        os.makedirs(self._data_dir, exist_ok=True)

    def _now(self) -> datetime:
        return datetime.now(timezone.utc)

    async def fetch_and_store_providers(self) -> Dict[str, Any]:
        results = {"mfl": 0, "sleeper": 0}
        # clear previous unmapped for this league on refresh start
        self._save_unmapped({"league_id": self.league_id, "season": self.season, "updated_at": self._now().isoformat(), "items": []})
        # MFL ADP via MFLClient export (TYPE=adp)
        if "MFL" in self.config.get("providers", []):
            try:
                data = await self.mfl.export({"TYPE": "adp", "YEAR": self.season, "JSON": 1})
                # Normalize potential nested formats
                rows = []
                if isinstance(data, dict):
                    # common shapes: { adp: { player: [...] } } or { adp: [...] }
                    adp = data.get("adp") if isinstance(data.get("adp"), (dict, list)) else data
                    if isinstance(adp, dict):
                        payload = adp.get("player") or adp.get("adp") or []
                        rows = payload if isinstance(payload, list) else ([payload] if payload else [])
                    elif isinstance(adp, list):
                        rows = adp
                elif isinstance(data, list):
                    rows = data
                results["mfl"] = self._upsert_mfl_adp(rows)
            except Exception:
                pass
        # FFC ADP (PPR)
        if "FFC" in self.config.get("providers", []):
            try:
                positions = ["QB","RB","WR","TE"]
                total = 0
                for pos in positions:
                    payload = await self.ffc.get_adp(scoring="ppr", teams=12, year=self.season, position=pos)
                    rows = payload.get("players", []) if isinstance(payload, dict) else []
                    total += self._upsert_ffc_adp(rows)
                results["ffc"] = total
            except Exception:
                pass
        # Apply rookie metadata from MFL players (years_exp or draft_year)
        try:
            results["mfl_rookies"] = await self._apply_mfl_rookies()
        except Exception:
            results["mfl_rookies"] = 0
        # Apply rookies override file rookies_{season}.json, if available
        try:
            results["rookies_file"] = self._apply_rookies()
        except Exception:
            results["rookies_file"] = 0
        return results

    def _excluded_position(self, pos: str | None) -> bool:
        if not pos:
            return False
        return pos.upper() in set(self.config.get("exclude_positions", []))

    def _rookies_file_path(self) -> str:
        from .adp_config import BASE_DIR
        return os.path.join(BASE_DIR, "data", "rookies", f"rookies_{self.season}.json")

    async def _apply_mfl_rookies(self) -> int:
        """Fetch MFL players and mark rookies based on years_exp or draft_year == season."""
        try:
            data = await self.mfl.export({"TYPE": "players", "JSON": 1})
        except Exception:
            return 0
        rows = []
        # try common shapes
        if isinstance(data, dict):
            players = data.get("players") or data.get("player") or data
            if isinstance(players, dict):
                payload = players.get("player") or players.get("players") or []
                rows = payload if isinstance(payload, list) else ([payload] if payload else [])
            elif isinstance(players, list):
                rows = players
        elif isinstance(data, list):
            rows = data
        updated = 0
        for r in rows:
            pid = str(r.get("id") or r.get("player_id") or "").strip()
            if not pid:
                continue
            try:
                years = r.get("years_exp") or r.get("years_exp_display")
                draft_year = r.get("draft_year") or r.get("draftroundyear")
                status = r.get("status")
            except Exception:
                years = None
                draft_year = None
                status = None
            is_rook = False
            # 1) Definitive flag from MFL: status == 'R'
            try:
                if status is not None and str(status).strip().upper() == "R":
                    is_rook = True
            except Exception:
                pass
            # 2) Fallbacks: years_exp == 0 or draft_year == season
            if not is_rook:
                try:
                    if years is not None and str(years).strip() != "":
                        is_rook = str(years).strip() in ("0", "R", "ROOKIE") or int(str(years).strip()) == 0
                except Exception:
                    pass
            if not is_rook and draft_year is not None:
                try:
                    is_rook = int(str(draft_year)) == int(self.season)
                except Exception:
                    pass
            if not is_rook:
                continue
            canonical_id = f"mfl_{pid}" if not pid.startswith("mfl_") else pid
            player = self.db.query(Player).filter(Player.id == canonical_id).first()
            if not player:
                continue
            md = dict(player.player_metadata or {})
            changed = False
            if not md.get("rookie"):
                md["rookie"] = True
                changed = True
            if md.get("rookie_year") != self.season:
                md["rookie_year"] = self.season
                changed = True
            if md.get("experience") is None:
                md["experience"] = 0
                changed = True
            if changed:
                player.player_metadata = md
                updated += 1
        if updated:
            self.db.commit()
        return updated

    def _apply_rookies(self) -> int:
        """Load rookies list and stamp metadata (rookie, rookie_year, experience=0)."""
        path = self._rookies_file_path()
        if not os.path.exists(path):
            return 0
        try:
            with open(path, "r") as f:
                payload = json.load(f)
        except Exception:
            return 0
        plist = payload.get("players") or []
        updated = 0
        for entry in plist:
            pid = str(entry.get("player_id") or "").strip()
            name = (entry.get("name") or "").strip()
            pos = (entry.get("position") or "").strip().upper() or None
            player: Player | None = None
            if pid:
                player = self.db.query(Player).filter(Player.id == pid).first()
            if not player and name:
                # try direct substring match of the full name
                q = self.db.query(Player).filter(Player.name.ilike(f"%{name}%"))
                candidates = q.all()
                if candidates and pos:
                    # prefer exact position match
                    for c in candidates:
                        try:
                            if str(c.position.value).upper() == pos:
                                player = c
                                break
                        except Exception:
                            continue
                if not player and candidates:
                    player = candidates[0]
                # token-based fallback (handles "Last, First" vs "First Last")
                if not player:
                    tokens = [t for t in re.split(r"[\s,]+", name) if t]
                    if len(tokens) >= 2:
                        q_tokens = self.db.query(Player)
                        for t in tokens:
                            q_tokens = q_tokens.filter(Player.name.ilike(f"%{t}%"))
                        token_candidates = q_tokens.all()
                        if token_candidates and pos:
                            for c in token_candidates:
                                try:
                                    if str(c.position.value).upper() == pos:
                                        player = c
                                        break
                                except Exception:
                                    continue
                        if not player and token_candidates:
                            player = token_candidates[0]
            if not player:
                continue
            md = dict(player.player_metadata or {})
            changed = False
            if not md.get("rookie"):
                md["rookie"] = True
                changed = True
            if md.get("rookie_year") != self.season:
                md["rookie_year"] = self.season
                changed = True
            # prefer experience=0 for rookies if absent
            if md.get("experience") is None:
                md["experience"] = 0
                changed = True
            if changed:
                player.player_metadata = md
                updated += 1
        if updated:
            self.db.commit()
        return updated

    def _upsert_mfl_adp(self, rows: List[Dict[str, Any]]) -> int:
        count = 0
        now = self._now().isoformat()
        for r in rows:
            pid = str(r.get("id") or r.get("player_id") or "").strip()
            pos = str(r.get("position") or r.get("pos") or "").upper()
            if not pid or self._excluded_position(pos):
                continue
            adp_val = r.get("adp") or r.get("averagePick") or r.get("avg_pick")
            try:
                adp = float(adp_val)
            except Exception:
                continue
            canonical_id = f"mfl_{pid}" if not str(pid).startswith("mfl_") else str(pid)
            self._upsert_ranking(
                player_id=canonical_id,
                source="MFL",
                ranking_type="adp",
                adp=adp,
                adp_std=None,
                draft_count=None,
                metadata={"fetched_at": now, "position": pos}
            )
            count += 1
        self.db.commit()
        return count

    def _map_sleeper_to_canonical(self, sleeper_pid: str, name: str | None, team: str | None, position: str | None) -> str | None:
        """
        Map external player reference to our canonical Player.id.

        Strategy:
        1) If a Sleeper ID is provided, try exact metadata match.
        2) Try name + team + position (best-effort).
        3) If none found, relax team constraint and try name + position only.
        """
        # 1) Exact mapping on stored sleeper_id
        if sleeper_pid:
            player = self.db.query(Player).filter(Player.player_metadata["sleeper_id"].as_string() == sleeper_pid).first()
            if player:
                return player.id

        # 2) Name + team (+ position preference)
        if not name:
            return None
        team_upper = str(team).upper() if team else None

        def pick_by_position(candidates: list[Player]) -> str | None:
            # choose first with matching position if possible; else first
            for p in candidates:
                try:
                    if position and str(p.position.value).upper() == str(position).upper():
                        return p.id
                except Exception:
                    continue
            return candidates[0].id if candidates else None

        q = self.db.query(Player).filter(Player.name.ilike(f"%{name}%"))
        if team_upper:
            q_team = q.filter(Player.team == team_upper)
            players = q_team.all()
            if players:
                picked = pick_by_position(players)
                if picked:
                    return picked
        else:
            players = q.all()
            if players:
                picked = pick_by_position(players)
                if picked:
                    return picked

        # 3) Relax team constraint: try name + position only
        q_relaxed = self.db.query(Player).filter(Player.name.ilike(f"%{name}%"))
        players_relaxed = q_relaxed.all()
        if players_relaxed:
            picked = pick_by_position(players_relaxed)
            if picked:
                return picked

        # 4) Token-based fallback: require all tokens (e.g., "First Last" vs "Last, First")
        try:
            tokens = [t for t in name.replace(",", " ").split() if t]
        except Exception:
            tokens = []
        if len(tokens) >= 2:
            q_tokens = self.db.query(Player)
            for t in tokens:
                q_tokens = q_tokens.filter(Player.name.ilike(f"%{t}%"))
            players_tokens = q_tokens.all()
            if players_tokens:
                picked = pick_by_position(players_tokens)
                if picked:
                    return picked

        return None

    def _upsert_ffc_adp(self, rows: List[Dict[str, Any]]) -> int:
        count = 0
        now = self._now().isoformat()
        for r in rows:
            name = (r.get("name") or "").strip()
            pos = (r.get("position") or "").upper()
            team = (r.get("team") or None)
            if not name or self._excluded_position(pos):
                continue
            try:
                adp = float(r.get("adp")) if r.get("adp") is not None else None
            except Exception:
                adp = None
            if adp is None:
                continue
            # map to canonical by name+team+position
            canonical_id = self._map_sleeper_to_canonical("", name, team, pos)  # reuse mapping logic sans sleeper_id
            if not canonical_id:
                continue
            self._upsert_ranking(
                player_id=canonical_id,
                source="FFC",
                ranking_type="adp",
                adp=adp,
                adp_std=float(r.get("stdev")) if r.get("stdev") is not None else None,
                draft_count=int(r.get("times_drafted")) if r.get("times_drafted") is not None else None,
                metadata={"fetched_at": now, "position": pos, "team": team, "ffc_player_id": r.get("player_id")}
            )
            count += 1
        self.db.commit()
        return count

    def _upsert_sleeper_adp(self, rows: List[Dict[str, Any]]) -> int:
        count = 0
        now = self._now().isoformat()
        unmapped: List[Dict[str, Any]] = []
        for r in rows:
            sleeper_id = str(r.get("player_id") or r.get("id") or "").strip()
            pos = str(r.get("position") or r.get("pos") or "").upper()
            team = str(r.get("team") or r.get("team_abbr") or "").upper() or None
            name = r.get("name") or r.get("full_name") or None
            if not sleeper_id or self._excluded_position(pos):
                continue
            adp_val = r.get("adp") or r.get("average_draft_position")
            try:
                adp = float(adp_val)
            except Exception:
                continue
            canonical_id = self._map_sleeper_to_canonical(sleeper_id, name, team, pos)
            if not canonical_id:
                # record unmapped for review UI
                unmapped.append({
                    "sleeper_id": sleeper_id,
                    "name": name,
                    "team": team,
                    "position": pos,
                    "adp": adp,
                })
                continue
            self._upsert_ranking(
                player_id=canonical_id,
                source="SLEEPER",
                ranking_type="adp",
                adp=adp,
                adp_std=None,
                draft_count=None,
                metadata={"fetched_at": now, "position": pos, "sleeper_id": sleeper_id}
            )
            count += 1
        self.db.commit()
        if unmapped:
            payload = {
                "league_id": self.league_id,
                "season": self.season,
                "updated_at": now,
                "items": unmapped,
            }
            self._save_unmapped(payload)
        return count

    def _upsert_ranking(self, *, player_id: str, source: str, ranking_type: str, adp: float | None, adp_std: float | None, draft_count: int | None, metadata: Dict[str, Any]) -> None:
        row = (
            self.db.query(Ranking)
            .filter(
                Ranking.player_id == player_id,
                Ranking.season == self.season,
                Ranking.source == source,
                Ranking.ranking_type == ranking_type,
            )
            .first()
        )
        if not row:
            row = Ranking(
                id=str(uuid.uuid4()),
                player_id=player_id,
                season=self.season,
                source=source,
                ranking_type=ranking_type,
                overall_rank=None,
                position_rank=None,
                tier=None,
                adp=Decimal(str(adp)) if adp is not None else None,
                adp_std=Decimal(str(adp_std)) if adp_std is not None else None,
                draft_count=draft_count,
                ranking_metadata=metadata,
                is_active=True,
            )
            self.db.add(row)
        else:
            row.adp = Decimal(str(adp)) if adp is not None else None
            row.adp_std = Decimal(str(adp_std)) if adp_std is not None else None
            row.draft_count = draft_count
            md = dict(row.ranking_metadata or {})
            md.update(metadata or {})
            row.ranking_metadata = md
            row.is_active = True

    def _unmapped_path(self) -> str:
        return os.path.join(self._data_dir, f"unmapped_{self.league_id}.json")

    def _save_unmapped(self, payload: Dict[str, Any]) -> None:
        try:
            with open(self._unmapped_path(), "w") as f:
                json.dump(payload, f, indent=2)
        except Exception:
            pass

    def load_unmapped(self) -> Dict[str, Any]:
        try:
            with open(self._unmapped_path(), "r") as f:
                return json.load(f)
        except Exception:
            return {"league_id": self.league_id, "season": self.season, "updated_at": None, "items": []}

    def remove_unmapped_by_sleeper_ids(self, sleeper_ids: List[str]) -> Dict[str, Any]:
        payload = self.load_unmapped()
        items = payload.get("items", [])
        remaining = [it for it in items if str(it.get("sleeper_id")) not in set(map(str, sleeper_ids))]
        payload["items"] = remaining
        payload["updated_at"] = self._now().isoformat()
        self._save_unmapped(payload)
        return payload

    def compute_and_store_index(self) -> Dict[str, Any]:
        cfg = self.config
        weights = {k.upper(): float(v) for k, v in cfg.get("weights", {}).items()}
        staleness_days = int(cfg.get("staleness_days", 14))
        cutoff = self._now() - timedelta(days=staleness_days)
        cutoff_iso = cutoff.isoformat()

        # Load all provider ADP rows for season
        # Load provider rows based on configured providers and positive weights
        provider_whitelist = [src for src, w in {k.upper(): float(v) for k, v in self.config.get("weights", {}).items()}.items() if w > 0.0]
        if not provider_whitelist:
            provider_whitelist = ["MFL", "FFC"]
        rows: List[Ranking] = (
            self.db.query(Ranking)
            .filter(
                Ranking.season == self.season,
                Ranking.ranking_type == "adp",
                Ranking.source.in_(provider_whitelist),
                Ranking.is_active == True,
            )
            .all()
        )
        # Group by player
        per_player: Dict[str, List[Ranking]] = {}
        for r in rows:
            # staleness check by fetched_at if present, otherwise include
            fetched_at = None
            try:
                fetched_at = r.ranking_metadata.get("fetched_at")
            except Exception:
                pass
            if fetched_at:
                try:
                    dt = datetime.fromisoformat(str(fetched_at))
                    if dt < cutoff:
                        continue
                except Exception:
                    pass
            per_player.setdefault(r.player_id, []).append(r)

        updated = 0
        # compute composite and write ranking_type='adp_index'
        for pid, items in per_player.items():
            # collect provider values and weights
            values: List[Tuple[float, float, str, str]] = []  # (adp, weight, source, fetched_at)
            meta_sources: List[Dict[str, Any]] = []
            for r in items:
                if r.adp is None:
                    continue
                src = (r.source or "").upper()
                w = float(weights.get(src, 0.0))
                if w <= 0:
                    continue
                fetched_at = None
                try:
                    fetched_at = r.ranking_metadata.get("fetched_at")
                except Exception:
                    fetched_at = None
                values.append((float(r.adp), w, src, fetched_at))
                meta_sources.append({
                    "source": src,
                    "adp": float(r.adp),
                    "weight": w,
                    "fetched_at": fetched_at,
                })
            if not values:
                continue
            total_w = sum(v[1] for v in values)
            if total_w <= 0:
                continue
            composite = sum(v[0] * v[1] for v in values) / total_w
            # simple dispersion measure
            mean = composite
            variance = sum(v[1] * (v[0] - mean) ** 2 for v in values) / total_w
            stddev = variance ** 0.5
            self._upsert_ranking(
                player_id=pid,
                source="composite",
                ranking_type="adp_index",
                adp=composite,
                adp_std=stddev,
                draft_count=None,
                metadata={"computed_at": self._now().isoformat(), "sources": meta_sources, "weights": weights, "staleness_days": staleness_days}
            )
            updated += 1
        self.db.commit()

        # Optionally compute overall ranks by ordering composite ADP
        comps: List[Ranking] = (
            self.db.query(Ranking)
            .filter(
                Ranking.season == self.season,
                Ranking.ranking_type == "adp_index",
                Ranking.source == "composite",
                Ranking.is_active == True,
            )
            .all()
        )
        comps_sorted = sorted([c for c in comps if c.adp is not None], key=lambda c: float(c.adp))
        for i, r in enumerate(comps_sorted, start=1):
            r.overall_rank = i
        self.db.commit()

        return {"composite_updated": updated, "total_index_rows": len(comps_sorted)}

