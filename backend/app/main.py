from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import os

from .api import get_api_router
from .middleware.monitoring import RequestLoggingMiddleware, ErrorHandlingMiddleware
from .core.logging import get_logger

logger = get_logger(__name__)

app = FastAPI(
    title="AI Fantasy Assistant API",
    description="""
    Fantasy football management with AI recommendations.
    
    This API provides comprehensive fantasy football management capabilities including:
    
    * **League Management**: Create and configure fantasy leagues with custom rules
    * **Franchise Management**: Manage teams, rosters, and financial settings
    * **Data Ingestion**: Upload projections, rankings, and integrate with MFL
    * **AI Recommendations**: Get intelligent suggestions for keepers, draft, trades, lineups, and waivers
    * **Decision Engines**: Access all optimization algorithms with detailed explanations
    * **Alerts & Notifications**: Stay informed of deadlines and opportunities
    
    All recommendations include detailed rationale and confidence levels for transparent decision-making.
    """,
    version="1.0.0",
    contact={
        "name": "AI Fantasy Assistant",
        "url": "https://github.com/your-repo/ai-fantasy-assistant",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    openapi_tags=[
        {
            "name": "leagues",
            "description": "League management and configuration"
        },
        {
            "name": "franchises", 
            "description": "Franchise and roster management"
        },
        {
            "name": "recommendations",
            "description": "AI recommendations with explanations"
        },
        {
            "name": "keepers",
            "description": "Keeper optimization and analysis"
        },
        {
            "name": "draft",
            "description": "Draft assistance and board generation"
        },
        {
            "name": "trades",
            "description": "Trade analysis and suggestions"
        },
        {
            "name": "lineup",
            "description": "Weekly lineup optimization"
        },
        {
            "name": "waiver",
            "description": "Waiver wire and FAAB optimization"
        },
        {
            "name": "projections",
            "description": "Player projections and aggregation"
        },
        {
            "name": "rules",
            "description": "League rules engine and validation"
        },
        {
            "name": "upload",
            "description": "File upload and data processing"
        },
        {
            "name": "alerts",
            "description": "Alerts and notifications"
        },
        {
            "name": "jobs",
            "description": "Background job monitoring and management"
        }
    ]
)

# Add monitoring middleware (gate behind env to diagnose timeouts)
_DISABLE_CUSTOM_MIDDLEWARE = os.getenv("DISABLE_CUSTOM_MIDDLEWARE", "0").lower() in ("1", "true", "yes")
if not _DISABLE_CUSTOM_MIDDLEWARE:
    app.add_middleware(ErrorHandlingMiddleware)
    app.add_middleware(RequestLoggingMiddleware)

# Configure CORS for frontend (configurable via CORS_ORIGINS env)
origins_env = os.getenv("CORS_ORIGINS", "http://localhost:3000")
allow_origins = [o.strip() for o in origins_env.split(",") if o.strip()]
app.add_middleware(
    CORSMiddleware,
    allow_origins=allow_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Conditionally include heavy API routes. In minimal mode, expose only basic endpoints for health.
_MINIMAL = os.getenv("MINIMAL_START", "0").lower() in ("1", "true", "yes")
if not _MINIMAL:
    # Include API routes lazily to avoid heavy import-time side effects
    app.include_router(get_api_router(include_all=True))

@app.get("/")
async def root():
    return {"message": "AI Fantasy Assistant API"}

@app.get("/health")
async def health_check():
    """Basic health check endpoint."""
    logger.info("Health check requested")
    return {
        "status": "healthy",
        "service": "ai-fantasy-assistant",
        "timestamp": "2024-01-01T00:00:00Z"
    }
