from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .api import api_router

app = FastAPI(
    title="AI Fantasy Assistant API",
    description="""
    Fantasy football management with AI recommendations.
    
    This API provides comprehensive fantasy football management capabilities including:
    
    * **League Management**: Create and configure fantasy leagues with custom rules
    * **Franchise Management**: Manage teams, rosters, and financial settings
    * **Data Ingestion**: Upload projections, rankings, and integrate with MFL
    * **AI Recommendations**: Get intelligent suggestions for keepers, draft, trades, lineups, and waivers
    * **Decision Engines**: Access all optimization algorithms with detailed explanations
    * **Alerts & Notifications**: Stay informed of deadlines and opportunities
    
    All recommendations include detailed rationale and confidence levels for transparent decision-making.
    """,
    version="1.0.0",
    contact={
        "name": "AI Fantasy Assistant",
        "url": "https://github.com/your-repo/ai-fantasy-assistant",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    openapi_tags=[
        {
            "name": "leagues",
            "description": "League management and configuration"
        },
        {
            "name": "franchises", 
            "description": "Franchise and roster management"
        },
        {
            "name": "recommendations",
            "description": "AI recommendations with explanations"
        },
        {
            "name": "keepers",
            "description": "Keeper optimization and analysis"
        },
        {
            "name": "draft",
            "description": "Draft assistance and board generation"
        },
        {
            "name": "trades",
            "description": "Trade analysis and suggestions"
        },
        {
            "name": "lineup",
            "description": "Weekly lineup optimization"
        },
        {
            "name": "waiver",
            "description": "Waiver wire and FAAB optimization"
        },
        {
            "name": "projections",
            "description": "Player projections and aggregation"
        },
        {
            "name": "rules",
            "description": "League rules engine and validation"
        },
        {
            "name": "upload",
            "description": "File upload and data processing"
        },
        {
            "name": "alerts",
            "description": "Alerts and notifications"
        }
    ]
)

# Configure CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Next.js dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router)

@app.get("/")
async def root():
    return {"message": "AI Fantasy Assistant API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}