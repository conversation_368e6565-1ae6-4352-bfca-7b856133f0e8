"""
Middleware for monitoring, logging, and metrics collection.
"""
import time
import uuid
from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from ..core.logging import get_logger, log_with_context
from ..core.metrics import increment_counter, observe_histogram, set_gauge

logger = get_logger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests and responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Add request ID to request state
        request.state.request_id = request_id
        
        # Log request start
        start_time = time.time()
        
        log_with_context(
            logger,
            20,  # INFO level
            f"Request started: {request.method} {request.url.path}",
            request_id=request_id,
            method=request.method,
            path=request.url.path,
            query_params=str(request.query_params),
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent", "")
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            duration_ms = duration * 1000
            
            # Log response
            log_with_context(
                logger,
                20,  # INFO level
                f"Request completed: {request.method} {request.url.path} - {response.status_code}",
                request_id=request_id,
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                duration_ms=duration_ms
            )
            
            # Update metrics
            increment_counter("http_requests_total", labels={
                "method": request.method,
                "path": request.url.path,
                "status": str(response.status_code)
            })
            
            observe_histogram("http_request_duration", duration, labels={
                "method": request.method,
                "path": request.url.path
            })
            
            # Track errors
            if response.status_code >= 400:
                increment_counter("http_requests_errors", labels={
                    "method": request.method,
                    "path": request.url.path,
                    "status": str(response.status_code)
                })
            
            return response
            
        except Exception as e:
            # Calculate duration for failed requests
            duration = time.time() - start_time
            duration_ms = duration * 1000
            
            # Log error
            log_with_context(
                logger,
                40,  # ERROR level
                f"Request failed: {request.method} {request.url.path} - {str(e)}",
                request_id=request_id,
                method=request.method,
                path=request.url.path,
                error=str(e),
                error_type=type(e).__name__,
                duration_ms=duration_ms
            )
            
            # Update error metrics
            increment_counter("http_requests_errors", labels={
                "method": request.method,
                "path": request.url.path,
                "status": "500"
            })
            
            # Re-raise the exception
            raise


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting application metrics."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Track active requests
        set_gauge("http_active_requests", 1)  # This would need proper increment/decrement
        
        try:
            response = await call_next(request)
            return response
        finally:
            # This is a simplified approach - in practice you'd want proper
            # increment/decrement for active requests
            pass


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for handling and logging unhandled exceptions."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            return await call_next(request)
        except Exception as e:
            # Log the unhandled exception
            request_id = getattr(request.state, 'request_id', 'unknown')
            
            log_with_context(
                logger,
                50,  # CRITICAL level
                f"Unhandled exception in request: {str(e)}",
                request_id=request_id,
                method=request.method,
                path=request.url.path,
                error=str(e),
                error_type=type(e).__name__
            )
            
            # Return a generic error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "request_id": request_id,
                    "timestamp": time.time()
                }
            )