"""
API endpoints for league rules management and validation.
"""
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..core.database import get_db
from ..models.league import League
from ..models.roster import Roster
from ..services.rules_engine import RulesEngine, LeagueRulesSchema, KeeperRulesSchema


router = APIRouter(prefix="/rules", tags=["rules"])


class RulesValidationRequest(BaseModel):
    """Request model for rules validation."""
    rules: Dict[str, Any]


class RulesValidationResponse(BaseModel):
    """Response model for rules validation."""
    is_valid: bool
    errors: List[str]


class ScoreCalculationRequest(BaseModel):
    """Request model for score calculation."""
    player_stats: Dict[str, float]
    scoring_rules: Dict[str, float]


class ScoreCalculationResponse(BaseModel):
    """Response model for score calculation."""
    total_points: float
    breakdown: Dict[str, float]


class RosterValidationRequest(BaseModel):
    """Request model for roster validation."""
    roster_id: str
    league_id: str


class RosterValidationResponse(BaseModel):
    """Response model for roster validation."""
    is_valid: bool
    violations: List[str]


@router.post("/validate", response_model=RulesValidationResponse)
async def validate_league_rules(
    request: RulesValidationRequest,
    rules_engine: RulesEngine = Depends(lambda: RulesEngine())
):
    """
    Validate league rules against the schema.
    
    Args:
        request: Rules validation request containing rules to validate
        rules_engine: Rules engine dependency
        
    Returns:
        Validation result with any errors
    """
    is_valid, errors = rules_engine.validate_league_rules(request.rules)
    return RulesValidationResponse(is_valid=is_valid, errors=errors)


@router.post("/calculate-score", response_model=ScoreCalculationResponse)
async def calculate_player_score(
    request: ScoreCalculationRequest,
    rules_engine: RulesEngine = Depends(lambda: RulesEngine())
):
    """
    Calculate fantasy points for a player based on stats and scoring rules.
    
    Args:
        request: Score calculation request with stats and rules
        rules_engine: Rules engine dependency
        
    Returns:
        Total points and breakdown by stat category
    """
    total_points = rules_engine.calculate_player_score(
        request.player_stats, 
        request.scoring_rules
    )
    
    # Calculate breakdown
    breakdown = {}
    for stat_name, stat_value in request.player_stats.items():
        if stat_name in request.scoring_rules:
            points = float(stat_value) * request.scoring_rules[stat_name]
            breakdown[stat_name] = round(points, 2)
    
    return ScoreCalculationResponse(
        total_points=total_points,
        breakdown=breakdown
    )


@router.post("/validate-roster", response_model=RosterValidationResponse)
async def validate_roster_constraints(
    request: RosterValidationRequest,
    db: Session = Depends(get_db),
    rules_engine: RulesEngine = Depends(lambda: RulesEngine())
):
    """
    Validate that a roster meets league constraints.
    
    Args:
        request: Roster validation request
        db: Database session
        rules_engine: Rules engine dependency
        
    Returns:
        Validation result with any constraint violations
    """
    # Get league and roster from database
    league = db.query(League).filter(League.id == request.league_id).first()
    if not league:
        raise HTTPException(status_code=404, detail="League not found")
    
    roster = db.query(Roster).filter(Roster.id == request.roster_id).first()
    if not roster:
        raise HTTPException(status_code=404, detail="Roster not found")
    
    # Validate roster constraints
    is_valid, violations = rules_engine.validate_roster_constraints(
        roster, 
        league.roster_slots
    )
    
    return RosterValidationResponse(
        is_valid=is_valid,
        violations=violations
    )


@router.get("/schema")
async def get_league_rules_schema(
    rules_engine: RulesEngine = Depends(lambda: RulesEngine())
):
    """
    Get the JSON schema for league rules validation.
    
    Returns:
        JSON schema for league rules
    """
    return rules_engine.get_league_rules_json_schema()


@router.get("/league/{league_id}/requirements")
async def get_roster_requirements(
    league_id: str,
    db: Session = Depends(get_db),
    rules_engine: RulesEngine = Depends(lambda: RulesEngine())
):
    """
    Get roster slot requirements for a league.
    
    Args:
        league_id: League identifier
        db: Database session
        rules_engine: Rules engine dependency
        
    Returns:
        Roster requirements by position
    """
    league = db.query(League).filter(League.id == league_id).first()
    if not league:
        raise HTTPException(status_code=404, detail="League not found")
    
    requirements = rules_engine.get_roster_slot_requirements(league.roster_slots)
    return requirements


@router.post("/keeper/validate")
async def validate_keeper_selection(
    league_id: str,
    keeper_player_ids: List[str],
    db: Session = Depends(get_db),
    rules_engine: RulesEngine = Depends(lambda: RulesEngine())
):
    """
    Validate keeper selections against league keeper rules.
    
    Args:
        league_id: League identifier
        keeper_player_ids: List of player IDs selected as keepers
        db: Database session
        rules_engine: Rules engine dependency
        
    Returns:
        Validation result with any rule violations
    """
    league = db.query(League).filter(League.id == league_id).first()
    if not league:
        raise HTTPException(status_code=404, detail="League not found")
    
    if not league.keeper_rules:
        return {"is_valid": True, "violations": []}
    
    # This would need to be implemented with actual roster player queries
    # For now, return a placeholder response
    return {
        "is_valid": True,
        "violations": [],
        "message": "Keeper validation endpoint - implementation pending roster player queries"
    }


@router.get("/examples/scoring")
async def get_scoring_examples():
    """
    Get examples of different scoring systems.
    
    Returns:
        Dictionary of example scoring configurations
    """
    return {
        "standard": {
            "passing_yards": 0.04,
            "passing_touchdowns": 4.0,
            "passing_interceptions": -2.0,
            "rushing_yards": 0.1,
            "rushing_touchdowns": 6.0,
            "receiving_yards": 0.1,
            "receiving_touchdowns": 6.0,
            "receptions": 0.0,
            "fumbles_lost": -2.0,
        },
        "ppr": {
            "passing_yards": 0.04,
            "passing_touchdowns": 4.0,
            "passing_interceptions": -2.0,
            "rushing_yards": 0.1,
            "rushing_touchdowns": 6.0,
            "receiving_yards": 0.1,
            "receiving_touchdowns": 6.0,
            "receptions": 1.0,  # Full PPR
            "fumbles_lost": -2.0,
        },
        "half_ppr": {
            "passing_yards": 0.04,
            "passing_touchdowns": 4.0,
            "passing_interceptions": -2.0,
            "rushing_yards": 0.1,
            "rushing_touchdowns": 6.0,
            "receiving_yards": 0.1,
            "receiving_touchdowns": 6.0,
            "receptions": 0.5,  # Half PPR
            "fumbles_lost": -2.0,
        },
        "superflex": {
            "passing_yards": 0.04,
            "passing_touchdowns": 6.0,  # Higher QB scoring
            "passing_interceptions": -2.0,
            "rushing_yards": 0.1,
            "rushing_touchdowns": 6.0,
            "receiving_yards": 0.1,
            "receiving_touchdowns": 6.0,
            "receptions": 1.0,
            "fumbles_lost": -2.0,
        }
    }


@router.get("/examples/roster")
async def get_roster_examples():
    """
    Get examples of different roster configurations.
    
    Returns:
        Dictionary of example roster configurations
    """
    return {
        "standard": [
            {"position": "QB", "count": 1, "type": "starting"},
            {"position": "RB", "count": 2, "type": "starting"},
            {"position": "WR", "count": 2, "type": "starting"},
            {"position": "TE", "count": 1, "type": "starting"},
            {"position": "FLEX", "count": 1, "type": "starting", "eligible_positions": ["RB", "WR", "TE"]},
            {"position": "K", "count": 1, "type": "starting"},
            {"position": "DEF", "count": 1, "type": "starting"},
            {"position": "BENCH", "count": 6, "type": "bench"},
        ],
        "superflex": [
            {"position": "QB", "count": 1, "type": "starting"},
            {"position": "RB", "count": 2, "type": "starting"},
            {"position": "WR", "count": 2, "type": "starting"},
            {"position": "TE", "count": 1, "type": "starting"},
            {"position": "FLEX", "count": 1, "type": "starting", "eligible_positions": ["RB", "WR", "TE"]},
            {"position": "SUPERFLEX", "count": 1, "type": "starting", "eligible_positions": ["QB", "RB", "WR", "TE"]},
            {"position": "K", "count": 1, "type": "starting"},
            {"position": "DEF", "count": 1, "type": "starting"},
            {"position": "BENCH", "count": 6, "type": "bench"},
        ],
        "2qb": [
            {"position": "QB", "count": 2, "type": "starting"},
            {"position": "RB", "count": 2, "type": "starting"},
            {"position": "WR", "count": 2, "type": "starting"},
            {"position": "TE", "count": 1, "type": "starting"},
            {"position": "FLEX", "count": 1, "type": "starting", "eligible_positions": ["RB", "WR", "TE"]},
            {"position": "K", "count": 1, "type": "starting"},
            {"position": "DEF", "count": 1, "type": "starting"},
            {"position": "BENCH", "count": 6, "type": "bench"},
        ]
    }