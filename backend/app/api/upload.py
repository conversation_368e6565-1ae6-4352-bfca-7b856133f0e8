"""
File upload API endpoints for external data processing.
"""
from typing import Dict, Any, Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..core.database import get_db
from ..services.file_upload import FileUploadService, FileUploadError, DataValidationError


router = APIRouter(prefix="/upload", tags=["upload"])


class UploadResponse(BaseModel):
    """Response model for file upload operations."""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    errors: Optional[list] = None


@router.post("/projections", response_model=UploadResponse)
async def upload_projections(
    file: UploadFile = File(...),
    source: str = Form(...),
    season: int = Form(...),
    db: Session = Depends(get_db)
):
    """
    Upload player projections from CSV/Excel file.
    
    Expected columns (case-insensitive, flexible naming):
    - player_name/name/player (required)
    - projected_points/fpts/points (required)
    - position/pos (optional)
    - team/tm (optional)
    - week/wk (optional, for weekly projections)
    - floor (optional)
    - ceiling (optional)
    - Statistical projections: passing_yards, passing_tds, rushing_yards, etc.
    """
    try:
        upload_service = FileUploadService(db)
        result = upload_service.process_file(
            file=file,
            data_type='projections',
            source=source,
            season=season
        )
        
        return UploadResponse(
            success=result['success'],
            message=f"Successfully processed {result['saved_count']} of {result['total_rows']} projections",
            data=result,
            errors=result['errors'] if result['errors'] else None
        )
        
    except FileUploadError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DataValidationError as e:
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/rankings", response_model=UploadResponse)
async def upload_rankings(
    file: UploadFile = File(...),
    source: str = Form(...),
    season: int = Form(...),
    ranking_type: str = Form(default="expert"),
    db: Session = Depends(get_db)
):
    """
    Upload player rankings from CSV/Excel file.
    
    Expected columns (case-insensitive, flexible naming):
    - player_name/name/player (required)
    - position/pos (optional)
    - team/tm (optional)
    - overall_rank/rank (optional)
    - position_rank/pos_rank (optional)
    - tier (optional)
    
    ranking_type options: 'expert', 'consensus', 'user'
    """
    try:
        if ranking_type not in ['expert', 'consensus', 'user']:
            raise HTTPException(status_code=400, detail="Invalid ranking_type. Must be 'expert', 'consensus', or 'user'")
        
        upload_service = FileUploadService(db)
        result = upload_service.process_file(
            file=file,
            data_type='rankings',
            source=source,
            season=season,
            ranking_type=ranking_type
        )
        
        return UploadResponse(
            success=result['success'],
            message=f"Successfully processed {result['saved_count']} of {result['total_rows']} rankings",
            data=result,
            errors=result['errors'] if result['errors'] else None
        )
        
    except FileUploadError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DataValidationError as e:
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/adp", response_model=UploadResponse)
async def upload_adp(
    file: UploadFile = File(...),
    source: str = Form(...),
    season: int = Form(...),
    db: Session = Depends(get_db)
):
    """
    Upload Average Draft Position (ADP) data from CSV/Excel file.
    
    Expected columns (case-insensitive, flexible naming):
    - player_name/name/player (required)
    - position/pos (optional)
    - team/tm (optional)
    - adp/avg_draft_pos/average_draft_position (optional)
    - adp_std/adp_stdev (optional)
    - draft_count (optional)
    - overall_rank/rank (optional)
    - position_rank/pos_rank (optional)
    """
    try:
        upload_service = FileUploadService(db)
        result = upload_service.process_file(
            file=file,
            data_type='adp',
            source=source,
            season=season
        )
        
        return UploadResponse(
            success=result['success'],
            message=f"Successfully processed {result['saved_count']} of {result['total_rows']} ADP records",
            data=result,
            errors=result['errors'] if result['errors'] else None
        )
        
    except FileUploadError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DataValidationError as e:
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/supported-formats")
async def get_supported_formats():
    """Get information about supported file formats and expected columns."""
    return {
        "supported_extensions": [".csv", ".xlsx", ".xls"],
        "max_file_size_mb": 10,
        "data_types": {
            "projections": {
                "description": "Player performance projections",
                "required_columns": ["player_name", "projected_points"],
                "optional_columns": [
                    "position", "team", "week", "season", "floor", "ceiling",
                    "passing_yards", "passing_tds", "rushing_yards", "rushing_tds",
                    "receiving_yards", "receiving_tds", "receptions"
                ]
            },
            "rankings": {
                "description": "Expert player rankings",
                "required_columns": ["player_name"],
                "optional_columns": [
                    "position", "team", "season", "overall_rank", "position_rank", "tier"
                ]
            },
            "adp": {
                "description": "Average Draft Position data",
                "required_columns": ["player_name"],
                "optional_columns": [
                    "position", "team", "season", "adp", "adp_std", "draft_count",
                    "overall_rank", "position_rank"
                ]
            }
        },
        "column_name_variations": {
            "player_name": ["player", "name", "full_name"],
            "position": ["pos"],
            "team": ["tm"],
            "week": ["wk"],
            "season": ["year", "yr"],
            "projected_points": ["fpts", "points", "fantasy_points", "proj_pts"],
            "overall_rank": ["rank"],
            "position_rank": ["pos_rank"],
            "adp": ["avg_draft_pos", "average_draft_position"]
        }
    }


@router.delete("/data/{data_type}")
async def delete_uploaded_data(
    data_type: str,
    source: str,
    season: int,
    db: Session = Depends(get_db)
):
    """Delete uploaded data by source and season."""
    try:
        if data_type not in ['projections', 'rankings', 'adp']:
            raise HTTPException(status_code=400, detail="Invalid data_type. Must be 'projections', 'rankings', or 'adp'")
        
        if data_type == 'projections':
            from ..models import Projection
            deleted_count = db.query(Projection).filter(
                Projection.source == source,
                Projection.season == season
            ).delete()
        else:
            from ..models import Ranking
            ranking_type = 'adp' if data_type == 'adp' else 'expert'
            deleted_count = db.query(Ranking).filter(
                Ranking.source == source,
                Ranking.season == season,
                Ranking.ranking_type == ranking_type
            ).delete()
        
        db.commit()
        
        return {
            "success": True,
            "message": f"Deleted {deleted_count} {data_type} records from source '{source}' for season {season}",
            "deleted_count": deleted_count
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting data: {str(e)}")