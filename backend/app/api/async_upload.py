"""
Async file upload API endpoints using background tasks.
"""
from typing import Dict, Any, Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..core.database import get_db
from ..tasks.file_processing import process_uploaded_file, cleanup_old_data, validate_uploaded_data
from ..core.celery import celery_app


router = APIRouter(prefix="/async-upload", tags=["async-upload"])


class AsyncUploadResponse(BaseModel):
    """Response model for async file upload operations."""
    success: bool
    message: str
    task_id: str
    status_url: str


class TaskStatusResponse(BaseModel):
    """Response model for task status queries."""
    task_id: str
    status: str
    result: Optional[Dict[str, Any]] = None
    meta: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.post("/projections", response_model=AsyncUploadResponse)
async def upload_projections_async(
    file: UploadFile = File(...),
    source: str = Form(...),
    season: int = Form(...),
    cleanup_old: bool = Form(default=False),
    db: Session = Depends(get_db)
):
    """
    Upload player projections asynchronously using background tasks.
    
    Args:
        file: CSV/Excel file with projection data
        source: Data source name
        season: Season year
        cleanup_old: Whether to clean up old data from same source/season first
    
    Returns:
        Task ID and status URL for monitoring progress
    """
    try:
        # Validate file size
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:  # 10MB
            raise HTTPException(status_code=413, detail="File too large")
        
        # Start cleanup task if requested
        cleanup_task_id = None
        if cleanup_old:
            cleanup_task = cleanup_old_data.delay('projections', source, season)
            cleanup_task_id = cleanup_task.id
        
        # Start processing task
        task = process_uploaded_file.delay(
            file_content=file_content,
            filename=file.filename,
            data_type='projections',
            source=source,
            season=season
        )
        
        return AsyncUploadResponse(
            success=True,
            message=f"Started processing {file.filename} in background",
            task_id=task.id,
            status_url=f"/api/v1/async-upload/status/{task.id}"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting upload task: {str(e)}")


@router.post("/rankings", response_model=AsyncUploadResponse)
async def upload_rankings_async(
    file: UploadFile = File(...),
    source: str = Form(...),
    season: int = Form(...),
    ranking_type: str = Form(default="expert"),
    cleanup_old: bool = Form(default=False),
    db: Session = Depends(get_db)
):
    """
    Upload player rankings asynchronously using background tasks.
    
    Args:
        file: CSV/Excel file with ranking data
        source: Data source name
        season: Season year
        ranking_type: Type of ranking ('expert', 'consensus', 'user')
        cleanup_old: Whether to clean up old data from same source/season first
    
    Returns:
        Task ID and status URL for monitoring progress
    """
    try:
        if ranking_type not in ['expert', 'consensus', 'user']:
            raise HTTPException(status_code=400, detail="Invalid ranking_type")
        
        # Validate file size
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:  # 10MB
            raise HTTPException(status_code=413, detail="File too large")
        
        # Start cleanup task if requested
        if cleanup_old:
            cleanup_old_data.delay('rankings', source, season)
        
        # Start processing task
        task = process_uploaded_file.delay(
            file_content=file_content,
            filename=file.filename,
            data_type='rankings',
            source=source,
            season=season,
            ranking_type=ranking_type
        )
        
        return AsyncUploadResponse(
            success=True,
            message=f"Started processing {file.filename} in background",
            task_id=task.id,
            status_url=f"/api/v1/async-upload/status/{task.id}"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting upload task: {str(e)}")


@router.post("/adp", response_model=AsyncUploadResponse)
async def upload_adp_async(
    file: UploadFile = File(...),
    source: str = Form(...),
    season: int = Form(...),
    cleanup_old: bool = Form(default=False),
    db: Session = Depends(get_db)
):
    """
    Upload ADP data asynchronously using background tasks.
    
    Args:
        file: CSV/Excel file with ADP data
        source: Data source name
        season: Season year
        cleanup_old: Whether to clean up old data from same source/season first
    
    Returns:
        Task ID and status URL for monitoring progress
    """
    try:
        # Validate file size
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:  # 10MB
            raise HTTPException(status_code=413, detail="File too large")
        
        # Start cleanup task if requested
        if cleanup_old:
            cleanup_old_data.delay('adp', source, season)
        
        # Start processing task
        task = process_uploaded_file.delay(
            file_content=file_content,
            filename=file.filename,
            data_type='adp',
            source=source,
            season=season
        )
        
        return AsyncUploadResponse(
            success=True,
            message=f"Started processing {file.filename} in background",
            task_id=task.id,
            status_url=f"/api/v1/async-upload/status/{task.id}"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting upload task: {str(e)}")


@router.get("/status/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """
    Get the status of a background upload task.
    
    Args:
        task_id: The task ID returned from upload endpoint
    
    Returns:
        Current task status and results
    """
    try:
        task_result = celery_app.AsyncResult(task_id)
        
        response = TaskStatusResponse(
            task_id=task_id,
            status=task_result.status,
            meta=task_result.info if task_result.info else None
        )
        
        if task_result.successful():
            response.result = task_result.result
        elif task_result.failed():
            response.error = str(task_result.info)
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting task status: {str(e)}")


@router.post("/validate/{task_id}")
async def validate_upload_task(task_id: str):
    """
    Start validation task for uploaded data.
    
    Args:
        task_id: The original upload task ID
    
    Returns:
        Validation task ID and status URL
    """
    try:
        # Get the original task result to extract parameters
        original_task = celery_app.AsyncResult(task_id)
        
        if not original_task.successful():
            raise HTTPException(status_code=400, detail="Original upload task not completed successfully")
        
        result = original_task.result
        if not result or 'result' not in result:
            raise HTTPException(status_code=400, detail="Invalid task result")
        
        upload_result = result['result']
        
        # Start validation task
        validation_task = validate_uploaded_data.delay(
            data_type=upload_result['data_type'],
            source=upload_result['source'],
            season=upload_result['season']
        )
        
        return {
            "success": True,
            "message": "Started data validation in background",
            "validation_task_id": validation_task.id,
            "status_url": f"/api/v1/async-upload/status/{validation_task.id}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting validation task: {str(e)}")


@router.delete("/cancel/{task_id}")
async def cancel_task(task_id: str):
    """
    Cancel a running background task.
    
    Args:
        task_id: The task ID to cancel
    
    Returns:
        Cancellation status
    """
    try:
        celery_app.control.revoke(task_id, terminate=True)
        
        return {
            "success": True,
            "message": f"Task {task_id} cancellation requested",
            "task_id": task_id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error cancelling task: {str(e)}")