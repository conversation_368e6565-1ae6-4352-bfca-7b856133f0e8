"""
Admin maintenance endpoints.

Provides a seasonal reset for uploaded data (ADP and composite index) and
removal of upload-created orphan players not referenced by rosters.
"""
from __future__ import annotations

from typing import Any, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from ..core.database import get_db
from ..models.ranking import Ranking
from ..models.player import Player
from ..models.roster import RosterPlayer

router = APIRouter(prefix="/admin", tags=["admin"]) 


def _bool(val: Optional[bool]) -> bool:
    return bool(val) if val is not None else False


@router.post("/reset-season")
async def reset_season_data(
    season: int = Query(..., description="Season year"),
    delete_adp: bool = Query(True, description="Delete per-provider ADP rows"),
    delete_composite: bool = Query(True, description="Delete composite ADP index rows"),
    delete_upload_players: bool = Query(True, description="Delete players created from uploads that are not on any roster"),
    rebuild_index: bool = Query(False, description="Rebuild composite ADP index after deletion"),
    league_id: Optional[str] = Query(None, description="League id used if rebuild_index is requested"),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """Reset uploaded data for the given season.

    - Deletes Ranking rows of ranking_type='adp' for all sources (if delete_adp)
    - Deletes Ranking rows of ranking_type='adp_index' & source='composite' (if delete_composite)
    - Deletes Player rows with player_metadata.created_from_upload == True that are not referenced in roster_players (if delete_upload_players)
    - Optionally rebuilds the composite ADP index using existing provider configs (if rebuild_index & league_id)
    """
    summary: Dict[str, Any] = {"season": season}

    try:
        if _bool(delete_adp):
            deleted_adp = (
                db.query(Ranking)
                .filter(
                    Ranking.season == season,
                    Ranking.ranking_type == "adp",
                )
                .delete(synchronize_session=False)
            )
            summary["deleted_adp"] = deleted_adp

        if _bool(delete_composite):
            deleted_index = (
                db.query(Ranking)
                .filter(
                    Ranking.season == season,
                    Ranking.ranking_type == "adp_index",
                    Ranking.source == "composite",
                )
                .delete(synchronize_session=False)
            )
            summary["deleted_adp_index"] = deleted_index

        if _bool(delete_upload_players):
            # Subquery: players referenced by any roster
            sub_rostered = db.query(RosterPlayer.player_id).distinct().subquery()
            # SQLite uses json_extract, Postgres can use ->> syntax; rely on SQLAlchemy JSON functions via cast to text if needed.
            # Here we check key presence/value via JSON string search as a portable fallback.
            # More robust backends can refine this.
            candidates = (
                db.query(Player)
                .filter(~Player.id.in_(sub_rostered))
                .all()
            )
            removed = 0
            for p in candidates:
                try:
                    md = dict(getattr(p, "player_metadata", {}) or {})
                except Exception:
                    md = {}
                created_from_upload = bool(md.get("created_from_upload") is True or str(md.get("created_from_upload")).lower() in ("1","true","yes"))
                if created_from_upload:
                    db.delete(p)
                    removed += 1
            summary["deleted_upload_players"] = removed

        db.commit()

        if _bool(rebuild_index):
            if not league_id:
                raise HTTPException(status_code=400, detail="league_id is required to rebuild index")
            # Trigger rebuild via service directly to avoid HTTP hop
            from ..services.adp_service import ADPAggregationService
            svc = ADPAggregationService(db, league_id=league_id, season=season)
            index_stats = svc.compute_and_store_index()
            summary["rebuild_index"] = index_stats

        return {"success": True, **summary}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Reset failed: {e}")

