"""
Preseason planning API: keeper selections persistence.

Stores predicted keeper selections per league in simple JSON files under data/preseason/.
This is intentionally lightweight and can be migrated to DB later.
"""
from __future__ import annotations

import os
import json
from typing import Any, Dict, List
from fastapi import APIRouter, HTTPException, Query

from ..services.adp_config import BASE_DIR

router = APIRouter(prefix="/preseason", tags=["preseason"])

DATA_DIR = os.path.join(BASE_DIR, "data", "preseason")
os.makedirs(DATA_DIR, exist_ok=True)


def _keepers_path(league_id: str) -> str:
    safe = league_id.replace("/", "_")
    return os.path.join(DATA_DIR, f"keepers_{safe}.json")


def _read_json(path: str) -> Dict[str, Any]:
    if not os.path.exists(path):
        return {"league_id": None, "selections": [], "updated_at": None}
    try:
        with open(path, "r") as f:
            return json.load(f)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read keepers: {e}")


def _write_json(path: str, payload: Dict[str, Any]) -> None:
    try:
        with open(path, "w") as f:
            json.dump(payload, f, indent=2)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to write keepers: {e}")


@router.get("/keepers")
async def get_keepers(
    league_id: str = Query(..., description="Canonical league id, e.g., mfl_30157"),
) -> Dict[str, Any]:
    path = _keepers_path(league_id)
    payload = _read_json(path)
    if not payload.get("league_id"):
        return {"league_id": league_id, "selections": [], "updated_at": None}
    return payload


@router.put("/keepers")
async def put_keepers(
    payload: Dict[str, Any],
    league_id: str = Query(..., description="Canonical league id, e.g., mfl_30157"),
) -> Dict[str, Any]:
    # normalize
    selections = payload.get("selections") or []
    norm: List[Dict[str, str]] = []
    for item in selections:
        fr = str(item.get("franchise_id", "")).strip()
        pid = str(item.get("player_id", "")).strip()
        if fr and pid:
            norm.append({"franchise_id": fr, "player_id": pid})
    out = {
        "league_id": league_id,
        "selections": norm,
        "updated_at": payload.get("updated_at"),
    }
    path = _keepers_path(league_id)
    _write_json(path, out)
    return out

