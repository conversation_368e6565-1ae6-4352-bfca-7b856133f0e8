"""
ADP API: configuration, manual refresh, and index retrieval.
"""
from __future__ import annotations

from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, asc, desc
import asyncio

from ..core.database import get_db
from ..models.player import Player
from ..models.ranking import Ranking
from ..models.league import League
from ..models.roster import RosterPlayer, Roster, Franchise
from ..services.adp_config import load_adp_config, save_adp_config
from ..services.adp_service import ADPAggregationService

router = APIRouter(prefix="/rankings/adp", tags=["adp"])


@router.get("/config")
async def get_adp_config(
    league_id: str = Query(..., description="Canonical league id or mfl_{id}"),
) -> Dict[str, Any]:
    cfg = load_adp_config(league_id)
    return {"league_id": league_id, "config": cfg}


@router.put("/config")
async def put_adp_config(
    payload: Dict[str, Any],
    league_id: str = Query(..., description="Canonical league id or mfl_{id}"),
) -> Dict[str, Any]:
    cfg = save_adp_config(league_id, payload or {})
    return {"league_id": league_id, "config": cfg}


@router.post("/refresh")
async def refresh_adp(
    season: int = Query(..., description="Season year"),
    league_id: str = Query(..., description="Canonical league id, e.g., mfl_30157"),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    try:
        svc = ADPAggregationService(db, league_id=league_id, season=season)
        provider_counts = await svc.fetch_and_store_providers()
        index_stats = svc.compute_and_store_index()
        return {"league_id": league_id, "season": season, "providers": provider_counts, "index": index_stats}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"ADP refresh failed: {e}")


@router.get("/unmapped")
async def get_unmapped_sleeper(
    league_id: str = Query(..., description="Canonical league id, e.g., mfl_30157"),
    season: int = Query(..., description="Season year"),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    try:
        svc = ADPAggregationService(db, league_id=league_id, season=season)
        payload = svc.load_unmapped()
        return payload
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load unmapped: {e}")


@router.post("/mappings/upsert")
async def upsert_mappings(
    payload: Dict[str, Any],
    league_id: str = Query(..., description="Canonical league id, e.g., mfl_30157"),
    season: int = Query(..., description="Season year"),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    Upsert mappings from Sleeper IDs to canonical player IDs, then remove them from unmapped list.
    Payload example: { "mappings": [{ "sleeper_id": "1234", "player_id": "mfl_14001" }, ...] }
    """
    try:
        mappings = (payload or {}).get("mappings", [])
        applied = 0
        sleeper_ids_cleared = []
        for m in mappings:
            sleeper_id = str(m.get("sleeper_id") or "").strip()
            player_id = str(m.get("player_id") or "").strip()
            if not sleeper_id or not player_id:
                continue
            # set Player.player_metadata.sleeper_id for chosen player
            player = db.query(Player).filter(Player.id == player_id).first()
            if not player:
                continue
            md = dict(player.player_metadata or {})
            md["sleeper_id"] = sleeper_id
            player.player_metadata = md
            applied += 1
            sleeper_ids_cleared.append(sleeper_id)
        db.commit()
        # remove from unmapped cache file
        svc = ADPAggregationService(db, league_id=league_id, season=season)
        remaining = svc.remove_unmapped_by_sleeper_ids(sleeper_ids_cleared)
        return {"updated": applied, "remaining_unmapped": remaining}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upsert mappings: {e}")


@router.get("/all")
async def get_adp_all(
    season: int = Query(..., description="Season year"),
    league_id: str | None = Query(None, description="Canonical league id (e.g., mfl_30157) to scope fantasy team assignment"),
    position: str | None = Query(None, description="Filter by position QB/RB/WR/TE"),
    team: str | None = Query(None, description="Filter by NFL team code"),
    q: str | None = Query(None, description="Search by player name contains"),
    sort: str = Query("adp_index.asc", description="Sort field: adp_index|mfl|ffc|name|position|team with .asc/.desc"),
    page: int = Query(1, ge=1),
    size: int = Query(100, ge=1, le=500),
    include_idp: bool = Query(False, description="Include IDP positions (DL/LB/DB) if true; default false"),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    try:
        # Build base player filter (for total)
        base_q = db.query(Player.id)
        from ..models.player import PlayerPosition as _PP
        # Exclude IDP positions unless explicitly asked to include them
        if not include_idp:
            base_q = base_q.filter(~Player.position.in_([_PP.DL, _PP.LB, _PP.DB]))
        if position:
            # Player.position is an Enum; compare against the enum value instead of applying UPPER() to the enum column
            pos = (position or "").strip().upper()
            try:
                pos_enum = getattr(_PP, pos)
                base_q = base_q.filter(Player.position == pos_enum)
            except AttributeError:
                # Unknown position filter; return no results by filtering to impossible condition
                base_q = base_q.filter(Player.id == "__no_such_player__")
        if team:
            base_q = base_q.filter(Player.team == team.upper())
        if q:
            like = f"%{q}%"
            base_q = base_q.filter(Player.name.ilike(like))

        # Total without ordering (no ORDER BY involved)
        total = base_q.count()

        # Sorting
        sort_field, _, sort_dir = sort.partition(".")
        sort_dir = sort_dir or "asc"

        # Build sorted query with a sortable key to avoid DISTINCT+ORDER issues
        # Default: composite ADP
        if sort_field in ("adp_index", ""):
            # Use aggregate on Ranking.adp to satisfy GROUP BY semantics
            sort_key = func.coalesce(func.min(Ranking.adp), 9999.0)
            sorted_q = base_q.join(
                Ranking,
                (Ranking.player_id == Player.id) &
                (Ranking.season == season) &
                (Ranking.ranking_type == "adp_index") &
                (Ranking.source == "composite") &
                (Ranking.is_active == True),
                isouter=True
            ).group_by(Player.id).order_by(asc(sort_key) if sort_dir == "asc" else desc(sort_key))
        else:
            # dynamic provider sort: allow weights/config providers as sort keys
            provider = sort_field.upper()
            if provider in {"MFL","FFC","ESPN","SLEEPER","CBS","NFL","RTSPORTS","FANTRAX"}:
                sort_key = func.coalesce(func.min(Ranking.adp), 9999.0)
                sorted_q = base_q.join(
                    Ranking,
                    (Ranking.player_id == Player.id) &
                    (Ranking.season == season) &
                    (Ranking.ranking_type == "adp") &
                    (Ranking.source == provider) &
                    (Ranking.is_active == True),
                    isouter=True
                ).group_by(Player.id).order_by(asc(sort_key) if sort_dir == "asc" else desc(sort_key))
            elif sort_field == "name":
                sort_key = func.min(Player.name)
                sorted_q = base_q.group_by(Player.id).order_by(asc(sort_key) if sort_dir == "asc" else desc(sort_key))
            elif sort_field == "position":
                sort_key = func.min(Player.position)
                sorted_q = base_q.group_by(Player.id).order_by(asc(sort_key) if sort_dir == "asc" else desc(sort_key))
            elif sort_field == "team":
                sort_key = func.min(Player.team)
                sorted_q = base_q.group_by(Player.id).order_by(asc(sort_key) if sort_dir == "asc" else desc(sort_key))
            else:
                # fallback to composite
                sort_key = func.coalesce(func.min(Ranking.adp), 9999.0)
                sorted_q = base_q.join(
                    Ranking,
                    (Ranking.player_id == Player.id) &
                    (Ranking.season == season) &
                    (Ranking.ranking_type == "adp_index") &
                    (Ranking.source == "composite") &
                    (Ranking.is_active == True),
                    isouter=True
                ).group_by(Player.id).order_by(asc(sort_key) if sort_dir == "asc" else desc(sort_key))
        elif sort_field == "name":
            sort_key = func.min(Player.name)
            sorted_q = base_q.group_by(Player.id).order_by(asc(sort_key) if sort_dir == "asc" else desc(sort_key))
        elif sort_field == "position":
            sort_key = func.min(Player.position)
            sorted_q = base_q.group_by(Player.id).order_by(asc(sort_key) if sort_dir == "asc" else desc(sort_key))
        elif sort_field == "team":
            sort_key = func.min(Player.team)
            sorted_q = base_q.group_by(Player.id).order_by(asc(sort_key) if sort_dir == "asc" else desc(sort_key))
        else:
            # fallback to composite
            sort_key = func.coalesce(func.min(Ranking.adp), 9999.0)
            sorted_q = base_q.join(
                Ranking,
                (Ranking.player_id == Player.id) &
                (Ranking.season == season) &
                (Ranking.ranking_type == "adp_index") &
                (Ranking.source == "composite") &
                (Ranking.is_active == True),
                isouter=True
            ).group_by(Player.id).order_by(asc(sort_key) if sort_dir == "asc" else desc(sort_key))

        # Pagination
        offset = (page - 1) * size
        ids = [row[0] for row in sorted_q.offset(offset).limit(size).all()]
        if not ids:
            return {"season": season, "items": [], "page": page, "size": size, "total": 0}
        # Fetch details
        plist = db.query(Player).filter(Player.id.in_(ids)).all()
        player_map = {p.id: p for p in plist}
        # Composite
        comp_rows = db.query(Ranking).filter(
            Ranking.player_id.in_(ids),
            Ranking.season == season,
            Ranking.ranking_type == "adp_index",
            Ranking.source == "composite",
            Ranking.is_active == True,
        ).all()
        comp_map = {r.player_id: r for r in comp_rows}
        # Load all provider rows
        providers = ["MFL","FFC","ESPN","SLEEPER","CBS","NFL","RTSPORTS","FANTRAX"]
        provider_maps = {}
        for prov in providers:
            rows_p = db.query(Ranking).filter(
                Ranking.player_id.in_(ids),
                Ranking.season == season,
                Ranking.ranking_type == "adp",
                Ranking.source == prov,
                Ranking.is_active == True,
            ).all()
            provider_maps[prov] = {r.player_id: r for r in rows_p}
        # Roster assignment (fantasy team)
        rp_query = db.query(RosterPlayer, Roster, Franchise).join(Roster, RosterPlayer.roster_id == Roster.id).join(Franchise, Roster.franchise_id == Franchise.id).filter(
            RosterPlayer.player_id.in_(ids)
        )
        # Scope by league if provided
        if league_id:
            rp_query = rp_query.filter(Franchise.league_id == league_id)
        # Be tolerant of legacy nulls; treat null as active
        rp_query = rp_query.filter((RosterPlayer.is_active == True) | (RosterPlayer.is_active.is_(None)))
        rp_rows = rp_query.all()
        team_map = {}
        for rp, ro, fr in rp_rows:
            # If multiple, keep first
            if rp.player_id not in team_map:
                team_map[rp.player_id] = {"id": fr.id, "name": fr.name}
        # Build items preserving order
        items: List[Dict[str, Any]] = []
        for pid in ids:
            p = player_map.get(pid)
            comp = comp_map.get(pid)
            mfl = mfl_map.get(pid)
            ffc = ffc_map.get(pid)
            # optional rookie flag if present in metadata
            md = getattr(p, "player_metadata", None) if p else None
            is_rookie = False
            if isinstance(md, dict):
                is_rookie = bool(md.get("rookie") or (md.get("experience") == 0) or (md.get("rookie_year") == season))
            # Normalize position to raw value (e.g., 'LB' not 'PlayerPosition.LB')
            pos_val = None
            try:
                pos_attr = getattr(p, "position", None)
                pos_val = pos_attr.value if hasattr(pos_attr, "value") else str(pos_attr) if pos_attr is not None else None
            except Exception:
                pos_val = getattr(p, "position", None)
            items.append({
                "player_id": pid,
                "name": getattr(p, "name", None),
                "position": pos_val,
                "nfl_team": getattr(p, "team", None),
                "fantasy_team": team_map.get(pid) or {"id": None, "name": "FA"},
                "is_rookie": is_rookie,
                "adp_index": {
                    "adp": float(comp.adp) if comp and comp.adp is not None else None,
                    "overall_rank": comp.overall_rank if comp else None,
                },
                "sources": {
                    **{
                        prov: {
                            "adp": float(provider_maps.get(prov, {}).get(pid).adp) if provider_maps.get(prov, {}).get(pid) and provider_maps[prov][pid].adp is not None else None,
                            "stdev": float(provider_maps.get(prov, {}).get(pid).adp_std) if provider_maps.get(prov, {}).get(pid) and provider_maps[prov][pid].adp_std is not None else None,
                            "times_drafted": provider_maps.get(prov, {}).get(pid).draft_count if provider_maps.get(prov, {}).get(pid) else None,
                        }
                        for prov in providers
                    }
                },
            })
        return {"season": season, "items": items, "page": page, "size": size, "total": total}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"ADP all retrieval failed: {e}")


@router.get("/index")
async def get_adp_index(
    player_id: Optional[List[str]] = Query(None, description="Repeatable player_id, canonical ids like mfl_12345"),
    franchise_id: Optional[str] = Query(None, description="Franchise id to fetch roster players' ADP Index"),
    season: int = Query(..., description="Season year"),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    try:
        query = db.query(Ranking).filter(
            Ranking.season == season,
            Ranking.ranking_type == "adp_index",
            Ranking.source == "composite",
            Ranking.is_active == True,
        )
        ids: List[str] = []
        if player_id:
            ids = list(player_id)
        elif franchise_id:
            # gather players by franchise from roster_players
            from ..models.roster import Roster, RosterPlayer
            ids = [rp.player_id for rp in db.query(RosterPlayer).join(Roster).filter(Roster.franchise_id == franchise_id).all()]
        if ids:
            query = query.filter(Ranking.player_id.in_(ids))
        rows: List[Ranking] = query.all()
        result: Dict[str, Any] = {}
        for r in rows:
            result[r.player_id] = {
                "adp_index": float(r.adp) if r.adp is not None else None,
                "overall_rank": r.overall_rank,
                "adp_std": float(r.adp_std) if r.adp_std is not None else None,
                "metadata": r.ranking_metadata,
            }
        return {"season": season, "adp_index": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"ADP index retrieval failed: {e}")

