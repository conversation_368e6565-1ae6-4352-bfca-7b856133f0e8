"""
Waiver wire API endpoints for fantasy football waiver/FAAB decisions.
"""
from typing import List, Optional
from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from ..core.database import get_db
from ..services.waiver_optimizer import (
    WaiverOptimizer, WaiverTarget, WaiverStrategy, StreamingOpportunity,
    FreeAgent, WaiverTargetType, WaiverPriority
)
from ..models.roster import Franchise

router = APIRouter(prefix="/waiver", tags=["waiver"])


# Pydantic models for API responses
class FreeAgentResponse(BaseModel):
    """Response model for free agent information."""
    player_id: str
    player_name: str
    position: str
    team: str
    projected_points: Decimal
    points_over_replacement: Decimal
    ownership_percentage: Optional[float]
    recent_performance: List[Decimal]
    upcoming_matchups: List[str]
    bye_week: Optional[int]
    injury_status: str
    target_type: str
    metadata: dict

    class Config:
        from_attributes = True


class DropCandidateResponse(BaseModel):
    """Response model for drop candidate information."""
    player_id: str
    player_name: str
    position: str
    projected_points: Decimal
    drop_priority: int
    reason: str

    class Config:
        from_attributes = True


class WaiverTargetResponse(BaseModel):
    """Response model for waiver target recommendations."""
    player_id: str
    player_name: str
    position: str
    target_type: str
    priority: str
    recommended_bid: Decimal
    max_bid: Decimal
    points_over_replacement: Decimal
    weekly_upside: Decimal
    rationale: str
    drop_candidates: List[DropCandidateResponse]
    streaming_weeks: Optional[List[int]]
    confidence: float
    metadata: dict

    class Config:
        from_attributes = True


class StreamingOpportunityResponse(BaseModel):
    """Response model for streaming opportunities."""
    position: str
    weeks: List[int]
    targets: List[WaiverTargetResponse]
    total_value: Decimal
    strategy: str
    confidence: float

    class Config:
        from_attributes = True


class WaiverStrategyResponse(BaseModel):
    """Response model for waiver strategy recommendations."""
    strategy_name: str
    targets: List[WaiverTargetResponse]
    total_faab_allocation: Decimal
    remaining_budget: Decimal
    expected_value: Decimal
    risk_level: str
    streaming_opportunities: List[StreamingOpportunityResponse]
    trade_offs: List[str]
    metadata: dict

    class Config:
        from_attributes = True


class TeamNeedsResponse(BaseModel):
    """Response model for team needs analysis."""
    position: str
    need_level: str
    starter_strength: Decimal
    depth_count: int
    required_starters: int
    avg_starter_points: Decimal

    class Config:
        from_attributes = True


# API request models
class FAABOptimizationRequest(BaseModel):
    """Request model for FAAB optimization."""
    franchise_id: str
    target_player_ids: List[str]
    budget_constraint: Optional[Decimal] = None


@router.get("/free-agents/{league_id}", response_model=List[FreeAgentResponse])
async def get_free_agents(
    league_id: str,
    season: int = Query(2024, description="Season year"),
    min_projected_points: Decimal = Query(Decimal('5'), description="Minimum projected points threshold"),
    position: Optional[str] = Query(None, description="Filter by position"),
    limit: int = Query(50, description="Maximum number of results"),
    db: Session = Depends(get_db)
):
    """
    Get available free agents in a league.
    
    Args:
        league_id: League identifier
        season: Season year
        min_projected_points: Minimum projected points threshold
        position: Optional position filter
        limit: Maximum number of results
        
    Returns:
        List of available free agents with projections and analysis
    """
    try:
        optimizer = WaiverOptimizer(db)
        free_agents = optimizer.get_available_free_agents(
            league_id=league_id,
            season=season,
            min_projected_points=min_projected_points
        )
        
        # Filter by position if specified
        if position:
            free_agents = [fa for fa in free_agents if fa.position.value == position.upper()]
        
        # Limit results
        free_agents = free_agents[:limit]
        
        # Convert to response models
        response_data = []
        for fa in free_agents:
            response_data.append(FreeAgentResponse(
                player_id=fa.player_id,
                player_name=fa.player_name,
                position=fa.position.value,
                team=fa.team,
                projected_points=fa.projected_points,
                points_over_replacement=fa.points_over_replacement,
                ownership_percentage=fa.ownership_percentage,
                recent_performance=fa.recent_performance,
                upcoming_matchups=fa.upcoming_matchups,
                bye_week=fa.bye_week,
                injury_status=fa.injury_status,
                target_type=fa.target_type.value,
                metadata=fa.metadata
            ))
        
        return response_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving free agents: {str(e)}")


@router.get("/targets/{franchise_id}", response_model=List[WaiverTargetResponse])
async def get_waiver_targets(
    franchise_id: str,
    week: int = Query(..., description="Current week number"),
    season: int = Query(2024, description="Season year"),
    max_targets: int = Query(10, description="Maximum number of targets"),
    db: Session = Depends(get_db)
):
    """
    Get waiver wire target recommendations for a franchise.
    
    Args:
        franchise_id: Franchise identifier
        week: Current week number
        season: Season year
        max_targets: Maximum number of targets to return
        
    Returns:
        List of waiver target recommendations with bid amounts and rationale
    """
    try:
        # Verify franchise exists
        franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise:
            raise HTTPException(status_code=404, detail=f"Franchise {franchise_id} not found")
        
        optimizer = WaiverOptimizer(db)
        targets = optimizer.analyze_waiver_targets(
            franchise_id=franchise_id,
            week=week,
            season=season,
            max_targets=max_targets
        )
        
        # Convert to response models
        response_data = []
        for target in targets:
            drop_candidates = [
                DropCandidateResponse(
                    player_id=dc.player_id,
                    player_name=dc.player_name,
                    position=dc.position.value,
                    projected_points=dc.projected_points,
                    drop_priority=dc.drop_priority,
                    reason=dc.reason
                )
                for dc in target.drop_candidates
            ]
            
            response_data.append(WaiverTargetResponse(
                player_id=target.player_id,
                player_name=target.player_name,
                position=target.position.value,
                target_type=target.target_type.value,
                priority=target.priority.value,
                recommended_bid=target.recommended_bid,
                max_bid=target.max_bid,
                points_over_replacement=target.points_over_replacement,
                weekly_upside=target.weekly_upside,
                rationale=target.rationale,
                drop_candidates=drop_candidates,
                streaming_weeks=target.streaming_weeks,
                confidence=target.confidence,
                metadata=target.metadata
            ))
        
        return response_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing waiver targets: {str(e)}")


@router.post("/optimize-faab", response_model=WaiverStrategyResponse)
async def optimize_faab_allocation(
    request: FAABOptimizationRequest,
    db: Session = Depends(get_db)
):
    """
    Optimize FAAB allocation across multiple waiver targets.
    
    Args:
        request: FAAB optimization request with franchise and target information
        
    Returns:
        Optimized waiver strategy with bid allocations
    """
    try:
        # Verify franchise exists
        franchise = db.query(Franchise).filter(Franchise.id == request.franchise_id).first()
        if not franchise:
            raise HTTPException(status_code=404, detail=f"Franchise {request.franchise_id} not found")
        
        optimizer = WaiverOptimizer(db)
        
        # First get waiver targets for the specified players
        all_targets = optimizer.analyze_waiver_targets(
            franchise_id=request.franchise_id,
            week=1,  # Use current week - would be passed in real implementation
            season=2024,
            max_targets=50  # Get all targets first
        )
        
        # Filter to requested players
        requested_targets = [
            target for target in all_targets 
            if target.player_id in request.target_player_ids
        ]
        
        if not requested_targets:
            raise HTTPException(
                status_code=400, 
                detail="No valid targets found for the specified player IDs"
            )
        
        # Optimize FAAB allocation
        strategy = optimizer.optimize_faab_allocation(
            franchise_id=request.franchise_id,
            targets=requested_targets,
            budget_constraint=request.budget_constraint
        )
        
        # Convert to response model
        target_responses = []
        for target in strategy.targets:
            drop_candidates = [
                DropCandidateResponse(
                    player_id=dc.player_id,
                    player_name=dc.player_name,
                    position=dc.position.value,
                    projected_points=dc.projected_points,
                    drop_priority=dc.drop_priority,
                    reason=dc.reason
                )
                for dc in target.drop_candidates
            ]
            
            target_responses.append(WaiverTargetResponse(
                player_id=target.player_id,
                player_name=target.player_name,
                position=target.position.value,
                target_type=target.target_type.value,
                priority=target.priority.value,
                recommended_bid=target.recommended_bid,
                max_bid=target.max_bid,
                points_over_replacement=target.points_over_replacement,
                weekly_upside=target.weekly_upside,
                rationale=target.rationale,
                drop_candidates=drop_candidates,
                streaming_weeks=target.streaming_weeks,
                confidence=target.confidence,
                metadata=target.metadata
            ))
        
        streaming_responses = [
            StreamingOpportunityResponse(
                position=so.position.value,
                weeks=so.weeks,
                targets=[],  # Simplified for now
                total_value=so.total_value,
                strategy=so.strategy,
                confidence=so.confidence
            )
            for so in strategy.streaming_opportunities
        ]
        
        return WaiverStrategyResponse(
            strategy_name=strategy.strategy_name,
            targets=target_responses,
            total_faab_allocation=strategy.total_faab_allocation,
            remaining_budget=strategy.remaining_budget,
            expected_value=strategy.expected_value,
            risk_level=strategy.risk_level,
            streaming_opportunities=streaming_responses,
            trade_offs=strategy.trade_offs,
            metadata=strategy.metadata
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error optimizing FAAB allocation: {str(e)}")


@router.get("/streaming/{franchise_id}", response_model=List[StreamingOpportunityResponse])
async def get_streaming_opportunities(
    franchise_id: str,
    current_week: int = Query(..., description="Current week number"),
    weeks_ahead: int = Query(4, description="Number of weeks to look ahead"),
    season: int = Query(2024, description="Season year"),
    db: Session = Depends(get_db)
):
    """
    Get streaming opportunities for upcoming weeks.
    
    Args:
        franchise_id: Franchise identifier
        current_week: Current week number
        weeks_ahead: Number of weeks to look ahead
        season: Season year
        
    Returns:
        List of streaming opportunities by position
    """
    try:
        # Verify franchise exists
        franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise:
            raise HTTPException(status_code=404, detail=f"Franchise {franchise_id} not found")
        
        optimizer = WaiverOptimizer(db)
        opportunities = optimizer.identify_streaming_opportunities(
            franchise_id=franchise_id,
            current_week=current_week,
            weeks_ahead=weeks_ahead,
            season=season
        )
        
        # Convert to response models
        response_data = []
        for opportunity in opportunities:
            target_responses = []
            for target in opportunity.targets:
                drop_candidates = [
                    DropCandidateResponse(
                        player_id=dc.player_id,
                        player_name=dc.player_name,
                        position=dc.position.value,
                        projected_points=dc.projected_points,
                        drop_priority=dc.drop_priority,
                        reason=dc.reason
                    )
                    for dc in target.drop_candidates
                ]
                
                target_responses.append(WaiverTargetResponse(
                    player_id=target.player_id,
                    player_name=target.player_name,
                    position=target.position.value,
                    target_type=target.target_type.value,
                    priority=target.priority.value,
                    recommended_bid=target.recommended_bid,
                    max_bid=target.max_bid,
                    points_over_replacement=target.points_over_replacement,
                    weekly_upside=target.weekly_upside,
                    rationale=target.rationale,
                    drop_candidates=drop_candidates,
                    streaming_weeks=target.streaming_weeks,
                    confidence=target.confidence,
                    metadata=target.metadata
                ))
            
            response_data.append(StreamingOpportunityResponse(
                position=opportunity.position.value,
                weeks=opportunity.weeks,
                targets=target_responses,
                total_value=opportunity.total_value,
                strategy=opportunity.strategy,
                confidence=opportunity.confidence
            ))
        
        return response_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error identifying streaming opportunities: {str(e)}")


@router.get("/team-needs/{franchise_id}", response_model=List[TeamNeedsResponse])
async def analyze_team_needs(
    franchise_id: str,
    week: int = Query(..., description="Current week number"),
    season: int = Query(2024, description="Season year"),
    db: Session = Depends(get_db)
):
    """
    Analyze team needs by position.
    
    Args:
        franchise_id: Franchise identifier
        week: Current week number
        season: Season year
        
    Returns:
        Team needs analysis by position
    """
    try:
        # Verify franchise exists
        franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise:
            raise HTTPException(status_code=404, detail=f"Franchise {franchise_id} not found")
        
        optimizer = WaiverOptimizer(db)
        team_needs = optimizer.analyze_team_needs(
            franchise_id=franchise_id,
            week=week,
            season=season
        )
        
        # Convert to response models
        response_data = []
        for position, needs in team_needs.items():
            response_data.append(TeamNeedsResponse(
                position=position.value,
                need_level=needs['need_level'],
                starter_strength=needs['starter_strength'],
                depth_count=needs['depth_count'],
                required_starters=needs['required_starters'],
                avg_starter_points=needs['avg_starter_points']
            ))
        
        return response_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing team needs: {str(e)}")


@router.post("/refresh-cache")
async def refresh_replacement_levels_cache(
    db: Session = Depends(get_db)
):
    """
    Refresh the replacement levels cache.
    
    Returns:
        Success message
    """
    try:
        optimizer = WaiverOptimizer(db)
        optimizer.refresh_replacement_levels_cache()
        
        return {"message": "Replacement levels cache refreshed successfully"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error refreshing cache: {str(e)}")