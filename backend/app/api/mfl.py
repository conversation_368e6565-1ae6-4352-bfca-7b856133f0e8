"""
MFL API endpoints for data ingestion and management.
"""
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from datetime import datetime, timezone

from ..core.database import get_db
from ..services.mfl_ingestion import create_mfl_ingestion_service, MFLIngestionError

router = APIRouter(prefix="/mfl", tags=["MFL Integration"])


class MFLIngestionRequest(BaseModel):
    """Request model for MFL data ingestion."""
    league_id: str = Field(..., description="MFL league identifier")
    api_key: str = Field(..., description="MFL API key for authentication")
    validate: bool = Field(True, description="Whether to validate ingested data")
    username: Optional[str] = Field(None, description="MFL username (if not provided via env)")
    password: Optional[str] = Field(None, description="MFL password (if not provided via env)")
    user_agent: Optional[str] = Field(None, description="Custom user agent for MFL requests")


class MFLIngestionResponse(BaseModel):
    """Response model for MFL data ingestion."""
    league_id: str
    ingestion_timestamp: str
    leagues_processed: int
    players_processed: int
    franchises_processed: int
    rosters_processed: int
    errors: list[str]
    validation: Optional[Dict[str, Any]] = None


class MFLValidationRequest(BaseModel):
    """Request model for MFL data validation."""
    league_id: str = Field(..., description="League ID to validate (with mfl_ prefix)")


@router.post("/ingest", response_model=MFLIngestionResponse)
async def ingest_league_data(
    request: MFLIngestionRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Ingest data from MFL API for a specific league.
    
    This endpoint triggers the complete ETL process for a league:
    - Fetches league configuration, players, franchises, and rosters
    - Normalizes data to canonical schema
    - Stores in database with provenance tracking
    - Optionally validates the ingested data
    
    The ingestion process runs in the background for large datasets.
    """
    try:
        service = create_mfl_ingestion_service(db)
        
        # Run ingestion
        result = service.ingest_league_data(request.league_id, request.api_key, username=request.username, password=request.password, user_agent=request.user_agent)
        
        # Add validation if requested
        if request.validate:
            validation_result = service.validate_ingested_data(f"mfl_{request.league_id}")
            result["validation"] = validation_result
        
        return MFLIngestionResponse(**result)
        
    except MFLIngestionError as e:
        raise HTTPException(status_code=400, detail=f"MFL ingestion failed: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/validate")
async def validate_league_data(
    request: MFLValidationRequest,
    db: Session = Depends(get_db)
):
    """
    Validate the integrity of previously ingested league data.
    
    Checks for:
    - League configuration completeness
    - Player data consistency
    - Roster integrity
    - Orphaned records
    """
    try:
        service = create_mfl_ingestion_service(db)
        validation_result = service.validate_ingested_data(request.league_id)
        
        return validation_result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")


@router.get("/leagues/{league_id}/status")
async def get_league_status(
    league_id: str,
    db: Session = Depends(get_db)
):
    """
    Get the current status of a league's data ingestion.
    
    Returns information about:
    - Last ingestion timestamp
    - Data completeness
    - Validation status
    """
    try:
        from ..models.league import League
        from ..models.roster import Franchise
        from ..models.player import Player
        
        # Check if league exists
        league = db.query(League).filter(League.id == f"mfl_{league_id}").first()
        if not league:
            raise HTTPException(status_code=404, detail="League not found")
        
        # Get franchise count
        franchise_count = db.query(Franchise).filter(Franchise.league_id == league.id).count()
        
        # Get total player count
        player_count = db.query(Player).count()
        
        # We don't persist ingestion provenance in the League model; report basic status only
        return {
            "league_id": league_id,
            "league_name": league.name,
            "season": league.season,
            "is_active": league.is_active,
            "franchise_count": franchise_count,
            "total_players": player_count,
            "last_updated": league.updated_at,
            "ingestion_source": None,
            "ingestion_timestamp": league.updated_at,
            "mfl_league_id": league.mfl_league_id,
            "has_scoring_rules": bool(league.scoring_rules),
            "has_roster_slots": bool(league.roster_slots),
            "has_keeper_rules": league.keeper_rules is not None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get league status: {str(e)}")


@router.get("/leagues")
async def list_leagues(
    db: Session = Depends(get_db)
):
    """
    List all leagues that have been ingested from MFL.
    
    Returns basic information about each league including
    ingestion status and data completeness.
    """
    try:
        from ..models.league import League
        from ..models.roster import Franchise
        
        leagues = db.query(League).filter(League.mfl_league_id.isnot(None)).all()
        
        result = []
        for league in leagues:
            franchise_count = db.query(Franchise).filter(Franchise.league_id == league.id).count()
            
            result.append({
                "league_id": league.mfl_league_id,
                "canonical_id": league.id,
                "name": league.name,
                "season": league.season,
                "is_active": league.is_active,
                "franchise_count": franchise_count,
                "last_updated": league.updated_at,
                "ingestion_timestamp": league.updated_at
            })
        
        return {"leagues": result, "total_count": len(result)}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list leagues: {str(e)}")


@router.delete("/leagues/{league_id}")
async def delete_league_data(
    league_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete all data for a specific league.
    
    WARNING: This will permanently delete all league data including
    franchises, rosters, and recommendations. This action cannot be undone.
    """
    try:
        from ..models.league import League
        
        # Find the league
        league = db.query(League).filter(League.id == f"mfl_{league_id}").first()
        if not league:
            raise HTTPException(status_code=404, detail="League not found")
        
        # Delete the league (cascading deletes will handle related data)
        db.delete(league)
        db.commit()
        
        return {
            "message": f"League {league_id} and all related data deleted successfully",
            "league_id": league_id,
            "deleted_at": league.updated_at
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to delete league: {str(e)}")


@router.post("/leagues/{league_id}/refresh")
async def refresh_league_data(
    league_id: str,
    api_key: str,
    validate: bool = True,
    db: Session = Depends(get_db)
):
    """
    Refresh data for an existing league.
    
    This will re-ingest all data from MFL API, updating existing records
    and adding new ones as needed.
    """
    try:
        service = create_mfl_ingestion_service(db)
        
        # Run ingestion (this will update existing data)
        result = service.ingest_league_data(league_id, api_key)
        
        # Add validation if requested
        if validate:
            validation_result = service.validate_ingested_data(f"mfl_{league_id}")
            result["validation"] = validation_result
        
        return {
            "message": f"League {league_id} data refreshed successfully",
            "ingestion_result": result
        }
        
    except MFLIngestionError as e:
        raise HTTPException(status_code=400, detail=f"MFL refresh failed: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/leagues/{league_id}/refresh-rosters")
async def refresh_league_rosters(
    league_id: str,
    year: Optional[int] = None,
    debug: bool = False,
    db: Session = Depends(get_db)
):
    """
    Refresh rosters for a league using a single MFL export call.

    Useful when franchise and player data already exist but rosters are missing.
    """
    try:
        from ..services.mfl_ingestion import create_mfl_ingestion_service, DataProvenance
        from ..models.roster import Franchise
        service = create_mfl_ingestion_service(db)
        y = year or datetime.now(timezone.utc).year
        prov = DataProvenance(source="mfl_rosters_export", timestamp=datetime.now(timezone.utc))
        franchises_touched, rosters_updated = service.refresh_rosters_via_export(league_id, y, prov)
        resp = {
            "league_id": league_id,
            "year": y,
            "franchises_touched": franchises_touched,
            "rosters_updated": rosters_updated,
        }
        if debug:
            try:
                canon = f"mfl_{league_id}"
                db_fr = db.query(Franchise).filter(Franchise.league_id == canon).all()
                resp["db_franchise_count"] = len(db_fr)
                resp["db_franchises"] = [{"id": f.id, "mfl_franchise_id": f.mfl_franchise_id} for f in db_fr[:5]]
                # peek export
                export = service._mfl_export("rosters", league_id=league_id, year=y)
                fr_list = []
                if isinstance(export, dict):
                    fr_list = (export.get("rosters") or {}).get("franchise") or []
                resp["export_franchise_count"] = len(fr_list) if isinstance(fr_list, list) else 0
                resp["export_ids_sample"] = [(str((f or {}).get("id"))) for f in (fr_list[:5] if isinstance(fr_list, list) else [])]
            except Exception:
                # ignore debug errors
                pass
        return resp
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to refresh rosters: {str(e)}")
