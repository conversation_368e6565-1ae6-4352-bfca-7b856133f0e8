"""
Draft assistance API endpoints.

Provides endpoints for draft board generation, real-time recommendations,
Monte Carlo simulations, and contingency planning.
"""
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, <PERSON>
from datetime import datetime
from decimal import Decimal

from ..core.database import get_db
from ..services.draft_assistant import (
    DraftAssistant, 
    DraftStrategy, 
    DraftBoard, 
    DraftRecommendation, 
    DraftScenario,
    PlayerTier
)
from ..models.player import PlayerPosition

router = APIRouter(prefix="/draft", tags=["draft"])


# Pydantic models for API responses
class PlayerTierResponse(BaseModel):
    """Response model for player tier."""
    tier_number: int
    position: PlayerPosition
    players: List[str]
    min_value: Decimal
    max_value: Decimal
    avg_value: Decimal
    tier_break_threshold: Decimal

    class Config:
        from_attributes = True


class DraftBoardResponse(BaseModel):
    """Response model for draft board."""
    tiers: List[PlayerTierResponse]
    overall_rankings: List[str]
    position_rankings: Dict[PlayerPosition, List[str]]
    value_over_replacement: Dict[str, Decimal]
    last_updated: datetime
    strategy: DraftStrategy

    class Config:
        from_attributes = True


class DraftRecommendationResponse(BaseModel):
    """Response model for draft recommendation."""
    player_id: str
    player_name: str
    position: PlayerPosition
    team: str
    projected_points: Decimal
    value_over_replacement: Decimal
    tier: int
    confidence: Decimal
    rationale: str
    alternatives: List[str]
    positional_need_score: Decimal
    opportunity_cost: Decimal

    class Config:
        from_attributes = True


class DraftPickResponse(BaseModel):
    """Response model for draft pick."""
    round_number: int
    pick_number: int
    overall_pick: int
    franchise_id: str
    player_id: Optional[str] = None
    timestamp: Optional[datetime] = None

    class Config:
        from_attributes = True


class DraftScenarioResponse(BaseModel):
    """Response model for draft scenario."""
    scenario_id: str
    picks: List[DraftPickResponse]
    final_roster: Dict[PlayerPosition, List[str]]
    projected_points: Decimal
    win_probability: Decimal
    strategy_score: Decimal

    class Config:
        from_attributes = True


class ContingencyPlanResponse(BaseModel):
    """Response model for contingency plans."""
    scenario_name: str
    recommendations: List[DraftRecommendationResponse]

    class Config:
        from_attributes = True


# Request models
class DraftBoardRequest(BaseModel):
    """Request model for generating draft board."""
    league_id: str
    season: int = Field(default=2024, ge=2020, le=2030)
    strategy: DraftStrategy = DraftStrategy.VALUE_BASED
    force_refresh: bool = False


class DraftRecommendationRequest(BaseModel):
    """Request model for draft recommendation."""
    league_id: str
    franchise_id: str
    available_players: List[str]
    current_pick: int = Field(ge=1)
    season: int = Field(default=2024, ge=2020, le=2030)
    strategy: DraftStrategy = DraftStrategy.VALUE_BASED


class MonteCarloRequest(BaseModel):
    """Request model for Monte Carlo simulation."""
    league_id: str
    franchise_id: str
    num_simulations: int = Field(default=1000, ge=100, le=10000)
    season: int = Field(default=2024, ge=2020, le=2030)
    strategy: DraftStrategy = DraftStrategy.VALUE_BASED


class ContingencyPlanRequest(BaseModel):
    """Request model for contingency planning."""
    league_id: str
    franchise_id: str
    target_players: List[str]
    season: int = Field(default=2024, ge=2020, le=2030)


class UpdateDraftBoardRequest(BaseModel):
    """Request model for updating draft board."""
    league_id: str
    picked_player_id: str
    season: int = Field(default=2024, ge=2020, le=2030)


@router.post("/board", response_model=DraftBoardResponse)
async def generate_draft_board(
    request: DraftBoardRequest,
    db: Session = Depends(get_db)
):
    """
    Generate a tiered draft board based on value over replacement.
    
    Creates player tiers, overall rankings, and position-specific rankings
    using the specified draft strategy.
    """
    try:
        draft_assistant = DraftAssistant(db)
        
        draft_board = draft_assistant.generate_draft_board(
            league_id=request.league_id,
            season=request.season,
            strategy=request.strategy,
            force_refresh=request.force_refresh
        )
        
        # Convert to response model
        tier_responses = [
            PlayerTierResponse(
                tier_number=tier.tier_number,
                position=tier.position,
                players=tier.players,
                min_value=tier.min_value,
                max_value=tier.max_value,
                avg_value=tier.avg_value,
                tier_break_threshold=tier.tier_break_threshold
            )
            for tier in draft_board.tiers
        ]
        
        return DraftBoardResponse(
            tiers=tier_responses,
            overall_rankings=draft_board.overall_rankings,
            position_rankings=draft_board.position_rankings,
            value_over_replacement=draft_board.value_over_replacement,
            last_updated=draft_board.last_updated,
            strategy=draft_board.strategy
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate draft board: {str(e)}")


@router.post("/recommendation", response_model=DraftRecommendationResponse)
async def get_draft_recommendation(
    request: DraftRecommendationRequest,
    db: Session = Depends(get_db)
):
    """
    Get real-time draft recommendation for a specific pick.
    
    Analyzes available players, current roster composition, and positional needs
    to provide the optimal pick recommendation with detailed rationale.
    """
    try:
        draft_assistant = DraftAssistant(db)
        
        recommendation = draft_assistant.get_draft_recommendation(
            league_id=request.league_id,
            franchise_id=request.franchise_id,
            available_players=request.available_players,
            current_pick=request.current_pick,
            season=request.season,
            strategy=request.strategy
        )
        
        return DraftRecommendationResponse(
            player_id=recommendation.player_id,
            player_name=recommendation.player_name,
            position=recommendation.position,
            team=recommendation.team,
            projected_points=recommendation.projected_points,
            value_over_replacement=recommendation.value_over_replacement,
            tier=recommendation.tier,
            confidence=recommendation.confidence,
            rationale=recommendation.rationale,
            alternatives=recommendation.alternatives,
            positional_need_score=recommendation.positional_need_score,
            opportunity_cost=recommendation.opportunity_cost
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get draft recommendation: {str(e)}")


@router.post("/simulate", response_model=List[DraftScenarioResponse])
async def run_monte_carlo_simulation(
    request: MonteCarloRequest,
    db: Session = Depends(get_db)
):
    """
    Run Monte Carlo simulation for draft scenarios.
    
    Simulates multiple draft outcomes to help with strategic planning
    and understanding potential roster compositions.
    """
    try:
        draft_assistant = DraftAssistant(db)
        
        scenarios = draft_assistant.run_monte_carlo_simulation(
            league_id=request.league_id,
            franchise_id=request.franchise_id,
            num_simulations=request.num_simulations,
            season=request.season,
            strategy=request.strategy
        )
        
        # Convert to response models
        scenario_responses = []
        for scenario in scenarios:
            pick_responses = [
                DraftPickResponse(
                    round_number=pick.round_number,
                    pick_number=pick.pick_number,
                    overall_pick=pick.overall_pick,
                    franchise_id=pick.franchise_id,
                    player_id=pick.player_id,
                    timestamp=pick.timestamp
                )
                for pick in scenario.picks
            ]
            
            scenario_responses.append(DraftScenarioResponse(
                scenario_id=scenario.scenario_id,
                picks=pick_responses,
                final_roster=scenario.final_roster,
                projected_points=scenario.projected_points,
                win_probability=scenario.win_probability,
                strategy_score=scenario.strategy_score
            ))
        
        return scenario_responses
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to run Monte Carlo simulation: {str(e)}")


@router.post("/contingency", response_model=List[ContingencyPlanResponse])
async def generate_contingency_plans(
    request: ContingencyPlanRequest,
    db: Session = Depends(get_db)
):
    """
    Generate contingency plans for different draft outcomes.
    
    Creates alternative draft strategies based on whether target players
    are available or not.
    """
    try:
        draft_assistant = DraftAssistant(db)
        
        contingency_plans = draft_assistant.generate_contingency_plans(
            league_id=request.league_id,
            franchise_id=request.franchise_id,
            target_players=request.target_players,
            season=request.season
        )
        
        # Convert to response models
        plan_responses = []
        for scenario_name, recommendations in contingency_plans.items():
            recommendation_responses = [
                DraftRecommendationResponse(
                    player_id=rec.player_id,
                    player_name=rec.player_name,
                    position=rec.position,
                    team=rec.team,
                    projected_points=rec.projected_points,
                    value_over_replacement=rec.value_over_replacement,
                    tier=rec.tier,
                    confidence=rec.confidence,
                    rationale=rec.rationale,
                    alternatives=rec.alternatives,
                    positional_need_score=rec.positional_need_score,
                    opportunity_cost=rec.opportunity_cost
                )
                for rec in recommendations
            ]
            
            plan_responses.append(ContingencyPlanResponse(
                scenario_name=scenario_name,
                recommendations=recommendation_responses
            ))
        
        return plan_responses
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate contingency plans: {str(e)}")


@router.put("/board/update", response_model=DraftBoardResponse)
async def update_draft_board_real_time(
    request: UpdateDraftBoardRequest,
    db: Session = Depends(get_db)
):
    """
    Update draft board in real-time after a pick is made.
    
    Refreshes the draft board to reflect the latest pick and provide
    updated recommendations for subsequent picks.
    """
    try:
        draft_assistant = DraftAssistant(db)
        
        updated_board = draft_assistant.update_draft_board_real_time(
            league_id=request.league_id,
            picked_player_id=request.picked_player_id,
            season=request.season
        )
        
        # Convert to response model
        tier_responses = [
            PlayerTierResponse(
                tier_number=tier.tier_number,
                position=tier.position,
                players=tier.players,
                min_value=tier.min_value,
                max_value=tier.max_value,
                avg_value=tier.avg_value,
                tier_break_threshold=tier.tier_break_threshold
            )
            for tier in updated_board.tiers
        ]
        
        return DraftBoardResponse(
            tiers=tier_responses,
            overall_rankings=updated_board.overall_rankings,
            position_rankings=updated_board.position_rankings,
            value_over_replacement=updated_board.value_over_replacement,
            last_updated=updated_board.last_updated,
            strategy=updated_board.strategy
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update draft board: {str(e)}")


@router.get("/strategies", response_model=List[str])
async def get_available_strategies():
    """
    Get list of available draft strategies.
    
    Returns all supported draft strategy types that can be used
    for board generation and recommendations.
    """
    return [strategy.value for strategy in DraftStrategy]


@router.get("/board/{league_id}", response_model=DraftBoardResponse)
async def get_cached_draft_board(
    league_id: str,
    season: int = Query(default=2024, ge=2020, le=2030),
    strategy: DraftStrategy = Query(default=DraftStrategy.VALUE_BASED),
    db: Session = Depends(get_db)
):
    """
    Get cached draft board for a league.
    
    Returns the most recently generated draft board for the specified
    league and strategy, if available in cache.
    """
    try:
        draft_assistant = DraftAssistant(db)
        
        draft_board = draft_assistant.generate_draft_board(
            league_id=league_id,
            season=season,
            strategy=strategy,
            force_refresh=False  # Use cache if available
        )
        
        # Convert to response model
        tier_responses = [
            PlayerTierResponse(
                tier_number=tier.tier_number,
                position=tier.position,
                players=tier.players,
                min_value=tier.min_value,
                max_value=tier.max_value,
                avg_value=tier.avg_value,
                tier_break_threshold=tier.tier_break_threshold
            )
            for tier in draft_board.tiers
        ]
        
        return DraftBoardResponse(
            tiers=tier_responses,
            overall_rankings=draft_board.overall_rankings,
            position_rankings=draft_board.position_rankings,
            value_over_replacement=draft_board.value_over_replacement,
            last_updated=draft_board.last_updated,
            strategy=draft_board.strategy
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get draft board: {str(e)}")


@router.get("/health")
async def health_check():
    """Health check endpoint for draft service."""
    return {
        "status": "healthy",
        "service": "draft_assistant",
        "timestamp": datetime.utcnow().isoformat()
    }