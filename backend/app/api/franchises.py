"""
Franchise management API endpoints.

Provides endpoints for creating, updating, and managing fantasy football franchises.
"""
from typing import List, Dict, Any, Optional
from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, <PERSON>
from datetime import datetime

from ..core.database import get_db
from ..models.league import League
from ..models.roster import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RosterPlayer
from ..models.player import Player

router = APIRouter(prefix="/franchises", tags=["franchises"])


# Pydantic models for API requests/responses
class FranchiseCreateRequest(BaseModel):
    """Request model for creating a new franchise."""
    id: str = Field(..., description="Unique franchise identifier")
    name: str = Field(..., description="Franchise name")
    owner_name: str = Field(..., description="Owner name")
    league_id: str = Field(..., description="League ID")
    mfl_franchise_id: Optional[str] = Field(None, description="MFL franchise ID for integration")
    salary_cap: Optional[Decimal] = Field(None, description="Salary cap limit")
    faab_budget: Optional[Decimal] = Field(None, description="FAAB budget")
    franchise_metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class FranchiseUpdateRequest(BaseModel):
    """Request model for updating a franchise."""
    name: Optional[str] = Field(None, description="Franchise name")
    owner_name: Optional[str] = Field(None, description="Owner name")
    mfl_franchise_id: Optional[str] = Field(None, description="MFL franchise ID for integration")
    salary_cap: Optional[Decimal] = Field(None, description="Salary cap limit")
    faab_budget: Optional[Decimal] = Field(None, description="FAAB budget")
    faab_spent: Optional[Decimal] = Field(None, description="FAAB spent")
    franchise_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    is_active: Optional[bool] = Field(None, description="Franchise active status")


class RosterPlayerResponse(BaseModel):
    """Response model for roster player information."""
    id: str
    player_id: str
    player_name: str
    position: str
    team: str
    roster_slot: Optional[str]
    salary: Optional[Decimal]
    keeper_cost: Optional[int]
    is_keeper: bool
    is_active: bool

    class Config:
        from_attributes = True


class FranchiseResponse(BaseModel):
    """Response model for franchise information."""
    id: str
    name: str
    owner_name: str
    league_id: str
    league_name: str
    mfl_franchise_id: Optional[str]
    salary_cap: Optional[Decimal]
    faab_budget: Optional[Decimal]
    faab_spent: Decimal
    remaining_faab: Decimal
    franchise_metadata: Dict[str, Any]
    is_active: bool
    roster_size: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class FranchiseListResponse(BaseModel):
    """Response model for franchise list."""
    id: str
    name: str
    owner_name: str
    league_id: str
    league_name: str
    roster_size: int
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


class RosterResponse(BaseModel):
    """Response model for franchise roster."""
    franchise_id: str
    franchise_name: str
    total_salary: Optional[Decimal]
    is_valid: bool
    players: List[RosterPlayerResponse]
    position_counts: Dict[str, int]
    starting_lineup: List[RosterPlayerResponse]
    bench_players: List[RosterPlayerResponse]

    class Config:
        from_attributes = True


@router.post("/", response_model=FranchiseResponse)
async def create_franchise(
    franchise_request: FranchiseCreateRequest,
    db: Session = Depends(get_db)
):
    """
    Create a new fantasy football franchise.
    
    Creates a franchise within an existing league with the specified
    configuration and financial settings.
    """
    # Check if franchise ID already exists
    existing_franchise = db.query(Franchise).filter(Franchise.id == franchise_request.id).first()
    if existing_franchise:
        raise HTTPException(status_code=400, detail=f"Franchise with ID '{franchise_request.id}' already exists")
    
    # Verify league exists
    league = db.query(League).filter(League.id == franchise_request.league_id).first()
    if not league:
        raise HTTPException(status_code=404, detail=f"League '{franchise_request.league_id}' not found")
    
    try:
        # Create new franchise
        franchise = Franchise(
            id=franchise_request.id,
            name=franchise_request.name,
            owner_name=franchise_request.owner_name,
            league_id=franchise_request.league_id,
            mfl_franchise_id=franchise_request.mfl_franchise_id,
            salary_cap=franchise_request.salary_cap,
            faab_budget=franchise_request.faab_budget,
            franchise_metadata=franchise_request.franchise_metadata
        )
        
        db.add(franchise)
        db.commit()
        db.refresh(franchise)
        
        # Create empty roster for the franchise
        roster = Roster(
            id=f"{franchise.id}_roster",
            franchise_id=franchise.id
        )
        db.add(roster)
        db.commit()
        
        return FranchiseResponse(
            id=franchise.id,
            name=franchise.name,
            owner_name=franchise.owner_name,
            league_id=franchise.league_id,
            league_name=league.name,
            mfl_franchise_id=franchise.mfl_franchise_id,
            salary_cap=franchise.salary_cap,
            faab_budget=franchise.faab_budget,
            faab_spent=franchise.faab_spent,
            remaining_faab=franchise.get_remaining_faab(),
            franchise_metadata=franchise.franchise_metadata,
            is_active=franchise.is_active,
            roster_size=0,
            created_at=franchise.created_at,
            updated_at=franchise.updated_at
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating franchise: {str(e)}")


@router.get("/health")
async def health_check():
    """Health check endpoint for franchise service."""
    return {
        "status": "healthy",
        "service": "franchise_management",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/", response_model=List[FranchiseListResponse])
async def list_franchises(
    league_id: Optional[str] = Query(None, description="Filter by league ID"),
    active_only: bool = Query(True, description="Only return active franchises"),
    db: Session = Depends(get_db)
):
    """
    List all fantasy football franchises.
    
    Returns a list of franchises with basic information, optionally filtered
    by league and active status.
    """
    try:
        query = db.query(Franchise).join(League)
        
        if league_id:
            query = query.filter(Franchise.league_id == league_id)
        
        if active_only:
            query = query.filter(Franchise.is_active == True)
        
        franchises = query.order_by(Franchise.created_at.desc()).all()
        
        # Get roster sizes for each franchise
        response = []
        for franchise in franchises:
            roster_size = 0
            if franchise.roster:
                roster_size = len(franchise.roster.get_active_players())
            
            response.append(FranchiseListResponse(
                id=franchise.id,
                name=franchise.name,
                owner_name=franchise.owner_name,
                league_id=franchise.league_id,
                league_name=franchise.league.name,
                roster_size=roster_size,
                is_active=franchise.is_active,
                created_at=franchise.created_at
            ))
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing franchises: {str(e)}")


@router.get("/{franchise_id}", response_model=FranchiseResponse)
async def get_franchise(
    franchise_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific franchise.
    
    Returns complete franchise information including financial status
    and roster size.
    """
    franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
    if not franchise:
        raise HTTPException(status_code=404, detail=f"Franchise '{franchise_id}' not found")
    
    try:
        # Get roster size
        roster_size = 0
        if franchise.roster:
            roster_size = len(franchise.roster.get_active_players())
        
        return FranchiseResponse(
            id=franchise.id,
            name=franchise.name,
            owner_name=franchise.owner_name,
            league_id=franchise.league_id,
            league_name=franchise.league.name,
            mfl_franchise_id=franchise.mfl_franchise_id,
            salary_cap=franchise.salary_cap,
            faab_budget=franchise.faab_budget,
            faab_spent=franchise.faab_spent,
            remaining_faab=franchise.get_remaining_faab(),
            franchise_metadata=franchise.franchise_metadata,
            is_active=franchise.is_active,
            roster_size=roster_size,
            created_at=franchise.created_at,
            updated_at=franchise.updated_at
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting franchise: {str(e)}")


@router.put("/{franchise_id}", response_model=FranchiseResponse)
async def update_franchise(
    franchise_id: str,
    franchise_update: FranchiseUpdateRequest,
    db: Session = Depends(get_db)
):
    """
    Update an existing franchise's information.
    
    Updates franchise settings including name, owner, financial limits,
    and metadata.
    """
    franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
    if not franchise:
        raise HTTPException(status_code=404, detail=f"Franchise '{franchise_id}' not found")
    
    try:
        # Update franchise fields
        if franchise_update.name is not None:
            franchise.name = franchise_update.name
        if franchise_update.owner_name is not None:
            franchise.owner_name = franchise_update.owner_name
        if franchise_update.mfl_franchise_id is not None:
            franchise.mfl_franchise_id = franchise_update.mfl_franchise_id
        if franchise_update.salary_cap is not None:
            franchise.salary_cap = franchise_update.salary_cap
        if franchise_update.faab_budget is not None:
            franchise.faab_budget = franchise_update.faab_budget
        if franchise_update.faab_spent is not None:
            franchise.faab_spent = franchise_update.faab_spent
        if franchise_update.franchise_metadata is not None:
            franchise.franchise_metadata = franchise_update.franchise_metadata
        if franchise_update.is_active is not None:
            franchise.is_active = franchise_update.is_active
        
        db.commit()
        db.refresh(franchise)
        
        # Get roster size
        roster_size = 0
        if franchise.roster:
            roster_size = len(franchise.roster.get_active_players())
        
        return FranchiseResponse(
            id=franchise.id,
            name=franchise.name,
            owner_name=franchise.owner_name,
            league_id=franchise.league_id,
            league_name=franchise.league.name,
            mfl_franchise_id=franchise.mfl_franchise_id,
            salary_cap=franchise.salary_cap,
            faab_budget=franchise.faab_budget,
            faab_spent=franchise.faab_spent,
            remaining_faab=franchise.get_remaining_faab(),
            franchise_metadata=franchise.franchise_metadata,
            is_active=franchise.is_active,
            roster_size=roster_size,
            created_at=franchise.created_at,
            updated_at=franchise.updated_at
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating franchise: {str(e)}")


@router.delete("/{franchise_id}")
async def delete_franchise(
    franchise_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete a franchise and all associated data.
    
    WARNING: This will permanently delete the franchise, roster,
    and all recommendations associated with it.
    """
    franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
    if not franchise:
        raise HTTPException(status_code=404, detail=f"Franchise '{franchise_id}' not found")
    
    try:
        db.delete(franchise)
        db.commit()
        
        return {"message": f"Franchise '{franchise_id}' deleted successfully"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting franchise: {str(e)}")


@router.get("/{franchise_id}/roster", response_model=RosterResponse)
async def get_franchise_roster(
    franchise_id: str,
    db: Session = Depends(get_db)
):
    """
    Get the current roster for a franchise.
    
    Returns detailed roster information including all players,
    their positions, and roster slot assignments.
    """
    franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
    if not franchise:
        raise HTTPException(status_code=404, detail=f"Franchise '{franchise_id}' not found")
    
    if not franchise.roster:
        raise HTTPException(status_code=404, detail=f"No roster found for franchise '{franchise_id}'")
    
    try:
        roster = franchise.roster
        active_players = roster.get_active_players()
        
        # Convert roster players to response format
        player_responses = []
        for roster_player in active_players:
            player_responses.append(RosterPlayerResponse(
                id=roster_player.id,
                player_id=roster_player.player_id,
                player_name=roster_player.player.name,
                position=roster_player.player.position.value,
                team=roster_player.player.team,
                roster_slot=roster_player.roster_slot,
                salary=roster_player.salary,
                keeper_cost=roster_player.keeper_cost,
                is_keeper=roster_player.is_keeper,
                is_active=roster_player.is_active
            ))
        
        # Calculate position counts
        position_counts = {}
        for player in player_responses:
            position = player.position
            position_counts[position] = position_counts.get(position, 0) + 1
        
        # Separate starting lineup and bench
        starting_lineup = [p for p in player_responses if p.roster_slot and p.roster_slot != "BENCH"]
        bench_players = [p for p in player_responses if not p.roster_slot or p.roster_slot == "BENCH"]
        
        return RosterResponse(
            franchise_id=franchise.id,
            franchise_name=franchise.name,
            total_salary=roster.total_salary,
            is_valid=roster.is_valid,
            players=player_responses,
            position_counts=position_counts,
            starting_lineup=starting_lineup,
            bench_players=bench_players
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting franchise roster: {str(e)}")


@router.post("/{franchise_id}/roster/add-player")
async def add_player_to_roster(
    franchise_id: str,
    player_id: str = Query(..., description="Player ID to add"),
    roster_slot: Optional[str] = Query(None, description="Roster slot assignment"),
    salary: Optional[Decimal] = Query(None, description="Player salary"),
    keeper_cost: Optional[int] = Query(None, description="Keeper cost in draft rounds"),
    is_keeper: bool = Query(False, description="Is this player a keeper"),
    db: Session = Depends(get_db)
):
    """
    Add a player to a franchise's roster.
    
    Adds the specified player to the franchise roster with optional
    slot assignment and financial information.
    """
    franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
    if not franchise:
        raise HTTPException(status_code=404, detail=f"Franchise '{franchise_id}' not found")
    
    if not franchise.roster:
        raise HTTPException(status_code=404, detail=f"No roster found for franchise '{franchise_id}'")
    
    # Verify player exists
    player = db.query(Player).filter(Player.id == player_id).first()
    if not player:
        raise HTTPException(status_code=404, detail=f"Player '{player_id}' not found")
    
    # Check if player is already on roster
    existing_roster_player = db.query(RosterPlayer).filter(
        RosterPlayer.roster_id == franchise.roster.id,
        RosterPlayer.player_id == player_id,
        RosterPlayer.is_active == True
    ).first()
    
    if existing_roster_player:
        raise HTTPException(status_code=400, detail=f"Player '{player_id}' is already on the roster")
    
    try:
        # Create roster player entry
        roster_player = RosterPlayer(
            id=f"{franchise.roster.id}_{player_id}",
            roster_id=franchise.roster.id,
            player_id=player_id,
            roster_slot=roster_slot,
            salary=salary,
            keeper_cost=keeper_cost,
            is_keeper=is_keeper
        )
        
        db.add(roster_player)
        db.commit()
        
        return {
            "message": f"Player '{player.name}' added to franchise '{franchise.name}' roster",
            "player_id": player_id,
            "franchise_id": franchise_id,
            "roster_slot": roster_slot
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error adding player to roster: {str(e)}")


@router.delete("/{franchise_id}/roster/remove-player")
async def remove_player_from_roster(
    franchise_id: str,
    player_id: str = Query(..., description="Player ID to remove"),
    db: Session = Depends(get_db)
):
    """
    Remove a player from a franchise's roster.
    
    Removes the specified player from the franchise roster.
    """
    franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
    if not franchise:
        raise HTTPException(status_code=404, detail=f"Franchise '{franchise_id}' not found")
    
    if not franchise.roster:
        raise HTTPException(status_code=404, detail=f"No roster found for franchise '{franchise_id}'")
    
    # Find roster player entry
    roster_player = db.query(RosterPlayer).filter(
        RosterPlayer.roster_id == franchise.roster.id,
        RosterPlayer.player_id == player_id,
        RosterPlayer.is_active == True
    ).first()
    
    if not roster_player:
        raise HTTPException(status_code=404, detail=f"Player '{player_id}' not found on roster")
    
    try:
        # Mark as inactive instead of deleting for audit trail
        roster_player.is_active = False
        db.commit()
        
        return {
            "message": f"Player '{roster_player.player.name}' removed from franchise '{franchise.name}' roster",
            "player_id": player_id,
            "franchise_id": franchise_id
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error removing player from roster: {str(e)}")