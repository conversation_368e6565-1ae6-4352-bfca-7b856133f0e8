"""
API routes package for the AI Fantasy Assistant backend.

Contains FastAPI routers for different API endpoints.
"""

from fastapi import APIRouter
# from .mfl import router as mfl_router  # Commented out due to pymfl dependency
from .leagues import router as leagues_router
from .franchises import router as franchises_router
from .recommendations import router as recommendations_router
from .upload import router as upload_router
from .async_upload import router as async_upload_router
from .rules import router as rules_router
from .projections import router as projections_router
from .keepers import router as keepers_router
from .draft import router as draft_router
from .trades import router as trades_router
from .lineup import router as lineup_router
from .waiver import router as waiver_router
from .alerts import router as alerts_router

# Create main API router
api_router = APIRouter(prefix="/api/v1")

# Include sub-routers
# api_router.include_router(mfl_router)  # Commented out due to pymfl dependency
api_router.include_router(leagues_router)
api_router.include_router(franchises_router)
api_router.include_router(recommendations_router)
api_router.include_router(upload_router)
api_router.include_router(async_upload_router)
api_router.include_router(rules_router)
api_router.include_router(projections_router)
api_router.include_router(keepers_router)
api_router.include_router(draft_router)
api_router.include_router(trades_router)
api_router.include_router(lineup_router)
api_router.include_router(waiver_router)
api_router.include_router(alerts_router)

__all__ = ["api_router"]