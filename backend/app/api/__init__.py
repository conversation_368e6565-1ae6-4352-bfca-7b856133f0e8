"""
API routes package for the AI Fantasy Assistant backend.

Contains FastAPI routers for different API endpoints.

This module avoids importing all routers at module import time to reduce
startup latency and side effects. Use get_api_router() to build the router
lazily when the application starts.
"""

from fastapi import APIRouter
from typing import Optional


def get_api_router(*, include_all: bool = True) -> APIRouter:
    """Build and return the main API router lazily.

    Args:
        include_all: Whether to include all sub-routers. Set to False to include
                     only essential endpoints for minimal startup.
    """
    router = APIRouter(prefix="/api/v1")

    # Import routers lazily to avoid heavy import-time side effects
    from .mfl_proxy import router as mfl_proxy_router
    router.include_router(mfl_proxy_router)

    if include_all:
        from .leagues import router as leagues_router
        from .franchises import router as franchises_router
        from .recommendations import router as recommendations_router
        from .upload import router as upload_router
        from .async_upload import router as async_upload_router
        from .rules import router as rules_router
        from .projections import router as projections_router
        from .keepers import router as keepers_router
        from .draft import router as draft_router
        from .trades import router as trades_router
        from .lineup import router as lineup_router
        from .waiver import router as waiver_router
        from .alerts import router as alerts_router
        from .jobs import router as jobs_router
        from .monitoring import router as monitoring_router
        from .rankings import router as rankings_router
        from .adp import router as adp_router
        from .mfl import router as mfl_router
        from .preseason import router as preseason_router
        from .master import router as master_router
        from .admin import router as admin_router

        router.include_router(leagues_router)
        router.include_router(franchises_router)
        router.include_router(recommendations_router)
        router.include_router(upload_router)
        router.include_router(async_upload_router)
        router.include_router(rules_router)
        router.include_router(projections_router)
        router.include_router(keepers_router)
        router.include_router(draft_router)
        router.include_router(trades_router)
        router.include_router(lineup_router)
        router.include_router(waiver_router)
        router.include_router(alerts_router)
        router.include_router(jobs_router)
        router.include_router(monitoring_router)
        router.include_router(rankings_router)
        router.include_router(adp_router)
        router.include_router(mfl_router)
        router.include_router(preseason_router)
        router.include_router(master_router)
        router.include_router(admin_router)

    return router


__all__ = ["get_api_router"]
