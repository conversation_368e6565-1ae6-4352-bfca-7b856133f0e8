"""
API endpoints for background job monitoring and management.
"""
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel
from celery.result import AsyncResult

from ..tasks.monitoring import JobMonitor, health_check, system_diagnostics
from ..tasks.data_refresh import refresh_mfl_data, refresh_player_data, refresh_projection_aggregates
from ..tasks.alert_processing import create_lineup_lock_alerts, process_late_breaking_news
from ..core.celery import celery_app

router = APIRouter(prefix="/jobs", tags=["jobs"])

# Pydantic models for request/response
class TaskResponse(BaseModel):
    task_id: str
    status: str
    message: str

class TaskStatusResponse(BaseModel):
    id: str
    status: str
    result: Optional[Dict[str, Any]] = None
    traceback: Optional[str] = None
    date_done: Optional[str] = None
    task_name: Optional[str] = None

class WorkerStatsResponse(BaseModel):
    workers: int
    details: Dict[str, Any]

class SystemHealthResponse(BaseModel):
    database: str
    redis: str
    celery: str
    timestamp: str

class NewsItem(BaseModel):
    title: str
    content: str
    player_id: Optional[str] = None
    severity: str = "medium"  # low, medium, high, urgent
    source: str
    published_at: str


# Initialize job monitor
job_monitor = JobMonitor()


@router.get("/health", response_model=Dict[str, Any])
async def check_system_health():
    """Check the health of the background job system."""
    try:
        # Run health check task
        task = health_check.delay()
        result = task.get(timeout=10)
        
        return {
            "status": "healthy",
            "celery": result,
            "timestamp": result["timestamp"]
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Health check failed: {str(e)}")


@router.get("/diagnostics", response_model=SystemHealthResponse)
async def run_system_diagnostics():
    """Run comprehensive system diagnostics."""
    try:
        task = system_diagnostics.delay()
        result = task.get(timeout=30)
        
        return SystemHealthResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Diagnostics failed: {str(e)}")


@router.get("/active", response_model=List[Dict[str, Any]])
async def get_active_tasks():
    """Get list of currently active background tasks."""
    try:
        return job_monitor.get_active_tasks()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get active tasks: {str(e)}")


@router.get("/scheduled", response_model=List[Dict[str, Any]])
async def get_scheduled_tasks():
    """Get list of scheduled background tasks."""
    try:
        return job_monitor.get_scheduled_tasks()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get scheduled tasks: {str(e)}")


@router.get("/workers", response_model=WorkerStatsResponse)
async def get_worker_stats():
    """Get statistics about Celery workers."""
    try:
        stats = job_monitor.get_worker_stats()
        return WorkerStatsResponse(**stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get worker stats: {str(e)}")


@router.get("/queues", response_model=Dict[str, int])
async def get_queue_lengths():
    """Get the length of each task queue."""
    try:
        return job_monitor.get_queue_lengths()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get queue lengths: {str(e)}")


@router.get("/task/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """Get detailed status of a specific task."""
    try:
        status = job_monitor.get_task_status(task_id)
        return TaskStatusResponse(**status)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get task status: {str(e)}")


@router.post("/task/{task_id}/retry", response_model=Dict[str, Any])
async def retry_task(task_id: str):
    """Retry a failed task."""
    try:
        result = job_monitor.retry_failed_task(task_id)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retry task: {str(e)}")


@router.delete("/task/{task_id}", response_model=Dict[str, Any])
async def cancel_task(task_id: str):
    """Cancel a running task."""
    try:
        result = job_monitor.cancel_task(task_id)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to cancel task: {str(e)}")


# Manual task triggers
@router.post("/trigger/mfl-refresh", response_model=TaskResponse)
async def trigger_mfl_refresh():
    """Manually trigger MFL data refresh."""
    try:
        task = refresh_mfl_data.delay()
        return TaskResponse(
            task_id=task.id,
            status="started",
            message="MFL data refresh task started"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start MFL refresh: {str(e)}")


@router.post("/trigger/projection-refresh", response_model=TaskResponse)
async def trigger_projection_refresh():
    """Manually trigger projection aggregation refresh."""
    try:
        task = refresh_projection_aggregates.delay()
        return TaskResponse(
            task_id=task.id,
            status="started",
            message="Projection aggregation task started"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start projection refresh: {str(e)}")


@router.post("/trigger/player-refresh/{league_id}", response_model=TaskResponse)
async def trigger_player_refresh(league_id: str):
    """Manually trigger player data refresh for a specific league."""
    try:
        task = refresh_player_data.delay(league_id)
        return TaskResponse(
            task_id=task.id,
            status="started",
            message=f"Player data refresh task started for league {league_id}"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start player refresh: {str(e)}")


@router.post("/trigger/lineup-alerts/{league_id}/{week}", response_model=TaskResponse)
async def trigger_lineup_alerts(league_id: str, week: int):
    """Manually trigger lineup lock alerts for a specific league and week."""
    try:
        task = create_lineup_lock_alerts.delay(league_id, week)
        return TaskResponse(
            task_id=task.id,
            status="started",
            message=f"Lineup lock alerts task started for league {league_id}, week {week}"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start lineup alerts: {str(e)}")


@router.post("/trigger/breaking-news", response_model=TaskResponse)
async def trigger_breaking_news_processing(news_item: NewsItem):
    """Manually trigger processing of late-breaking news."""
    try:
        task = process_late_breaking_news.delay(news_item.dict())
        return TaskResponse(
            task_id=task.id,
            status="started",
            message="Late-breaking news processing task started"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start news processing: {str(e)}")


@router.get("/failed", response_model=List[Dict[str, Any]])
async def get_failed_tasks(limit: int = 50):
    """Get list of recently failed tasks."""
    try:
        return job_monitor.get_failed_tasks(limit)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get failed tasks: {str(e)}")


@router.post("/purge-queue/{queue_name}", response_model=Dict[str, Any])
async def purge_queue(queue_name: str):
    """Purge all tasks from a specific queue."""
    try:
        celery_app.control.purge()
        return {
            "success": True,
            "message": f"Queue {queue_name} has been purged"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to purge queue: {str(e)}")


@router.get("/stats", response_model=Dict[str, Any])
async def get_job_stats():
    """Get comprehensive job statistics."""
    try:
        active_tasks = job_monitor.get_active_tasks()
        scheduled_tasks = job_monitor.get_scheduled_tasks()
        worker_stats = job_monitor.get_worker_stats()
        queue_lengths = job_monitor.get_queue_lengths()
        
        return {
            "active_tasks_count": len(active_tasks),
            "scheduled_tasks_count": len(scheduled_tasks),
            "worker_count": worker_stats["workers"],
            "queue_lengths": queue_lengths,
            "total_queued_tasks": sum(queue_lengths.values()),
            "timestamp": "2024-01-01T00:00:00Z"  # Would use actual timestamp
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job stats: {str(e)}")