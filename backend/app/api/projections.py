"""
API endpoints for projections aggregation.
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from decimal import Decimal

from ..core.database import get_db
from ..services.projections_aggregator import (
    ProjectionsAggregator,
    ProjectionsCacheManager,
    AggregatedProjection,
    BacktestResult
)

router = APIRouter(prefix="/projections", tags=["projections"])


class AggregatedProjectionResponse(BaseModel):
    """Response model for aggregated projections."""
    player_id: str
    week: Optional[int]
    season: int
    projected_points: float
    confidence_interval: tuple[float, float]
    variance: float
    source_count: int
    source_weights: Dict[str, float]
    metadata: Dict[str, Any]

    @classmethod
    def from_aggregated_projection(cls, proj: AggregatedProjection) -> "AggregatedProjectionResponse":
        """Convert AggregatedProjection to response model."""
        return cls(
            player_id=proj.player_id,
            week=proj.week,
            season=proj.season,
            projected_points=float(proj.projected_points),
            confidence_interval=(float(proj.confidence_interval[0]), float(proj.confidence_interval[1])),
            variance=float(proj.variance),
            source_count=proj.source_count,
            source_weights=proj.source_weights,
            metadata=proj.metadata
        )


class BacktestResultResponse(BaseModel):
    """Response model for backtesting results."""
    source: str
    mae: float = Field(description="Mean Absolute Error")
    rmse: float = Field(description="Root Mean Square Error")
    accuracy_score: float = Field(description="Custom accuracy metric")
    sample_size: int
    optimal_weight: float


class SourceStatisticsResponse(BaseModel):
    """Response model for source statistics."""
    source: str
    season: int
    total_projections: int
    mean_points: float
    median_points: float
    std_dev: float
    min_points: float
    max_points: float
    weekly_projections: int
    season_projections: int


@router.get("/aggregate/{player_id}", response_model=AggregatedProjectionResponse)
async def get_aggregated_projection(
    player_id: str,
    week: Optional[int] = Query(None, description="Week number (None for season-long)"),
    season: int = Query(2024, description="Season year"),
    min_sources: int = Query(2, description="Minimum number of sources required"),
    use_cache: bool = Query(True, description="Whether to use cached results"),
    db: Session = Depends(get_db)
):
    """
    Get aggregated projection for a specific player.
    
    Args:
        player_id: Player identifier
        week: Week number (None for season-long projections)
        season: Season year
        min_sources: Minimum number of sources required for aggregation
        use_cache: Whether to use cached results
        db: Database session
        
    Returns:
        Aggregated projection with confidence intervals and source weights
    """
    aggregator = ProjectionsAggregator(db)
    
    if use_cache:
        cache_manager = ProjectionsCacheManager(db, aggregator)
        result = cache_manager.get_cached_projection(
            player_id=player_id,
            week=week,
            season=season
        )
    else:
        result = aggregator.aggregate_projections(
            player_id=player_id,
            week=week,
            season=season,
            min_sources=min_sources
        )
    
    if not result:
        raise HTTPException(
            status_code=404,
            detail=f"Insufficient projection sources for player {player_id}, week {week}, season {season}"
        )
    
    return AggregatedProjectionResponse.from_aggregated_projection(result)


@router.get("/aggregate", response_model=List[AggregatedProjectionResponse])
async def get_all_aggregated_projections(
    week: Optional[int] = Query(None, description="Week number (None for season-long)"),
    season: int = Query(2024, description="Season year"),
    min_sources: int = Query(2, description="Minimum number of sources required"),
    limit: int = Query(100, description="Maximum number of results"),
    db: Session = Depends(get_db)
):
    """
    Get aggregated projections for all players in a given week/season.
    
    Args:
        week: Week number (None for season-long projections)
        season: Season year
        min_sources: Minimum number of sources required for aggregation
        limit: Maximum number of results to return
        db: Database session
        
    Returns:
        List of aggregated projections
    """
    aggregator = ProjectionsAggregator(db)
    
    results = aggregator.aggregate_all_projections(
        week=week,
        season=season,
        min_sources=min_sources
    )
    
    # Apply limit
    results = results[:limit]
    
    return [
        AggregatedProjectionResponse.from_aggregated_projection(result)
        for result in results
    ]


@router.get("/sources", response_model=List[str])
async def get_projection_sources(
    season: int = Query(2024, description="Season year"),
    db: Session = Depends(get_db)
):
    """
    Get all available projection sources for a season.
    
    Args:
        season: Season year
        db: Database session
        
    Returns:
        List of source names
    """
    aggregator = ProjectionsAggregator(db)
    return aggregator.get_projection_sources(season)


@router.get("/sources/{source}/statistics", response_model=SourceStatisticsResponse)
async def get_source_statistics(
    source: str,
    season: int = Query(2024, description="Season year"),
    db: Session = Depends(get_db)
):
    """
    Get statistics for a specific projection source.
    
    Args:
        source: Source name
        season: Season year
        db: Database session
        
    Returns:
        Source statistics including mean, median, std dev, etc.
    """
    aggregator = ProjectionsAggregator(db)
    stats = aggregator.get_source_statistics(source, season)
    
    if not stats:
        raise HTTPException(
            status_code=404,
            detail=f"No statistics found for source {source} in season {season}"
        )
    
    return SourceStatisticsResponse(**stats)


@router.get("/backtesting", response_model=List[BacktestResultResponse])
async def run_backtesting(
    season: int = Query(2024, description="Season year"),
    weeks_back: int = Query(8, description="Number of weeks to look back"),
    min_samples: int = Query(10, description="Minimum samples required per source"),
    db: Session = Depends(get_db)
):
    """
    Run backtesting to evaluate projection source accuracy.
    
    Args:
        season: Season year
        weeks_back: Number of weeks to look back for testing
        min_samples: Minimum number of samples required per source
        db: Database session
        
    Returns:
        List of backtesting results for each source
    """
    aggregator = ProjectionsAggregator(db)
    
    results = aggregator.run_backtesting(
        season=season,
        weeks_back=weeks_back,
        min_samples=min_samples
    )
    
    return [
        BacktestResultResponse(
            source=result.source,
            mae=result.mae,
            rmse=result.rmse,
            accuracy_score=result.accuracy_score,
            sample_size=result.sample_size,
            optimal_weight=result.optimal_weight
        )
        for result in results
    ]


@router.get("/weights", response_model=Dict[str, float])
async def get_optimized_weights(
    season: int = Query(2024, description="Season year"),
    db: Session = Depends(get_db)
):
    """
    Get optimized weights for projection sources based on backtesting.
    
    Args:
        season: Season year
        db: Database session
        
    Returns:
        Dictionary mapping source names to optimized weights
    """
    aggregator = ProjectionsAggregator(db)
    return aggregator.optimize_source_weights(season)


@router.post("/cache/refresh")
async def refresh_cache(
    db: Session = Depends(get_db)
):
    """
    Refresh the projections cache by clearing cached weights and projections.
    
    Args:
        db: Database session
        
    Returns:
        Success message
    """
    aggregator = ProjectionsAggregator(db)
    cache_manager = ProjectionsCacheManager(db, aggregator)
    
    # Clear both aggregator cache and projection cache
    aggregator.refresh_projections_cache()
    cache_manager.clear_cache()
    
    return {"message": "Projections cache refreshed successfully"}


@router.delete("/cache")
async def clear_cache(
    player_id: Optional[str] = Query(None, description="Player ID to clear (None for all)"),
    week: Optional[int] = Query(None, description="Week to clear (None for all)"),
    season: Optional[int] = Query(None, description="Season to clear (None for all)"),
    db: Session = Depends(get_db)
):
    """
    Clear cached projections matching the specified criteria.
    
    Args:
        player_id: Player ID to clear (None for all players)
        week: Week to clear (None for all weeks)
        season: Season to clear (None for all seasons)
        db: Database session
        
    Returns:
        Success message
    """
    aggregator = ProjectionsAggregator(db)
    cache_manager = ProjectionsCacheManager(db, aggregator)
    
    if player_id is None and week is None and season is None:
        cache_manager.clear_cache()
        message = "Cleared all cached projections"
    else:
        cache_manager.invalidate_cache(
            player_id=player_id,
            week=week,
            season=season
        )
        message = f"Cleared cached projections for player_id={player_id}, week={week}, season={season}"
    
    return {"message": message}


@router.get("/cache/stats")
async def get_cache_stats(
    db: Session = Depends(get_db)
):
    """
    Get cache statistics.
    
    Args:
        db: Database session
        
    Returns:
        Cache statistics including hit ratio, entry counts, etc.
    """
    aggregator = ProjectionsAggregator(db)
    cache_manager = ProjectionsCacheManager(db, aggregator)
    
    return cache_manager.get_cache_stats()