"""
Ranking retrieval API endpoints.
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func

from ..core.database import get_db
from ..models.ranking import Ranking

router = APIRouter(prefix="/rankings", tags=["rankings"])


@router.get("/ecr")
async def get_expert_consensus_ranking(
    player_id: List[str] = Query(..., description="Repeatable player_id query parameter"),
    season: int = Query(2025, description="Season year"),
    include_sources: bool = Query(True, description="Include per-source ranks"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get consensus expert ranking for one or more players.

    Returns a mapping of player_id -> { consensus_rank, sources: [{source, ranking_type, overall_rank}] }
    """
    # Fetch all expert/consensus rankings for requested players
    q = (
        db.query(Ranking)
        .filter(
            Ranking.season == season,
            Ranking.player_id.in_(player_id),
            Ranking.is_active == True,
            Ranking.ranking_type.in_(["expert", "consensus"])  # consider both
        )
    )
    rows: List[Ranking] = q.all()

    if not rows:
        # Not fatal; return empty mapping
        return {"season": season, "ecr": {}}

    # Group by player
    grouped: Dict[str, List[Ranking]] = {}
    for r in rows:
        grouped.setdefault(r.player_id, []).append(r)

    result: Dict[str, Any] = {}
    for pid, ranks in grouped.items():
        values = [r.overall_rank for r in ranks if r.overall_rank is not None]
        if values:
            consensus = float(sum(values)) / float(len(values))
        else:
            consensus = None
        payload: Dict[str, Any] = {
            "consensus_rank": consensus,
            "source_count": len(values),
        }
        if include_sources:
            payload["sources"] = [
                {
                    "source": r.source,
                    "ranking_type": r.ranking_type,
                    "overall_rank": r.overall_rank,
                    "position_rank": r.position_rank,
                    "tier": r.tier,
                }
                for r in ranks
            ]
        result[pid] = payload

    return {"season": season, "ecr": result}
