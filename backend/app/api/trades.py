"""
Trade analysis API endpoints for fantasy football trade suggestions and evaluation.
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..services.trade_analyzer import (
    TradeAnalyzer, TeamAnalysis, TradeProposal, TradeImpactAnalysis,
    PositionNeed, PositionSurplus, TradeFairness, TradeType
)
from ..models.player import PlayerPosition

router = APIRouter(prefix="/trades", tags=["trades"])


# Pydantic models for API responses
class PositionNeedResponse(BaseModel):
    position: str
    need_level: float
    current_strength: float
    replacement_level: float
    depth_score: float
    injury_risk: float
    bye_week_coverage: float


class PositionSurplusResponse(BaseModel):
    position: str
    surplus_level: float
    tradeable_players: List[str]
    surplus_value: float
    depth_quality: float


class TeamAnalysisResponse(BaseModel):
    franchise_id: str
    franchise_name: str
    needs: Dict[str, PositionNeedResponse]
    surpluses: Dict[str, PositionSurplusResponse]
    overall_strength: float
    win_probability: float
    trade_urgency: float
    metadata: dict


class TradeProposalResponse(BaseModel):
    trade_id: str
    team_a_id: str
    team_b_id: str
    team_a_gives: List[str]
    team_a_receives: List[str]
    team_b_gives: List[str]
    team_b_receives: List[str]
    trade_type: str
    fairness: str
    fairness_score: float
    acceptance_probability: float
    win_probability_impact_a: float
    win_probability_impact_b: float
    rationale: str
    metadata: dict


class TradeImpactResponse(BaseModel):
    franchise_id: str
    pre_trade_strength: Dict[str, float]
    post_trade_strength: Dict[str, float]
    position_changes: Dict[str, float]
    overall_impact: float
    win_probability_change: float
    risk_change: float
    depth_impact: Dict[str, float]
    bye_week_impact: Dict[int, float]


class TradeEvaluationRequest(BaseModel):
    team_a_id: str = Field(..., description="First team's franchise ID")
    team_b_id: str = Field(..., description="Second team's franchise ID")
    team_a_gives: List[str] = Field(..., description="Player IDs team A is trading away")
    team_b_gives: List[str] = Field(..., description="Player IDs team B is trading away")
    season: int = Field(2024, description="Season year")


@router.get("/analyze-league/{league_id}", response_model=Dict[str, TeamAnalysisResponse])
async def analyze_league_teams(
    league_id: str,
    season: int = Query(2024, description="Season year"),
    db: Session = Depends(get_db)
):
    """
    Analyze needs and surpluses for all teams in a league.
    
    Returns comprehensive analysis of each team's position strengths,
    needs, and tradeable surpluses.
    """
    try:
        trade_analyzer = TradeAnalyzer(db)
        team_analyses = trade_analyzer.analyze_all_teams(league_id, season)
        
        # Convert to response format
        response = {}
        for franchise_id, analysis in team_analyses.items():
            needs_response = {}
            for position, need in analysis.needs.items():
                needs_response[position.value] = PositionNeedResponse(
                    position=position.value,
                    need_level=need.need_level,
                    current_strength=float(need.current_strength),
                    replacement_level=float(need.replacement_level),
                    depth_score=need.depth_score,
                    injury_risk=need.injury_risk,
                    bye_week_coverage=need.bye_week_coverage
                )
            
            surpluses_response = {}
            for position, surplus in analysis.surpluses.items():
                surpluses_response[position.value] = PositionSurplusResponse(
                    position=position.value,
                    surplus_level=surplus.surplus_level,
                    tradeable_players=surplus.tradeable_players,
                    surplus_value=float(surplus.surplus_value),
                    depth_quality=surplus.depth_quality
                )
            
            response[franchise_id] = TeamAnalysisResponse(
                franchise_id=analysis.franchise_id,
                franchise_name=analysis.franchise_name,
                needs=needs_response,
                surpluses=surpluses_response,
                overall_strength=float(analysis.overall_strength),
                win_probability=analysis.win_probability,
                trade_urgency=analysis.trade_urgency,
                metadata=analysis.metadata
            )
        
        return response
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing league teams: {str(e)}")


@router.get("/suggestions/{franchise_id}", response_model=List[TradeProposalResponse])
async def get_trade_suggestions(
    franchise_id: str,
    league_id: str = Query(..., description="League ID"),
    season: int = Query(2024, description="Season year"),
    max_suggestions: int = Query(10, description="Maximum number of suggestions"),
    db: Session = Depends(get_db)
):
    """
    Generate Pareto-optimal trade suggestions for a specific team.
    
    Returns trade proposals that would benefit both teams involved,
    ordered by acceptance probability and fairness.
    """
    try:
        trade_analyzer = TradeAnalyzer(db)
        proposals = trade_analyzer.suggest_trades(
            league_id, franchise_id, season, max_suggestions
        )
        
        # Convert to response format
        response = []
        for proposal in proposals:
            response.append(TradeProposalResponse(
                trade_id=proposal.trade_id,
                team_a_id=proposal.team_a_id,
                team_b_id=proposal.team_b_id,
                team_a_gives=proposal.team_a_gives,
                team_a_receives=proposal.team_a_receives,
                team_b_gives=proposal.team_b_gives,
                team_b_receives=proposal.team_b_receives,
                trade_type=proposal.trade_type.value,
                fairness=proposal.fairness.value,
                fairness_score=proposal.fairness_score,
                acceptance_probability=proposal.acceptance_probability,
                win_probability_impact_a=proposal.win_probability_impact_a,
                win_probability_impact_b=proposal.win_probability_impact_b,
                rationale=proposal.rationale,
                metadata=proposal.metadata
            ))
        
        return response
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating trade suggestions: {str(e)}")


@router.post("/evaluate", response_model=TradeProposalResponse)
async def evaluate_trade(
    trade_request: TradeEvaluationRequest,
    db: Session = Depends(get_db)
):
    """
    Evaluate a specific trade proposal between two teams.
    
    Analyzes trade fairness, win probability impacts, and acceptance likelihood.
    """
    try:
        trade_analyzer = TradeAnalyzer(db)
        proposal = trade_analyzer.evaluate_trade(
            team_a_id=trade_request.team_a_id,
            team_b_id=trade_request.team_b_id,
            team_a_gives=trade_request.team_a_gives,
            team_b_gives=trade_request.team_b_gives,
            season=trade_request.season
        )
        
        return TradeProposalResponse(
            trade_id=proposal.trade_id,
            team_a_id=proposal.team_a_id,
            team_b_id=proposal.team_b_id,
            team_a_gives=proposal.team_a_gives,
            team_a_receives=proposal.team_a_receives,
            team_b_gives=proposal.team_b_gives,
            team_b_receives=proposal.team_b_receives,
            trade_type=proposal.trade_type.value,
            fairness=proposal.fairness.value,
            fairness_score=proposal.fairness_score,
            acceptance_probability=proposal.acceptance_probability,
            win_probability_impact_a=proposal.win_probability_impact_a,
            win_probability_impact_b=proposal.win_probability_impact_b,
            rationale=proposal.rationale,
            metadata=proposal.metadata
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error evaluating trade: {str(e)}")


@router.get("/impact/{franchise_id}")
async def get_trade_impact(
    franchise_id: str,
    players_out: List[str] = Query(..., description="Player IDs being traded away"),
    players_in: List[str] = Query(..., description="Player IDs being acquired"),
    season: int = Query(2024, description="Season year"),
    db: Session = Depends(get_db)
):
    """
    Analyze the impact of a potential trade on a specific team.
    
    Returns detailed breakdown of how the trade would affect team strength,
    win probability, and position depth.
    """
    try:
        trade_analyzer = TradeAnalyzer(db)
        
        # Get team analysis
        team_analysis = trade_analyzer._analyze_single_team_by_id(franchise_id, season)
        
        # Calculate trade impact
        impact = trade_analyzer._calculate_trade_impact(
            team_analysis, players_out, players_in, season
        )
        
        return TradeImpactResponse(
            franchise_id=impact.franchise_id,
            pre_trade_strength={pos.value: float(strength) for pos, strength in impact.pre_trade_strength.items()},
            post_trade_strength={pos.value: float(strength) for pos, strength in impact.post_trade_strength.items()},
            position_changes={pos.value: float(change) for pos, change in impact.position_changes.items()},
            overall_impact=float(impact.overall_impact),
            win_probability_change=impact.win_probability_change,
            risk_change=impact.risk_change,
            depth_impact=impact.depth_impact,
            bye_week_impact=impact.bye_week_impact
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating trade impact: {str(e)}")


@router.get("/team-analysis/{franchise_id}", response_model=TeamAnalysisResponse)
async def get_team_analysis(
    franchise_id: str,
    season: int = Query(2024, description="Season year"),
    db: Session = Depends(get_db)
):
    """
    Get detailed analysis of a single team's needs and surpluses.
    
    Returns comprehensive breakdown of team strengths, weaknesses,
    and trading opportunities.
    """
    try:
        trade_analyzer = TradeAnalyzer(db)
        analysis = trade_analyzer._analyze_single_team_by_id(franchise_id, season)
        
        # Convert needs to response format
        needs_response = {}
        for position, need in analysis.needs.items():
            needs_response[position.value] = PositionNeedResponse(
                position=position.value,
                need_level=need.need_level,
                current_strength=float(need.current_strength),
                replacement_level=float(need.replacement_level),
                depth_score=need.depth_score,
                injury_risk=need.injury_risk,
                bye_week_coverage=need.bye_week_coverage
            )
        
        # Convert surpluses to response format
        surpluses_response = {}
        for position, surplus in analysis.surpluses.items():
            surpluses_response[position.value] = PositionSurplusResponse(
                position=position.value,
                surplus_level=surplus.surplus_level,
                tradeable_players=surplus.tradeable_players,
                surplus_value=float(surplus.surplus_value),
                depth_quality=surplus.depth_quality
            )
        
        return TeamAnalysisResponse(
            franchise_id=analysis.franchise_id,
            franchise_name=analysis.franchise_name,
            needs=needs_response,
            surpluses=surpluses_response,
            overall_strength=float(analysis.overall_strength),
            win_probability=analysis.win_probability,
            trade_urgency=analysis.trade_urgency,
            metadata=analysis.metadata
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing team: {str(e)}")