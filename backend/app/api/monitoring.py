"""
API endpoints for monitoring, health checks, and metrics.
"""
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from datetime import datetime, timedelta

from ..core.health import health_manager, HealthStatus
from ..core.metrics import metrics_registry, MetricSummary
from ..core.logging import get_logger

router = APIRouter(prefix="/monitoring", tags=["monitoring"])
logger = get_logger(__name__)


# Pydantic models for responses
class HealthCheckResponse(BaseModel):
    status: str
    timestamp: str
    duration_ms: float
    checks: Dict[str, Dict[str, Any]]


class MetricsResponse(BaseModel):
    timestamp: str
    metrics: Dict[str, Dict[str, Any]]


class SystemInfoResponse(BaseModel):
    service: str
    version: str
    environment: str
    uptime_seconds: float
    timestamp: str


# Track service start time for uptime calculation
service_start_time = datetime.utcnow()


@router.get("/health", response_model=HealthCheckResponse)
async def get_health_status():
    """
    Get comprehensive health status of all system components.
    
    Returns health status for:
    - Database connectivity
    - Redis cache
    - Celery workers
    - Disk space
    - Memory usage
    """
    try:
        health_result = await health_manager.run_all_checks()
        return HealthCheckResponse(**health_result)
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@router.get("/health/{component}")
async def get_component_health(component: str):
    """Get health status for a specific component."""
    try:
        result = await health_manager.run_check(component)
        if result is None:
            raise HTTPException(status_code=404, detail=f"Health checker '{component}' not found")
        
        return {
            "name": result.name,
            "status": result.status.value,
            "message": result.message,
            "duration_ms": result.duration_ms,
            "timestamp": result.timestamp.isoformat() + "Z",
            "details": result.details
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Component health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Component health check failed: {str(e)}")


@router.get("/metrics", response_model=MetricsResponse)
async def get_metrics():
    """
    Get current metrics snapshot.
    
    Returns metrics for:
    - HTTP requests and errors
    - Database queries and performance
    - Background job statistics
    - Business metrics (recommendations, leagues, etc.)
    """
    try:
        snapshot = metrics_registry.get_metrics_snapshot()
        return MetricsResponse(**snapshot)
    except Exception as e:
        logger.error(f"Metrics collection failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Metrics collection failed: {str(e)}")


@router.get("/metrics/{metric_name}")
async def get_metric_details(metric_name: str):
    """Get detailed information about a specific metric."""
    try:
        metric = metrics_registry.get_metric(metric_name)
        if metric is None:
            raise HTTPException(status_code=404, detail=f"Metric '{metric_name}' not found")
        
        summary = metric.get_summary()
        
        return {
            "name": summary.name,
            "type": summary.type.value,
            "description": metric.description,
            "current_value": summary.current_value,
            "total_samples": summary.total_samples,
            "min_value": summary.min_value,
            "max_value": summary.max_value,
            "avg_value": summary.avg_value,
            "last_updated": summary.last_updated.isoformat() + "Z" if summary.last_updated else None
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Metric details failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Metric details failed: {str(e)}")


@router.get("/metrics/{metric_name}/history")
async def get_metric_history(
    metric_name: str,
    hours: int = Query(default=1, ge=1, le=24, description="Hours of history to retrieve")
):
    """Get historical values for a specific metric."""
    try:
        metric = metrics_registry.get_metric(metric_name)
        if metric is None:
            raise HTTPException(status_code=404, detail=f"Metric '{metric_name}' not found")
        
        since = datetime.utcnow() - timedelta(hours=hours)
        values = metric.get_values_since(since)
        
        return {
            "metric_name": metric_name,
            "since": since.isoformat() + "Z",
            "values": [
                {
                    "value": v.value,
                    "timestamp": v.timestamp.isoformat() + "Z",
                    "labels": v.labels
                }
                for v in values
            ]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Metric history failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Metric history failed: {str(e)}")


@router.get("/info", response_model=SystemInfoResponse)
async def get_system_info():
    """Get basic system information."""
    try:
        uptime = (datetime.utcnow() - service_start_time).total_seconds()
        
        return SystemInfoResponse(
            service="ai-fantasy-assistant",
            version="1.0.0",
            environment="development",  # Would come from config
            uptime_seconds=uptime,
            timestamp=datetime.utcnow().isoformat() + "Z"
        )
    except Exception as e:
        logger.error(f"System info failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"System info failed: {str(e)}")


@router.get("/status")
async def get_status_summary():
    """Get a quick status summary for monitoring dashboards."""
    try:
        # Run health checks
        health_result = await health_manager.run_all_checks()
        
        # Get key metrics
        metrics_snapshot = metrics_registry.get_metrics_snapshot()
        
        # Calculate uptime
        uptime = (datetime.utcnow() - service_start_time).total_seconds()
        
        # Count healthy vs unhealthy checks
        healthy_checks = sum(1 for check in health_result["checks"].values() 
                           if check["status"] == "healthy")
        total_checks = len(health_result["checks"])
        
        return {
            "status": health_result["status"],
            "uptime_seconds": uptime,
            "health_checks": {
                "healthy": healthy_checks,
                "total": total_checks,
                "success_rate": (healthy_checks / total_checks * 100) if total_checks > 0 else 0
            },
            "metrics_count": len(metrics_snapshot["metrics"]),
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
    except Exception as e:
        logger.error(f"Status summary failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Status summary failed: {str(e)}")


@router.get("/alerts")
async def get_active_alerts():
    """Get list of active system alerts."""
    try:
        alerts = []
        
        # Run health checks to identify issues
        health_result = await health_manager.run_all_checks()
        
        for check_name, check_result in health_result["checks"].items():
            if check_result["status"] in ["unhealthy", "degraded"]:
                alerts.append({
                    "type": "health_check",
                    "severity": "critical" if check_result["status"] == "unhealthy" else "warning",
                    "component": check_name,
                    "message": check_result["message"],
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                    "details": check_result.get("details", {})
                })
        
        # Check for metric-based alerts
        metrics_snapshot = metrics_registry.get_metrics_snapshot()
        
        # Example: Alert on high error rates
        error_counter = metrics_registry.get_counter("http_requests_errors")
        total_counter = metrics_registry.get_counter("http_requests_total")
        
        if error_counter and total_counter:
            error_rate = (error_counter.get_current_value() / 
                         max(total_counter.get_current_value(), 1)) * 100
            
            if error_rate > 5:  # Alert if error rate > 5%
                alerts.append({
                    "type": "metric",
                    "severity": "warning" if error_rate < 10 else "critical",
                    "component": "http_errors",
                    "message": f"High HTTP error rate: {error_rate:.1f}%",
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                    "details": {"error_rate": error_rate}
                })
        
        return {
            "alerts": alerts,
            "total_alerts": len(alerts),
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        
    except Exception as e:
        logger.error(f"Alerts retrieval failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Alerts retrieval failed: {str(e)}")


@router.post("/test-alert")
async def create_test_alert():
    """Create a test alert for monitoring system validation."""
    try:
        logger.warning("Test alert created via API", alert_type="test", severity="warning")
        
        return {
            "message": "Test alert created successfully",
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
    except Exception as e:
        logger.error(f"Test alert creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Test alert creation failed: {str(e)}")


@router.get("/dashboard")
async def get_monitoring_dashboard():
    """Get data for a monitoring dashboard."""
    try:
        # Get health status
        health_result = await health_manager.run_all_checks()
        
        # Get metrics
        metrics_snapshot = metrics_registry.get_metrics_snapshot()
        
        # Get system info
        uptime = (datetime.utcnow() - service_start_time).total_seconds()
        
        # Get active alerts
        alerts_response = await get_active_alerts()
        
        return {
            "system": {
                "status": health_result["status"],
                "uptime_seconds": uptime,
                "timestamp": datetime.utcnow().isoformat() + "Z"
            },
            "health": health_result,
            "metrics": {
                "summary": {
                    "total_metrics": len(metrics_snapshot["metrics"]),
                    "http_requests": metrics_snapshot["metrics"].get("http_requests_total", {}).get("current_value", 0),
                    "active_tasks": metrics_snapshot["metrics"].get("celery_active_tasks", {}).get("current_value", 0),
                    "active_leagues": metrics_snapshot["metrics"].get("active_leagues", {}).get("current_value", 0)
                },
                "details": metrics_snapshot["metrics"]
            },
            "alerts": alerts_response["alerts"]
        }
        
    except Exception as e:
        logger.error(f"Dashboard data failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Dashboard data failed: {str(e)}")