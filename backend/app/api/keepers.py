"""
API endpoints for keeper optimization and management.
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from ..core.database import get_db
from ..services.keeper_optimizer import <PERSON><PERSON><PERSON><PERSON><PERSON>, KeeperScenario, KeeperCandidate
from ..models.roster import Franchise

router = APIRouter(prefix="/keepers", tags=["keepers"])


class KeeperCandidateResponse(BaseModel):
    """Response model for keeper candidates."""
    player_id: str
    player_name: str
    position: str
    current_cost: int
    projected_points: float
    replacement_level: float
    value_over_replacement: float
    keeper_cost: int
    is_eligible: bool
    constraints_violated: List[str]
    metadata: dict


class KeeperAlternativeResponse(BaseModel):
    """Response model for keeper alternatives."""
    player_id: str
    player_name: str
    position: str
    keeper_cost: int
    value_over_replacement: float
    reason: str


class KeeperRecommendationResponse(BaseModel):
    """Response model for keeper recommendations."""
    player_id: str
    player_name: str
    position: str
    keeper_cost: int
    projected_points: float
    value_over_replacement: float
    confidence: float
    rationale: str
    alternatives: List[KeeperAlternativeResponse]
    metadata: dict


class KeeperScenarioResponse(BaseModel):
    """Response model for keeper scenarios."""
    scenario_name: str
    selected_keepers: List[KeeperRecommendationResponse]
    total_value: float
    remaining_budget: Optional[float]
    constraints_satisfied: bool
    trade_offs: List[str]
    metadata: dict


class ReplacementLevelsResponse(BaseModel):
    """Response model for replacement levels."""
    league_id: str
    season: int
    replacement_levels: dict
    calculation_timestamp: str


@router.get("/candidates/{franchise_id}", response_model=List[KeeperCandidateResponse])
async def get_keeper_candidates(
    franchise_id: str,
    season: int = Query(2024, description="Season year"),
    db: Session = Depends(get_db)
):
    """
    Get all potential keeper candidates for a franchise.
    
    Args:
        franchise_id: Franchise identifier
        season: Season year
        db: Database session
        
    Returns:
        List of keeper candidates with value analysis
    """
    # Verify franchise exists
    franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
    if not franchise:
        raise HTTPException(status_code=404, detail=f"Franchise {franchise_id} not found")
    
    try:
        optimizer = KeeperOptimizer(db)
        candidates = optimizer.get_keeper_candidates(franchise_id, season)
        
        # Convert to response models
        response_candidates = []
        for candidate in candidates:
            response_candidate = KeeperCandidateResponse(
                player_id=candidate.player_id,
                player_name=candidate.player_name,
                position=candidate.position.value,
                current_cost=candidate.current_cost,
                projected_points=float(candidate.projected_points),
                replacement_level=float(candidate.replacement_level),
                value_over_replacement=float(candidate.value_over_replacement),
                keeper_cost=candidate.keeper_cost,
                is_eligible=candidate.is_eligible,
                constraints_violated=candidate.constraints_violated,
                metadata=candidate.metadata
            )
            response_candidates.append(response_candidate)
        
        return response_candidates
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting keeper candidates: {str(e)}")


@router.get("/optimize/{franchise_id}", response_model=List[KeeperScenarioResponse])
async def optimize_keepers(
    franchise_id: str,
    season: int = Query(2024, description="Season year"),
    max_scenarios: int = Query(5, description="Maximum number of scenarios to generate", ge=1, le=10),
    db: Session = Depends(get_db)
):
    """
    Optimize keeper selections for a franchise using integer linear programming.
    
    Args:
        franchise_id: Franchise identifier
        season: Season year
        max_scenarios: Maximum number of scenarios to generate
        db: Database session
        
    Returns:
        List of optimized keeper scenarios
    """
    # Verify franchise exists
    franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
    if not franchise:
        raise HTTPException(status_code=404, detail=f"Franchise {franchise_id} not found")
    
    if not franchise.league.keeper_rules:
        raise HTTPException(
            status_code=400, 
            detail=f"League {franchise.league.id} has no keeper rules configured"
        )
    
    try:
        optimizer = KeeperOptimizer(db)
        scenarios = optimizer.optimize_keepers(franchise_id, season, max_scenarios)
        
        # Convert to response models
        response_scenarios = []
        for scenario in scenarios:
            # Convert keeper recommendations
            keeper_responses = []
            for keeper in scenario.selected_keepers:
                # Convert alternatives
                alternative_responses = [
                    KeeperAlternativeResponse(
                        player_id=alt.player_id,
                        player_name=alt.player_name,
                        position=alt.position.value,
                        keeper_cost=alt.keeper_cost,
                        value_over_replacement=float(alt.value_over_replacement),
                        reason=alt.reason
                    )
                    for alt in keeper.alternatives
                ]
                
                keeper_response = KeeperRecommendationResponse(
                    player_id=keeper.player_id,
                    player_name=keeper.player_name,
                    position=keeper.position.value,
                    keeper_cost=keeper.keeper_cost,
                    projected_points=float(keeper.projected_points),
                    value_over_replacement=float(keeper.value_over_replacement),
                    confidence=keeper.confidence,
                    rationale=keeper.rationale,
                    alternatives=alternative_responses,
                    metadata=keeper.metadata
                )
                keeper_responses.append(keeper_response)
            
            scenario_response = KeeperScenarioResponse(
                scenario_name=scenario.scenario_name,
                selected_keepers=keeper_responses,
                total_value=float(scenario.total_value),
                remaining_budget=float(scenario.remaining_budget) if scenario.remaining_budget else None,
                constraints_satisfied=scenario.constraints_satisfied,
                trade_offs=scenario.trade_offs,
                metadata=scenario.metadata
            )
            response_scenarios.append(scenario_response)
        
        return response_scenarios
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error optimizing keepers: {str(e)}")


@router.get("/replacement-levels/{league_id}", response_model=ReplacementLevelsResponse)
async def get_replacement_levels(
    league_id: str,
    season: int = Query(2024, description="Season year"),
    db: Session = Depends(get_db)
):
    """
    Get replacement level baselines for all positions in a league.
    
    Args:
        league_id: League identifier
        season: Season year
        db: Database session
        
    Returns:
        Replacement levels for each position
    """
    try:
        optimizer = KeeperOptimizer(db)
        replacement_levels = optimizer.calculate_replacement_levels(league_id, season)
        
        # Convert Decimal values to float for JSON serialization
        levels_dict = {
            position.value: float(level) 
            for position, level in replacement_levels.items()
        }
        
        return ReplacementLevelsResponse(
            league_id=league_id,
            season=season,
            replacement_levels=levels_dict,
            calculation_timestamp=str(datetime.utcnow())
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating replacement levels: {str(e)}")


@router.post("/refresh-cache")
async def refresh_keeper_cache(
    db: Session = Depends(get_db)
):
    """
    Refresh the keeper optimization cache (replacement levels, projections).
    
    Args:
        db: Database session
        
    Returns:
        Success message
    """
    try:
        optimizer = KeeperOptimizer(db)
        optimizer.refresh_replacement_levels_cache()
        optimizer.projections_aggregator.refresh_projections_cache()
        
        return {"message": "Keeper optimization cache refreshed successfully"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error refreshing cache: {str(e)}")


