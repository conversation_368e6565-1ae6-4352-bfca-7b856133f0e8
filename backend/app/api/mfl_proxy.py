"""
Backend MFL proxy endpoints.

Provides a simple pass-through to the MFL export API so the frontend can call
our backend instead of hitting MFL directly.
"""
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Query

from app.services.mfl_client import MFLClient

router = APIRouter(prefix="/mfl", tags=["MFL Proxy"])


@router.get("/export")
async def mfl_export(
    TYPE: str = Query(..., description="MFL export TYPE parameter, e.g., 'league', 'players', 'rosters'"),
    L: str | None = Query(None, description="League ID"),
    YEAR: int | None = Query(None, description="Season year, defaults to current"),
    JSON: int | None = Query(1, description="Force JSON output if supported"),
    # Catch-all extra query string parameters (FastAPI doesn't support arbitrary kwargs directly),
    # but we can accept a Request and read query_params; to stay simple, list a few common ones:
    W: int | None = Query(None, description="Week"),
    FRANCHISE: str | None = Query(None, description="Franchise id for filtered endpoints"),
    SEARCH: str | None = Query(None, description="Search term for league search"),
    POSITION: str | None = Query(None, description="Position filter"),
    DAYS: int | None = Query(None, description="Transactions days window"),
    APIKEY: str | None = Query(None, description="Override API key"),
) -> Dict[str, Any]:
    try:
        params: Dict[str, Any] = {
            "TYPE": TYPE,
        }
        # Only include non-None params
        for k, v in {
            "L": L,
            "YEAR": YEAR,
            "JSON": JSON,
            "W": W,
            "FRANCHISE": FRANCHISE,
            "SEARCH": SEARCH,
            "POSITION": POSITION,
            "DAYS": DAYS,
            "APIKEY": APIKEY,
        }.items():
            if v is not None:
                params[k] = v

        client = MFLClient(year=YEAR)
        return await client.export(params)
    except Exception as e:
        raise HTTPException(status_code=502, detail=f"MFL proxy failed: {e}")
