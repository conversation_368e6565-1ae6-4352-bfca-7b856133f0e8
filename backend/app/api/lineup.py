"""
API endpoints for lineup optimization and management.
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from ..core.database import get_db
from ..services.lineup_optimizer import LineupOptimizer, PlayerNews, LineupRecommendation
from ..models.recommendation import Recommendation, RecommendationType, RecommendationPriority

router = APIRouter(prefix="/lineup", tags=["lineup"])


class LineupOptimizationRequest(BaseModel):
    """Request model for lineup optimization."""
    franchise_id: str = Field(..., description="ID of the franchise to optimize")
    week: int = Field(..., ge=1, le=18, description="Week number to optimize for")
    season: int = Field(default=2024, description="Season year")
    opponent_projection: Optional[float] = Field(None, description="Expected opponent score")


class StartSitRequest(BaseModel):
    """Request model for start/sit recommendations."""
    franchise_id: str = Field(..., description="ID of the franchise")
    week: int = Field(..., ge=1, le=18, description="Week number")
    season: int = Field(default=2024, description="Season year")


class NewsItem(BaseModel):
    """Model for player news items."""
    player_id: str = Field(..., description="ID of the affected player")
    news_type: str = Field(..., description="Type of news (injury, inactive, etc.)")
    severity: str = Field(..., description="Severity level (low, medium, high)")
    description: str = Field(..., description="News description")
    impact_on_projection: Optional[float] = Field(None, description="Percentage impact on projection")


class NewsAdjustmentRequest(BaseModel):
    """Request model for news-based lineup adjustments."""
    franchise_id: str = Field(..., description="ID of the franchise")
    week: int = Field(..., ge=1, le=18, description="Week number")
    news_items: List[NewsItem] = Field(..., description="List of news items to process")


class LineupLockRequest(BaseModel):
    """Request model for lineup lock checks."""
    franchise_id: str = Field(..., description="ID of the franchise")
    week: int = Field(..., ge=1, le=18, description="Week number")


class LineupOptimizationResponse(BaseModel):
    """Response model for lineup optimization."""
    lineup: Dict[str, str] = Field(..., description="Optimized lineup (slot -> player_id)")
    projected_points: float = Field(..., description="Total projected points")
    win_probability: float = Field(..., description="Estimated win probability")
    confidence: float = Field(..., description="Confidence level (0-1)")
    rationale: str = Field(..., description="Explanation of the lineup choices")
    alternatives: List[Dict[str, Any]] = Field(..., description="Alternative lineup options")
    risk_level: float = Field(..., description="Risk level (0-1)")


class StartSitRecommendation(BaseModel):
    """Model for start/sit recommendations."""
    type: str = Field(..., description="Recommendation type")
    slot: str = Field(..., description="Lineup slot")
    start_player_id: str = Field(..., description="Player to start")
    sit_player_id: Optional[str] = Field(None, description="Player to sit")
    projected_improvement: float = Field(..., description="Expected point improvement")
    confidence: float = Field(..., description="Confidence level")
    rationale: str = Field(..., description="Reasoning for the recommendation")


class LineupLockAlert(BaseModel):
    """Model for lineup lock alerts."""
    type: str = Field(..., description="Alert type")
    player_id: str = Field(..., description="Player ID")
    player_name: str = Field(..., description="Player name")
    slot: str = Field(..., description="Lineup slot")
    lock_time: datetime = Field(..., description="Lock time")
    time_until_lock: str = Field(..., description="Time remaining until lock")
    urgency: str = Field(..., description="Urgency level")


class NewsAdjustment(BaseModel):
    """Model for news-based lineup adjustments."""
    type: str = Field(..., description="Adjustment type")
    slot: str = Field(..., description="Lineup slot")
    old_player_id: str = Field(..., description="Current player")
    new_player_id: str = Field(..., description="Recommended replacement")
    news_items: List[str] = Field(..., description="Relevant news descriptions")
    projected_improvement: float = Field(..., description="Expected improvement")
    urgency: str = Field(..., description="Urgency level")


@router.post("/optimize", response_model=LineupOptimizationResponse)
async def optimize_lineup(
    request: LineupOptimizationRequest,
    db: Session = Depends(get_db)
) -> LineupOptimizationResponse:
    """
    Generate optimal lineup recommendation for a franchise.
    
    Uses win probability maximization with player variance, matchup context,
    and injury status to recommend the best possible lineup.
    """
    try:
        optimizer = LineupOptimizer(db)
        
        recommendation = optimizer.optimize_lineup(
            franchise_id=request.franchise_id,
            week=request.week,
            season=request.season,
            opponent_projection=request.opponent_projection
        )
        
        # Convert LineupSlot enum keys to strings for JSON serialization
        lineup_dict = {slot.value: player_id for slot, player_id in recommendation.lineup.items()}
        
        return LineupOptimizationResponse(
            lineup=lineup_dict,
            projected_points=recommendation.projected_points,
            win_probability=recommendation.win_probability,
            confidence=recommendation.confidence,
            rationale=recommendation.rationale,
            alternatives=recommendation.alternatives,
            risk_level=recommendation.risk_level
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lineup optimization failed: {str(e)}")


@router.post("/start-sit", response_model=List[StartSitRecommendation])
async def get_start_sit_recommendations(
    request: StartSitRequest,
    db: Session = Depends(get_db)
) -> List[StartSitRecommendation]:
    """
    Get start/sit recommendations for borderline players.
    
    Compares optimal lineup with current lineup and suggests changes
    that would improve win probability.
    """
    try:
        optimizer = LineupOptimizer(db)
        
        recommendations = optimizer.get_start_sit_recommendations(
            franchise_id=request.franchise_id,
            week=request.week,
            season=request.season
        )
        
        return [
            StartSitRecommendation(
                type=rec["type"],
                slot=rec["slot"],
                start_player_id=rec["start_player_id"],
                sit_player_id=rec.get("sit_player_id"),
                projected_improvement=rec["projected_improvement"],
                confidence=rec["confidence"],
                rationale=rec["rationale"]
            )
            for rec in recommendations
        ]
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Start/sit analysis failed: {str(e)}")


@router.post("/lock-alerts", response_model=List[LineupLockAlert])
async def check_lineup_locks(
    request: LineupLockRequest,
    db: Session = Depends(get_db)
) -> List[LineupLockAlert]:
    """
    Check for upcoming lineup lock times and generate alerts.
    
    Returns alerts for players whose games are locking soon,
    allowing users to make last-minute lineup changes.
    """
    try:
        optimizer = LineupOptimizer(db)
        
        alerts = optimizer.check_lineup_locks(
            franchise_id=request.franchise_id,
            week=request.week
        )
        
        return [
            LineupLockAlert(
                type=alert["type"],
                player_id=alert["player_id"],
                player_name=alert["player_name"],
                slot=alert["slot"],
                lock_time=alert["lock_time"],
                time_until_lock=str(alert["time_until_lock"]),
                urgency=alert["urgency"]
            )
            for alert in alerts
        ]
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lock time check failed: {str(e)}")


@router.post("/news-adjustments", response_model=List[NewsAdjustment])
async def apply_news_adjustments(
    request: NewsAdjustmentRequest,
    db: Session = Depends(get_db)
) -> List[NewsAdjustment]:
    """
    Apply late-breaking news to lineup recommendations.
    
    Processes player news (injuries, inactives, role changes) and suggests
    lineup adjustments based on the updated information.
    """
    try:
        optimizer = LineupOptimizer(db)
        
        # Convert request news items to PlayerNews objects
        news_items = [
            PlayerNews(
                player_id=item.player_id,
                news_type=item.news_type,
                severity=item.severity,
                description=item.description,
                timestamp=datetime.now(),
                impact_on_projection=item.impact_on_projection
            )
            for item in request.news_items
        ]
        
        adjustments = optimizer.apply_late_breaking_news(
            franchise_id=request.franchise_id,
            week=request.week,
            news_items=news_items
        )
        
        return [
            NewsAdjustment(
                type=adj["type"],
                slot=adj["slot"],
                old_player_id=adj["old_player_id"],
                new_player_id=adj["new_player_id"],
                news_items=adj["news_items"],
                projected_improvement=adj["projected_improvement"],
                urgency=adj["urgency"]
            )
            for adj in adjustments
        ]
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"News adjustment failed: {str(e)}")


@router.get("/recommendations/{franchise_id}")
async def get_lineup_recommendations(
    franchise_id: str,
    week: int = Query(..., ge=1, le=18, description="Week number"),
    season: int = Query(default=2024, description="Season year"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get comprehensive lineup recommendations for a franchise.
    
    Returns optimization, start/sit recommendations, lock alerts,
    and any relevant news adjustments in a single response.
    """
    try:
        optimizer = LineupOptimizer(db)
        
        # Get optimal lineup
        optimization = optimizer.optimize_lineup(
            franchise_id=franchise_id,
            week=week,
            season=season
        )
        
        # Get start/sit recommendations
        start_sit = optimizer.get_start_sit_recommendations(
            franchise_id=franchise_id,
            week=week,
            season=season
        )
        
        # Get lock alerts
        lock_alerts = optimizer.check_lineup_locks(
            franchise_id=franchise_id,
            week=week
        )
        
        return {
            "optimization": {
                "lineup": {slot.value: player_id for slot, player_id in optimization.lineup.items()},
                "projected_points": optimization.projected_points,
                "win_probability": optimization.win_probability,
                "confidence": optimization.confidence,
                "rationale": optimization.rationale,
                "alternatives": optimization.alternatives,
                "risk_level": optimization.risk_level
            },
            "start_sit_recommendations": start_sit,
            "lock_alerts": lock_alerts,
            "generated_at": datetime.now().isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lineup recommendations failed: {str(e)}")


@router.post("/save-recommendation")
async def save_lineup_recommendation(
    franchise_id: str,
    week: int,
    season: int = 2024,
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """
    Save lineup recommendation to the database.
    
    Creates a Recommendation record for the optimized lineup
    that can be tracked and referenced later.
    """
    try:
        optimizer = LineupOptimizer(db)
        
        # Generate optimization
        optimization = optimizer.optimize_lineup(
            franchise_id=franchise_id,
            week=week,
            season=season
        )
        
        # Get franchise to get league_id
        from ..models.roster import Franchise
        franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
        if not franchise:
            raise HTTPException(status_code=404, detail="Franchise not found")
        
        # Create recommendation record
        recommendation = Recommendation(
            id=f"lineup_{franchise_id}_week_{week}_{season}",
            league_id=franchise.league_id,
            franchise_id=franchise_id,
            type=RecommendationType.LINEUP,
            priority=RecommendationPriority.MEDIUM,
            title=f"Week {week} Lineup Optimization",
            description=f"Optimal lineup for week {week} with {optimization.projected_points:.1f} projected points",
            rationale=optimization.rationale,
            confidence=optimization.confidence,
            expected_impact=optimization.projected_points,
            risk_level=optimization.risk_level,
            recommendation_data={
                "lineup": {slot.value: player_id for slot, player_id in optimization.lineup.items()},
                "week": week,
                "season": season,
                "win_probability": optimization.win_probability,
                "alternatives": optimization.alternatives
            }
        )
        
        db.add(recommendation)
        db.commit()
        
        return {"message": "Lineup recommendation saved successfully", "recommendation_id": recommendation.id}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to save recommendation: {str(e)}")