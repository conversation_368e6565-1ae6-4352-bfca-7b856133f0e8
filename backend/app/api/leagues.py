"""
League management API endpoints.

Provides endpoints for creating, updating, and managing fantasy football leagues.
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from datetime import datetime

from ..core.database import get_db
from ..models.league import League
from ..models.roster import Franchise
from ..services.rules_engine import RulesEngine

router = APIRouter(prefix="/leagues", tags=["leagues"])


# Pydantic models for API requests/responses
class LeagueCreateRequest(BaseModel):
    """Request model for creating a new league."""
    id: str = Field(..., description="Unique league identifier")
    name: str = Field(..., description="League name")
    season: int = Field(..., description="Season year", ge=2020, le=2030)
    scoring_rules: Dict[str, Any] = Field(default_factory=dict, description="League scoring rules")
    roster_slots: List[Dict[str, Any]] = Field(default_factory=list, description="Roster slot configuration")
    keeper_rules: Optional[Dict[str, Any]] = Field(None, description="Keeper rules configuration")
    mfl_league_id: Optional[str] = Field(None, description="MFL league ID for integration")
    mfl_api_key: Optional[str] = Field(None, description="MFL API key")
    description: Optional[str] = Field(None, description="League description")


class LeagueUpdateRequest(BaseModel):
    """Request model for updating a league."""
    name: Optional[str] = Field(None, description="League name")
    scoring_rules: Optional[Dict[str, Any]] = Field(None, description="League scoring rules")
    roster_slots: Optional[List[Dict[str, Any]]] = Field(None, description="Roster slot configuration")
    keeper_rules: Optional[Dict[str, Any]] = Field(None, description="Keeper rules configuration")
    mfl_league_id: Optional[str] = Field(None, description="MFL league ID for integration")
    mfl_api_key: Optional[str] = Field(None, description="MFL API key")
    description: Optional[str] = Field(None, description="League description")
    is_active: Optional[bool] = Field(None, description="League active status")


class LeagueResponse(BaseModel):
    """Response model for league information."""
    id: str
    name: str
    season: int
    scoring_rules: Dict[str, Any]
    roster_slots: List[Dict[str, Any]]
    keeper_rules: Optional[Dict[str, Any]]
    mfl_league_id: Optional[str]
    description: Optional[str]
    is_active: bool
    franchise_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class LeagueListResponse(BaseModel):
    """Response model for league list."""
    id: str
    name: str
    season: int
    franchise_count: int
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


@router.post("/", response_model=LeagueResponse)
async def create_league(
    league_request: LeagueCreateRequest,
    db: Session = Depends(get_db)
):
    """
    Create a new fantasy football league.
    
    Creates a league with the specified configuration including scoring rules,
    roster slots, and optional keeper rules.
    """
    # Check if league ID already exists
    existing_league = db.query(League).filter(League.id == league_request.id).first()
    if existing_league:
        raise HTTPException(status_code=400, detail=f"League with ID '{league_request.id}' already exists")
    
    # Validate league rules if provided
    if league_request.scoring_rules or league_request.roster_slots:
        rules_engine = RulesEngine()
        is_valid, validation_errors = rules_engine.validate_league_rules({
            "scoring_rules": league_request.scoring_rules,
            "roster_slots": league_request.roster_slots,
            "keeper_rules": league_request.keeper_rules
        })
        
        if not is_valid:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid league rules: {', '.join(validation_errors)}"
            )
    
    try:
        # Create new league
        league = League(
            id=league_request.id,
            name=league_request.name,
            season=league_request.season,
            scoring_rules=league_request.scoring_rules,
            roster_slots=league_request.roster_slots,
            keeper_rules=league_request.keeper_rules,
            mfl_league_id=league_request.mfl_league_id,
            mfl_api_key=league_request.mfl_api_key,
            description=league_request.description
        )
        
        db.add(league)
        db.commit()
        db.refresh(league)
        
        # Get franchise count
        franchise_count = db.query(Franchise).filter(Franchise.league_id == league.id).count()
        
        return LeagueResponse(
            id=league.id,
            name=league.name,
            season=league.season,
            scoring_rules=league.scoring_rules,
            roster_slots=league.roster_slots,
            keeper_rules=league.keeper_rules,
            mfl_league_id=league.mfl_league_id,
            description=league.description,
            is_active=league.is_active,
            franchise_count=franchise_count,
            created_at=league.created_at,
            updated_at=league.updated_at
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating league: {str(e)}")


@router.get("/health")
async def health_check():
    """Health check endpoint for league service."""
    return {
        "status": "healthy",
        "service": "league_management",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/", response_model=List[LeagueListResponse])
async def list_leagues(
    season: Optional[int] = Query(None, description="Filter by season"),
    active_only: bool = Query(True, description="Only return active leagues"),
    db: Session = Depends(get_db)
):
    """
    List all fantasy football leagues.
    
    Returns a list of leagues with basic information, optionally filtered
    by season and active status.
    """
    try:
        query = db.query(League)
        
        if season:
            query = query.filter(League.season == season)
        
        if active_only:
            query = query.filter(League.is_active == True)
        
        leagues = query.order_by(League.created_at.desc()).all()
        
        # Get franchise counts for each league
        response = []
        for league in leagues:
            franchise_count = db.query(Franchise).filter(Franchise.league_id == league.id).count()
            
            response.append(LeagueListResponse(
                id=league.id,
                name=league.name,
                season=league.season,
                franchise_count=franchise_count,
                is_active=league.is_active,
                created_at=league.created_at
            ))
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing leagues: {str(e)}")


@router.get("/{league_id}", response_model=LeagueResponse)
async def get_league(
    league_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific league.
    
    Returns complete league configuration including scoring rules,
    roster slots, and keeper rules.
    """
    league = db.query(League).filter(League.id == league_id).first()
    if not league:
        raise HTTPException(status_code=404, detail=f"League '{league_id}' not found")
    
    try:
        # Get franchise count
        franchise_count = db.query(Franchise).filter(Franchise.league_id == league.id).count()
        
        return LeagueResponse(
            id=league.id,
            name=league.name,
            season=league.season,
            scoring_rules=league.scoring_rules,
            roster_slots=league.roster_slots,
            keeper_rules=league.keeper_rules,
            mfl_league_id=league.mfl_league_id,
            description=league.description,
            is_active=league.is_active,
            franchise_count=franchise_count,
            created_at=league.created_at,
            updated_at=league.updated_at
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting league: {str(e)}")


@router.put("/{league_id}", response_model=LeagueResponse)
async def update_league(
    league_id: str,
    league_update: LeagueUpdateRequest,
    db: Session = Depends(get_db)
):
    """
    Update an existing league's configuration.
    
    Updates league settings including scoring rules, roster slots,
    and keeper rules. Validates rules before applying changes.
    """
    league = db.query(League).filter(League.id == league_id).first()
    if not league:
        raise HTTPException(status_code=404, detail=f"League '{league_id}' not found")
    
    # Validate updated rules if provided
    if league_update.scoring_rules is not None or league_update.roster_slots is not None:
        rules_engine = RulesEngine()
        
        # Use existing values if not being updated
        scoring_rules = league_update.scoring_rules if league_update.scoring_rules is not None else league.scoring_rules
        roster_slots = league_update.roster_slots if league_update.roster_slots is not None else league.roster_slots
        keeper_rules = league_update.keeper_rules if league_update.keeper_rules is not None else league.keeper_rules
        
        is_valid, validation_errors = rules_engine.validate_league_rules({
            "scoring_rules": scoring_rules,
            "roster_slots": roster_slots,
            "keeper_rules": keeper_rules
        })
        
        if not is_valid:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid league rules: {', '.join(validation_errors)}"
            )
    
    try:
        # Update league fields
        if league_update.name is not None:
            league.name = league_update.name
        if league_update.scoring_rules is not None:
            league.scoring_rules = league_update.scoring_rules
        if league_update.roster_slots is not None:
            league.roster_slots = league_update.roster_slots
        if league_update.keeper_rules is not None:
            league.keeper_rules = league_update.keeper_rules
        if league_update.mfl_league_id is not None:
            league.mfl_league_id = league_update.mfl_league_id
        if league_update.mfl_api_key is not None:
            league.mfl_api_key = league_update.mfl_api_key
        if league_update.description is not None:
            league.description = league_update.description
        if league_update.is_active is not None:
            league.is_active = league_update.is_active
        
        db.commit()
        db.refresh(league)
        
        # Get franchise count
        franchise_count = db.query(Franchise).filter(Franchise.league_id == league.id).count()
        
        return LeagueResponse(
            id=league.id,
            name=league.name,
            season=league.season,
            scoring_rules=league.scoring_rules,
            roster_slots=league.roster_slots,
            keeper_rules=league.keeper_rules,
            mfl_league_id=league.mfl_league_id,
            description=league.description,
            is_active=league.is_active,
            franchise_count=franchise_count,
            created_at=league.created_at,
            updated_at=league.updated_at
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating league: {str(e)}")


@router.delete("/{league_id}")
async def delete_league(
    league_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete a league and all associated data.
    
    WARNING: This will permanently delete the league, all franchises,
    rosters, and recommendations associated with it.
    """
    league = db.query(League).filter(League.id == league_id).first()
    if not league:
        raise HTTPException(status_code=404, detail=f"League '{league_id}' not found")
    
    try:
        db.delete(league)
        db.commit()
        
        return {"message": f"League '{league_id}' deleted successfully"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting league: {str(e)}")


@router.post("/{league_id}/validate-rules")
async def validate_league_rules(
    league_id: str,
    db: Session = Depends(get_db)
):
    """
    Validate the current league rules configuration.
    
    Checks that scoring rules, roster slots, and keeper rules are
    properly configured and consistent.
    """
    league = db.query(League).filter(League.id == league_id).first()
    if not league:
        raise HTTPException(status_code=404, detail=f"League '{league_id}' not found")
    
    try:
        rules_engine = RulesEngine()
        is_valid, validation_errors = rules_engine.validate_league_rules({
            "scoring_rules": league.scoring_rules,
            "roster_slots": league.roster_slots,
            "keeper_rules": league.keeper_rules
        })
        
        return {
            "league_id": league_id,
            "is_valid": is_valid,
            "validation_errors": validation_errors,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error validating league rules: {str(e)}")


@router.get("/{league_id}/franchises", response_model=List[str])
async def get_league_franchises(
    league_id: str,
    db: Session = Depends(get_db)
):
    """
    Get list of franchise IDs in a league.
    
    Returns a simple list of franchise identifiers for the specified league.
    """
    league = db.query(League).filter(League.id == league_id).first()
    if not league:
        raise HTTPException(status_code=404, detail=f"League '{league_id}' not found")
    
    try:
        franchises = db.query(Franchise.id).filter(Franchise.league_id == league_id).all()
        return [franchise.id for franchise in franchises]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting league franchises: {str(e)}")