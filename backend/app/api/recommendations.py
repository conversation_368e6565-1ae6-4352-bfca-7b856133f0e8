"""
Recommendations API endpoints for retrieving and explaining AI recommendations.

Provides unified access to all recommendation types with detailed explanations
and rationale for decision-making transparency.
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

from ..core.database import get_db
from ..models.recommendation import Recommendation, RecommendationType
from ..models.roster import Franchise

router = APIRouter(prefix="/recommendations", tags=["recommendations"])


class RecommendationTypeFilter(str, Enum):
    """Enum for filtering recommendations by type."""
    KEEPER = "keeper"
    DRAFT = "draft"
    TRADE = "trade"
    LINEUP = "lineup"
    WAIVER = "waiver"
    ALERT = "alert"


class ConfidenceLevel(str, Enum):
    """Enum for confidence level categories."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


class RecommendationResponse(BaseModel):
    """Response model for recommendation information."""
    id: str
    type: str
    franchise_id: str
    franchise_name: str
    league_id: str
    title: str
    description: str
    rationale: str
    confidence: float
    confidence_level: ConfidenceLevel
    alternatives: List[Dict[str, Any]]
    supporting_data: Dict[str, Any]
    risk_factors: List[str]
    expected_impact: Dict[str, Any]
    expires_at: Optional[datetime]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RecommendationSummaryResponse(BaseModel):
    """Response model for recommendation summary."""
    id: str
    type: str
    title: str
    confidence: float
    expires_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


class ExplanationRequest(BaseModel):
    """Request model for detailed recommendation explanation."""
    include_alternatives: bool = Field(True, description="Include alternative recommendations")
    include_supporting_data: bool = Field(True, description="Include supporting data and calculations")
    include_risk_analysis: bool = Field(True, description="Include risk factor analysis")
    detail_level: str = Field("standard", description="Detail level: basic, standard, or detailed")


class RecommendationExplanationResponse(BaseModel):
    """Response model for detailed recommendation explanation."""
    recommendation_id: str
    explanation: str
    methodology: str
    key_factors: List[Dict[str, Any]]
    assumptions: List[str]
    limitations: List[str]
    confidence_breakdown: Dict[str, float]
    sensitivity_analysis: Optional[Dict[str, Any]]
    alternative_scenarios: List[Dict[str, Any]]
    data_sources: List[str]
    last_updated: datetime


@router.get("/health")
async def health_check():
    """Health check endpoint for recommendations service."""
    return {
        "status": "healthy",
        "service": "recommendations",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/", response_model=List[RecommendationSummaryResponse])
async def list_recommendations(
    franchise_id: Optional[str] = Query(None, description="Filter by franchise ID"),
    league_id: Optional[str] = Query(None, description="Filter by league ID"),
    recommendation_type: Optional[RecommendationTypeFilter] = Query(None, description="Filter by recommendation type"),
    active_only: bool = Query(True, description="Only return active recommendations"),
    min_confidence: float = Query(0.0, description="Minimum confidence threshold", ge=0.0, le=1.0),
    limit: int = Query(50, description="Maximum number of recommendations to return", ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    List recommendations with optional filtering.
    
    Returns a summary list of recommendations that can be filtered by
    franchise, league, type, confidence level, and active status.
    """
    try:
        query = db.query(Recommendation).join(Franchise)
        
        if franchise_id:
            query = query.filter(Recommendation.franchise_id == franchise_id)
        
        if league_id:
            query = query.filter(Franchise.league_id == league_id)
        
        if recommendation_type:
            # Map enum to RecommendationType
            type_mapping = {
                RecommendationTypeFilter.KEEPER: RecommendationType.KEEPER,
                RecommendationTypeFilter.DRAFT: RecommendationType.DRAFT,
                RecommendationTypeFilter.TRADE: RecommendationType.TRADE,
                RecommendationTypeFilter.LINEUP: RecommendationType.LINEUP,
                RecommendationTypeFilter.WAIVER: RecommendationType.WAIVER,
                RecommendationTypeFilter.ALERT: RecommendationType.ALERT
            }
            query = query.filter(Recommendation.type == type_mapping[recommendation_type])
        
        if active_only:
            query = query.filter(Recommendation.is_active == True)
        
        if min_confidence > 0.0:
            query = query.filter(Recommendation.confidence >= min_confidence)
        
        recommendations = query.order_by(
            Recommendation.confidence.desc(),
            Recommendation.created_at.desc()
        ).limit(limit).all()
        
        return [
            RecommendationSummaryResponse(
                id=rec.id,
                type=rec.type.value,
                title=rec.title,
                confidence=rec.confidence,
                expires_at=rec.expires_at,
                created_at=rec.created_at
            )
            for rec in recommendations
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing recommendations: {str(e)}")


@router.get("/{recommendation_id}", response_model=RecommendationResponse)
async def get_recommendation(
    recommendation_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific recommendation.
    
    Returns complete recommendation details including rationale,
    alternatives, and supporting data.
    """
    recommendation = db.query(Recommendation).filter(Recommendation.id == recommendation_id).first()
    if not recommendation:
        raise HTTPException(status_code=404, detail=f"Recommendation '{recommendation_id}' not found")
    
    try:
        # Determine confidence level category
        confidence_level = ConfidenceLevel.LOW
        if recommendation.confidence >= 0.9:
            confidence_level = ConfidenceLevel.VERY_HIGH
        elif recommendation.confidence >= 0.7:
            confidence_level = ConfidenceLevel.HIGH
        elif recommendation.confidence >= 0.5:
            confidence_level = ConfidenceLevel.MEDIUM
        
        # Extract risk factors from metadata
        risk_factors = recommendation.metadata.get("risk_factors", [])
        
        # Extract expected impact from metadata
        expected_impact = recommendation.metadata.get("expected_impact", {})
        
        # Extract supporting data from metadata
        supporting_data = recommendation.metadata.get("supporting_data", {})
        
        return RecommendationResponse(
            id=recommendation.id,
            type=recommendation.type.value,
            franchise_id=recommendation.franchise_id,
            franchise_name=recommendation.franchise.name,
            league_id=recommendation.franchise.league_id,
            title=recommendation.title,
            description=recommendation.description,
            rationale=recommendation.rationale,
            confidence=recommendation.confidence,
            confidence_level=confidence_level,
            alternatives=recommendation.alternatives,
            supporting_data=supporting_data,
            risk_factors=risk_factors,
            expected_impact=expected_impact,
            expires_at=recommendation.expires_at,
            is_active=recommendation.is_active,
            created_at=recommendation.created_at,
            updated_at=recommendation.updated_at
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting recommendation: {str(e)}")


@router.post("/{recommendation_id}/explain", response_model=RecommendationExplanationResponse)
async def explain_recommendation(
    recommendation_id: str,
    explanation_request: ExplanationRequest,
    db: Session = Depends(get_db)
):
    """
    Get detailed explanation of how a recommendation was generated.
    
    Provides comprehensive explanation including methodology, key factors,
    assumptions, and sensitivity analysis for transparency.
    """
    recommendation = db.query(Recommendation).filter(Recommendation.id == recommendation_id).first()
    if not recommendation:
        raise HTTPException(status_code=404, detail=f"Recommendation '{recommendation_id}' not found")
    
    try:
        # Generate explanation based on recommendation type and metadata
        explanation_data = _generate_explanation(recommendation, explanation_request)
        
        return RecommendationExplanationResponse(
            recommendation_id=recommendation.id,
            explanation=explanation_data["explanation"],
            methodology=explanation_data["methodology"],
            key_factors=explanation_data["key_factors"],
            assumptions=explanation_data["assumptions"],
            limitations=explanation_data["limitations"],
            confidence_breakdown=explanation_data["confidence_breakdown"],
            sensitivity_analysis=explanation_data.get("sensitivity_analysis"),
            alternative_scenarios=explanation_data["alternative_scenarios"],
            data_sources=explanation_data["data_sources"],
            last_updated=recommendation.updated_at
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error explaining recommendation: {str(e)}")


@router.get("/franchise/{franchise_id}/active", response_model=List[RecommendationResponse])
async def get_active_recommendations_for_franchise(
    franchise_id: str,
    recommendation_type: Optional[RecommendationTypeFilter] = Query(None, description="Filter by recommendation type"),
    db: Session = Depends(get_db)
):
    """
    Get all active recommendations for a specific franchise.
    
    Returns detailed information for all active recommendations
    that haven't expired for the specified franchise.
    """
    # Verify franchise exists
    franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
    if not franchise:
        raise HTTPException(status_code=404, detail=f"Franchise '{franchise_id}' not found")
    
    try:
        query = db.query(Recommendation).filter(
            Recommendation.franchise_id == franchise_id,
            Recommendation.is_active == True
        )
        
        if recommendation_type:
            # Map enum to RecommendationType
            type_mapping = {
                RecommendationTypeFilter.KEEPER: RecommendationType.KEEPER,
                RecommendationTypeFilter.DRAFT: RecommendationType.DRAFT,
                RecommendationTypeFilter.TRADE: RecommendationType.TRADE,
                RecommendationTypeFilter.LINEUP: RecommendationType.LINEUP,
                RecommendationTypeFilter.WAIVER: RecommendationType.WAIVER,
                RecommendationTypeFilter.ALERT: RecommendationType.ALERT
            }
            query = query.filter(Recommendation.type == type_mapping[recommendation_type])
        
        # Filter out expired recommendations
        current_time = datetime.utcnow()
        query = query.filter(
            (Recommendation.expires_at.is_(None)) | 
            (Recommendation.expires_at > current_time)
        )
        
        recommendations = query.order_by(
            Recommendation.confidence.desc(),
            Recommendation.created_at.desc()
        ).all()
        
        response = []
        for rec in recommendations:
            # Determine confidence level category
            confidence_level = ConfidenceLevel.LOW
            if rec.confidence >= 0.9:
                confidence_level = ConfidenceLevel.VERY_HIGH
            elif rec.confidence >= 0.7:
                confidence_level = ConfidenceLevel.HIGH
            elif rec.confidence >= 0.5:
                confidence_level = ConfidenceLevel.MEDIUM
            
            # Extract data from metadata
            risk_factors = rec.metadata.get("risk_factors", [])
            expected_impact = rec.metadata.get("expected_impact", {})
            supporting_data = rec.metadata.get("supporting_data", {})
            
            response.append(RecommendationResponse(
                id=rec.id,
                type=rec.type.value,
                franchise_id=rec.franchise_id,
                franchise_name=rec.franchise.name,
                league_id=rec.franchise.league_id,
                title=rec.title,
                description=rec.description,
                rationale=rec.rationale,
                confidence=rec.confidence,
                confidence_level=confidence_level,
                alternatives=rec.alternatives,
                supporting_data=supporting_data,
                risk_factors=risk_factors,
                expected_impact=expected_impact,
                expires_at=rec.expires_at,
                is_active=rec.is_active,
                created_at=rec.created_at,
                updated_at=rec.updated_at
            ))
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting franchise recommendations: {str(e)}")


@router.put("/{recommendation_id}/dismiss")
async def dismiss_recommendation(
    recommendation_id: str,
    db: Session = Depends(get_db)
):
    """
    Dismiss (deactivate) a recommendation.
    
    Marks the recommendation as inactive so it won't appear in
    active recommendation lists.
    """
    recommendation = db.query(Recommendation).filter(Recommendation.id == recommendation_id).first()
    if not recommendation:
        raise HTTPException(status_code=404, detail=f"Recommendation '{recommendation_id}' not found")
    
    try:
        recommendation.is_active = False
        db.commit()
        
        return {
            "message": f"Recommendation '{recommendation_id}' dismissed successfully",
            "recommendation_id": recommendation_id,
            "dismissed_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error dismissing recommendation: {str(e)}")


def _generate_explanation(recommendation: Recommendation, request: ExplanationRequest) -> Dict[str, Any]:
    """
    Generate detailed explanation for a recommendation based on its type and metadata.
    
    This is a helper function that creates comprehensive explanations
    tailored to each recommendation type.
    """
    metadata = recommendation.metadata
    rec_type = recommendation.type
    
    # Base explanation structure
    explanation_data = {
        "explanation": "",
        "methodology": "",
        "key_factors": [],
        "assumptions": [],
        "limitations": [],
        "confidence_breakdown": {},
        "alternative_scenarios": [],
        "data_sources": []
    }
    
    # Type-specific explanations
    if rec_type == RecommendationType.KEEPER:
        # Extract keeper-specific metadata
        total_value = metadata.get("total_value", 0)
        keeper_count = metadata.get("keeper_count", 0)
        budget_used = metadata.get("budget_used", 0)

        explanation_data.update({
            "explanation": f"This keeper recommendation optimizes your {keeper_count} keeper selections to maximize "
                          f"total value over replacement ({total_value:.1f} points). The analysis considers each player's "
                          f"projected performance, keeper cost, and opportunity cost of alternative selections. "
                          f"The recommended combination provides the highest expected surplus value while staying "
                          f"within your budget constraints.",
            "methodology": "Integer Linear Programming optimization using value over replacement calculations with "
                          "constraint satisfaction for budget and roster limits",
            "key_factors": [
                {
                    "factor": "Projected Points",
                    "weight": 0.4,
                    "description": "Season-long point projections based on consensus rankings",
                    "value": f"{metadata.get('avg_projected_points', 0):.1f} avg per keeper",
                    "impact": "positive"
                },
                {
                    "factor": "Replacement Level",
                    "weight": 0.3,
                    "description": "Position-specific replacement baselines from draft ADP",
                    "value": f"{metadata.get('replacement_baseline', 0):.1f} avg baseline",
                    "impact": "positive"
                },
                {
                    "factor": "Keeper Cost",
                    "weight": 0.2,
                    "description": "Draft round or salary cost efficiency",
                    "value": f"${budget_used} of budget used" if budget_used else f"Rounds {metadata.get('rounds_used', 'N/A')}",
                    "impact": "neutral"
                },
                {
                    "factor": "Risk Assessment",
                    "weight": 0.1,
                    "description": "Injury history and performance volatility",
                    "value": f"{metadata.get('avg_risk_score', 0.5):.2f} avg risk score",
                    "impact": "negative" if metadata.get('avg_risk_score', 0.5) > 0.6 else "neutral"
                }
            ],
            "assumptions": [
                "Consensus projections are accurate within ±15% confidence intervals",
                "League rules and scoring system remain unchanged",
                "No major injuries or trades before season start",
                "Other managers make rational keeper decisions",
                "Draft ADP reflects true player values"
            ],
            "limitations": [
                "Cannot predict unexpected breakout or bust performances",
                "Assumes current team situations remain stable",
                "Limited by quality of available projection sources"
            ],
            "data_sources": [
                "Consensus fantasy projections (FantasyPros, ESPN, Yahoo)",
                "Historical performance data (3+ seasons)",
                "League configuration and scoring rules",
                "Current ADP and market values",
                "Injury reports and depth chart analysis"
            ]
        })
    
    elif rec_type == RecommendationType.DRAFT:
        # Extract draft-specific metadata
        player_name = metadata.get("player_name", "Unknown Player")
        position = metadata.get("position", "N/A")
        tier = metadata.get("tier", 1)
        vor_value = metadata.get("value_over_replacement", 0)
        positional_need = metadata.get("positional_need_score", 0.5)

        explanation_data.update({
            "explanation": f"The recommendation to draft {player_name} ({position}) is based on maximizing expected "
                          f"roster value while addressing positional needs. With a value over replacement of "
                          f"{vor_value:.1f} points and tier {tier} ranking, this player offers optimal combination "
                          f"of immediate impact and long-term roster construction. The positional need score of "
                          f"{positional_need:.2f} indicates this pick addresses a {'critical' if positional_need > 0.7 else 'moderate' if positional_need > 0.4 else 'minor'} "
                          f"roster gap.",
            "methodology": "Tiered value-based drafting with dynamic positional need weighting and opportunity cost analysis",
            "key_factors": [
                {
                    "factor": "Value Over Replacement",
                    "weight": 0.35,
                    "description": "Expected points above position replacement level",
                    "value": f"{vor_value:.1f} points",
                    "impact": "positive" if vor_value > 0 else "negative"
                },
                {
                    "factor": "Positional Need",
                    "weight": 0.25,
                    "description": "How well this pick addresses current roster gaps",
                    "value": f"{positional_need:.2f} need score",
                    "impact": "positive" if positional_need > 0.5 else "neutral"
                },
                {
                    "factor": "Tier Position",
                    "weight": 0.25,
                    "description": "Player's ranking within their value tier",
                    "value": f"Tier {tier}, rank {metadata.get('tier_rank', 'N/A')}",
                    "impact": "positive" if tier <= 3 else "neutral"
                },
                {
                    "factor": "Opportunity Cost",
                    "weight": 0.15,
                    "description": "Value difference vs. next best alternatives",
                    "value": f"{metadata.get('opportunity_cost', 0):.1f} point advantage",
                    "impact": "positive" if metadata.get('opportunity_cost', 0) > 0 else "neutral"
                }
            ],
            "assumptions": [
                "Other teams draft based on ADP and positional needs",
                "No significant injury or trade news breaks during draft",
                "Consensus projections remain stable throughout draft",
                "Starting lineup requirements drive positional value",
                "Best available player strategy is suboptimal"
            ],
            "limitations": [
                "Cannot predict other teams' draft strategies perfectly",
                "Projections may not account for recent developments",
                "Positional scarcity may change as draft progresses"
            ],
            "data_sources": [
                "Real-time consensus rankings and projections",
                "Current draft ADP and positional scarcity data",
                "Team roster composition analysis",
                "Historical draft value curves",
                "Live draft room pick tracking"
            ],
            "data_sources": ["Draft board tiers", "Positional analysis", "Monte Carlo simulations"]
        })
    
    elif rec_type == RecommendationType.TRADE:
        # Extract trade-specific metadata
        trade_partner = metadata.get("trade_partner", "Unknown Team")
        your_players = metadata.get("your_players", [])
        their_players = metadata.get("their_players", [])
        fairness_score = metadata.get("fairness_score", 0)
        win_prob_impact = metadata.get("win_probability_impact", 0)
        acceptance_prob = metadata.get("acceptance_probability", 0.5)

        explanation_data.update({
            "explanation": f"This trade with {trade_partner} is recommended based on mutual benefit analysis. "
                          f"You would send {', '.join(your_players[:2])}{'...' if len(your_players) > 2 else ''} "
                          f"and receive {', '.join(their_players[:2])}{'...' if len(their_players) > 2 else ''}. "
                          f"The trade has a fairness score of {fairness_score:.2f} and is projected to "
                          f"{'increase' if win_prob_impact > 0 else 'decrease'} your win probability by "
                          f"{abs(win_prob_impact*100):.1f}%. With {acceptance_prob*100:.0f}% acceptance likelihood, "
                          f"this represents a {'strong' if acceptance_prob > 0.7 else 'moderate' if acceptance_prob > 0.4 else 'challenging'} "
                          f"negotiation opportunity.",
            "methodology": "Pareto-optimal trade analysis with multi-factor fairness scoring and acceptance modeling",
            "key_factors": [
                {
                    "factor": "Team Need Alignment",
                    "weight": 0.3,
                    "description": "How well trade addresses both teams' positional needs",
                    "value": f"{metadata.get('need_alignment_score', 0.5):.2f} alignment score",
                    "impact": "positive" if metadata.get('need_alignment_score', 0.5) > 0.6 else "neutral"
                },
                {
                    "factor": "Value Fairness",
                    "weight": 0.25,
                    "description": "Balance of player values exchanged",
                    "value": f"{fairness_score:.2f} fairness score",
                    "impact": "positive" if abs(fairness_score) < 0.2 else "negative"
                },
                {
                    "factor": "Win Probability Impact",
                    "weight": 0.25,
                    "description": "Expected change in your championship odds",
                    "value": f"{win_prob_impact*100:+.1f}% win probability",
                    "impact": "positive" if win_prob_impact > 0 else "negative"
                },
                {
                    "factor": "Acceptance Likelihood",
                    "weight": 0.2,
                    "description": "Probability the other team accepts this trade",
                    "value": f"{acceptance_prob*100:.0f}% acceptance chance",
                    "impact": "positive" if acceptance_prob > 0.6 else "neutral"
                }
            ],
            "assumptions": [
                "Both teams evaluate trades based on win probability improvement",
                "No private information about player injuries or situations",
                "League trade rules and deadlines are followed",
                "Player values remain stable during negotiation period",
                "Other team acts rationally in their best interest"
            ],
            "limitations": [
                "Cannot predict personal preferences or team loyalties",
                "May not account for future trade opportunities",
                "Assumes current roster construction priorities"
            ],
            "data_sources": [
                "Team roster analysis and positional needs assessment",
                "Consensus player valuations and projections",
                "Historical trade acceptance patterns",
                "League-specific scoring and roster rules",
                "Current standings and playoff implications"
            ]
        })
    
    elif rec_type == RecommendationType.LINEUP:
        # Extract lineup-specific metadata
        week = metadata.get("week", "N/A")
        projected_points = metadata.get("projected_points", 0)
        win_probability = metadata.get("win_probability", 0.5)
        risk_level = metadata.get("risk_level", 0.5)
        key_decisions = metadata.get("key_decisions", [])

        explanation_data.update({
            "explanation": f"This Week {week} lineup recommendation is optimized to maximize your win probability "
                          f"({win_probability*100:.1f}%) while projecting {projected_points:.1f} total points. "
                          f"The lineup balances floor and ceiling considerations with a "
                          f"{'high' if risk_level > 0.7 else 'moderate' if risk_level > 0.4 else 'conservative'} "
                          f"risk profile. Key decisions include {', '.join(key_decisions[:2]) if key_decisions else 'standard lineup construction'}. "
                          f"The strategy prioritizes {'upside potential' if risk_level > 0.6 else 'consistent scoring'} "
                          f"based on your matchup context.",
            "methodology": "Win probability maximization using variance-adjusted projections with Monte Carlo simulation",
            "key_factors": [
                {
                    "factor": "Matchup Quality",
                    "weight": 0.3,
                    "description": "Opponent defensive strength and pace factors",
                    "value": f"{metadata.get('avg_matchup_score', 0.5):.2f} avg matchup rating",
                    "impact": "positive" if metadata.get('avg_matchup_score', 0.5) > 0.6 else "neutral"
                },
                {
                    "factor": "Player Variance",
                    "weight": 0.25,
                    "description": "Ceiling and floor projection spreads",
                    "value": f"{risk_level:.2f} risk/variance score",
                    "impact": "positive" if 0.4 < risk_level < 0.7 else "neutral"
                },
                {
                    "factor": "Game Script",
                    "weight": 0.2,
                    "description": "Expected game flow and target/touch distribution",
                    "value": f"{metadata.get('game_script_score', 0.5):.2f} script favorability",
                    "impact": "positive" if metadata.get('game_script_score', 0.5) > 0.6 else "neutral"
                },
                {
                    "factor": "Weather/Conditions",
                    "weight": 0.15,
                    "description": "Environmental factors affecting performance",
                    "value": f"{metadata.get('weather_impact', 0):.1f} point impact",
                    "impact": "negative" if metadata.get('weather_impact', 0) < -1 else "neutral"
                },
                {
                    "factor": "Injury Risk",
                    "weight": 0.1,
                    "description": "Player health and availability concerns",
                    "value": f"{metadata.get('injury_risk_score', 0.1):.2f} avg risk score",
                    "impact": "negative" if metadata.get('injury_risk_score', 0.1) > 0.3 else "neutral"
                }
            ],
            "assumptions": [
                "Weather forecasts remain accurate through game time",
                "No unexpected late scratches or snap count changes",
                "Game scripts develop according to Vegas projections",
                "Injury reports reflect actual player availability",
                "Defensive matchup data is current and accurate"
            ],
            "limitations": [
                "Cannot predict in-game injuries or ejections",
                "Weather conditions may change rapidly",
                "Coaching decisions may deviate from expected usage"
            ],
            "data_sources": [
                "Real-time weather forecasts and field conditions",
                "Updated injury reports and practice participation",
                "Defensive rankings and recent performance trends",
                "Vegas betting lines and game totals",
                "Target share and snap count projections"
            ]
        })
    
    elif rec_type == RecommendationType.WAIVER:
        # Extract waiver-specific metadata
        target_player = metadata.get("target_player", "Unknown Player")
        recommended_bid = metadata.get("recommended_bid", 0)
        max_bid = metadata.get("max_bid", 0)
        points_over_replacement = metadata.get("points_over_replacement", 0)
        acquisition_probability = metadata.get("acquisition_probability", 0.5)
        remaining_budget = metadata.get("remaining_budget", 100)

        explanation_data.update({
            "explanation": f"The recommendation to target {target_player} with a ${recommended_bid} bid "
                          f"(max ${max_bid}) is based on expected value analysis. This player projects "
                          f"{points_over_replacement:.1f} points over your current replacement level, "
                          f"with {acquisition_probability*100:.0f}% probability of successful acquisition. "
                          f"The bid amount balances acquisition likelihood against budget preservation, "
                          f"leaving ${remaining_budget - recommended_bid} for future waiver opportunities. "
                          f"This represents {'excellent' if points_over_replacement > 10 else 'good' if points_over_replacement > 5 else 'moderate'} "
                          f"value relative to opportunity cost.",
            "methodology": "Expected value analysis with FAAB budget optimization and competitive bidding modeling",
            "key_factors": [
                {
                    "factor": "Points Over Replacement",
                    "weight": 0.35,
                    "description": "Expected weekly scoring improvement vs. current roster",
                    "value": f"{points_over_replacement:.1f} points/week",
                    "impact": "positive" if points_over_replacement > 0 else "negative"
                },
                {
                    "factor": "Opportunity Cost",
                    "weight": 0.25,
                    "description": "Value of alternative FAAB uses and targets",
                    "value": f"${metadata.get('opportunity_cost_value', 0)} alternative value",
                    "impact": "neutral"
                },
                {
                    "factor": "Acquisition Probability",
                    "weight": 0.2,
                    "description": "Likelihood of winning bid based on competition",
                    "value": f"{acquisition_probability*100:.0f}% win probability",
                    "impact": "positive" if acquisition_probability > 0.6 else "neutral"
                },
                {
                    "factor": "Remaining Budget",
                    "weight": 0.2,
                    "description": "Impact on future waiver wire flexibility",
                    "value": f"${remaining_budget - recommended_bid} remaining after bid",
                    "impact": "positive" if (remaining_budget - recommended_bid) > 20 else "neutral"
                }
            ],
            "assumptions": [
                "Other teams bid based on similar value calculations",
                "Player maintains current role and opportunity share",
                "No higher-priority waiver targets emerge this week",
                "Team situations remain stable (no trades/injuries)",
                "Bidding patterns follow historical league trends"
            ],
            "limitations": [
                "Cannot predict other teams' specific budget constraints",
                "Player role changes could affect projected value",
                "Late-breaking news may alter competitive landscape"
            ],
            "data_sources": [
                "Free agent scoring projections and target shares",
                "Historical FAAB bidding patterns in similar leagues",
                "Current team budget tracking and constraints",
                "Depth chart analysis and opportunity projections",
                "Competitive landscape and alternative targets"
            ]
        })
    
    # Add confidence breakdown
    confidence = recommendation.confidence
    explanation_data["confidence_breakdown"] = {
        "data_quality": min(0.95, confidence + 0.1),
        "model_accuracy": confidence,
        "situational_factors": max(0.6, confidence - 0.1),
        "overall_confidence": confidence
    }
    
    # Add alternative scenarios if requested
    if request.include_alternatives and recommendation.alternatives:
        explanation_data["alternative_scenarios"] = [
            {
                "scenario": alt.get("title", "Alternative"),
                "description": alt.get("description", ""),
                "confidence": alt.get("confidence", 0.5),
                "trade_offs": alt.get("trade_offs", [])
            }
            for alt in recommendation.alternatives[:3]  # Limit to top 3 alternatives
        ]
    
    # Add sensitivity analysis if detailed level requested
    if request.detail_level == "detailed":
        explanation_data["sensitivity_analysis"] = metadata.get("sensitivity_analysis", {
            "projection_variance": "±15% change in projections affects recommendation confidence by ±10%",
            "rule_changes": "League rule modifications could impact recommendation validity",
            "external_factors": "Injuries, trades, or news could alter recommendation value"
        })
    
    return explanation_data