"""
Recommendations API endpoints for retrieving and explaining AI recommendations.

Provides unified access to all recommendation types with detailed explanations
and rationale for decision-making transparency.
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

from ..core.database import get_db
from ..models.recommendation import Recommendation, RecommendationType
from ..models.roster import Franchise

router = APIRouter(prefix="/recommendations", tags=["recommendations"])


class RecommendationTypeFilter(str, Enum):
    """Enum for filtering recommendations by type."""
    KEEPER = "keeper"
    DRAFT = "draft"
    TRADE = "trade"
    LINEUP = "lineup"
    WAIVER = "waiver"
    ALERT = "alert"


class ConfidenceLevel(str, Enum):
    """Enum for confidence level categories."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


class RecommendationResponse(BaseModel):
    """Response model for recommendation information."""
    id: str
    type: str
    franchise_id: str
    franchise_name: str
    league_id: str
    title: str
    description: str
    rationale: str
    confidence: float
    confidence_level: ConfidenceLevel
    alternatives: List[Dict[str, Any]]
    supporting_data: Dict[str, Any]
    risk_factors: List[str]
    expected_impact: Dict[str, Any]
    expires_at: Optional[datetime]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RecommendationSummaryResponse(BaseModel):
    """Response model for recommendation summary."""
    id: str
    type: str
    title: str
    confidence: float
    expires_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


class ExplanationRequest(BaseModel):
    """Request model for detailed recommendation explanation."""
    include_alternatives: bool = Field(True, description="Include alternative recommendations")
    include_supporting_data: bool = Field(True, description="Include supporting data and calculations")
    include_risk_analysis: bool = Field(True, description="Include risk factor analysis")
    detail_level: str = Field("standard", description="Detail level: basic, standard, or detailed")


class RecommendationExplanationResponse(BaseModel):
    """Response model for detailed recommendation explanation."""
    recommendation_id: str
    explanation: str
    methodology: str
    key_factors: List[Dict[str, Any]]
    assumptions: List[str]
    limitations: List[str]
    confidence_breakdown: Dict[str, float]
    sensitivity_analysis: Optional[Dict[str, Any]]
    alternative_scenarios: List[Dict[str, Any]]
    data_sources: List[str]
    last_updated: datetime


@router.get("/health")
async def health_check():
    """Health check endpoint for recommendations service."""
    return {
        "status": "healthy",
        "service": "recommendations",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/", response_model=List[RecommendationSummaryResponse])
async def list_recommendations(
    franchise_id: Optional[str] = Query(None, description="Filter by franchise ID"),
    league_id: Optional[str] = Query(None, description="Filter by league ID"),
    recommendation_type: Optional[RecommendationTypeFilter] = Query(None, description="Filter by recommendation type"),
    active_only: bool = Query(True, description="Only return active recommendations"),
    min_confidence: float = Query(0.0, description="Minimum confidence threshold", ge=0.0, le=1.0),
    limit: int = Query(50, description="Maximum number of recommendations to return", ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    List recommendations with optional filtering.
    
    Returns a summary list of recommendations that can be filtered by
    franchise, league, type, confidence level, and active status.
    """
    try:
        query = db.query(Recommendation).join(Franchise)
        
        if franchise_id:
            query = query.filter(Recommendation.franchise_id == franchise_id)
        
        if league_id:
            query = query.filter(Franchise.league_id == league_id)
        
        if recommendation_type:
            # Map enum to RecommendationType
            type_mapping = {
                RecommendationTypeFilter.KEEPER: RecommendationType.KEEPER,
                RecommendationTypeFilter.DRAFT: RecommendationType.DRAFT,
                RecommendationTypeFilter.TRADE: RecommendationType.TRADE,
                RecommendationTypeFilter.LINEUP: RecommendationType.LINEUP,
                RecommendationTypeFilter.WAIVER: RecommendationType.WAIVER,
                RecommendationTypeFilter.ALERT: RecommendationType.ALERT
            }
            query = query.filter(Recommendation.type == type_mapping[recommendation_type])
        
        if active_only:
            query = query.filter(Recommendation.is_active == True)
        
        if min_confidence > 0.0:
            query = query.filter(Recommendation.confidence >= min_confidence)
        
        recommendations = query.order_by(
            Recommendation.confidence.desc(),
            Recommendation.created_at.desc()
        ).limit(limit).all()
        
        return [
            RecommendationSummaryResponse(
                id=rec.id,
                type=rec.type.value,
                title=rec.title,
                confidence=rec.confidence,
                expires_at=rec.expires_at,
                created_at=rec.created_at
            )
            for rec in recommendations
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing recommendations: {str(e)}")


@router.get("/{recommendation_id}", response_model=RecommendationResponse)
async def get_recommendation(
    recommendation_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific recommendation.
    
    Returns complete recommendation details including rationale,
    alternatives, and supporting data.
    """
    recommendation = db.query(Recommendation).filter(Recommendation.id == recommendation_id).first()
    if not recommendation:
        raise HTTPException(status_code=404, detail=f"Recommendation '{recommendation_id}' not found")
    
    try:
        # Determine confidence level category
        confidence_level = ConfidenceLevel.LOW
        if recommendation.confidence >= 0.9:
            confidence_level = ConfidenceLevel.VERY_HIGH
        elif recommendation.confidence >= 0.7:
            confidence_level = ConfidenceLevel.HIGH
        elif recommendation.confidence >= 0.5:
            confidence_level = ConfidenceLevel.MEDIUM
        
        # Extract risk factors from metadata
        risk_factors = recommendation.metadata.get("risk_factors", [])
        
        # Extract expected impact from metadata
        expected_impact = recommendation.metadata.get("expected_impact", {})
        
        # Extract supporting data from metadata
        supporting_data = recommendation.metadata.get("supporting_data", {})
        
        return RecommendationResponse(
            id=recommendation.id,
            type=recommendation.type.value,
            franchise_id=recommendation.franchise_id,
            franchise_name=recommendation.franchise.name,
            league_id=recommendation.franchise.league_id,
            title=recommendation.title,
            description=recommendation.description,
            rationale=recommendation.rationale,
            confidence=recommendation.confidence,
            confidence_level=confidence_level,
            alternatives=recommendation.alternatives,
            supporting_data=supporting_data,
            risk_factors=risk_factors,
            expected_impact=expected_impact,
            expires_at=recommendation.expires_at,
            is_active=recommendation.is_active,
            created_at=recommendation.created_at,
            updated_at=recommendation.updated_at
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting recommendation: {str(e)}")


@router.post("/{recommendation_id}/explain", response_model=RecommendationExplanationResponse)
async def explain_recommendation(
    recommendation_id: str,
    explanation_request: ExplanationRequest,
    db: Session = Depends(get_db)
):
    """
    Get detailed explanation of how a recommendation was generated.
    
    Provides comprehensive explanation including methodology, key factors,
    assumptions, and sensitivity analysis for transparency.
    """
    recommendation = db.query(Recommendation).filter(Recommendation.id == recommendation_id).first()
    if not recommendation:
        raise HTTPException(status_code=404, detail=f"Recommendation '{recommendation_id}' not found")
    
    try:
        # Generate explanation based on recommendation type and metadata
        explanation_data = _generate_explanation(recommendation, explanation_request)
        
        return RecommendationExplanationResponse(
            recommendation_id=recommendation.id,
            explanation=explanation_data["explanation"],
            methodology=explanation_data["methodology"],
            key_factors=explanation_data["key_factors"],
            assumptions=explanation_data["assumptions"],
            limitations=explanation_data["limitations"],
            confidence_breakdown=explanation_data["confidence_breakdown"],
            sensitivity_analysis=explanation_data.get("sensitivity_analysis"),
            alternative_scenarios=explanation_data["alternative_scenarios"],
            data_sources=explanation_data["data_sources"],
            last_updated=recommendation.updated_at
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error explaining recommendation: {str(e)}")


@router.get("/franchise/{franchise_id}/active", response_model=List[RecommendationResponse])
async def get_active_recommendations_for_franchise(
    franchise_id: str,
    recommendation_type: Optional[RecommendationTypeFilter] = Query(None, description="Filter by recommendation type"),
    db: Session = Depends(get_db)
):
    """
    Get all active recommendations for a specific franchise.
    
    Returns detailed information for all active recommendations
    that haven't expired for the specified franchise.
    """
    # Verify franchise exists
    franchise = db.query(Franchise).filter(Franchise.id == franchise_id).first()
    if not franchise:
        raise HTTPException(status_code=404, detail=f"Franchise '{franchise_id}' not found")
    
    try:
        query = db.query(Recommendation).filter(
            Recommendation.franchise_id == franchise_id,
            Recommendation.is_active == True
        )
        
        if recommendation_type:
            # Map enum to RecommendationType
            type_mapping = {
                RecommendationTypeFilter.KEEPER: RecommendationType.KEEPER,
                RecommendationTypeFilter.DRAFT: RecommendationType.DRAFT,
                RecommendationTypeFilter.TRADE: RecommendationType.TRADE,
                RecommendationTypeFilter.LINEUP: RecommendationType.LINEUP,
                RecommendationTypeFilter.WAIVER: RecommendationType.WAIVER,
                RecommendationTypeFilter.ALERT: RecommendationType.ALERT
            }
            query = query.filter(Recommendation.type == type_mapping[recommendation_type])
        
        # Filter out expired recommendations
        current_time = datetime.utcnow()
        query = query.filter(
            (Recommendation.expires_at.is_(None)) | 
            (Recommendation.expires_at > current_time)
        )
        
        recommendations = query.order_by(
            Recommendation.confidence.desc(),
            Recommendation.created_at.desc()
        ).all()
        
        response = []
        for rec in recommendations:
            # Determine confidence level category
            confidence_level = ConfidenceLevel.LOW
            if rec.confidence >= 0.9:
                confidence_level = ConfidenceLevel.VERY_HIGH
            elif rec.confidence >= 0.7:
                confidence_level = ConfidenceLevel.HIGH
            elif rec.confidence >= 0.5:
                confidence_level = ConfidenceLevel.MEDIUM
            
            # Extract data from metadata
            risk_factors = rec.metadata.get("risk_factors", [])
            expected_impact = rec.metadata.get("expected_impact", {})
            supporting_data = rec.metadata.get("supporting_data", {})
            
            response.append(RecommendationResponse(
                id=rec.id,
                type=rec.type.value,
                franchise_id=rec.franchise_id,
                franchise_name=rec.franchise.name,
                league_id=rec.franchise.league_id,
                title=rec.title,
                description=rec.description,
                rationale=rec.rationale,
                confidence=rec.confidence,
                confidence_level=confidence_level,
                alternatives=rec.alternatives,
                supporting_data=supporting_data,
                risk_factors=risk_factors,
                expected_impact=expected_impact,
                expires_at=rec.expires_at,
                is_active=rec.is_active,
                created_at=rec.created_at,
                updated_at=rec.updated_at
            ))
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting franchise recommendations: {str(e)}")


@router.put("/{recommendation_id}/dismiss")
async def dismiss_recommendation(
    recommendation_id: str,
    db: Session = Depends(get_db)
):
    """
    Dismiss (deactivate) a recommendation.
    
    Marks the recommendation as inactive so it won't appear in
    active recommendation lists.
    """
    recommendation = db.query(Recommendation).filter(Recommendation.id == recommendation_id).first()
    if not recommendation:
        raise HTTPException(status_code=404, detail=f"Recommendation '{recommendation_id}' not found")
    
    try:
        recommendation.is_active = False
        db.commit()
        
        return {
            "message": f"Recommendation '{recommendation_id}' dismissed successfully",
            "recommendation_id": recommendation_id,
            "dismissed_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error dismissing recommendation: {str(e)}")


def _generate_explanation(recommendation: Recommendation, request: ExplanationRequest) -> Dict[str, Any]:
    """
    Generate detailed explanation for a recommendation based on its type and metadata.
    
    This is a helper function that creates comprehensive explanations
    tailored to each recommendation type.
    """
    metadata = recommendation.metadata
    rec_type = recommendation.type
    
    # Base explanation structure
    explanation_data = {
        "explanation": "",
        "methodology": "",
        "key_factors": [],
        "assumptions": [],
        "limitations": [],
        "confidence_breakdown": {},
        "alternative_scenarios": [],
        "data_sources": []
    }
    
    # Type-specific explanations
    if rec_type == RecommendationType.KEEPER:
        explanation_data.update({
            "explanation": f"This keeper recommendation is based on value over replacement analysis. "
                          f"The recommended players provide the highest surplus value considering "
                          f"their keeper costs and projected performance.",
            "methodology": "Integer Linear Programming optimization using value over replacement calculations",
            "key_factors": [
                {"factor": "Projected Points", "weight": 0.4, "description": "Season-long point projections"},
                {"factor": "Replacement Level", "weight": 0.3, "description": "Position-specific replacement baselines"},
                {"factor": "Keeper Cost", "weight": 0.2, "description": "Draft round or salary cost"},
                {"factor": "Risk Assessment", "weight": 0.1, "description": "Injury and performance risk"}
            ],
            "assumptions": [
                "Projections are accurate within confidence intervals",
                "League rules remain consistent",
                "No major injuries before season start"
            ],
            "data_sources": ["Aggregated projections", "Historical performance", "League configuration"]
        })
    
    elif rec_type == RecommendationType.DRAFT:
        explanation_data.update({
            "explanation": f"This draft recommendation maximizes expected value while considering "
                          f"positional needs and tier breaks. The suggested player offers the "
                          f"best combination of value and roster construction.",
            "methodology": "Tiered value-based drafting with positional need weighting",
            "key_factors": [
                {"factor": "Value Over Replacement", "weight": 0.35, "description": "Player value vs. replacement level"},
                {"factor": "Positional Need", "weight": 0.25, "description": "Current roster composition gaps"},
                {"factor": "Tier Position", "weight": 0.25, "description": "Position within value tier"},
                {"factor": "Opportunity Cost", "weight": 0.15, "description": "Value of next best alternatives"}
            ],
            "assumptions": [
                "Other teams draft rationally",
                "No significant news breaks during draft",
                "Projections remain stable"
            ],
            "data_sources": ["Draft board tiers", "Positional analysis", "Monte Carlo simulations"]
        })
    
    elif rec_type == RecommendationType.TRADE:
        explanation_data.update({
            "explanation": f"This trade recommendation addresses team needs while providing "
                          f"fair value exchange. The analysis considers both teams' roster "
                          f"construction and win probability impacts.",
            "methodology": "Pareto-optimal trade analysis with fairness scoring",
            "key_factors": [
                {"factor": "Team Need Alignment", "weight": 0.3, "description": "How well trade addresses positional needs"},
                {"factor": "Value Fairness", "weight": 0.25, "description": "Balanced value exchange"},
                {"factor": "Win Probability Impact", "weight": 0.25, "description": "Expected change in win probability"},
                {"factor": "Acceptance Likelihood", "weight": 0.2, "description": "Probability other team accepts"}
            ],
            "assumptions": [
                "Both teams act rationally",
                "No hidden information about player status",
                "League rules allow proposed trade"
            ],
            "data_sources": ["Team analysis", "Player valuations", "Historical trade patterns"]
        })
    
    elif rec_type == RecommendationType.LINEUP:
        explanation_data.update({
            "explanation": f"This lineup recommendation maximizes win probability by considering "
                          f"matchup context, player variance, and late-breaking news. "
                          f"The suggested lineup optimizes for upside potential.",
            "methodology": "Win probability maximization using variance-adjusted projections",
            "key_factors": [
                {"factor": "Matchup Quality", "weight": 0.3, "description": "Opponent strength and defensive rankings"},
                {"factor": "Player Variance", "weight": 0.25, "description": "Ceiling and floor projections"},
                {"factor": "Game Script", "weight": 0.2, "description": "Expected game flow and usage"},
                {"factor": "Weather/Conditions", "weight": 0.15, "description": "Environmental factors"},
                {"factor": "Injury Risk", "weight": 0.1, "description": "Player health status"}
            ],
            "assumptions": [
                "Weather forecasts are accurate",
                "No late scratches or lineup changes",
                "Game scripts play out as expected"
            ],
            "data_sources": ["Matchup analysis", "Weather data", "Injury reports", "Vegas lines"]
        })
    
    elif rec_type == RecommendationType.WAIVER:
        explanation_data.update({
            "explanation": f"This waiver recommendation identifies players with the highest "
                          f"expected value considering opportunity cost and budget constraints. "
                          f"The suggested bid maximizes acquisition probability while preserving budget.",
            "methodology": "Expected value analysis with budget optimization",
            "key_factors": [
                {"factor": "Points Over Replacement", "weight": 0.35, "description": "Expected value vs. current roster"},
                {"factor": "Opportunity Cost", "weight": 0.25, "description": "Value of alternative uses of budget"},
                {"factor": "Acquisition Probability", "weight": 0.2, "description": "Likelihood of winning bid"},
                {"factor": "Remaining Budget", "weight": 0.2, "description": "Impact on future waiver flexibility"}
            ],
            "assumptions": [
                "Other teams bid rationally",
                "Player maintains current role",
                "No competing waiver targets emerge"
            ],
            "data_sources": ["Free agent analysis", "Budget tracking", "Bidding patterns", "Role projections"]
        })
    
    # Add confidence breakdown
    confidence = recommendation.confidence
    explanation_data["confidence_breakdown"] = {
        "data_quality": min(0.95, confidence + 0.1),
        "model_accuracy": confidence,
        "situational_factors": max(0.6, confidence - 0.1),
        "overall_confidence": confidence
    }
    
    # Add alternative scenarios if requested
    if request.include_alternatives and recommendation.alternatives:
        explanation_data["alternative_scenarios"] = [
            {
                "scenario": alt.get("title", "Alternative"),
                "description": alt.get("description", ""),
                "confidence": alt.get("confidence", 0.5),
                "trade_offs": alt.get("trade_offs", [])
            }
            for alt in recommendation.alternatives[:3]  # Limit to top 3 alternatives
        ]
    
    # Add sensitivity analysis if detailed level requested
    if request.detail_level == "detailed":
        explanation_data["sensitivity_analysis"] = metadata.get("sensitivity_analysis", {
            "projection_variance": "±15% change in projections affects recommendation confidence by ±10%",
            "rule_changes": "League rule modifications could impact recommendation validity",
            "external_factors": "Injuries, trades, or news could alter recommendation value"
        })
    
    return explanation_data