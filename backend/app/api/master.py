"""
Master players cache API.

Provides manual refresh and fast retrieval of a cached master list of players
(including ADP fields), stored as JSON under data/master/players_{season}.json.
"""
from __future__ import annotations

import os
import json
from typing import Any, Dict
from fastapi import APIRouter, HTTPException, Query, Depends
from sqlalchemy.orm import Session
from datetime import datetime, timezone

from ..services.adp_config import BASE_DIR
from ..core.database import get_db
from .adp import get_adp_all
from ..services.adp_service import ADPAggregationService

router = APIRouter(prefix="/master", tags=["master"]) 

MASTER_DIR = os.path.join(BASE_DIR, "data", "master")
os.makedirs(MASTER_DIR, exist_ok=True)


def _master_path(season: int) -> str:
    return os.path.join(MASTER_DIR, f"players_{season}.json")


@router.post("/players/refresh")
async def refresh_master_players(
    season: int = Query(..., description="Season year"),
    league_id: str = Query("mfl_0000", description="League id for provider refresh context"),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    Refresh provider ADP (MFL/FFC) and composite index, then write the master cache file.
    """
    try:
        # Refresh providers and composite
        svc = ADPAggregationService(db, league_id=league_id, season=season)
        providers = await svc.fetch_and_store_providers()
        index_stats = svc.compute_and_store_index()
        # Build the full list as in adp/all (single page big size)
        # Exclude IDP positions by default for master list
        payload = await get_adp_all(season=season, league_id=None, position=None, team=None, q=None, sort="adp_index.asc", page=1, size=2000, db=db)
        filtered_items = [it for it in payload.get("items", []) if str(it.get("position", "")).upper() not in {"DL", "LB", "DB"}]
        # RFC3339 with Z suffix to avoid client timezone ambiguity
        updated_at = datetime.now(timezone.utc).isoformat().replace("+00:00", "Z")
        out = {
            "season": season,
            "updated_at": updated_at,
            "providers": providers,
            "index": index_stats,
            "items": filtered_items,
            "total": len(filtered_items)
        }
        with open(_master_path(season), "w") as f:
            json.dump(out, f, indent=2)
        return {"status": "ok", "season": season, "total": out["total"]}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to refresh master players: {e}")


@router.get("/players")
async def get_master_players(
    season: int = Query(..., description="Season year"),
) -> Dict[str, Any]:
    path = _master_path(season)
    if not os.path.exists(path):
        raise HTTPException(status_code=404, detail="Master player list not built yet. Click refresh to build it.")
    try:
        with open(path, "r") as f:
            data = json.load(f)
        # Safety filter: exclude IDP if present in older cache files
        items = data.get("items", [])
        def _norm_pos(p):
            pos = str(p.get("position", ""))
            # handle Enum string like 'PlayerPosition.LB'
            if "." in pos:
                pos = pos.split(".")[-1]
            return pos.upper()
        filtered = [p for p in items if _norm_pos(p) not in {"DL", "LB", "DB"}]
        if len(filtered) != len(items):
            data["items"] = filtered
            data["total"] = len(filtered)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read master players: {e}")

