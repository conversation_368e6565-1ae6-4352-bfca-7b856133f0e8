"""
Alert and notification API endpoints.
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from ..core.database import get_db
from ..services.alert_service import AlertService
from ..models.alert import <PERSON><PERSON>, AlertSchedule, PlayerNewsAlert, AlertType, AlertPriority


router = APIRouter(prefix="/alerts", tags=["alerts"])


# Pydantic models for API
class AlertResponse(BaseModel):
    """Response model for alerts."""
    id: str
    alert_type: str
    priority: str
    status: str
    title: str
    message: str
    league_id: Optional[str] = None
    franchise_id: Optional[str] = None
    player_id: Optional[str] = None
    scheduled_for: Optional[datetime] = None
    sent_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    delivery_methods: List[str]
    alert_data: dict
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AlertScheduleResponse(BaseModel):
    """Response model for alert schedules."""
    id: str
    name: str
    alert_type: str
    league_id: Optional[str] = None
    franchise_id: Optional[str] = None
    target_datetime: datetime
    advance_notice_hours: List[int]
    is_active: bool
    last_processed: Optional[datetime] = None
    schedule_data: dict
    created_at: datetime

    class Config:
        from_attributes = True


class PlayerNewsResponse(BaseModel):
    """Response model for player news alerts."""
    id: str
    player_id: str
    headline: str
    content: str
    source: str
    impact_score: Optional[float] = None
    affected_positions: List[str]
    fantasy_impact: Optional[str] = None
    is_processed: bool
    alerts_generated: int
    created_at: datetime

    class Config:
        from_attributes = True


class CreateAlertRequest(BaseModel):
    """Request model for creating alerts."""
    alert_type: AlertType
    title: str = Field(..., min_length=1, max_length=255)
    message: str = Field(..., min_length=1)
    priority: AlertPriority = AlertPriority.MEDIUM
    league_id: Optional[str] = None
    franchise_id: Optional[str] = None
    player_id: Optional[str] = None
    scheduled_for: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    delivery_methods: Optional[List[str]] = None
    alert_data: Optional[dict] = None


class CreateAlertScheduleRequest(BaseModel):
    """Request model for creating alert schedules."""
    name: str = Field(..., min_length=1, max_length=255)
    alert_type: AlertType
    target_datetime: datetime
    advance_notice_hours: List[int] = Field(..., min_length=1)
    league_id: Optional[str] = None
    franchise_id: Optional[str] = None
    schedule_data: Optional[dict] = None


class CreatePlayerNewsRequest(BaseModel):
    """Request model for creating player news alerts."""
    player_id: str
    headline: str = Field(..., min_length=1, max_length=500)
    content: str = Field(..., min_length=1)
    source: str = Field(..., min_length=1, max_length=255)
    impact_score: Optional[float] = Field(None, ge=0, le=10)
    affected_positions: Optional[List[str]] = None
    fantasy_impact: Optional[str] = None


class AlertSummary(BaseModel):
    """Summary of alerts by priority."""
    urgent: int
    high: int
    medium: int
    low: int
    total: int


@router.get("/", response_model=List[AlertResponse])
async def get_alerts(
    franchise_id: Optional[str] = Query(None, description="Filter by franchise ID"),
    league_id: Optional[str] = Query(None, description="Filter by league ID"),
    alert_type: Optional[AlertType] = Query(None, description="Filter by alert type"),
    priority: Optional[AlertPriority] = Query(None, description="Filter by priority"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of alerts to return"),
    db: Session = Depends(get_db)
) -> List[AlertResponse]:
    """
    Get active alerts with optional filtering.
    
    Returns alerts that are pending or sent and not expired.
    """
    try:
        alert_service = AlertService(db)
        alerts = alert_service.get_active_alerts(
            franchise_id=franchise_id,
            league_id=league_id,
            limit=limit
        )
        
        # Apply additional filters
        if alert_type:
            alerts = [a for a in alerts if a.alert_type == alert_type]
        if priority:
            alerts = [a for a in alerts if a.priority == priority]
        
        return [AlertResponse.model_validate(alert) for alert in alerts]
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get alerts: {str(e)}")


@router.get("/summary", response_model=AlertSummary)
async def get_alert_summary(
    franchise_id: Optional[str] = Query(None, description="Filter by franchise ID"),
    league_id: Optional[str] = Query(None, description="Filter by league ID"),
    db: Session = Depends(get_db)
) -> AlertSummary:
    """
    Get a summary of active alerts by priority.
    """
    try:
        alert_service = AlertService(db)
        alerts = alert_service.get_active_alerts(
            franchise_id=franchise_id,
            league_id=league_id,
            limit=1000  # Get all for summary
        )
        
        summary = {
            "urgent": 0,
            "high": 0,
            "medium": 0,
            "low": 0,
            "total": len(alerts)
        }
        
        for alert in alerts:
            priority_key = alert.priority.value.lower()
            if priority_key in summary:
                summary[priority_key] += 1
        
        return AlertSummary(**summary)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get alert summary: {str(e)}")


@router.post("/", response_model=AlertResponse)
async def create_alert(
    request: CreateAlertRequest,
    db: Session = Depends(get_db)
) -> AlertResponse:
    """
    Create a new alert.
    """
    try:
        alert_service = AlertService(db)
        alert = alert_service.create_alert(
            alert_type=request.alert_type,
            title=request.title,
            message=request.message,
            priority=request.priority,
            league_id=request.league_id,
            franchise_id=request.franchise_id,
            player_id=request.player_id,
            scheduled_for=request.scheduled_for,
            expires_at=request.expires_at,
            delivery_methods=request.delivery_methods,
            alert_data=request.alert_data
        )
        
        return AlertResponse.model_validate(alert)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create alert: {str(e)}")


@router.post("/{alert_id}/deliver")
async def mark_alert_delivered(
    alert_id: str,
    db: Session = Depends(get_db)
) -> dict:
    """
    Mark an alert as delivered.
    """
    try:
        alert_service = AlertService(db)
        success = alert_service.mark_alert_delivered(alert_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        return {"message": "Alert marked as delivered", "alert_id": alert_id}
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to mark alert as delivered: {str(e)}")


@router.post("/{alert_id}/dismiss")
async def dismiss_alert(
    alert_id: str,
    db: Session = Depends(get_db)
) -> dict:
    """
    Dismiss an alert.
    """
    try:
        alert_service = AlertService(db)
        success = alert_service.dismiss_alert(alert_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        return {"message": "Alert dismissed", "alert_id": alert_id}
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to dismiss alert: {str(e)}")


@router.get("/schedules", response_model=List[AlertScheduleResponse])
async def get_alert_schedules(
    league_id: Optional[str] = Query(None, description="Filter by league ID"),
    franchise_id: Optional[str] = Query(None, description="Filter by franchise ID"),
    active_only: bool = Query(True, description="Only return active schedules"),
    db: Session = Depends(get_db)
) -> List[AlertScheduleResponse]:
    """
    Get alert schedules with optional filtering.
    """
    try:
        query = db.query(AlertSchedule)
        
        if active_only:
            query = query.filter(AlertSchedule.is_active == True)
        if league_id:
            query = query.filter(AlertSchedule.league_id == league_id)
        if franchise_id:
            query = query.filter(AlertSchedule.franchise_id == franchise_id)
        
        schedules = query.order_by(AlertSchedule.target_datetime).all()
        return [AlertScheduleResponse.model_validate(schedule) for schedule in schedules]
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get alert schedules: {str(e)}")


@router.post("/schedules", response_model=AlertScheduleResponse)
async def create_alert_schedule(
    request: CreateAlertScheduleRequest,
    db: Session = Depends(get_db)
) -> AlertScheduleResponse:
    """
    Create a new alert schedule.
    """
    try:
        alert_service = AlertService(db)
        schedule = alert_service.create_alert_schedule(
            name=request.name,
            alert_type=request.alert_type,
            target_datetime=request.target_datetime,
            advance_notice_hours=request.advance_notice_hours,
            league_id=request.league_id,
            franchise_id=request.franchise_id,
            schedule_data=request.schedule_data
        )
        
        return AlertScheduleResponse.model_validate(schedule)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create alert schedule: {str(e)}")


@router.get("/news", response_model=List[PlayerNewsResponse])
async def get_player_news(
    player_id: Optional[str] = Query(None, description="Filter by player ID"),
    processed_only: bool = Query(False, description="Only return processed news"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of news items to return"),
    db: Session = Depends(get_db)
) -> List[PlayerNewsResponse]:
    """
    Get player news alerts with optional filtering.
    """
    try:
        query = db.query(PlayerNewsAlert)
        
        if player_id:
            query = query.filter(PlayerNewsAlert.player_id == player_id)
        if processed_only:
            query = query.filter(PlayerNewsAlert.is_processed == True)
        
        news_items = query.order_by(PlayerNewsAlert.created_at.desc()).limit(limit).all()
        return [PlayerNewsResponse.model_validate(news) for news in news_items]
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get player news: {str(e)}")


@router.post("/news", response_model=PlayerNewsResponse)
async def create_player_news(
    request: CreatePlayerNewsRequest,
    db: Session = Depends(get_db)
) -> PlayerNewsResponse:
    """
    Create a new player news alert.
    """
    try:
        alert_service = AlertService(db)
        news = alert_service.create_player_news_alert(
            player_id=request.player_id,
            headline=request.headline,
            content=request.content,
            source=request.source,
            impact_score=request.impact_score,
            affected_positions=request.affected_positions,
            fantasy_impact=request.fantasy_impact
        )
        
        return PlayerNewsResponse.model_validate(news)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create player news: {str(e)}")


@router.post("/process-deadlines")
async def process_deadline_monitoring(
    db: Session = Depends(get_db)
) -> dict:
    """
    Process all deadline monitoring schedules and generate alerts.
    
    This endpoint can be called by a scheduler or manually to check
    for upcoming deadlines and create appropriate alerts.
    """
    try:
        alert_service = AlertService(db)
        alerts_created = alert_service.process_deadline_monitoring()
        
        return {
            "message": "Deadline monitoring processed",
            "alerts_created": len(alerts_created),
            "alert_ids": [alert.id for alert in alerts_created]
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to process deadline monitoring: {str(e)}")


@router.post("/process-news")
async def process_player_news(
    db: Session = Depends(get_db)
) -> dict:
    """
    Process unprocessed player news and generate alerts.
    
    This endpoint can be called by a scheduler or manually to process
    player news and create alerts for affected franchises.
    """
    try:
        alert_service = AlertService(db)
        alerts_created = alert_service.process_player_news()
        
        return {
            "message": "Player news processed",
            "alerts_created": len(alerts_created),
            "alert_ids": [alert.id for alert in alerts_created]
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to process player news: {str(e)}")