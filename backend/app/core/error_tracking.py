"""
Error tracking and alerting system.
"""
import traceback
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from collections import defaultdict, deque
from dataclasses import dataclass
from enum import Enum
import threading

from .logging import get_logger, LoggerMixin
from .metrics import increment_counter

logger = get_logger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorEvent:
    """Represents an error event."""
    id: str
    error_type: str
    message: str
    severity: ErrorSeverity
    timestamp: datetime
    context: Dict[str, Any]
    stack_trace: Optional[str] = None
    count: int = 1


class ErrorTracker(LoggerMixin):
    """Tracks and aggregates errors for monitoring and alerting."""
    
    def __init__(self, max_events: int = 1000):
        self.max_events = max_events
        self.errors: deque = deque(maxlen=max_events)
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.error_rates: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self._lock = threading.Lock()
        self.alert_handlers: List[Callable[[ErrorEvent], None]] = []
    
    def track_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM
    ) -> str:
        """Track an error event."""
        error_id = f"{type(error).__name__}_{hash(str(error)) % 10000}"
        error_type = type(error).__name__
        message = str(error)
        timestamp = datetime.utcnow()
        
        # Get stack trace
        stack_trace = traceback.format_exc() if hasattr(error, '__traceback__') else None
        
        with self._lock:
            # Check if this is a duplicate error
            existing_error = None
            for err in reversed(self.errors):
                if (err.error_type == error_type and 
                    err.message == message and 
                    (timestamp - err.timestamp).total_seconds() < 60):  # Within 1 minute
                    existing_error = err
                    break
            
            if existing_error:
                # Update existing error
                existing_error.count += 1
                existing_error.timestamp = timestamp
                error_event = existing_error
            else:
                # Create new error event
                error_event = ErrorEvent(
                    id=error_id,
                    error_type=error_type,
                    message=message,
                    severity=severity,
                    timestamp=timestamp,
                    context=context or {},
                    stack_trace=stack_trace
                )
                self.errors.append(error_event)
            
            # Update counters
            self.error_counts[error_type] += 1
            self.error_rates[error_type].append(timestamp)
            
            # Update metrics
            increment_counter("errors_total", labels={
                "error_type": error_type,
                "severity": severity.value
            })
        
        # Log the error
        self.log_error(
            f"Error tracked: {error_type}",
            error_id=error_id,
            error_type=error_type,
            error_message=message,
            severity=severity.value,
            count=error_event.count,
            **context or {}
        )
        
        # Check for alerts
        self._check_alert_conditions(error_event)
        
        return error_id
    
    def _check_alert_conditions(self, error_event: ErrorEvent):
        """Check if error conditions warrant an alert."""
        with self._lock:
            # Alert on critical errors immediately
            if error_event.severity == ErrorSeverity.CRITICAL:
                self._trigger_alert(error_event, "Critical error occurred")
                return
            
            # Alert on high frequency of same error
            if error_event.count >= 5:
                self._trigger_alert(error_event, f"Error occurred {error_event.count} times")
                return
            
            # Alert on high error rate
            error_type = error_event.error_type
            recent_errors = [
                ts for ts in self.error_rates[error_type]
                if (datetime.utcnow() - ts).total_seconds() < 300  # Last 5 minutes
            ]
            
            if len(recent_errors) >= 10:
                self._trigger_alert(error_event, f"High error rate: {len(recent_errors)} errors in 5 minutes")
    
    def _trigger_alert(self, error_event: ErrorEvent, alert_message: str):
        """Trigger an alert for an error event."""
        self.log_warning(
            f"Alert triggered: {alert_message}",
            error_id=error_event.id,
            error_type=error_event.error_type,
            alert_message=alert_message
        )
        
        # Call registered alert handlers
        for handler in self.alert_handlers:
            try:
                handler(error_event)
            except Exception as e:
                self.log_error(f"Alert handler failed: {str(e)}")
    
    def add_alert_handler(self, handler: Callable[[ErrorEvent], None]):
        """Add an alert handler function."""
        self.alert_handlers.append(handler)
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get error summary for the specified time period."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        with self._lock:
            recent_errors = [err for err in self.errors if err.timestamp >= cutoff_time]
            
            # Group by error type
            error_types = defaultdict(list)
            for err in recent_errors:
                error_types[err.error_type].append(err)
            
            # Calculate statistics
            summary = {
                "total_errors": len(recent_errors),
                "unique_error_types": len(error_types),
                "time_period_hours": hours,
                "error_types": {}
            }
            
            for error_type, errors in error_types.items():
                total_count = sum(err.count for err in errors)
                latest_error = max(errors, key=lambda x: x.timestamp)
                
                summary["error_types"][error_type] = {
                    "count": total_count,
                    "unique_occurrences": len(errors),
                    "latest_message": latest_error.message,
                    "latest_timestamp": latest_error.timestamp.isoformat() + "Z",
                    "severity_distribution": {
                        severity.value: sum(1 for err in errors if err.severity == severity)
                        for severity in ErrorSeverity
                    }
                }
            
            return summary
    
    def get_recent_errors(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent errors."""
        with self._lock:
            recent_errors = list(self.errors)[-limit:]
            
            return [
                {
                    "id": err.id,
                    "error_type": err.error_type,
                    "message": err.message,
                    "severity": err.severity.value,
                    "timestamp": err.timestamp.isoformat() + "Z",
                    "count": err.count,
                    "context": err.context,
                    "has_stack_trace": err.stack_trace is not None
                }
                for err in reversed(recent_errors)
            ]
    
    def get_error_details(self, error_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific error."""
        with self._lock:
            for err in self.errors:
                if err.id == error_id:
                    return {
                        "id": err.id,
                        "error_type": err.error_type,
                        "message": err.message,
                        "severity": err.severity.value,
                        "timestamp": err.timestamp.isoformat() + "Z",
                        "count": err.count,
                        "context": err.context,
                        "stack_trace": err.stack_trace
                    }
            return None
    
    def clear_errors(self):
        """Clear all tracked errors (useful for testing)."""
        with self._lock:
            self.errors.clear()
            self.error_counts.clear()
            self.error_rates.clear()
            self.log_info("All tracked errors cleared")


# Global error tracker instance
error_tracker = ErrorTracker()


# Convenience functions
def track_error(
    error: Exception,
    context: Optional[Dict[str, Any]] = None,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
) -> str:
    """Track an error using the global error tracker."""
    return error_tracker.track_error(error, context, severity)


def get_error_summary(hours: int = 24) -> Dict[str, Any]:
    """Get error summary using the global error tracker."""
    return error_tracker.get_error_summary(hours)


# Alert handler for logging critical errors
def log_critical_error_alert(error_event: ErrorEvent):
    """Alert handler that logs critical errors."""
    logger.critical(
        f"CRITICAL ERROR ALERT: {error_event.error_type} - {error_event.message}",
        extra={
            "error_id": error_event.id,
            "error_type": error_event.error_type,
            "severity": error_event.severity.value,
            "count": error_event.count,
            "context": error_event.context
        }
    )


# Register default alert handlers
error_tracker.add_alert_handler(log_critical_error_alert)