from pydantic_settings import BaseSettings
from pydantic import ConfigDict
from typing import Optional

class Settings(BaseSettings):
    model_config = ConfigDict(env_file=".env", extra="ignore")
    
    # Database
    database_url: str = "postgresql://fantasy:fantasy@localhost:5432/fantasy_db"
    database_url_dev: str = "sqlite:///./fantasy.db"
    
    # Redis
    redis_url: str = "redis://localhost:6379"
    
    # MFL API
    mfl_api_key: Optional[str] = None
    mfl_league_id: Optional[str] = None
    
    # Environment
    environment: str = "development"
    DEBUG: bool = True

settings = Settings()