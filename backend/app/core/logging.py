"""
Structured logging configuration for the AI Fantasy Assistant.
"""
import logging
import logging.config
import sys
import json
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

from .config import settings


class StructuredFormatter(logging.Formatter):
    """Custom formatter that outputs structured JSON logs."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields from record
        for key, value in record.__dict__.items():
            if key not in {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                'filename', 'module', 'lineno', 'funcName', 'created',
                'msecs', 'relativeCreated', 'thread', 'threadName',
                'processName', 'process', 'getMessage', 'exc_info',
                'exc_text', 'stack_info'
            }:
                log_entry[key] = value
        
        return json.dumps(log_entry, default=str)


class ContextFilter(logging.Filter):
    """Filter that adds contextual information to log records."""
    
    def __init__(self, service_name: str = "ai-fantasy-assistant"):
        super().__init__()
        self.service_name = service_name
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add context to log record."""
        record.service = self.service_name
        record.environment = getattr(settings, 'ENVIRONMENT', 'development')
        
        # Add request ID if available (would be set by middleware)
        if hasattr(record, 'request_id'):
            record.request_id = getattr(record, 'request_id', None)
        
        return True


def setup_logging(
    log_level: str = "INFO",
    log_format: str = "structured",
    log_file: Optional[str] = None
) -> None:
    """
    Set up application logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Format type ('structured' for JSON, 'simple' for human-readable)
        log_file: Optional file path for log output
    """
    
    # Create logs directory if it doesn't exist
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure formatters
    formatters = {
        "structured": {
            "()": StructuredFormatter,
        },
        "simple": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        }
    }
    
    # Configure handlers
    handlers = {
        "console": {
            "class": "logging.StreamHandler",
            "level": log_level,
            "formatter": log_format,
            "stream": sys.stdout,
            "filters": ["context"]
        }
    }
    
    # Add file handler if specified
    if log_file:
        handlers["file"] = {
            "class": "logging.handlers.RotatingFileHandler",
            "level": log_level,
            "formatter": "structured",  # Always use structured format for files
            "filename": log_file,
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "filters": ["context"]
        }
    
    # Configure filters
    filters = {
        "context": {
            "()": ContextFilter,
            "service_name": "ai-fantasy-assistant"
        }
    }
    
    # Configure loggers
    loggers = {
        "": {  # Root logger
            "level": log_level,
            "handlers": list(handlers.keys()),
            "propagate": False
        },
        "app": {
            "level": log_level,
            "handlers": list(handlers.keys()),
            "propagate": False
        },
        "celery": {
            "level": "INFO",
            "handlers": list(handlers.keys()),
            "propagate": False
        },
        "sqlalchemy.engine": {
            "level": "WARNING",  # Reduce SQL query noise
            "handlers": list(handlers.keys()),
            "propagate": False
        },
        "uvicorn": {
            "level": "INFO",
            "handlers": list(handlers.keys()),
            "propagate": False
        }
    }
    
    # Apply configuration
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": formatters,
        "filters": filters,
        "handlers": handlers,
        "loggers": loggers
    }
    
    logging.config.dictConfig(logging_config)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the specified name."""
    return logging.getLogger(name)


def log_with_context(
    logger: logging.Logger,
    level: int,
    message: str,
    **context: Any
) -> None:
    """
    Log a message with additional context.
    
    Args:
        logger: Logger instance
        level: Log level (logging.DEBUG, logging.INFO, etc.)
        message: Log message
        **context: Additional context to include in log
    """
    # Create a log record with extra context
    extra = {f"ctx_{key}": value for key, value in context.items()}
    logger.log(level, message, extra=extra)


class LoggerMixin:
    """Mixin class to add logging capabilities to other classes."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class."""
        return get_logger(self.__class__.__module__ + "." + self.__class__.__name__)
    
    def log_info(self, message: str, **context: Any) -> None:
        """Log info message with context."""
        log_with_context(self.logger, logging.INFO, message, **context)
    
    def log_warning(self, message: str, **context: Any) -> None:
        """Log warning message with context."""
        log_with_context(self.logger, logging.WARNING, message, **context)
    
    def log_error(self, message: str, **context: Any) -> None:
        """Log error message with context."""
        log_with_context(self.logger, logging.ERROR, message, **context)
    
    def log_debug(self, message: str, **context: Any) -> None:
        """Log debug message with context."""
        log_with_context(self.logger, logging.DEBUG, message, **context)


# Performance logging utilities
class PerformanceLogger:
    """Utility for logging performance metrics."""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def log_operation_time(
        self,
        operation: str,
        duration_ms: float,
        **context: Any
    ) -> None:
        """Log operation timing."""
        log_with_context(
            self.logger,
            logging.INFO,
            f"Operation completed: {operation}",
            operation=operation,
            duration_ms=duration_ms,
            **context
        )
    
    def log_query_performance(
        self,
        query_type: str,
        duration_ms: float,
        rows_affected: Optional[int] = None,
        **context: Any
    ) -> None:
        """Log database query performance."""
        log_with_context(
            self.logger,
            logging.INFO,
            f"Database query: {query_type}",
            query_type=query_type,
            duration_ms=duration_ms,
            rows_affected=rows_affected,
            **context
        )


# Error tracking utilities
class ErrorTracker:
    """Utility for tracking and logging errors."""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def track_error(
        self,
        error: Exception,
        operation: str,
        **context: Any
    ) -> None:
        """Track an error with context."""
        log_with_context(
            self.logger,
            logging.ERROR,
            f"Error in {operation}: {str(error)}",
            operation=operation,
            error_type=type(error).__name__,
            error_message=str(error),
            **context
        )
    
    def track_validation_error(
        self,
        field: str,
        value: Any,
        error_message: str,
        **context: Any
    ) -> None:
        """Track a validation error."""
        log_with_context(
            self.logger,
            logging.WARNING,
            f"Validation error for field {field}: {error_message}",
            field=field,
            value=str(value),
            error_message=error_message,
            **context
        )


# Initialize logging on module import
def init_logging():
    """Initialize logging with default configuration."""
    import os
    # Read directly from environment to respect exported variables even if not in Settings
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    log_format = os.getenv('LOG_FORMAT', 'structured')
    log_file = os.getenv('LOG_FILE', None)
    
    setup_logging(
        log_level=log_level,
        log_format=log_format,
        log_file=log_file
    )


# Auto-initialize logging
init_logging()