"""
Health check system for the AI Fantasy Assistant.
"""
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass
from sqlalchemy import text
from sqlalchemy.orm import Session

from .database import SessionLocal
from .logging import get_logger, LoggerMixin

logger = get_logger(__name__)


class HealthStatus(Enum):
    """Health check status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check."""
    name: str
    status: HealthStatus
    message: str
    duration_ms: float
    timestamp: datetime
    details: Optional[Dict[str, Any]] = None


class HealthChecker(LoggerMixin):
    """Base class for health checkers."""
    
    def __init__(self, name: str, timeout: float = 10.0):
        self.name = name
        self.timeout = timeout
    
    async def check(self) -> HealthCheckResult:
        """Perform the health check."""
        start_time = time.time()
        timestamp = datetime.utcnow()
        
        try:
            result = await asyncio.wait_for(
                self._perform_check(),
                timeout=self.timeout
            )
            duration_ms = (time.time() - start_time) * 1000
            
            return HealthCheckResult(
                name=self.name,
                status=result.get("status", HealthStatus.UNKNOWN),
                message=result.get("message", "Check completed"),
                duration_ms=duration_ms,
                timestamp=timestamp,
                details=result.get("details")
            )
            
        except asyncio.TimeoutError:
            duration_ms = (time.time() - start_time) * 1000
            self.log_error(f"Health check timeout: {self.name}")
            
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"Health check timed out after {self.timeout}s",
                duration_ms=duration_ms,
                timestamp=timestamp
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            self.log_error(f"Health check failed: {self.name}", error=str(e))
            
            return HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp=timestamp
            )
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Override this method to implement the actual health check."""
        raise NotImplementedError


class DatabaseHealthChecker(HealthChecker):
    """Health checker for database connectivity."""
    
    def __init__(self):
        super().__init__("database", timeout=5.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check database connectivity."""
        db = SessionLocal()
        try:
            # Simple query to test connection
            result = db.execute(text("SELECT 1 as health_check"))
            row = result.fetchone()
            
            if row and row[0] == 1:
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": "Database connection successful",
                    "details": {"connection_pool": "active"}
                }
            else:
                return {
                    "status": HealthStatus.UNHEALTHY,
                    "message": "Database query returned unexpected result"
                }
                
        except Exception as e:
            return {
                "status": HealthStatus.UNHEALTHY,
                "message": f"Database connection failed: {str(e)}"
            }
        finally:
            db.close()


class RedisHealthChecker(HealthChecker):
    """Health checker for Redis connectivity."""
    
    def __init__(self):
        super().__init__("redis", timeout=5.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check Redis connectivity."""
        try:
            import redis
            from urllib.parse import urlparse
            from ..core.config import settings
            
            # Parse REDIS_URL from settings (e.g., redis://:password@host:port/db)
            parsed = urlparse(settings.redis_url)
            host = parsed.hostname or 'localhost'
            port = parsed.port or 6379
            # parsed.path like '/0'; handle empty path gracefully
            db_str = (parsed.path or '/0').lstrip('/') or '0'
            db = int(db_str) if db_str.isdigit() else 0
            password = parsed.password
            
            # Create Redis client
            redis_client = redis.Redis(
                host=host,
                port=port,
                db=db,
                password=password,
                decode_responses=True
            )
            
            # Test connection with ping
            response = redis_client.ping()
            
            if response:
                # Get some basic info
                info = redis_client.info()
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": "Redis connection successful",
                    "details": {
                        "connected_clients": info.get("connected_clients", 0),
                        "used_memory_human": info.get("used_memory_human", "unknown")
                    }
                }
            else:
                return {
                    "status": HealthStatus.UNHEALTHY,
                    "message": "Redis ping failed"
                }
                
        except ImportError:
            return {
                "status": HealthStatus.UNKNOWN,
                "message": "Redis client not available"
            }
        except Exception as e:
            return {
                "status": HealthStatus.UNHEALTHY,
                "message": f"Redis connection failed: {str(e)}"
            }


class CeleryHealthChecker(HealthChecker):
    """Health checker for Celery workers."""
    
    def __init__(self):
        super().__init__("celery", timeout=10.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check Celery worker health."""
        try:
            from ..core.celery import celery_app
            
            # Check if workers are available
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            
            if not stats:
                return {
                    "status": HealthStatus.UNHEALTHY,
                    "message": "No Celery workers available"
                }
            
            # Count active workers
            worker_count = len(stats)
            active_tasks = inspect.active()
            total_active = sum(len(tasks) for tasks in active_tasks.values()) if active_tasks else 0
            
            return {
                "status": HealthStatus.HEALTHY,
                "message": f"{worker_count} Celery workers available",
                "details": {
                    "worker_count": worker_count,
                    "active_tasks": total_active,
                    "workers": list(stats.keys())
                }
            }
            
        except Exception as e:
            return {
                "status": HealthStatus.UNHEALTHY,
                "message": f"Celery health check failed: {str(e)}"
            }


class DiskSpaceHealthChecker(HealthChecker):
    """Health checker for disk space."""
    
    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 90.0):
        super().__init__("disk_space", timeout=5.0)
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check disk space usage."""
        try:
            import shutil
            
            # Get disk usage for current directory
            total, used, free = shutil.disk_usage(".")
            
            # Calculate percentage
            used_percent = (used / total) * 100
            
            # Determine status
            if used_percent >= self.critical_threshold:
                status = HealthStatus.UNHEALTHY
                message = f"Critical disk space usage: {used_percent:.1f}%"
            elif used_percent >= self.warning_threshold:
                status = HealthStatus.DEGRADED
                message = f"High disk space usage: {used_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Disk space usage: {used_percent:.1f}%"
            
            return {
                "status": status,
                "message": message,
                "details": {
                    "total_gb": round(total / (1024**3), 2),
                    "used_gb": round(used / (1024**3), 2),
                    "free_gb": round(free / (1024**3), 2),
                    "used_percent": round(used_percent, 1)
                }
            }
            
        except Exception as e:
            return {
                "status": HealthStatus.UNKNOWN,
                "message": f"Disk space check failed: {str(e)}"
            }


class MemoryHealthChecker(HealthChecker):
    """Health checker for memory usage."""
    
    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 90.0):
        super().__init__("memory", timeout=5.0)
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Check memory usage."""
        try:
            import psutil
            
            # Get memory info
            memory = psutil.virtual_memory()
            
            # Determine status
            if memory.percent >= self.critical_threshold:
                status = HealthStatus.UNHEALTHY
                message = f"Critical memory usage: {memory.percent:.1f}%"
            elif memory.percent >= self.warning_threshold:
                status = HealthStatus.DEGRADED
                message = f"High memory usage: {memory.percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Memory usage: {memory.percent:.1f}%"
            
            return {
                "status": status,
                "message": message,
                "details": {
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "used_percent": round(memory.percent, 1)
                }
            }
            
        except ImportError:
            return {
                "status": HealthStatus.UNKNOWN,
                "message": "psutil not available for memory monitoring"
            }
        except Exception as e:
            return {
                "status": HealthStatus.UNKNOWN,
                "message": f"Memory check failed: {str(e)}"
            }


class HealthCheckManager(LoggerMixin):
    """Manager for running multiple health checks."""
    
    def __init__(self):
        self.checkers: List[HealthChecker] = []
        self._register_default_checkers()
    
    def _register_default_checkers(self):
        """Register default health checkers."""
        self.checkers = [
            DatabaseHealthChecker(),
            RedisHealthChecker(),
            CeleryHealthChecker(),
            DiskSpaceHealthChecker(),
            MemoryHealthChecker()
        ]
    
    def add_checker(self, checker: HealthChecker):
        """Add a custom health checker."""
        self.checkers.append(checker)
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks and return results."""
        start_time = time.time()
        
        # Run all checks concurrently
        tasks = [checker.check() for checker in self.checkers]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        check_results = {}
        overall_status = HealthStatus.HEALTHY
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # Handle exceptions from individual checks
                checker_name = self.checkers[i].name
                check_results[checker_name] = HealthCheckResult(
                    name=checker_name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Health check exception: {str(result)}",
                    duration_ms=0,
                    timestamp=datetime.utcnow()
                )
                overall_status = HealthStatus.UNHEALTHY
            else:
                check_results[result.name] = result
                
                # Update overall status
                if result.status == HealthStatus.UNHEALTHY:
                    overall_status = HealthStatus.UNHEALTHY
                elif result.status == HealthStatus.DEGRADED and overall_status == HealthStatus.HEALTHY:
                    overall_status = HealthStatus.DEGRADED
        
        total_duration = (time.time() - start_time) * 1000
        
        # Build response
        response = {
            "status": overall_status.value,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "duration_ms": round(total_duration, 2),
            "checks": {
                name: {
                    "status": result.status.value,
                    "message": result.message,
                    "duration_ms": round(result.duration_ms, 2),
                    "details": result.details
                }
                for name, result in check_results.items()
            }
        }
        
        # Log overall health status
        self.log_info(
            f"Health check completed: {overall_status.value}",
            overall_status=overall_status.value,
            duration_ms=total_duration,
            failed_checks=[name for name, result in check_results.items() 
                          if result.status == HealthStatus.UNHEALTHY]
        )
        
        return response
    
    async def run_check(self, checker_name: str) -> Optional[HealthCheckResult]:
        """Run a specific health check by name."""
        for checker in self.checkers:
            if checker.name == checker_name:
                return await checker.check()
        return None


# Global health check manager instance
health_manager = HealthCheckManager()