"""
Sample data creation for development and testing.
"""
from decimal import Decimal
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.models import (
    League, Player, PlayerPosition, InjuryStatus, Fran<PERSON>se, Roster, 
    RosterPlayer, Projection, Recommendation, RecommendationType, 
    RecommendationPriority
)


def create_sample_data(db: Session) -> None:
    """Create sample data for development and testing."""
    
    # Check if data already exists
    if db.query(League).first():
        print("Sample data already exists, skipping creation.")
        return
    
    print("Creating sample data...")
    
    # Create sample league
    league = League(
        id="sample_league_2024",
        name="Sample Fantasy League",
        season=2024,
        scoring_rules={
            "passing_yards": 0.04,
            "passing_touchdowns": 4,
            "rushing_yards": 0.1,
            "rushing_touchdowns": 6,
            "receiving_yards": 0.1,
            "receiving_touchdowns": 6,
            "interceptions": -2,
            "fumbles": -2
        },
        roster_slots=[
            {"position": "QB", "count": 1, "type": "starter"},
            {"position": "RB", "count": 2, "type": "starter"},
            {"position": "WR", "count": 2, "type": "starter"},
            {"position": "TE", "count": 1, "type": "starter"},
            {"position": "FLEX", "count": 1, "type": "starter"},
            {"position": "K", "count": 1, "type": "starter"},
            {"position": "DEF", "count": 1, "type": "starter"},
            {"position": "BENCH", "count": 6, "type": "bench"}
        ],
        keeper_rules={
            "max_keepers": 3,
            "round_escalation": True,
            "franchise_tag_allowed": True
        },
        mfl_league_id="12345",
        description="A sample league for development and testing"
    )
    db.add(league)
    
    # Create sample players
    players = [
        Player(
            id="player_mahomes",
            name="Patrick Mahomes",
            position=PlayerPosition.QB,
            team="KC",
            bye_week=10,
            injury_status=InjuryStatus.HEALTHY,
            mfl_id="13604"
        ),
        Player(
            id="player_mccaffrey",
            name="Christian McCaffrey",
            position=PlayerPosition.RB,
            team="SF",
            bye_week=9,
            injury_status=InjuryStatus.HEALTHY,
            mfl_id="12110"
        ),
        Player(
            id="player_jefferson",
            name="Justin Jefferson",
            position=PlayerPosition.WR,
            team="MIN",
            bye_week=6,
            injury_status=InjuryStatus.HEALTHY,
            mfl_id="14881"
        ),
        Player(
            id="player_kelce",
            name="Travis Kelce",
            position=PlayerPosition.TE,
            team="KC",
            bye_week=10,
            injury_status=InjuryStatus.QUESTIONABLE,
            mfl_id="10404"
        ),
        Player(
            id="player_tucker",
            name="Justin Tucker",
            position=PlayerPosition.K,
            team="BAL",
            bye_week=14,
            injury_status=InjuryStatus.HEALTHY,
            mfl_id="10068"
        )
    ]
    
    for player in players:
        db.add(player)
    
    # Create sample franchises
    franchises = [
        Franchise(
            id="franchise_1",
            name="The Champions",
            owner_name="John Doe",
            league_id=league.id,
            mfl_franchise_id="0001",
            faab_budget=Decimal("100.00"),
            faab_spent=Decimal("25.50")
        ),
        Franchise(
            id="franchise_2", 
            name="Fantasy Gurus",
            owner_name="Jane Smith",
            league_id=league.id,
            mfl_franchise_id="0002",
            faab_budget=Decimal("100.00"),
            faab_spent=Decimal("15.00")
        )
    ]
    
    for franchise in franchises:
        db.add(franchise)
    
    # Create sample rosters
    roster1 = Roster(
        id="roster_1",
        franchise_id="franchise_1"
    )
    db.add(roster1)
    
    # Add players to roster
    roster_players = [
        RosterPlayer(
            id="rp_1_mahomes",
            roster_id=roster1.id,
            player_id="player_mahomes",
            roster_slot="QB",
            is_keeper=True,
            keeper_cost=3
        ),
        RosterPlayer(
            id="rp_1_mccaffrey",
            roster_id=roster1.id,
            player_id="player_mccaffrey",
            roster_slot="RB1",
            is_keeper=False
        )
    ]
    
    for rp in roster_players:
        db.add(rp)
    
    # Create sample projections
    projections = [
        Projection(
            id="proj_mahomes_season",
            player_id="player_mahomes",
            week=None,  # Season-long
            season=2024,
            source="FantasyPros",
            projected_points=Decimal("285.6"),
            floor=Decimal("260.0"),
            ceiling=Decimal("310.0"),
            confidence_level=Decimal("0.85"),
            stats={
                "passing_yards": 4200,
                "passing_touchdowns": 28,
                "rushing_yards": 180,
                "rushing_touchdowns": 2,
                "interceptions": 12
            }
        ),
        Projection(
            id="proj_mccaffrey_week1",
            player_id="player_mccaffrey",
            week=1,
            season=2024,
            source="ESPN",
            projected_points=Decimal("18.5"),
            floor=Decimal("12.0"),
            ceiling=Decimal("25.0"),
            confidence_level=Decimal("0.75"),
            stats={
                "rushing_yards": 85,
                "rushing_touchdowns": 1,
                "receiving_yards": 45,
                "receiving_touchdowns": 0
            }
        )
    ]
    
    for projection in projections:
        db.add(projection)
    
    # Create sample recommendations
    recommendations = [
        Recommendation(
            id="rec_keeper_mahomes",
            league_id=league.id,
            franchise_id="franchise_1",
            type=RecommendationType.KEEPER,
            priority=RecommendationPriority.HIGH,
            title="Keep Patrick Mahomes",
            description="Patrick Mahomes should be kept as your QB1 for the 2024 season.",
            rationale="Mahomes provides excellent value at a 3rd round cost and is projected to be the #2 QB this season. His consistency and upside make him an ideal keeper candidate.",
            confidence=Decimal("0.92"),
            expected_impact=Decimal("15.5"),
            expires_at=datetime.now() + timedelta(days=30),
            recommendation_data={
                "player_id": "player_mahomes",
                "keeper_cost": 3,
                "projected_value": 285.6,
                "replacement_value": 245.2
            },
            alternatives=[
                {
                    "title": "Keep Christian McCaffrey instead",
                    "rationale": "McCaffrey offers RB1 upside but at a higher cost",
                    "confidence": 0.78
                }
            ]
        ),
        Recommendation(
            id="rec_lineup_week1",
            league_id=league.id,
            franchise_id="franchise_1",
            type=RecommendationType.LINEUP,
            priority=RecommendationPriority.MEDIUM,
            title="Week 1 Lineup Optimization",
            description="Start Christian McCaffrey at RB1 for Week 1.",
            rationale="McCaffrey has a favorable matchup against a weak run defense and is projected for 18.5 points with high floor.",
            confidence=Decimal("0.78"),
            expected_impact=Decimal("3.2"),
            deadline_at=datetime.now() + timedelta(days=2),
            recommendation_data={
                "week": 1,
                "position": "RB1",
                "player_id": "player_mccaffrey",
                "projected_points": 18.5
            }
        )
    ]
    
    for recommendation in recommendations:
        db.add(recommendation)
    
    print("Sample data created successfully!")


def clear_sample_data(db: Session) -> None:
    """Clear all sample data from the database."""
    print("Clearing sample data...")
    
    # Delete in reverse order of dependencies
    db.query(Recommendation).delete()
    db.query(Projection).delete()
    db.query(RosterPlayer).delete()
    db.query(Roster).delete()
    db.query(Franchise).delete()
    db.query(Player).delete()
    db.query(League).delete()
    
    db.commit()
    print("Sample data cleared successfully!")