"""
Metrics collection system for the AI Fantasy Assistant.
"""
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum

from .logging import get_logger, LoggerMixin

logger = get_logger(__name__)


class MetricType(Enum):
    """Types of metrics that can be collected."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class MetricValue:
    """A single metric value with timestamp."""
    value: Union[int, float]
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class MetricSummary:
    """Summary statistics for a metric."""
    name: str
    type: MetricType
    current_value: Union[int, float]
    total_samples: int
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    avg_value: Optional[float] = None
    last_updated: Optional[datetime] = None


class Metric(LoggerMixin):
    """Base class for metrics."""
    
    def __init__(self, name: str, metric_type: MetricType, description: str = ""):
        self.name = name
        self.type = metric_type
        self.description = description
        self.values: deque = deque(maxlen=1000)  # Keep last 1000 values
        self._lock = threading.Lock()
    
    def add_value(self, value: Union[int, float], labels: Optional[Dict[str, str]] = None):
        """Add a value to the metric."""
        with self._lock:
            metric_value = MetricValue(
                value=value,
                timestamp=datetime.utcnow(),
                labels=labels or {}
            )
            self.values.append(metric_value)
    
    def get_current_value(self) -> Optional[Union[int, float]]:
        """Get the most recent value."""
        with self._lock:
            if self.values:
                return self.values[-1].value
            return None
    
    def get_summary(self) -> MetricSummary:
        """Get summary statistics for this metric."""
        with self._lock:
            if not self.values:
                return MetricSummary(
                    name=self.name,
                    type=self.type,
                    current_value=0,
                    total_samples=0
                )
            
            values = [v.value for v in self.values]
            return MetricSummary(
                name=self.name,
                type=self.type,
                current_value=values[-1],
                total_samples=len(values),
                min_value=min(values),
                max_value=max(values),
                avg_value=sum(values) / len(values),
                last_updated=self.values[-1].timestamp
            )
    
    def get_values_since(self, since: datetime) -> List[MetricValue]:
        """Get all values since a specific timestamp."""
        with self._lock:
            return [v for v in self.values if v.timestamp >= since]


class Counter(Metric):
    """Counter metric that only increases."""
    
    def __init__(self, name: str, description: str = ""):
        super().__init__(name, MetricType.COUNTER, description)
        self._value = 0
    
    def increment(self, amount: Union[int, float] = 1, labels: Optional[Dict[str, str]] = None):
        """Increment the counter."""
        with self._lock:
            self._value += amount
            self.add_value(self._value, labels)
    
    def get_current_value(self) -> Union[int, float]:
        """Get current counter value."""
        with self._lock:
            return self._value


class Gauge(Metric):
    """Gauge metric that can increase or decrease."""
    
    def __init__(self, name: str, description: str = ""):
        super().__init__(name, MetricType.GAUGE, description)
        self._value = 0
    
    def set(self, value: Union[int, float], labels: Optional[Dict[str, str]] = None):
        """Set the gauge value."""
        with self._lock:
            self._value = value
            self.add_value(self._value, labels)
    
    def increment(self, amount: Union[int, float] = 1, labels: Optional[Dict[str, str]] = None):
        """Increment the gauge."""
        with self._lock:
            self._value += amount
            self.add_value(self._value, labels)
    
    def decrement(self, amount: Union[int, float] = 1, labels: Optional[Dict[str, str]] = None):
        """Decrement the gauge."""
        with self._lock:
            self._value -= amount
            self.add_value(self._value, labels)
    
    def get_current_value(self) -> Union[int, float]:
        """Get current gauge value."""
        with self._lock:
            return self._value


class Histogram(Metric):
    """Histogram metric for tracking distributions."""
    
    def __init__(self, name: str, description: str = "", buckets: Optional[List[float]] = None):
        super().__init__(name, MetricType.HISTOGRAM, description)
        self.buckets = buckets or [0.1, 0.5, 1.0, 2.5, 5.0, 10.0, float('inf')]
        self.bucket_counts = defaultdict(int)
        self.sum = 0
        self.count = 0
    
    def observe(self, value: float, labels: Optional[Dict[str, str]] = None):
        """Observe a value."""
        with self._lock:
            self.sum += value
            self.count += 1
            
            # Update bucket counts
            for bucket in self.buckets:
                if value <= bucket:
                    self.bucket_counts[bucket] += 1
            
            self.add_value(value, labels)
    
    def get_percentile(self, percentile: float) -> Optional[float]:
        """Get a percentile value (approximate)."""
        with self._lock:
            if not self.values:
                return None
            
            values = sorted([v.value for v in self.values])
            index = int(len(values) * percentile / 100)
            return values[min(index, len(values) - 1)]


class Timer(Metric):
    """Timer metric for measuring durations."""
    
    def __init__(self, name: str, description: str = ""):
        super().__init__(name, MetricType.TIMER, description)
        self.histogram = Histogram(f"{name}_duration", f"{description} (duration)")
    
    def time(self, labels: Optional[Dict[str, str]] = None):
        """Context manager for timing operations."""
        return TimerContext(self, labels)
    
    def record(self, duration: float, labels: Optional[Dict[str, str]] = None):
        """Record a duration."""
        self.histogram.observe(duration, labels)
        self.add_value(duration, labels)


class TimerContext:
    """Context manager for timing operations."""
    
    def __init__(self, timer: Timer, labels: Optional[Dict[str, str]] = None):
        self.timer = timer
        self.labels = labels
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            duration = time.time() - self.start_time
            self.timer.record(duration, self.labels)


class MetricsRegistry(LoggerMixin):
    """Registry for managing metrics."""
    
    def __init__(self):
        self.metrics: Dict[str, Metric] = {}
        self._lock = threading.Lock()
        self._setup_default_metrics()
    
    def _setup_default_metrics(self):
        """Set up default application metrics."""
        # API metrics
        self.register(Counter("http_requests_total", "Total HTTP requests"))
        self.register(Counter("http_requests_errors", "HTTP request errors"))
        self.register(Histogram("http_request_duration", "HTTP request duration"))
        
        # Database metrics
        self.register(Counter("db_queries_total", "Total database queries"))
        self.register(Counter("db_query_errors", "Database query errors"))
        self.register(Histogram("db_query_duration", "Database query duration"))
        
        # Background job metrics
        self.register(Counter("celery_tasks_total", "Total Celery tasks"))
        self.register(Counter("celery_tasks_failed", "Failed Celery tasks"))
        self.register(Gauge("celery_active_tasks", "Active Celery tasks"))
        self.register(Histogram("celery_task_duration", "Celery task duration"))
        
        # Business metrics
        self.register(Counter("recommendations_generated", "Recommendations generated"))
        self.register(Counter("data_ingestion_events", "Data ingestion events"))
        self.register(Gauge("active_leagues", "Active leagues"))
        self.register(Gauge("active_users", "Active users"))
    
    def register(self, metric: Metric):
        """Register a metric."""
        with self._lock:
            if metric.name in self.metrics:
                self.log_warning(f"Metric {metric.name} already registered, replacing")
            self.metrics[metric.name] = metric
            self.log_debug(f"Registered metric: {metric.name}")
    
    def get_metric(self, name: str) -> Optional[Metric]:
        """Get a metric by name."""
        with self._lock:
            return self.metrics.get(name)
    
    def get_counter(self, name: str) -> Optional[Counter]:
        """Get a counter metric."""
        metric = self.get_metric(name)
        return metric if isinstance(metric, Counter) else None
    
    def get_gauge(self, name: str) -> Optional[Gauge]:
        """Get a gauge metric."""
        metric = self.get_metric(name)
        return metric if isinstance(metric, Gauge) else None
    
    def get_histogram(self, name: str) -> Optional[Histogram]:
        """Get a histogram metric."""
        metric = self.get_metric(name)
        return metric if isinstance(metric, Histogram) else None
    
    def get_timer(self, name: str) -> Optional[Timer]:
        """Get a timer metric."""
        metric = self.get_metric(name)
        return metric if isinstance(metric, Timer) else None
    
    def get_all_metrics(self) -> Dict[str, MetricSummary]:
        """Get summaries of all metrics."""
        with self._lock:
            return {name: metric.get_summary() for name, metric in self.metrics.items()}
    
    def get_metrics_snapshot(self) -> Dict[str, Any]:
        """Get a snapshot of all current metric values."""
        with self._lock:
            snapshot = {
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "metrics": {}
            }
            
            for name, metric in self.metrics.items():
                summary = metric.get_summary()
                snapshot["metrics"][name] = {
                    "type": summary.type.value,
                    "current_value": summary.current_value,
                    "total_samples": summary.total_samples,
                    "min_value": summary.min_value,
                    "max_value": summary.max_value,
                    "avg_value": summary.avg_value,
                    "last_updated": summary.last_updated.isoformat() + "Z" if summary.last_updated else None
                }
            
            return snapshot
    
    def clear_metrics(self):
        """Clear all metrics (useful for testing)."""
        with self._lock:
            for metric in self.metrics.values():
                metric.values.clear()
            self.log_info("All metrics cleared")


# Global metrics registry
metrics_registry = MetricsRegistry()


# Convenience functions for common operations
def increment_counter(name: str, amount: Union[int, float] = 1, labels: Optional[Dict[str, str]] = None):
    """Increment a counter metric."""
    counter = metrics_registry.get_counter(name)
    if counter:
        counter.increment(amount, labels)


def set_gauge(name: str, value: Union[int, float], labels: Optional[Dict[str, str]] = None):
    """Set a gauge metric value."""
    gauge = metrics_registry.get_gauge(name)
    if gauge:
        gauge.set(value, labels)


def observe_histogram(name: str, value: float, labels: Optional[Dict[str, str]] = None):
    """Observe a value in a histogram."""
    histogram = metrics_registry.get_histogram(name)
    if histogram:
        histogram.observe(value, labels)


def time_operation(name: str, labels: Optional[Dict[str, str]] = None):
    """Time an operation."""
    timer = metrics_registry.get_timer(name)
    if timer:
        return timer.time(labels)
    else:
        # Return a no-op context manager if timer doesn't exist
        class NoOpTimer:
            def __enter__(self):
                return self
            def __exit__(self, exc_type, exc_val, exc_tb):
                pass
        return NoOpTimer()