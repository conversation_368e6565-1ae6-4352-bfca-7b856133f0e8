from celery import Celery
from celery.schedules import crontab
from app.core.config import settings
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

celery_app = Celery(
    "fantasy_assistant",
    broker=settings.redis_url,
    backend=settings.redis_url,
    include=["app.tasks.file_processing", "app.tasks.data_refresh", "app.tasks.alert_processing"]
)

# Configure Celery with enhanced settings
celery_app.conf.update(
    # Serialization
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    
    # Timezone
    timezone="UTC",
    enable_utc=True,
    
    # Task tracking and monitoring
    task_track_started=True,
    task_send_sent_event=True,
    worker_send_task_events=True,
    
    # Time limits
    task_time_limit=30 * 60,  # 30 minutes hard limit
    task_soft_time_limit=25 * 60,  # 25 minutes soft limit
    
    # Worker configuration
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=False,
    
    # Retry configuration
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_persistent=True,
    
    # Dead letter queue configuration
    task_routes={
        'app.tasks.*.failed': {'queue': 'failed'},
        'app.tasks.data_refresh.*': {'queue': 'data_refresh'},
        'app.tasks.alert_processing.*': {'queue': 'alerts'},
        'app.tasks.file_processing.*': {'queue': 'file_processing'},
    },
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'refresh-mfl-data': {
            'task': 'app.tasks.data_refresh.refresh_mfl_data',
            'schedule': crontab(minute=0, hour='*/6'),  # Every 6 hours
        },
        'process-player-news': {
            'task': 'app.tasks.alert_processing.process_player_news',
            'schedule': crontab(minute='*/15'),  # Every 15 minutes
        },
        'check-deadlines': {
            'task': 'app.tasks.alert_processing.check_upcoming_deadlines',
            'schedule': crontab(minute='*/30'),  # Every 30 minutes
        },
        'cleanup-old-tasks': {
            'task': 'app.tasks.data_refresh.cleanup_old_task_results',
            'schedule': crontab(minute=0, hour=2),  # Daily at 2 AM
        },
        'refresh-projections': {
            'task': 'app.tasks.data_refresh.refresh_projection_aggregates',
            'schedule': crontab(minute=0, hour='8,20'),  # 8 AM and 8 PM
        },
    },
    
    # Queue configuration
    task_default_queue='default',
    task_queues={
        'default': {
            'exchange': 'default',
            'routing_key': 'default',
        },
        'data_refresh': {
            'exchange': 'data_refresh',
            'routing_key': 'data_refresh',
        },
        'alerts': {
            'exchange': 'alerts',
            'routing_key': 'alerts',
        },
        'file_processing': {
            'exchange': 'file_processing',
            'routing_key': 'file_processing',
        },
        'failed': {
            'exchange': 'failed',
            'routing_key': 'failed',
        },
    },
)

# Error handling and monitoring
@celery_app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery configuration."""
    logger.info(f'Request: {self.request!r}')
    return f'Debug task executed successfully'

# Task failure handler
@celery_app.task(bind=True, name='handle_task_failure')
def handle_task_failure(self, task_id, error, traceback):
    """Handle failed tasks by logging and potentially retrying."""
    logger.error(f"Task {task_id} failed with error: {error}")
    logger.error(f"Traceback: {traceback}")
    
    # Could implement additional failure handling logic here
    # such as sending alerts, storing failure info, etc.
    return {
        'task_id': task_id,
        'error': str(error),
        'handled_at': self.request.utc,
        'status': 'failure_handled'
    }