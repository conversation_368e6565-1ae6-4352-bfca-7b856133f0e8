"""
Background job monitoring and management utilities.
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from celery import current_app
from celery.result import AsyncResult
from celery.events.state import State
from sqlalchemy.orm import Session

from ..core.celery import celery_app
from ..core.database import SessionLocal

logger = logging.getLogger(__name__)


class JobMonitor:
    """Monitor and manage Celery background jobs."""
    
    def __init__(self):
        self.celery_app = celery_app
        self.state = State()
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """Get list of currently active tasks."""
        inspect = self.celery_app.control.inspect()
        active_tasks = inspect.active()
        
        if not active_tasks:
            return []
        
        tasks = []
        for worker, worker_tasks in active_tasks.items():
            for task in worker_tasks:
                tasks.append({
                    'id': task['id'],
                    'name': task['name'],
                    'worker': worker,
                    'args': task.get('args', []),
                    'kwargs': task.get('kwargs', {}),
                    'time_start': task.get('time_start'),
                    'acknowledged': task.get('acknowledged', False)
                })
        
        return tasks
    
    def get_scheduled_tasks(self) -> List[Dict[str, Any]]:
        """Get list of scheduled tasks."""
        inspect = self.celery_app.control.inspect()
        scheduled_tasks = inspect.scheduled()
        
        if not scheduled_tasks:
            return []
        
        tasks = []
        for worker, worker_tasks in scheduled_tasks.items():
            for task in worker_tasks:
                tasks.append({
                    'id': task['request']['id'],
                    'name': task['request']['task'],
                    'worker': worker,
                    'eta': task['eta'],
                    'priority': task['priority']
                })
        
        return tasks
    
    def get_failed_tasks(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get list of recently failed tasks."""
        # This would typically query a failure tracking system
        # For now, we'll return a placeholder structure
        return []
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get detailed status of a specific task."""
        result = AsyncResult(task_id, app=self.celery_app)
        
        return {
            'id': task_id,
            'status': result.status,
            'result': result.result if result.ready() else None,
            'traceback': result.traceback,
            'date_done': result.date_done,
            'task_name': result.name,
            'args': getattr(result, 'args', None),
            'kwargs': getattr(result, 'kwargs', None)
        }
    
    def retry_failed_task(self, task_id: str) -> Dict[str, Any]:
        """Retry a failed task."""
        try:
            result = AsyncResult(task_id, app=self.celery_app)
            
            if result.status != 'FAILURE':
                return {
                    'success': False,
                    'error': f'Task {task_id} is not in FAILURE state'
                }
            
            # Get original task info
            task_info = self.get_task_status(task_id)
            
            # Retry the task (this is a simplified approach)
            # In practice, you'd need to store original task args/kwargs
            new_result = self.celery_app.send_task(
                task_info['task_name'],
                args=task_info.get('args', []),
                kwargs=task_info.get('kwargs', {})
            )
            
            return {
                'success': True,
                'new_task_id': new_result.id,
                'original_task_id': task_id
            }
            
        except Exception as e:
            logger.error(f"Failed to retry task {task_id}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """Cancel a running task."""
        try:
            self.celery_app.control.revoke(task_id, terminate=True)
            
            return {
                'success': True,
                'message': f'Task {task_id} has been cancelled'
            }
            
        except Exception as e:
            logger.error(f"Failed to cancel task {task_id}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_worker_stats(self) -> Dict[str, Any]:
        """Get statistics about Celery workers."""
        inspect = self.celery_app.control.inspect()
        
        stats = inspect.stats()
        if not stats:
            return {'workers': 0, 'details': {}}
        
        worker_details = {}
        for worker, worker_stats in stats.items():
            worker_details[worker] = {
                'status': 'online',
                'processed_tasks': worker_stats.get('total', {}),
                'active_tasks': len(worker_stats.get('active', [])),
                'load_avg': worker_stats.get('rusage', {}).get('utime', 0),
                'memory_usage': worker_stats.get('rusage', {}).get('maxrss', 0)
            }
        
        return {
            'workers': len(stats),
            'details': worker_details
        }
    
    def get_queue_lengths(self) -> Dict[str, int]:
        """Get the length of each queue."""
        inspect = self.celery_app.control.inspect()
        
        # Get reserved tasks (approximation of queue length)
        reserved = inspect.reserved()
        if not reserved:
            return {}
        
        queue_lengths = {}
        for worker, tasks in reserved.items():
            for task in tasks:
                queue = task.get('delivery_info', {}).get('routing_key', 'default')
                queue_lengths[queue] = queue_lengths.get(queue, 0) + 1
        
        return queue_lengths


@celery_app.task(bind=True)
def health_check(self) -> Dict[str, Any]:
    """Health check task to verify Celery is working."""
    return {
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'worker_id': self.request.hostname,
        'task_id': self.request.id
    }


@celery_app.task(bind=True)
def system_diagnostics(self) -> Dict[str, Any]:
    """Run system diagnostics and return health information."""
    try:
        db = SessionLocal()
        diagnostics = {
            'database': 'unknown',
            'redis': 'unknown',
            'celery': 'healthy',
            'timestamp': datetime.utcnow().isoformat()
        }
        
        try:
            # Test database connection
            db.execute("SELECT 1")
            diagnostics['database'] = 'healthy'
        except Exception as e:
            diagnostics['database'] = f'error: {str(e)}'
        finally:
            db.close()
        
        # Test Redis connection (Celery broker)
        try:
            # If we can execute this task, Redis is working
            diagnostics['redis'] = 'healthy'
        except Exception as e:
            diagnostics['redis'] = f'error: {str(e)}'
        
        return diagnostics
        
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 60})
def monitor_system_resources(self) -> Dict[str, Any]:
    """Monitor system resources and create alerts if needed."""
    try:
        import psutil
        
        # Get system metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        metrics = {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_available_gb': memory.available / (1024**3),
            'disk_percent': disk.percent,
            'disk_free_gb': disk.free / (1024**3),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Check for resource alerts
        alerts = []
        if cpu_percent > 90:
            alerts.append('High CPU usage detected')
        if memory.percent > 90:
            alerts.append('High memory usage detected')
        if disk.percent > 90:
            alerts.append('Low disk space detected')
        
        metrics['alerts'] = alerts
        
        return {
            'status': 'SUCCESS',
            'metrics': metrics
        }
        
    except ImportError:
        return {
            'status': 'WARNING',
            'message': 'psutil not available for system monitoring'
        }
    except Exception as e:
        logger.error(f"System resource monitoring failed: {str(e)}")
        raise


# Dead letter queue handler
@celery_app.task(bind=True, queue='failed')
def handle_dead_letter(self, original_task_id: str, error: str, task_name: str, args: list, kwargs: dict) -> Dict[str, Any]:
    """Handle tasks that have been moved to the dead letter queue."""
    try:
        logger.error(f"Dead letter task received: {task_name} (original: {original_task_id})")
        logger.error(f"Error: {error}")
        
        # Store dead letter information for later analysis
        dead_letter_info = {
            'original_task_id': original_task_id,
            'task_name': task_name,
            'error': error,
            'args': args,
            'kwargs': kwargs,
            'received_at': datetime.utcnow().isoformat(),
            'handler_task_id': self.request.id
        }
        
        # In a production system, you might:
        # 1. Store this in a database for analysis
        # 2. Send alerts to administrators
        # 3. Attempt automatic recovery for certain error types
        
        logger.info(f"Dead letter task processed: {dead_letter_info}")
        
        return {
            'status': 'PROCESSED',
            'dead_letter_info': dead_letter_info
        }
        
    except Exception as e:
        logger.error(f"Failed to handle dead letter task: {str(e)}")
        raise