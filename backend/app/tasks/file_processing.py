"""
Background tasks for file processing and data ingestion.
"""
import tempfile
import os
from typing import Dict, Any
from celery import current_task
from sqlalchemy.orm import Session

from ..core.celery import celery_app
from ..core.database import SessionLocal
from ..services.file_upload import FileUploadService, FileUploadError, DataValidationError


@celery_app.task(bind=True)
def process_uploaded_file(
    self,
    file_content: bytes,
    filename: str,
    data_type: str,
    source: str,
    season: int,
    **kwargs
) -> Dict[str, Any]:
    """
    Background task to process uploaded file.
    
    Args:
        file_content: Raw file content as bytes
        filename: Original filename
        data_type: Type of data ('projections', 'rankings', 'adp')
        source: Data source name
        season: Season year
        **kwargs: Additional parameters (e.g., ranking_type)
    
    Returns:
        Dict with processing results
    """
    try:
        # Update task state
        self.update_state(state='PROGRESS', meta={'status': 'Starting file processing'})
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{filename}") as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name
        
        try:
            # Create database session
            db = SessionLocal()
            
            try:
                # Update task state
                self.update_state(state='PROGRESS', meta={'status': 'Parsing file data'})
                
                # Create upload service and process file
                upload_service = FileUploadService(db)
                
                # Create a mock UploadFile object for processing
                class MockUploadFile:
                    def __init__(self, file_path: str, filename: str):
                        self.filename = filename
                        self.file = open(file_path, 'rb')
                    
                    def __enter__(self):
                        return self
                    
                    def __exit__(self, exc_type, exc_val, exc_tb):
                        self.file.close()
                
                with MockUploadFile(temp_file_path, filename) as mock_file:
                    result = upload_service.process_file(
                        file=mock_file,
                        data_type=data_type,
                        source=source,
                        season=season,
                        **kwargs
                    )
                
                # Update task state
                self.update_state(
                    state='PROGRESS', 
                    meta={
                        'status': 'Processing complete',
                        'saved_count': result['saved_count'],
                        'total_rows': result['total_rows']
                    }
                )
                
                return {
                    'status': 'SUCCESS',
                    'result': result
                }
                
            finally:
                db.close()
                
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    except FileUploadError as e:
        self.update_state(
            state='FAILURE',
            meta={'status': 'File upload error', 'error': str(e)}
        )
        raise
    
    except DataValidationError as e:
        self.update_state(
            state='FAILURE',
            meta={'status': 'Data validation error', 'error': str(e)}
        )
        raise
    
    except Exception as e:
        self.update_state(
            state='FAILURE',
            meta={'status': 'Unexpected error', 'error': str(e)}
        )
        raise


@celery_app.task(bind=True)
def cleanup_old_data(self, data_type: str, source: str, season: int) -> Dict[str, Any]:
    """
    Background task to clean up old data before importing new data.
    
    Args:
        data_type: Type of data to clean up
        source: Data source name
        season: Season year
    
    Returns:
        Dict with cleanup results
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': 'Starting data cleanup'})
        
        db = SessionLocal()
        try:
            deleted_count = 0
            
            if data_type == 'projections':
                from ..models import Projection
                deleted_count = db.query(Projection).filter(
                    Projection.source == source,
                    Projection.season == season
                ).delete()
            
            elif data_type in ['rankings', 'adp']:
                from ..models import Ranking
                ranking_type = 'adp' if data_type == 'adp' else 'expert'
                deleted_count = db.query(Ranking).filter(
                    Ranking.source == source,
                    Ranking.season == season,
                    Ranking.ranking_type == ranking_type
                ).delete()
            
            db.commit()
            
            return {
                'status': 'SUCCESS',
                'deleted_count': deleted_count,
                'message': f'Cleaned up {deleted_count} old records'
            }
            
        finally:
            db.close()
    
    except Exception as e:
        self.update_state(
            state='FAILURE',
            meta={'status': 'Cleanup error', 'error': str(e)}
        )
        raise


@celery_app.task(bind=True)
def validate_uploaded_data(self, data_type: str, source: str, season: int) -> Dict[str, Any]:
    """
    Background task to validate uploaded data integrity.
    
    Args:
        data_type: Type of data to validate
        source: Data source name
        season: Season year
    
    Returns:
        Dict with validation results
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': 'Starting data validation'})
        
        db = SessionLocal()
        try:
            validation_results = {
                'total_records': 0,
                'valid_records': 0,
                'invalid_records': 0,
                'issues': []
            }
            
            if data_type == 'projections':
                from ..models import Projection
                projections = db.query(Projection).filter(
                    Projection.source == source,
                    Projection.season == season
                ).all()
                
                validation_results['total_records'] = len(projections)
                
                for proj in projections:
                    issues = []
                    
                    # Validate projected points
                    if proj.projected_points <= 0:
                        issues.append('Projected points must be positive')
                    
                    # Validate confidence interval
                    if proj.floor and proj.ceiling and proj.floor > proj.ceiling:
                        issues.append('Floor cannot be greater than ceiling')
                    
                    # Validate player exists
                    if not proj.player:
                        issues.append('Player not found')
                    
                    if issues:
                        validation_results['invalid_records'] += 1
                        validation_results['issues'].extend([
                            f"Projection {proj.id}: {issue}" for issue in issues
                        ])
                    else:
                        validation_results['valid_records'] += 1
            
            elif data_type in ['rankings', 'adp']:
                from ..models import Ranking
                ranking_type = 'adp' if data_type == 'adp' else 'expert'
                rankings = db.query(Ranking).filter(
                    Ranking.source == source,
                    Ranking.season == season,
                    Ranking.ranking_type == ranking_type
                ).all()
                
                validation_results['total_records'] = len(rankings)
                
                for rank in rankings:
                    issues = []
                    
                    # Validate ranks are positive
                    if rank.overall_rank and rank.overall_rank <= 0:
                        issues.append('Overall rank must be positive')
                    if rank.position_rank and rank.position_rank <= 0:
                        issues.append('Position rank must be positive')
                    
                    # Validate ADP
                    if rank.adp and rank.adp <= 0:
                        issues.append('ADP must be positive')
                    
                    # Validate player exists
                    if not rank.player:
                        issues.append('Player not found')
                    
                    if issues:
                        validation_results['invalid_records'] += 1
                        validation_results['issues'].extend([
                            f"Ranking {rank.id}: {issue}" for issue in issues
                        ])
                    else:
                        validation_results['valid_records'] += 1
            
            return {
                'status': 'SUCCESS',
                'validation_results': validation_results
            }
            
        finally:
            db.close()
    
    except Exception as e:
        self.update_state(
            state='FAILURE',
            meta={'status': 'Validation error', 'error': str(e)}
        )
        raise