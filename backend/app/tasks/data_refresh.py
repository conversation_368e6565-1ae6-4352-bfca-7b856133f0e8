"""
Background tasks for data refresh and maintenance.
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
from celery import current_task
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..core.celery import celery_app
from ..core.database import SessionLocal
try:
    from ..services.mfl_ingestion import MFLIngestionService
except ImportError:
    # Handle case where MFL service is not available
    MFLIngestionService = None
try:
    from ..services.projections_aggregator import ProjectionsAggregator
except ImportError:
    # Handle case where projections aggregator is not available
    ProjectionsAggregator = None
from ..models import League, Player, Projection, Ranking

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 60})
def refresh_mfl_data(self) -> Dict[str, Any]:
    """
    Scheduled task to refresh MFL data for all configured leagues.
    
    Returns:
        Dict with refresh results
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': 'Starting MFL data refresh'})
        
        db = SessionLocal()
        results = {
            'leagues_processed': 0,
            'leagues_failed': 0,
            'total_players_updated': 0,
            'errors': []
        }
        
        try:
            # Get all active leagues
            leagues = db.query(League).filter(League.is_active == True).all()
            
            for league in leagues:
                try:
                    self.update_state(
                        state='PROGRESS', 
                        meta={
                            'status': f'Processing league {league.name}',
                            'current_league': league.id
                        }
                    )
                    
                    # Initialize MFL service
                    if MFLIngestionService is None:
                        raise ImportError("MFL ingestion service not available")
                    mfl_service = MFLIngestionService(db)
                    
                    # Refresh league data
                    league_result = mfl_service.refresh_league_data(league.id)
                    
                    results['leagues_processed'] += 1
                    results['total_players_updated'] += league_result.get('players_updated', 0)
                    
                    logger.info(f"Successfully refreshed data for league {league.name}")
                    
                except Exception as e:
                    results['leagues_failed'] += 1
                    error_msg = f"Failed to refresh league {league.name}: {str(e)}"
                    results['errors'].append(error_msg)
                    logger.error(error_msg)
            
            self.update_state(
                state='PROGRESS',
                meta={
                    'status': 'MFL data refresh complete',
                    'leagues_processed': results['leagues_processed'],
                    'leagues_failed': results['leagues_failed']
                }
            )
            
            return {
                'status': 'SUCCESS',
                'results': results,
                'completed_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"MFL data refresh failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'status': 'MFL data refresh failed', 'error': str(e)}
        )
        raise


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 120})
def refresh_projection_aggregates(self) -> Dict[str, Any]:
    """
    Scheduled task to refresh projection aggregates for all players.
    
    Returns:
        Dict with aggregation results
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': 'Starting projection aggregation'})
        
        db = SessionLocal()
        results = {
            'players_processed': 0,
            'projections_aggregated': 0,
            'errors': []
        }
        
        try:
            # Initialize projections aggregator
            if ProjectionsAggregator is None:
                raise ImportError("Projections aggregator service not available")
            aggregator = ProjectionsAggregator(db)
            
            # Get all active players
            players = db.query(Player).filter(Player.is_active == True).all()
            
            for player in players:
                try:
                    self.update_state(
                        state='PROGRESS',
                        meta={
                            'status': f'Processing projections for {player.name}',
                            'current_player': player.id,
                            'processed': results['players_processed'],
                            'total': len(players)
                        }
                    )
                    
                    # Aggregate projections for this player
                    aggregation_result = aggregator.aggregate_player_projections(player.id)
                    
                    if aggregation_result:
                        results['players_processed'] += 1
                        results['projections_aggregated'] += aggregation_result.get('sources_count', 0)
                    
                except Exception as e:
                    error_msg = f"Failed to aggregate projections for {player.name}: {str(e)}"
                    results['errors'].append(error_msg)
                    logger.error(error_msg)
            
            self.update_state(
                state='PROGRESS',
                meta={
                    'status': 'Projection aggregation complete',
                    'players_processed': results['players_processed']
                }
            )
            
            return {
                'status': 'SUCCESS',
                'results': results,
                'completed_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Projection aggregation failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'status': 'Projection aggregation failed', 'error': str(e)}
        )
        raise


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 2, 'countdown': 30})
def cleanup_old_task_results(self) -> Dict[str, Any]:
    """
    Scheduled task to clean up old Celery task results and expired data.
    
    Returns:
        Dict with cleanup results
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': 'Starting cleanup of old data'})
        
        db = SessionLocal()
        results = {
            'old_projections_deleted': 0,
            'old_rankings_deleted': 0,
            'task_results_cleaned': 0
        }
        
        try:
            # Clean up old projections (older than 30 days)
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            old_projections = db.query(Projection).filter(
                Projection.created_at < cutoff_date
            ).delete()
            results['old_projections_deleted'] = old_projections
            
            # Clean up old rankings (older than 30 days)
            old_rankings = db.query(Ranking).filter(
                Ranking.created_at < cutoff_date
            ).delete()
            results['old_rankings_deleted'] = old_rankings
            
            db.commit()
            
            # Clean up Celery task results (this would typically be done via Redis)
            # For now, we'll just log this action
            logger.info("Celery task result cleanup would be performed here")
            results['task_results_cleaned'] = 1
            
            return {
                'status': 'SUCCESS',
                'results': results,
                'completed_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Cleanup task failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'status': 'Cleanup failed', 'error': str(e)}
        )
        raise


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 60})
def refresh_player_data(self, league_id: str) -> Dict[str, Any]:
    """
    On-demand task to refresh player data for a specific league.
    
    Args:
        league_id: ID of the league to refresh
        
    Returns:
        Dict with refresh results
    """
    try:
        self.update_state(
            state='PROGRESS', 
            meta={'status': f'Starting player data refresh for league {league_id}'}
        )
        
        db = SessionLocal()
        
        try:
            # Verify league exists
            league = db.query(League).filter(League.id == league_id).first()
            if not league:
                raise ValueError(f"League {league_id} not found")
            
            # Initialize MFL service
            if MFLIngestionService is None:
                raise ImportError("MFL ingestion service not available")
            mfl_service = MFLIngestionService(db)
            
            # Refresh player data
            result = mfl_service.refresh_player_data(league_id)
            
            return {
                'status': 'SUCCESS',
                'league_id': league_id,
                'result': result,
                'completed_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Player data refresh failed for league {league_id}: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'status': 'Player data refresh failed', 'error': str(e)}
        )
        raise


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 2, 'countdown': 30})
def update_player_injury_status(self) -> Dict[str, Any]:
    """
    Scheduled task to update player injury statuses from external sources.
    
    Returns:
        Dict with update results
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': 'Starting injury status updates'})
        
        db = SessionLocal()
        results = {
            'players_checked': 0,
            'players_updated': 0,
            'errors': []
        }
        
        try:
            # Get all active players
            players = db.query(Player).filter(Player.is_active == True).all()
            
            for player in players:
                try:
                    # This would integrate with injury report APIs
                    # For now, we'll simulate the process
                    results['players_checked'] += 1
                    
                    # Placeholder for actual injury status checking logic
                    # injury_status = check_player_injury_status(player.id)
                    # if injury_status != player.injury_status:
                    #     player.injury_status = injury_status
                    #     results['players_updated'] += 1
                    
                except Exception as e:
                    error_msg = f"Failed to check injury status for {player.name}: {str(e)}"
                    results['errors'].append(error_msg)
                    logger.error(error_msg)
            
            db.commit()
            
            return {
                'status': 'SUCCESS',
                'results': results,
                'completed_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Injury status update failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'status': 'Injury status update failed', 'error': str(e)}
        )
        raise