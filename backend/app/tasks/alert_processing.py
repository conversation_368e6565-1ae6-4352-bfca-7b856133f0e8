"""
Background tasks for alert processing and notifications.
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from celery import current_task
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..core.celery import celery_app
from ..core.database import SessionLocal
try:
    from ..services.alert_service import AlertService
except ImportError:
    # Handle case where alert service is not available
    AlertService = None
from ..models import League, Alert, Player, Roster

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 60})
def check_upcoming_deadlines(self) -> Dict[str, Any]:
    """
    Scheduled task to check for upcoming deadlines and create alerts.
    
    Returns:
        Dict with deadline check results
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': 'Checking upcoming deadlines'})
        
        db = SessionLocal()
        results = {
            'leagues_checked': 0,
            'alerts_created': 0,
            'deadline_types': {
                'keeper': 0,
                'waiver': 0,
                'lineup': 0,
                'trade': 0
            }
        }
        
        try:
            if AlertService is None:
                raise ImportError("Alert service not available")
            alert_service = AlertService(db)
            
            # Get all active leagues
            leagues = db.query(League).filter(League.is_active == True).all()
            
            for league in leagues:
                try:
                    self.update_state(
                        state='PROGRESS',
                        meta={
                            'status': f'Checking deadlines for league {league.name}',
                            'current_league': league.id
                        }
                    )
                    
                    results['leagues_checked'] += 1
                    
                    # Check keeper deadlines
                    keeper_alerts = alert_service.check_keeper_deadlines(league.id)
                    results['alerts_created'] += len(keeper_alerts)
                    results['deadline_types']['keeper'] += len(keeper_alerts)
                    
                    # Check waiver deadlines
                    waiver_alerts = alert_service.check_waiver_deadlines(league.id)
                    results['alerts_created'] += len(waiver_alerts)
                    results['deadline_types']['waiver'] += len(waiver_alerts)
                    
                    # Check lineup lock deadlines
                    lineup_alerts = alert_service.check_lineup_deadlines(league.id)
                    results['alerts_created'] += len(lineup_alerts)
                    results['deadline_types']['lineup'] += len(lineup_alerts)
                    
                    # Check trade deadlines
                    trade_alerts = alert_service.check_trade_deadlines(league.id)
                    results['alerts_created'] += len(trade_alerts)
                    results['deadline_types']['trade'] += len(trade_alerts)
                    
                    logger.info(f"Checked deadlines for league {league.name}")
                    
                except Exception as e:
                    error_msg = f"Failed to check deadlines for league {league.name}: {str(e)}"
                    logger.error(error_msg)
            
            return {
                'status': 'SUCCESS',
                'results': results,
                'completed_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Deadline check failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'status': 'Deadline check failed', 'error': str(e)}
        )
        raise


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def process_player_news(self) -> Dict[str, Any]:
    """
    Scheduled task to process player news and create relevant alerts.
    
    Returns:
        Dict with news processing results
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': 'Processing player news'})
        
        db = SessionLocal()
        results = {
            'news_items_processed': 0,
            'alerts_created': 0,
            'players_affected': 0,
            'news_types': {
                'injury': 0,
                'trade': 0,
                'suspension': 0,
                'other': 0
            }
        }
        
        try:
            if AlertService is None:
                raise ImportError("Alert service not available")
            alert_service = AlertService(db)
            
            # This would typically fetch from external news APIs
            # For now, we'll simulate processing recent news
            
            # Get players with recent status changes
            recent_cutoff = datetime.utcnow() - timedelta(hours=1)
            players_with_changes = db.query(Player).filter(
                Player.updated_at > recent_cutoff
            ).all()
            
            for player in players_with_changes:
                try:
                    self.update_state(
                        state='PROGRESS',
                        meta={
                            'status': f'Processing news for {player.name}',
                            'current_player': player.id
                        }
                    )
                    
                    results['players_affected'] += 1
                    
                    # Check for injury status changes
                    if player.injury_status and player.injury_status != 'healthy':
                        injury_alerts = alert_service.create_injury_alerts(player.id)
                        results['alerts_created'] += len(injury_alerts)
                        results['news_types']['injury'] += len(injury_alerts)
                    
                    # Check for team changes (trades)
                    team_change_alerts = alert_service.check_team_changes(player.id)
                    results['alerts_created'] += len(team_change_alerts)
                    results['news_types']['trade'] += len(team_change_alerts)
                    
                    results['news_items_processed'] += 1
                    
                except Exception as e:
                    error_msg = f"Failed to process news for {player.name}: {str(e)}"
                    logger.error(error_msg)
            
            return {
                'status': 'SUCCESS',
                'results': results,
                'completed_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Player news processing failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'status': 'Player news processing failed', 'error': str(e)}
        )
        raise


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 2, 'countdown': 30})
def send_pending_notifications(self) -> Dict[str, Any]:
    """
    Task to send pending notifications to users.
    
    Returns:
        Dict with notification sending results
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': 'Sending pending notifications'})
        
        db = SessionLocal()
        results = {
            'notifications_sent': 0,
            'notifications_failed': 0,
            'notification_types': {
                'email': 0,
                'push': 0,
                'in_app': 0
            }
        }
        
        try:
            if AlertService is None:
                raise ImportError("Alert service not available")
            alert_service = AlertService(db)
            
            # Get pending alerts that need to be sent
            pending_alerts = db.query(Alert).filter(
                and_(
                    Alert.status == 'pending',
                    Alert.scheduled_for <= datetime.utcnow()
                )
            ).all()
            
            for alert in pending_alerts:
                try:
                    self.update_state(
                        state='PROGRESS',
                        meta={
                            'status': f'Sending alert {alert.id}',
                            'current_alert': alert.id
                        }
                    )
                    
                    # Send the notification
                    send_result = alert_service.send_notification(alert.id)
                    
                    if send_result['success']:
                        results['notifications_sent'] += 1
                        results['notification_types'][send_result.get('type', 'in_app')] += 1
                        
                        # Update alert status
                        alert.status = 'sent'
                        alert.sent_at = datetime.utcnow()
                    else:
                        results['notifications_failed'] += 1
                        alert.status = 'failed'
                        alert.error_message = send_result.get('error', 'Unknown error')
                    
                except Exception as e:
                    results['notifications_failed'] += 1
                    error_msg = f"Failed to send alert {alert.id}: {str(e)}"
                    logger.error(error_msg)
                    
                    alert.status = 'failed'
                    alert.error_message = str(e)
            
            db.commit()
            
            return {
                'status': 'SUCCESS',
                'results': results,
                'completed_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Notification sending failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'status': 'Notification sending failed', 'error': str(e)}
        )
        raise


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 60})
def create_lineup_lock_alerts(self, league_id: str, week: int) -> Dict[str, Any]:
    """
    On-demand task to create lineup lock alerts for a specific week.
    
    Args:
        league_id: ID of the league
        week: Week number
        
    Returns:
        Dict with alert creation results
    """
    try:
        self.update_state(
            state='PROGRESS',
            meta={'status': f'Creating lineup lock alerts for league {league_id}, week {week}'}
        )
        
        db = SessionLocal()
        
        try:
            if AlertService is None:
                raise ImportError("Alert service not available")
            alert_service = AlertService(db)
            
            # Create lineup lock alerts
            alerts = alert_service.create_lineup_lock_alerts(league_id, week)
            
            return {
                'status': 'SUCCESS',
                'league_id': league_id,
                'week': week,
                'alerts_created': len(alerts),
                'completed_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Lineup lock alert creation failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'status': 'Lineup lock alert creation failed', 'error': str(e)}
        )
        raise


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 2, 'countdown': 30})
def cleanup_old_alerts(self) -> Dict[str, Any]:
    """
    Scheduled task to clean up old alerts and notifications.
    
    Returns:
        Dict with cleanup results
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': 'Cleaning up old alerts'})
        
        db = SessionLocal()
        results = {
            'alerts_deleted': 0,
            'notifications_deleted': 0
        }
        
        try:
            # Delete alerts older than 30 days
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            old_alerts = db.query(Alert).filter(
                Alert.created_at < cutoff_date
            ).delete()
            results['alerts_deleted'] = old_alerts
            
            db.commit()
            
            return {
                'status': 'SUCCESS',
                'results': results,
                'completed_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Alert cleanup failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'status': 'Alert cleanup failed', 'error': str(e)}
        )
        raise


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 120})
def process_late_breaking_news(self, news_item: Dict[str, Any]) -> Dict[str, Any]:
    """
    High-priority task to process late-breaking news that affects lineups.
    
    Args:
        news_item: Dictionary containing news information
        
    Returns:
        Dict with processing results
    """
    try:
        self.update_state(
            state='PROGRESS',
            meta={'status': f'Processing late-breaking news: {news_item.get("title", "Unknown")}'}
        )
        
        db = SessionLocal()
        
        try:
            if AlertService is None:
                raise ImportError("Alert service not available")
            alert_service = AlertService(db)
            
            # Process the news item and create urgent alerts
            alerts = alert_service.process_urgent_news(news_item)
            
            return {
                'status': 'SUCCESS',
                'news_item': news_item,
                'alerts_created': len(alerts),
                'completed_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Late-breaking news processing failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'status': 'Late-breaking news processing failed', 'error': str(e)}
        )
        raise