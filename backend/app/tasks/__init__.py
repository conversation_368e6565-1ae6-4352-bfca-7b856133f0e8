"""
Background tasks for the AI Fantasy Assistant.

This module contains Celery tasks for background processing including:
- File processing and data ingestion
- Data refresh and maintenance
- Alert processing and notifications
- Job monitoring and system health
"""

from .file_processing import process_uploaded_file, cleanup_old_data, validate_uploaded_data
from .data_refresh import (
    refresh_mfl_data, 
    refresh_projection_aggregates, 
    cleanup_old_task_results,
    refresh_player_data,
    update_player_injury_status
)
from .alert_processing import (
    check_upcoming_deadlines,
    process_player_news,
    send_pending_notifications,
    create_lineup_lock_alerts,
    cleanup_old_alerts,
    process_late_breaking_news
)
from .monitoring import (
    JobMonitor,
    health_check,
    system_diagnostics,
    monitor_system_resources,
    handle_dead_letter
)

__all__ = [
    # File processing
    "process_uploaded_file",
    "cleanup_old_data", 
    "validate_uploaded_data",
    
    # Data refresh
    "refresh_mfl_data",
    "refresh_projection_aggregates",
    "cleanup_old_task_results",
    "refresh_player_data",
    "update_player_injury_status",
    
    # <PERSON>ert processing
    "check_upcoming_deadlines",
    "process_player_news",
    "send_pending_notifications",
    "create_lineup_lock_alerts",
    "cleanup_old_alerts",
    "process_late_breaking_news",
    
    # Monitoring
    "JobMonitor",
    "health_check",
    "system_diagnostics",
    "monitor_system_resources",
    "handle_dead_letter"
]