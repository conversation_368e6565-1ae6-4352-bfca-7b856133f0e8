"""
League model for fantasy football leagues.
"""
from typing import Dict, Any, Optional, List
from sqlalchemy import String, Integer, JSON, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base, TimestampMixin


class League(Base, TimestampMixin):
    """
    Represents a fantasy football league with its configuration and rules.
    
    Supports MFL league integration and custom league configurations.
    """
    __tablename__ = "leagues"
    
    # Primary identifier
    id: Mapped[str] = mapped_column(String(50), primary_key=True)
    
    # Basic league information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    season: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # League configuration stored as JSON
    scoring_rules: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    roster_slots: Mapped[List[Dict[str, Any]]] = mapped_column(JSON, nullable=False, default=list)
    keeper_rules: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # MFL integration
    mfl_league_id: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    mfl_api_key: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Additional metadata
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    is_active: Mapped[bool] = mapped_column(default=True, nullable=False)
    
    # Relationships
    franchises: Mapped[List["Franchise"]] = relationship(
        "Franchise", back_populates="league", cascade="all, delete-orphan"
    )
    recommendations: Mapped[List["Recommendation"]] = relationship(
        "Recommendation", back_populates="league", cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<League(id='{self.id}', name='{self.name}', season={self.season})>"
    
    def validate_scoring_rules(self) -> bool:
        """Validate that scoring rules contain required fields."""
        from ..services.rules_engine import RulesEngine
        
        rules_engine = RulesEngine()
        is_valid, _ = rules_engine.validate_league_rules({
            "scoring_rules": self.scoring_rules,
            "roster_slots": self.roster_slots,
            "keeper_rules": self.keeper_rules
        })
        return is_valid
    
    def get_roster_slot_count(self, position: str) -> int:
        """Get the number of roster slots for a specific position."""
        return sum(
            slot.get("count", 1) 
            for slot in self.roster_slots 
            if slot.get("position") == position
        )
    
    def calculate_player_score(self, player_stats: Dict[str, Any]) -> float:
        """Calculate fantasy points for a player using league scoring rules."""
        from ..services.rules_engine import RulesEngine
        
        rules_engine = RulesEngine()
        return rules_engine.calculate_player_score(player_stats, self.scoring_rules)