"""
Ranking model for player rankings and ADP data.
"""
from typing import Dict, Any, Optional
from decimal import Decimal
from sqlalchemy import String, Integer, DECIMAL, ForeignKey, JSON, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base, TimestampMixin


class Ranking(Base, TimestampMixin):
    """
    Represents player rankings from various sources including ADP data.
    """
    __tablename__ = "rankings"
    
    # Primary identifier
    id: Mapped[str] = mapped_column(String(50), primary_key=True)
    
    # Player and time period
    player_id: Mapped[str] = mapped_column(String(50), ForeignKey("players.id"), nullable=False)
    season: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # Ranking source and type
    source: Mapped[str] = mapped_column(String(100), nullable=False)
    ranking_type: Mapped[str] = mapped_column(String(50), nullable=False)  # 'expert', 'adp', 'consensus'
    
    # Ranking values
    overall_rank: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    position_rank: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    tier: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # ADP specific data
    adp: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(6, 2), nullable=True)  # Average Draft Position
    adp_std: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(6, 2), nullable=True)  # Standard deviation
    draft_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # Number of drafts
    
    # Additional metadata
    ranking_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    is_active: Mapped[bool] = mapped_column(default=True, nullable=False)
    
    # Relationships
    player: Mapped["Player"] = relationship("Player", back_populates="rankings")
    
    # Database indexes for performance
    __table_args__ = (
        Index('idx_rankings_player_season', 'player_id', 'season'),
        Index('idx_rankings_source_type', 'source', 'ranking_type', 'season'),
        Index('idx_rankings_overall', 'overall_rank', 'season'),
        Index('idx_rankings_position', 'position_rank', 'season'),
        Index('idx_rankings_adp', 'adp', 'season'),
    )
    
    def __repr__(self) -> str:
        return f"<Ranking(player_id='{self.player_id}', source='{self.source}', rank={self.overall_rank}, adp={self.adp})>"
    
    def is_adp_ranking(self) -> bool:
        """Check if this ranking contains ADP data."""
        return self.adp is not None
    
    def get_draft_frequency(self) -> Optional[float]:
        """Calculate how frequently this player is drafted (0-1)."""
        if not self.draft_count or not self.ranking_metadata.get('total_drafts'):
            return None
        return self.draft_count / self.ranking_metadata['total_drafts']
    
    def get_adp_range(self) -> tuple[Optional[Decimal], Optional[Decimal]]:
        """Get ADP range based on standard deviation."""
        if not self.adp or not self.adp_std:
            return (None, None)
        return (self.adp - self.adp_std, self.adp + self.adp_std)