"""
Alert and notification models for fantasy football management.
"""
from typing import Dict, Any, Optional, List
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from sqlalchemy import String, Integer, JSON, Foreign<PERSON>ey, Boolean, DateTime, Text, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base, TimestampMixin


class AlertType(str, Enum):
    """Enumeration of alert types."""
    KEEPER_DEADLINE = "KEEPER_DEADLINE"
    WAIVER_DEADLINE = "WAIVER_DEADLINE"
    LINEUP_LOCK = "LINEUP_LOCK"
    TRADE_DEADLINE = "TRADE_DEADLINE"
    PLAYER_NEWS = "PLAYER_NEWS"
    INJURY_UPDATE = "INJURY_UPDATE"
    LATE_BREAKING_NEWS = "LATE_BREAKING_NEWS"
    DEADLINE_REMINDER = "DEADLINE_REMINDER"


class AlertPriority(str, Enum):
    """Enumeration of alert priorities."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    URGENT = "URGENT"


class AlertStatus(str, Enum):
    """Enumeration of alert statuses."""
    PENDING = "PENDING"
    SENT = "SENT"
    DELIVERED = "DELIVERED"
    FAILED = "FAILED"
    DISMISSED = "DISMISSED"


class DeliveryMethod(str, Enum):
    """Enumeration of delivery methods."""
    IN_APP = "IN_APP"
    EMAIL = "EMAIL"
    PUSH = "PUSH"


class Alert(Base, TimestampMixin):
    """
    Represents an alert or notification for fantasy football events.
    """
    __tablename__ = "alerts"
    
    # Primary identifier
    id: Mapped[str] = mapped_column(String(50), primary_key=True)
    
    # Alert classification
    alert_type: Mapped[AlertType] = mapped_column(SQLEnum(AlertType), nullable=False)
    priority: Mapped[AlertPriority] = mapped_column(SQLEnum(AlertPriority), nullable=False)
    status: Mapped[AlertStatus] = mapped_column(SQLEnum(AlertStatus), default=AlertStatus.PENDING, nullable=False)
    
    # Content
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    message: Mapped[str] = mapped_column(Text, nullable=False)
    
    # Targeting
    league_id: Mapped[Optional[str]] = mapped_column(String(50), ForeignKey("leagues.id"), nullable=True)
    franchise_id: Mapped[Optional[str]] = mapped_column(String(50), ForeignKey("franchises.id"), nullable=True)
    player_id: Mapped[Optional[str]] = mapped_column(String(50), ForeignKey("players.id"), nullable=True)
    
    # Timing
    scheduled_for: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    sent_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Delivery
    delivery_methods: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)
    delivery_attempts: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    last_delivery_attempt: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Additional data
    alert_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    
    # Relationships
    league: Mapped[Optional["League"]] = relationship("League")
    franchise: Mapped[Optional["Franchise"]] = relationship("Franchise")
    player: Mapped[Optional["Player"]] = relationship("Player")
    
    def __repr__(self) -> str:
        return f"<Alert(id='{self.id}', type='{self.alert_type}', priority='{self.priority}')>"
    
    def is_expired(self) -> bool:
        """Check if the alert has expired."""
        if self.expires_at is None:
            return False
        return datetime.now(self.expires_at.tzinfo) > self.expires_at
    
    def is_due(self) -> bool:
        """Check if the alert is due to be sent."""
        if self.scheduled_for is None:
            return True
        return datetime.now(self.scheduled_for.tzinfo) >= self.scheduled_for
    
    def should_retry(self, max_attempts: int = 3) -> bool:
        """Check if the alert should be retried."""
        return (
            self.status == AlertStatus.FAILED and 
            self.delivery_attempts < max_attempts and
            not self.is_expired()
        )


class AlertSchedule(Base, TimestampMixin):
    """
    Represents scheduled alert monitoring for deadlines and events.
    """
    __tablename__ = "alert_schedules"
    
    # Primary identifier
    id: Mapped[str] = mapped_column(String(50), primary_key=True)
    
    # Schedule details
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    alert_type: Mapped[AlertType] = mapped_column(SQLEnum(AlertType), nullable=False)
    
    # Targeting
    league_id: Mapped[Optional[str]] = mapped_column(String(50), ForeignKey("leagues.id"), nullable=True)
    franchise_id: Mapped[Optional[str]] = mapped_column(String(50), ForeignKey("franchises.id"), nullable=True)
    
    # Timing configuration
    target_datetime: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    advance_notice_hours: Mapped[List[int]] = mapped_column(JSON, nullable=False, default=list)  # [24, 2, 0.5]
    
    # Status
    is_active: Mapped[bool] = mapped_column(default=True, nullable=False)
    last_processed: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Configuration
    schedule_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    
    # Relationships
    league: Mapped[Optional["League"]] = relationship("League")
    franchise: Mapped[Optional["Franchise"]] = relationship("Franchise")
    
    def __repr__(self) -> str:
        return f"<AlertSchedule(id='{self.id}', name='{self.name}', target='{self.target_datetime}')>"
    
    def get_next_alert_time(self) -> Optional[datetime]:
        """Get the next scheduled alert time."""
        now = datetime.now(self.target_datetime.tzinfo)
        
        for hours in sorted(self.advance_notice_hours, reverse=True):
            alert_time = self.target_datetime - timedelta(hours=hours)
            if alert_time > now:
                return alert_time
        
        return None
    
    def is_due_for_processing(self) -> bool:
        """Check if this schedule needs processing."""
        if not self.is_active:
            return False
            
        next_alert_time = self.get_next_alert_time()
        if next_alert_time is None:
            return False
            
        now = datetime.now(self.target_datetime.tzinfo)
        return now >= next_alert_time


class PlayerNewsAlert(Base, TimestampMixin):
    """
    Represents player news and impact analysis for alert generation.
    """
    __tablename__ = "player_news_alerts"
    
    # Primary identifier
    id: Mapped[str] = mapped_column(String(50), primary_key=True)
    
    # News details
    player_id: Mapped[str] = mapped_column(String(50), ForeignKey("players.id"), nullable=False)
    headline: Mapped[str] = mapped_column(String(500), nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    source: Mapped[str] = mapped_column(String(255), nullable=False)
    
    # Impact analysis
    impact_score: Mapped[Optional[float]] = mapped_column(nullable=True)  # 0-10 scale
    affected_positions: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)
    fantasy_impact: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Processing status
    is_processed: Mapped[bool] = mapped_column(default=False, nullable=False)
    alerts_generated: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # News metadata
    news_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    
    # Relationships
    player: Mapped["Player"] = relationship("Player")
    
    def __repr__(self) -> str:
        return f"<PlayerNewsAlert(id='{self.id}', player_id='{self.player_id}', impact={self.impact_score})>"
    
    def should_generate_alerts(self) -> bool:
        """Check if this news should generate alerts."""
        return (
            not self.is_processed and
            self.impact_score is not None and
            self.impact_score >= 5.0  # Threshold for alert-worthy news
        )