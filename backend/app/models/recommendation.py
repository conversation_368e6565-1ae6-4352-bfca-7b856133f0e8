"""
Recommendation model for AI-generated fantasy football recommendations.
"""
from typing import Dict, Any, Optional, List
from datetime import datetime
from decimal import Decimal
from sqlalchemy import String, Integer, DECIMAL, ForeignKey, JSON, DateTime, Text, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from enum import Enum
from .base import Base, TimestampMixin


class RecommendationType(str, Enum):
    """Enumeration of recommendation types."""
    KEEPER = "KEEPER"
    DRAFT = "DRAFT"
    TRADE = "TRADE"
    LINEUP = "LINEUP"
    WAIVER = "WAIVER"
    FAAB = "FAAB"
    DROP = "DROP"
    ALERT = "ALERT"


class RecommendationPriority(str, Enum):
    """Enumeration of recommendation priorities."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    URGENT = "URGENT"


class RecommendationStatus(str, Enum):
    """Enumeration of recommendation statuses."""
    ACTIVE = "ACTIVE"
    ACCEPTED = "ACCEPTED"
    REJECTED = "REJECTED"
    EXPIRED = "EXPIRED"


class Recommendation(Base, TimestampMixin):
    """
    Represents an AI-generated recommendation for fantasy football decisions.
    
    Includes detailed rationale, alternatives, and confidence metrics.
    """
    __tablename__ = "recommendations"
    
    # Primary identifier
    id: Mapped[str] = mapped_column(String(50), primary_key=True)
    
    # Associated entities
    league_id: Mapped[str] = mapped_column(String(50), ForeignKey("leagues.id"), nullable=False)
    franchise_id: Mapped[Optional[str]] = mapped_column(String(50), ForeignKey("franchises.id"), nullable=True)
    
    # Recommendation details
    type: Mapped[RecommendationType] = mapped_column(SQLEnum(RecommendationType), nullable=False)
    priority: Mapped[RecommendationPriority] = mapped_column(
        SQLEnum(RecommendationPriority), 
        default=RecommendationPriority.MEDIUM, 
        nullable=False
    )
    status: Mapped[RecommendationStatus] = mapped_column(
        SQLEnum(RecommendationStatus), 
        default=RecommendationStatus.ACTIVE, 
        nullable=False
    )
    
    # Content
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    rationale: Mapped[str] = mapped_column(Text, nullable=False)
    
    # Confidence and impact metrics
    confidence: Mapped[Decimal] = mapped_column(DECIMAL(5, 4), nullable=False)
    expected_impact: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(8, 2), nullable=True)
    risk_level: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(5, 4), nullable=True)
    
    # Time constraints
    expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    deadline_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Structured data
    recommendation_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    alternatives: Mapped[List[Dict[str, Any]]] = mapped_column(JSON, nullable=False, default=list)
    
    # Additional metadata
    recommendation_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    
    # Relationships
    league: Mapped["League"] = relationship("League", back_populates="recommendations")
    franchise: Mapped[Optional["Franchise"]] = relationship("Franchise", back_populates="recommendations")
    
    def __repr__(self) -> str:
        return f"<Recommendation(id='{self.id}', type='{self.type}', title='{self.title}')>"
    
    def is_expired(self) -> bool:
        """Check if the recommendation has expired."""
        if self.expires_at is None:
            return False
        return datetime.now(self.expires_at.tzinfo) > self.expires_at
    
    def is_urgent(self) -> bool:
        """Check if the recommendation is urgent based on deadline."""
        if self.deadline_at is None:
            return self.priority == RecommendationPriority.URGENT
        
        now = datetime.now(self.deadline_at.tzinfo)
        time_until_deadline = self.deadline_at - now
        
        # Consider urgent if less than 2 hours until deadline
        return time_until_deadline.total_seconds() < 7200
    
    def get_primary_alternative(self) -> Optional[Dict[str, Any]]:
        """Get the primary alternative recommendation."""
        return self.alternatives[0] if self.alternatives else None
    
    def add_alternative(self, alternative: Dict[str, Any]) -> None:
        """Add an alternative recommendation."""
        if not self.alternatives:
            self.alternatives = []
        self.alternatives.append(alternative)
    
    def mark_accepted(self) -> None:
        """Mark the recommendation as accepted."""
        self.status = RecommendationStatus.ACCEPTED
    
    def mark_rejected(self) -> None:
        """Mark the recommendation as rejected."""
        self.status = RecommendationStatus.REJECTED
    
    def update_confidence(self, new_confidence: Decimal) -> None:
        """Update the confidence level of the recommendation."""
        self.confidence = max(Decimal('0'), min(Decimal('1'), new_confidence))