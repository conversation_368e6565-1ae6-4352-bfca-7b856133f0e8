"""
Projection model for player performance projections.
"""
from typing import Dict, Any, Optional, Tuple
from decimal import Decimal
from sqlalchemy import String, Integer, DECIMAL, ForeignKey, JSON, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base, TimestampMixin


class Projection(Base, TimestampMixin):
    """
    Represents a player's projected performance for a specific time period.
    
    Can represent weekly projections or season-long projections from various sources.
    """
    __tablename__ = "projections"
    
    # Primary identifier
    id: Mapped[str] = mapped_column(String(50), primary_key=True)
    
    # Player and time period
    player_id: Mapped[str] = mapped_column(String(50), ForeignKey("players.id"), nullable=False)
    week: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # None for season-long
    season: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # Projection source and metadata
    source: Mapped[str] = mapped_column(String(100), nullable=False)
    source_weight: Mapped[Decimal] = mapped_column(DECIMAL(5, 4), default=1.0, nullable=False)
    
    # Core projection values
    projected_points: Mapped[Decimal] = mapped_column(DECIMAL(8, 2), nullable=False)
    floor: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(8, 2), nullable=True)
    ceiling: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(8, 2), nullable=True)
    
    # Statistical projections stored as JSON
    stats: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    
    # Confidence and uncertainty
    confidence_level: Mapped[Decimal] = mapped_column(DECIMAL(5, 4), default=0.5, nullable=False)
    variance: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(8, 4), nullable=True)
    
    # Additional metadata
    projection_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    is_active: Mapped[bool] = mapped_column(default=True, nullable=False)
    
    # Relationships
    player: Mapped["Player"] = relationship("Player", back_populates="projections")
    
    # Database indexes for performance
    __table_args__ = (
        Index('idx_projections_player_week', 'player_id', 'week', 'season'),
        Index('idx_projections_source', 'source', 'season'),
        Index('idx_projections_active', 'is_active', 'season'),
    )
    
    def __repr__(self) -> str:
        week_str = f"Week {self.week}" if self.week else "Season"
        return f"<Projection(player_id='{self.player_id}', {week_str}, source='{self.source}', points={self.projected_points})>"
    
    def get_confidence_interval(self) -> Tuple[Optional[Decimal], Optional[Decimal]]:
        """Get the confidence interval as a tuple (floor, ceiling)."""
        return (self.floor, self.ceiling)
    
    def get_stat_projection(self, stat_name: str) -> Optional[float]:
        """Get a specific statistical projection."""
        return self.stats.get(stat_name)
    
    def calculate_points_per_game(self, games_played: int) -> Optional[Decimal]:
        """Calculate projected points per game."""
        if games_played <= 0:
            return None
        return self.projected_points / games_played
    
    def is_season_long(self) -> bool:
        """Check if this is a season-long projection."""
        return self.week is None
    
    def update_confidence_interval(self, floor: Optional[Decimal], ceiling: Optional[Decimal]) -> None:
        """Update the confidence interval bounds."""
        self.floor = floor
        self.ceiling = ceiling
        if floor and ceiling:
            # Calculate variance from confidence interval
            self.variance = ((ceiling - floor) / 4) ** 2  # Rough approximation