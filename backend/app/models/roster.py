"""
Roster and franchise models for fantasy football team management.
"""
from typing import Dict, Any, Optional, List
from decimal import Decimal
from sqlalchemy import String, Integer, JSON, ForeignKey, Boolean, DECIMAL, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base, TimestampMixin


class Franchise(Base, TimestampMixin):
    """
    Represents a fantasy football franchise (team) within a league.
    """
    __tablename__ = "franchises"
    
    # Primary identifier
    id: Mapped[str] = mapped_column(String(50), primary_key=True)
    
    # Basic franchise information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    owner_name: Mapped[str] = mapped_column(String(255), nullable=False)
    league_id: Mapped[str] = mapped_column(String(50), ForeignKey("leagues.id"), nullable=False)
    
    # MFL integration
    mfl_franchise_id: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    
    # Financial information
    salary_cap: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(10, 2), nullable=True)
    faab_budget: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(10, 2), nullable=True)
    faab_spent: Mapped[Decimal] = mapped_column(DECIMAL(10, 2), default=0, nullable=False)
    
    # Additional metadata
    franchise_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    is_active: Mapped[bool] = mapped_column(default=True, nullable=False)
    
    # Relationships
    league: Mapped["League"] = relationship("League", back_populates="franchises")
    roster: Mapped[Optional["Roster"]] = relationship(
        "Roster", back_populates="franchise", uselist=False, cascade="all, delete-orphan"
    )
    recommendations: Mapped[List["Recommendation"]] = relationship(
        "Recommendation", back_populates="franchise", cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Franchise(id='{self.id}', name='{self.name}', owner='{self.owner_name}')>"
    
    def get_remaining_faab(self) -> Decimal:
        """Calculate remaining FAAB budget."""
        if self.faab_budget is None:
            return Decimal('0')
        return self.faab_budget - self.faab_spent


class Roster(Base, TimestampMixin):
    """
    Represents the current roster for a franchise.
    """
    __tablename__ = "rosters"
    
    # Primary identifier
    id: Mapped[str] = mapped_column(String(50), primary_key=True)
    franchise_id: Mapped[str] = mapped_column(String(50), ForeignKey("franchises.id"), nullable=False)
    
    # Roster metadata
    total_salary: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(10, 2), nullable=True)
    is_valid: Mapped[bool] = mapped_column(default=True, nullable=False)
    
    # Relationships
    franchise: Mapped["Franchise"] = relationship("Franchise", back_populates="roster")
    roster_players: Mapped[List["RosterPlayer"]] = relationship(
        "RosterPlayer", back_populates="roster", cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Roster(id='{self.id}', franchise_id='{self.franchise_id}')>"
    
    def get_active_players(self) -> List["RosterPlayer"]:
        """Get all active players on this roster."""
        return [rp for rp in self.roster_players if rp.is_active]
    
    def get_players_by_position(self, position: str) -> List["RosterPlayer"]:
        """Get all active players at a specific position."""
        return [
            rp for rp in self.get_active_players() 
            if rp.player.position.value == position
        ]


class RosterPlayer(Base, TimestampMixin):
    """
    Represents a player's assignment to a specific roster.
    """
    __tablename__ = "roster_players"
    
    # Composite primary key
    id: Mapped[str] = mapped_column(String(50), primary_key=True)
    roster_id: Mapped[str] = mapped_column(String(50), ForeignKey("rosters.id"), nullable=False)
    player_id: Mapped[str] = mapped_column(String(50), ForeignKey("players.id"), nullable=False)
    
    # Assignment details
    roster_slot: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # Starting lineup slot
    salary: Mapped[Optional[Decimal]] = mapped_column(DECIMAL(10, 2), nullable=True)
    keeper_cost: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # Draft round cost
    is_keeper: Mapped[bool] = mapped_column(default=False, nullable=False)
    is_active: Mapped[bool] = mapped_column(default=True, nullable=False)
    
    # Additional metadata
    roster_player_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    
    # Relationships
    roster: Mapped["Roster"] = relationship("Roster", back_populates="roster_players")
    player: Mapped["Player"] = relationship("Player", back_populates="roster_players")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('roster_id', 'player_id', name='unique_roster_player'),
    )
    
    def __repr__(self) -> str:
        return f"<RosterPlayer(roster_id='{self.roster_id}', player_id='{self.player_id}', slot='{self.roster_slot}')>"
    
    def is_starting(self) -> bool:
        """Check if this player is in a starting lineup slot."""
        return self.roster_slot is not None and self.roster_slot != "BENCH"