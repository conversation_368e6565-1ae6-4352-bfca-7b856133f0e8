"""
Player model for fantasy football players.
"""
from typing import Dict, Any, Optional, List
from sqlalchemy import String, Integer, JSON, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from enum import Enum
from .base import Base, TimestampMixin


class PlayerPosition(str, Enum):
    """Enumeration of fantasy football positions."""
    QB = "QB"
    RB = "RB"
    WR = "WR"
    TE = "TE"
    K = "K"
    DEF = "DEF"
    DL = "DL"
    LB = "LB"
    DB = "DB"


class InjuryStatus(str, Enum):
    """Enumeration of player injury statuses."""
    HEALTHY = "HEALTHY"
    QUESTIONABLE = "QUESTIONABLE"
    DOUBTFUL = "DOUBTFUL"
    OUT = "OUT"
    IR = "IR"
    PUP = "PUP"
    SUSPENDED = "SUSPENDED"


class Player(Base, TimestampMixin):
    """
    Represents a fantasy football player with their basic information.
    
    Stores player metadata and links to projections and roster assignments.
    """
    __tablename__ = "players"
    
    # Primary identifier
    id: Mapped[str] = mapped_column(String(50), primary_key=True)
    
    # Basic player information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    position: Mapped[PlayerPosition] = mapped_column(SQLEnum(PlayerPosition), nullable=False)
    team: Mapped[str] = mapped_column(String(10), nullable=False)
    
    # Season information
    bye_week: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    injury_status: Mapped[InjuryStatus] = mapped_column(
        SQLEnum(InjuryStatus), 
        default=InjuryStatus.HEALTHY, 
        nullable=False
    )
    
    # External identifiers
    mfl_id: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    espn_id: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    yahoo_id: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    
    # Additional metadata stored as JSON
    player_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    
    # Relationships
    projections: Mapped[List["Projection"]] = relationship(
        "Projection", back_populates="player", cascade="all, delete-orphan"
    )
    rankings: Mapped[List["Ranking"]] = relationship(
        "Ranking", back_populates="player", cascade="all, delete-orphan"
    )
    roster_players: Mapped[List["RosterPlayer"]] = relationship(
        "RosterPlayer", back_populates="player", cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Player(id='{self.id}', name='{self.name}', position='{self.position}', team='{self.team}')>"
    
    def is_available(self) -> bool:
        """Check if player is available (not on any roster)."""
        return len(self.roster_players) == 0
    
    def get_current_team_assignment(self) -> Optional[str]:
        """Get the franchise ID where this player is currently rostered."""
        active_assignments = [rp for rp in self.roster_players if rp.is_active]
        return active_assignments[0].roster.franchise_id if active_assignments else None