"""
Example usage of the projections aggregation system.

This script demonstrates how to use the ProjectionsAggregator and 
ProjectionsCacheManager to combine multiple projection sources.
"""
import sys
import os
from decimal import Decimal
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.core.database import SessionLocal
from app.models.projection import Projection
from app.models.player import Player
from app.services.projections_aggregator import ProjectionsAggregator, ProjectionsCacheManager


def create_sample_data(db):
    """Create sample players and projections for demonstration."""
    
    # Create a sample player
    player = Player(
        id="player_mahomes",
        name="<PERSON> Mahom<PERSON>",
        position="QB",
        team="KC",
        bye_week=10,
        metadata={"height": "6-3", "weight": 230}
    )
    
    # Check if player already exists
    existing_player = db.query(Player).filter(Player.id == player.id).first()
    if not existing_player:
        db.add(player)
        db.commit()
        print(f"Created player: {player.name}")
    else:
        print(f"Player already exists: {existing_player.name}")
    
    # Create sample projections from different sources
    projection_data = [
        {
            'source': 'FantasyPros',
            'projected_points': 24.5,
            'floor': 18.0,
            'ceiling': 32.0,
            'stats': {'pass_yards': 285, 'pass_tds': 2.1, 'rush_yards': 15}
        },
        {
            'source': 'ESPN',
            'projected_points': 23.8,
            'floor': 17.5,
            'ceiling': 30.5,
            'stats': {'pass_yards': 275, 'pass_tds': 2.0, 'rush_yards': 12}
        },
        {
            'source': 'Yahoo',
            'projected_points': 25.2,
            'floor': 19.0,
            'ceiling': 33.0,
            'stats': {'pass_yards': 295, 'pass_tds': 2.2, 'rush_yards': 18}
        },
        {
            'source': 'NFL',
            'projected_points': 24.1,
            'floor': 18.5,
            'ceiling': 31.0,
            'stats': {'pass_yards': 280, 'pass_tds': 2.0, 'rush_yards': 14}
        }
    ]
    
    # Create projections
    for i, data in enumerate(projection_data):
        projection_id = f"proj_mahomes_w1_{data['source'].lower()}"
        
        # Check if projection already exists
        existing_proj = db.query(Projection).filter(Projection.id == projection_id).first()
        if existing_proj:
            print(f"Projection already exists: {data['source']}")
            continue
        
        projection = Projection(
            id=projection_id,
            player_id=player.id,
            week=1,
            season=2024,
            source=data['source'],
            projected_points=Decimal(str(data['projected_points'])),
            floor=Decimal(str(data['floor'])),
            ceiling=Decimal(str(data['ceiling'])),
            stats=data['stats'],
            confidence_level=Decimal('0.75'),
            is_active=True
        )
        
        db.add(projection)
        print(f"Created projection: {data['source']} - {data['projected_points']} points")
    
    db.commit()


def demonstrate_aggregation(db):
    """Demonstrate the projections aggregation functionality."""
    
    print("\n" + "="*60)
    print("PROJECTIONS AGGREGATION DEMONSTRATION")
    print("="*60)
    
    # Create aggregator
    aggregator = ProjectionsAggregator(db)
    
    # 1. Get available sources
    print("\n1. Available projection sources for 2024:")
    sources = aggregator.get_projection_sources(2024)
    for source in sources:
        print(f"   - {source}")
    
    # 2. Get source statistics
    print("\n2. Source statistics:")
    for source in sources:
        stats = aggregator.get_source_statistics(source, 2024)
        if stats:
            print(f"   {source}:")
            print(f"     Total projections: {stats['total_projections']}")
            print(f"     Mean points: {stats['mean_points']:.2f}")
            print(f"     Std deviation: {stats['std_dev']:.2f}")
    
    # 3. Run backtesting
    print("\n3. Backtesting results:")
    backtest_results = aggregator.run_backtesting(2024, weeks_back=4, min_samples=1)
    for result in backtest_results:
        print(f"   {result.source}:")
        print(f"     MAE: {result.mae:.3f}")
        print(f"     RMSE: {result.rmse:.3f}")
        print(f"     Accuracy Score: {result.accuracy_score:.3f}")
        print(f"     Sample Size: {result.sample_size}")
    
    # 4. Get optimized weights
    print("\n4. Optimized source weights:")
    weights = aggregator.optimize_source_weights(2024)
    for source, weight in weights.items():
        print(f"   {source}: {weight:.3f}")
    
    # 5. Aggregate projections for Patrick Mahomes
    print("\n5. Aggregated projection for Patrick Mahomes (Week 1):")
    aggregated = aggregator.aggregate_projections(
        player_id="player_mahomes",
        week=1,
        season=2024,
        min_sources=2
    )
    
    if aggregated:
        print(f"   Player: {aggregated.player_id}")
        print(f"   Week: {aggregated.week}")
        print(f"   Projected Points: {aggregated.projected_points:.2f}")
        print(f"   Confidence Interval: ({aggregated.confidence_interval[0]:.2f}, {aggregated.confidence_interval[1]:.2f})")
        print(f"   Variance: {aggregated.variance:.3f}")
        print(f"   Source Count: {aggregated.source_count}")
        print(f"   Source Weights: {aggregated.source_weights}")
        print(f"   Metadata: {aggregated.metadata}")
    else:
        print("   No aggregated projection available (insufficient sources)")


def demonstrate_caching(db):
    """Demonstrate the caching functionality."""
    
    print("\n" + "="*60)
    print("CACHING DEMONSTRATION")
    print("="*60)
    
    # Create aggregator and cache manager
    aggregator = ProjectionsAggregator(db)
    cache_manager = ProjectionsCacheManager(db, aggregator)
    
    # 1. Get cached projection (first call - cache miss)
    print("\n1. First call (cache miss):")
    start_time = datetime.now()
    cached_proj = cache_manager.get_cached_projection(
        player_id="player_mahomes",
        week=1,
        season=2024
    )
    first_call_time = (datetime.now() - start_time).total_seconds()
    
    if cached_proj:
        print(f"   Projected Points: {cached_proj.projected_points:.2f}")
        print(f"   Time taken: {first_call_time:.4f} seconds")
    
    # 2. Get cached projection (second call - cache hit)
    print("\n2. Second call (cache hit):")
    start_time = datetime.now()
    cached_proj2 = cache_manager.get_cached_projection(
        player_id="player_mahomes",
        week=1,
        season=2024
    )
    second_call_time = (datetime.now() - start_time).total_seconds()
    
    if cached_proj2:
        print(f"   Projected Points: {cached_proj2.projected_points:.2f}")
        print(f"   Time taken: {second_call_time:.4f} seconds")
        print(f"   Speed improvement: {first_call_time / second_call_time:.1f}x faster")
    
    # 3. Cache statistics
    print("\n3. Cache statistics:")
    stats = cache_manager.get_cache_stats()
    print(f"   Total entries: {stats['total_entries']}")
    print(f"   Active entries: {stats['active_entries']}")
    print(f"   Expired entries: {stats['expired_entries']}")
    
    # 4. Clear cache
    print("\n4. Clearing cache...")
    cache_manager.clear_cache()
    stats_after = cache_manager.get_cache_stats()
    print(f"   Total entries after clear: {stats_after['total_entries']}")


def main():
    """Main demonstration function."""
    
    print("AI Fantasy Assistant - Projections Aggregation Example")
    print("=" * 60)
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Create sample data
        print("Creating sample data...")
        create_sample_data(db)
        
        # Demonstrate aggregation
        demonstrate_aggregation(db)
        
        # Demonstrate caching
        demonstrate_caching(db)
        
        print("\n" + "="*60)
        print("DEMONSTRATION COMPLETE")
        print("="*60)
        print("\nKey features demonstrated:")
        print("- Weighted ensemble aggregation of multiple projection sources")
        print("- Backtesting framework for optimizing source weights")
        print("- Confidence interval calculation for projection uncertainty")
        print("- Caching system for improved performance")
        print("- Source statistics and analysis")
        
    except Exception as e:
        print(f"Error during demonstration: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


if __name__ == "__main__":
    main()