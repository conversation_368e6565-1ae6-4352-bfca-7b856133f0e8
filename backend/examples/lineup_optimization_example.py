"""
Example demonstrating lineup optimization functionality.

This script shows how to use the LineupOptimizer service to generate
optimal lineup recommendations for fantasy football.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unittest.mock import Mock
from decimal import Decimal
from datetime import datetime

from app.services.lineup_optimizer import (
    LineupOptimizer, LineupSlot, WeatherCondition, MatchupContext, PlayerNews
)
from app.models.player import Player, PlayerPosition, InjuryStatus
from app.models.roster import Roster, RosterPlayer, Franchise
from app.models.projection import Projection
from app.models.league import League


def create_sample_data():
    """Create sample data for demonstration."""
    
    # Create sample league
    league = League(
        id="demo_league",
        name="Demo Fantasy League",
        season=2024,
        roster_slots=[
            {"position": "QB", "count": 1, "type": "starting"},
            {"position": "RB", "count": 2, "type": "starting"},
            {"position": "WR", "count": 2, "type": "starting"},
            {"position": "TE", "count": 1, "type": "starting"},
            {"position": "FLEX", "count": 1, "type": "starting"},
            {"position": "K", "count": 1, "type": "starting"},
            {"position": "DEF", "count": 1, "type": "starting"}
        ]
    )
    
    # Create sample players
    players = [
        Player(id="qb1", name="Josh Allen", position=PlayerPosition.QB, team="BUF", injury_status=InjuryStatus.HEALTHY),
        Player(id="rb1", name="Christian McCaffrey", position=PlayerPosition.RB, team="SF", injury_status=InjuryStatus.HEALTHY),
        Player(id="rb2", name="Derrick Henry", position=PlayerPosition.RB, team="BAL", injury_status=InjuryStatus.HEALTHY),
        Player(id="rb3", name="Josh Jacobs", position=PlayerPosition.RB, team="LV", injury_status=InjuryStatus.QUESTIONABLE),
        Player(id="wr1", name="Tyreek Hill", position=PlayerPosition.WR, team="MIA", injury_status=InjuryStatus.HEALTHY),
        Player(id="wr2", name="Stefon Diggs", position=PlayerPosition.WR, team="HOU", injury_status=InjuryStatus.HEALTHY),
        Player(id="wr3", name="DK Metcalf", position=PlayerPosition.WR, team="SEA", injury_status=InjuryStatus.HEALTHY),
        Player(id="te1", name="Travis Kelce", position=PlayerPosition.TE, team="KC", injury_status=InjuryStatus.HEALTHY),
        Player(id="k1", name="Justin Tucker", position=PlayerPosition.K, team="BAL", injury_status=InjuryStatus.HEALTHY),
        Player(id="def1", name="San Francisco DEF", position=PlayerPosition.DEF, team="SF", injury_status=InjuryStatus.HEALTHY)
    ]
    
    # Create sample projections
    projections_data = [
        ("qb1", 22.5, 18.0, 28.0, 3.5),
        ("rb1", 18.2, 14.0, 24.0, 4.0),
        ("rb2", 16.8, 12.5, 22.0, 3.8),
        ("rb3", 14.5, 10.0, 19.0, 4.2),
        ("wr1", 16.3, 12.0, 22.0, 4.5),
        ("wr2", 15.1, 11.5, 20.0, 3.9),
        ("wr3", 13.8, 9.5, 18.5, 4.1),
        ("te1", 14.2, 10.5, 19.0, 3.7),
        ("k1", 8.5, 5.0, 12.0, 2.5),
        ("def1", 9.8, 6.0, 15.0, 3.2)
    ]
    
    projections = []
    for player_id, points, floor, ceiling, variance in projections_data:
        projection = Projection(
            id=f"proj_{player_id}",
            player_id=player_id,
            week=1,
            season=2024,
            source="demo",
            projected_points=Decimal(str(points)),
            floor=Decimal(str(floor)),
            ceiling=Decimal(str(ceiling)),
            variance=Decimal(str(variance)),
            is_active=True
        )
        # Link player to projection
        player = next(p for p in players if p.id == player_id)
        projection.player = player
        projections.append(projection)
    
    # Create franchise and roster
    franchise = Franchise(
        id="demo_franchise",
        name="Demo Team",
        owner_name="Demo Owner",
        league_id=league.id
    )
    franchise.league = league
    
    roster = Roster(
        id="demo_roster",
        franchise_id=franchise.id
    )
    roster.franchise = franchise
    franchise.roster = roster
    
    # Create roster players
    roster_players = []
    for player in players:
        roster_player = RosterPlayer(
            id=f"rp_{player.id}",
            roster_id=roster.id,
            player_id=player.id,
            is_active=True
        )
        roster_player.player = player
        roster_player.roster = roster
        roster_players.append(roster_player)
    
    roster.roster_players = roster_players
    
    return franchise, players, projections


def demonstrate_lineup_optimization():
    """Demonstrate lineup optimization functionality."""
    
    print("🏈 Fantasy Football Lineup Optimization Demo")
    print("=" * 50)
    
    # Create sample data
    franchise, players, projections = create_sample_data()
    
    # Create mock database
    mock_db = Mock()
    
    # Mock database queries
    mock_db.query.return_value.filter.return_value.first.return_value = franchise
    
    def mock_projection_query(*args):
        # Return projections based on player_id filter
        mock_query = Mock()
        def mock_filter(*filter_args):
            mock_filtered = Mock()
            mock_filtered.first.return_value = projections[0]  # Return first projection for simplicity
            return mock_filtered
        mock_query.filter = mock_filter
        return mock_query
    
    mock_db.query.side_effect = [
        Mock(return_value=Mock(filter=Mock(return_value=Mock(first=Mock(return_value=franchise))))),
        mock_projection_query
    ]
    
    # Create optimizer
    optimizer = LineupOptimizer(mock_db)
    
    print("\n📊 Sample Player Projections:")
    print("-" * 30)
    for projection in projections[:5]:  # Show first 5
        player = projection.player
        print(f"{player.name} ({player.position.value}): {projection.projected_points} pts "
              f"(Floor: {projection.floor}, Ceiling: {projection.ceiling})")
    print("...")
    
    # Mock the internal methods to use our sample data
    def mock_get_players_with_projections(roster, week, season):
        players_data = {}
        for projection in projections:
            player = projection.player
            players_data[player.id] = {
                "player": player,
                "projection": projection,
                "base_projection": float(projection.projected_points),
                "variance": float(projection.variance),
                "floor": float(projection.floor),
                "ceiling": float(projection.ceiling),
                "adjusted_projection": float(projection.projected_points),
                "matchup_context": MatchupContext(
                    opponent_team="OPP",
                    opponent_rank_vs_position=16,
                    weather_condition=WeatherCondition.CLEAR,
                    temperature=70,
                    wind_speed=5,
                    is_home_game=True,
                    game_total=45.0,
                    spread=3.0,
                    implied_team_total=24.0
                )
            }
        return players_data
    
    # Patch the method
    optimizer._get_available_players_with_projections = mock_get_players_with_projections
    
    try:
        # Generate lineup recommendation
        print("\n🎯 Generating Optimal Lineup...")
        print("-" * 30)
        
        recommendation = optimizer.optimize_lineup("demo_franchise", 1)
        
        print(f"\n✅ Optimal Lineup Generated!")
        print(f"📈 Projected Points: {recommendation.projected_points:.1f}")
        print(f"🎲 Win Probability: {recommendation.win_probability:.1%}")
        print(f"🎯 Confidence: {recommendation.confidence:.1%}")
        print(f"⚠️  Risk Level: {recommendation.risk_level:.1%}")
        
        print(f"\n🏆 Starting Lineup:")
        print("-" * 20)
        for slot, player_id in recommendation.lineup.items():
            if player_id in [p.id for p in players]:
                player = next(p for p in players if p.id == player_id)
                projection = next(p for p in projections if p.player_id == player_id)
                print(f"{slot.value:>4}: {player.name} ({projection.projected_points} pts)")
        
        print(f"\n💡 Rationale:")
        print(f"{recommendation.rationale}")
        
        if recommendation.alternatives:
            print(f"\n🔄 Top Alternative:")
            alt = recommendation.alternatives[0]
            print(f"   {alt['change']}")
            print(f"   Win Probability: {alt['win_probability']:.1%}")
        
    except Exception as e:
        print(f"❌ Error during optimization: {e}")
        return
    
    # Demonstrate weather adjustments
    print(f"\n🌤️  Weather Impact Analysis:")
    print("-" * 30)
    
    qb_clear = optimizer._get_weather_adjustment(PlayerPosition.QB, WeatherCondition.CLEAR)
    qb_wind = optimizer._get_weather_adjustment(PlayerPosition.QB, WeatherCondition.WIND)
    qb_dome = optimizer._get_weather_adjustment(PlayerPosition.QB, WeatherCondition.DOME)
    
    print(f"QB in Clear Weather: {qb_clear:.2f}x multiplier")
    print(f"QB in Windy Weather: {qb_wind:.2f}x multiplier")
    print(f"QB in Dome: {qb_dome:.2f}x multiplier")
    
    rb_rain = optimizer._get_weather_adjustment(PlayerPosition.RB, WeatherCondition.RAIN)
    wr_wind = optimizer._get_weather_adjustment(PlayerPosition.WR, WeatherCondition.WIND)
    
    print(f"RB in Rain: {rb_rain:.2f}x multiplier")
    print(f"WR in Wind: {wr_wind:.2f}x multiplier")
    
    # Demonstrate injury adjustments
    print(f"\n🏥 Injury Status Impact:")
    print("-" * 25)
    
    healthy = optimizer._get_injury_adjustment(InjuryStatus.HEALTHY)
    questionable = optimizer._get_injury_adjustment(InjuryStatus.QUESTIONABLE)
    doubtful = optimizer._get_injury_adjustment(InjuryStatus.DOUBTFUL)
    out = optimizer._get_injury_adjustment(InjuryStatus.OUT)
    
    print(f"Healthy: {healthy:.2f}x multiplier")
    print(f"Questionable: {questionable:.2f}x multiplier")
    print(f"Doubtful: {doubtful:.2f}x multiplier")
    print(f"Out: {out:.2f}x multiplier")
    
    # Demonstrate late-breaking news
    print(f"\n📰 Late-Breaking News Impact:")
    print("-" * 35)
    
    news_items = [
        PlayerNews(
            player_id="rb3",
            news_type="injury",
            severity="high",
            description="Josh Jacobs ruled out with ankle injury",
            timestamp=datetime.now(),
            impact_on_projection=-1.0
        )
    ]
    
    print("News: Josh Jacobs ruled out with ankle injury")
    print("Impact: Would trigger lineup adjustment recommendation")
    print("Action: Replace with next best RB or FLEX option")
    
    print(f"\n🎉 Lineup Optimization Demo Complete!")
    print("=" * 50)


if __name__ == "__main__":
    demonstrate_lineup_optimization()