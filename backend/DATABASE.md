# Database Models Documentation

This document describes the database models for the AI Fantasy Assistant application.

## Overview

The database uses SQLAlchemy ORM with support for both PostgreSQL (production) and SQLite (development). The models are designed to support fantasy football league management with MFL integration, projections aggregation, and AI-powered recommendations.

## Core Models

### Base Models

#### `Base`
- SQLAlchemy declarative base class for all models
- Provides common database functionality

#### `TimestampMixin`
- Mixin class that adds `created_at` and `updated_at` timestamps
- Automatically managed by SQLAlchemy

### League Management

#### `League`
- Represents a fantasy football league
- **Key Fields:**
  - `id`: Unique league identifier
  - `name`: League name
  - `season`: Season year (e.g., 2024)
  - `scoring_rules`: JSON field with scoring configuration
  - `roster_slots`: JSON array defining roster positions
  - `keeper_rules`: Optional JSON with keeper configuration
  - `mfl_league_id`: MFL API integration ID
- **Methods:**
  - `validate_scoring_rules()`: Validates scoring configuration
  - `get_roster_slot_count(position)`: Gets slot count for position

#### `Franchise`
- Represents a team within a league
- **Key Fields:**
  - `id`: Unique franchise identifier
  - `name`: Team name
  - `owner_name`: Owner's name
  - `league_id`: Foreign key to League
  - `faab_budget`: FAAB budget amount
  - `faab_spent`: Amount of FAAB spent
- **Methods:**
  - `get_remaining_faab()`: Calculates remaining FAAB budget

### Player Management

#### `Player`
- Represents a fantasy football player
- **Key Fields:**
  - `id`: Unique player identifier
  - `name`: Player name
  - `position`: Player position (enum)
  - `team`: NFL team abbreviation
  - `bye_week`: Bye week number
  - `injury_status`: Current injury status (enum)
  - `mfl_id`: MFL player ID for integration
- **Enums:**
  - `PlayerPosition`: QB, RB, WR, TE, K, DEF, DL, LB, DB
  - `InjuryStatus`: HEALTHY, QUESTIONABLE, DOUBTFUL, OUT, IR, PUP, SUSPENDED
- **Methods:**
  - `is_available()`: Checks if player is on any roster
  - `get_current_team_assignment()`: Gets franchise ID if rostered

### Roster Management

#### `Roster`
- Represents a franchise's current roster
- **Key Fields:**
  - `id`: Unique roster identifier
  - `franchise_id`: Foreign key to Franchise
  - `total_salary`: Optional salary cap total
  - `is_valid`: Roster validity flag
- **Methods:**
  - `get_active_players()`: Gets all active roster players
  - `get_players_by_position(position)`: Gets players at specific position

#### `RosterPlayer`
- Represents a player's assignment to a roster
- **Key Fields:**
  - `id`: Unique assignment identifier
  - `roster_id`: Foreign key to Roster
  - `player_id`: Foreign key to Player
  - `roster_slot`: Starting lineup position (optional)
  - `salary`: Player salary (optional)
  - `keeper_cost`: Draft round cost for keepers
  - `is_keeper`: Keeper flag
  - `is_active`: Active assignment flag
- **Methods:**
  - `is_starting()`: Checks if player is in starting lineup

### Projections

#### `Projection`
- Represents player performance projections
- **Key Fields:**
  - `id`: Unique projection identifier
  - `player_id`: Foreign key to Player
  - `week`: Week number (None for season-long)
  - `season`: Season year
  - `source`: Projection source name
  - `projected_points`: Projected fantasy points
  - `floor`: Confidence interval floor
  - `ceiling`: Confidence interval ceiling
  - `stats`: JSON field with statistical projections
  - `confidence_level`: Projection confidence (0-1)
- **Methods:**
  - `is_season_long()`: Checks if season-long projection
  - `get_confidence_interval()`: Returns (floor, ceiling) tuple
  - `get_stat_projection(stat_name)`: Gets specific stat projection
  - `update_confidence_interval(floor, ceiling)`: Updates bounds

### Recommendations

#### `Recommendation`
- Represents AI-generated recommendations
- **Key Fields:**
  - `id`: Unique recommendation identifier
  - `league_id`: Foreign key to League
  - `franchise_id`: Foreign key to Franchise (optional)
  - `type`: Recommendation type (enum)
  - `priority`: Priority level (enum)
  - `status`: Current status (enum)
  - `title`: Recommendation title
  - `description`: Detailed description
  - `rationale`: AI reasoning
  - `confidence`: Confidence level (0-1)
  - `expected_impact`: Expected point impact
  - `expires_at`: Expiration timestamp
  - `recommendation_data`: JSON with structured data
  - `alternatives`: JSON array of alternatives
- **Enums:**
  - `RecommendationType`: KEEPER, DRAFT, TRADE, LINEUP, WAIVER, FAAB, DROP, ALERT
  - `RecommendationPriority`: LOW, MEDIUM, HIGH, URGENT
  - `RecommendationStatus`: ACTIVE, ACCEPTED, REJECTED, EXPIRED
- **Methods:**
  - `is_expired()`: Checks if recommendation expired
  - `is_urgent()`: Checks if recommendation is urgent
  - `add_alternative(alternative)`: Adds alternative option
  - `mark_accepted()`: Marks as accepted
  - `mark_rejected()`: Marks as rejected
  - `update_confidence(confidence)`: Updates confidence level

## Database Configuration

### Connection Setup

The database connection is configured in `app/core/database.py`:

```python
# Development (SQLite)
DATABASE_URL = "sqlite:///./fantasy_assistant.db"

# Production (PostgreSQL)
DATABASE_URL = "postgresql://user:password@localhost:5432/fantasy_db"
```

### Session Management

Use the `get_db()` dependency for database sessions:

```python
from app.core.database import get_db

def my_function(db: Session = Depends(get_db)):
    # Use db session here
    pass
```

## Migrations

### Alembic Setup

Alembic is configured for database migrations:

```bash
# Create a new migration
python scripts/create_migration.py -m "Add new field" --autogenerate

# Apply migrations (when dependencies are installed)
alembic upgrade head

# Downgrade migrations
alembic downgrade -1
```

### Migration Files

- `alembic.ini`: Alembic configuration
- `alembic/env.py`: Migration environment setup
- `alembic/versions/`: Migration files directory

## Sample Data

### Creating Sample Data

Use the initialization script to create sample data:

```bash
# Create tables only
python scripts/init_db.py --tables-only

# Create tables with sample data
python scripts/init_db.py --sample-data

# Clear existing data first
python scripts/init_db.py --clear-data --sample-data
```

### Sample Data Contents

The sample data includes:
- Sample league with scoring rules and roster configuration
- Sample players (Mahomes, McCaffrey, Jefferson, Kelce, Tucker)
- Sample franchises with FAAB budgets
- Sample roster assignments
- Sample projections (season-long and weekly)
- Sample recommendations (keeper and lineup)

## Testing

### Unit Tests

Comprehensive unit tests are provided in `tests/test_models.py`:

```bash
# Run tests (when dependencies are installed)
python -m pytest tests/test_models.py -v

# Run specific test class
python -m pytest tests/test_models.py::TestLeague -v
```

### Test Coverage

Tests cover:
- Model creation and validation
- Relationship integrity
- Business logic methods
- Enum handling
- Edge cases and error conditions

### Validation Script

For environments without dependencies installed:

```bash
# Validate model structure
python scripts/validate_models.py
```

## Usage Examples

### Creating a League

```python
from app.models import League

league = League(
    id="my_league_2024",
    name="My Fantasy League",
    season=2024,
    scoring_rules={
        "passing_yards": 0.04,
        "passing_touchdowns": 4,
        "rushing_yards": 0.1,
        "rushing_touchdowns": 6,
        "receiving_yards": 0.1,
        "receiving_touchdowns": 6
    },
    roster_slots=[
        {"position": "QB", "count": 1, "type": "starter"},
        {"position": "RB", "count": 2, "type": "starter"},
        {"position": "WR", "count": 2, "type": "starter"},
        {"position": "FLEX", "count": 1, "type": "starter"},
        {"position": "BENCH", "count": 6, "type": "bench"}
    ]
)
```

### Adding Players to Roster

```python
from app.models import Player, Roster, RosterPlayer, PlayerPosition

# Create player
player = Player(
    id="mahomes_2024",
    name="Patrick Mahomes",
    position=PlayerPosition.QB,
    team="KC",
    bye_week=10
)

# Add to roster
roster_player = RosterPlayer(
    id="roster_mahomes",
    roster_id="my_roster",
    player_id=player.id,
    roster_slot="QB",
    is_keeper=True,
    keeper_cost=3
)
```

### Creating Projections

```python
from app.models import Projection
from decimal import Decimal

projection = Projection(
    id="mahomes_week1_2024",
    player_id="mahomes_2024",
    week=1,
    season=2024,
    source="FantasyPros",
    projected_points=Decimal("22.5"),
    floor=Decimal("18.0"),
    ceiling=Decimal("28.0"),
    confidence_level=Decimal("0.85"),
    stats={
        "passing_yards": 275,
        "passing_touchdowns": 2,
        "rushing_yards": 15,
        "interceptions": 1
    }
)
```

### Generating Recommendations

```python
from app.models import Recommendation, RecommendationType, RecommendationPriority
from decimal import Decimal

recommendation = Recommendation(
    id="keeper_mahomes_2024",
    league_id="my_league_2024",
    franchise_id="my_franchise",
    type=RecommendationType.KEEPER,
    priority=RecommendationPriority.HIGH,
    title="Keep Patrick Mahomes",
    description="Mahomes provides excellent value as a keeper",
    rationale="At a 3rd round cost, Mahomes projects to be the #2 QB with high consistency",
    confidence=Decimal("0.92"),
    expected_impact=Decimal("15.5"),
    recommendation_data={
        "player_id": "mahomes_2024",
        "keeper_cost": 3,
        "projected_value": 285.6
    }
)
```

## Performance Considerations

### Indexes

The models include strategic indexes for performance:
- `projections`: Indexed on `(player_id, week, season)`, `source`, and `is_active`
- Foreign key relationships are automatically indexed

### Query Optimization

- Use `select_related()` and `prefetch_related()` for relationship queries
- Consider pagination for large result sets
- Use database-level aggregations when possible

### Caching

- Projection data is suitable for caching
- League configuration changes infrequently
- Recommendation data should be fresh

## Security Considerations

### Data Validation

- All models include appropriate field validation
- Enums prevent invalid status values
- Decimal fields prevent precision issues with financial data

### Access Control

- Models support franchise-level data isolation
- Recommendations can be franchise-specific or league-wide
- Audit trails via timestamp mixins

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all model imports are in `__init__.py`
2. **Migration Conflicts**: Use `alembic merge` for conflicting migrations
3. **Relationship Issues**: Check foreign key constraints and cascade settings
4. **JSON Field Issues**: Ensure JSON data is properly serializable

### Debug Mode

Enable debug mode in settings to see SQL queries:

```python
# In config.py
DEBUG = True  # Shows SQL queries in logs
```

This completes the database models implementation for task 2. All models are properly structured with relationships, validation, and comprehensive test coverage.