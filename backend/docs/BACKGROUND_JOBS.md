# Background Job Processing System

This document describes the comprehensive background job processing system implemented using Ce<PERSON><PERSON> with <PERSON><PERSON> as the message broker.

## Overview

The background job system handles:
- **Data Refresh**: Periodic updates of MFL data, projections, and player information
- **Alert Processing**: Deadline monitoring, news processing, and notifications
- **File Processing**: Asynchronous handling of uploaded data files
- **System Monitoring**: Health checks, diagnostics, and job management

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI App   │    │  Celery Worker  │    │  Celery Beat    │
│                 │    │                 │    │   Scheduler     │
│ - Job Triggers  │    │ - Task Execution│    │ - Periodic Jobs │
│ - Job Monitoring│    │ - Error Handling│    │ - Cron Schedule │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │      Redis      │
                    │                 │
                    │ - Message Broker│
                    │ - Result Backend│
                    │ - Task Queue    │
                    └─────────────────┘
```

## Components

### 1. Task Modules

#### Data Refresh (`app.tasks.data_refresh`)
- `refresh_mfl_data()`: Updates league data from MFL API
- `refresh_projection_aggregates()`: Recalculates projection ensembles
- `cleanup_old_task_results()`: Removes expired data and task results
- `refresh_player_data(league_id)`: Updates specific league player data
- `update_player_injury_status()`: Refreshes injury reports

#### Alert Processing (`app.tasks.alert_processing`)
- `check_upcoming_deadlines()`: Monitors keeper, waiver, and lineup deadlines
- `process_player_news()`: Analyzes player news for relevant alerts
- `send_pending_notifications()`: Delivers queued notifications
- `create_lineup_lock_alerts(league_id, week)`: Creates lineup deadline alerts
- `process_late_breaking_news(news_item)`: Handles urgent news updates

#### File Processing (`app.tasks.file_processing`)
- `process_uploaded_file()`: Handles CSV/Excel file processing
- `cleanup_old_data()`: Removes old data before imports
- `validate_uploaded_data()`: Validates data integrity

#### Monitoring (`app.tasks.monitoring`)
- `health_check()`: Basic system health verification
- `system_diagnostics()`: Comprehensive system status check
- `monitor_system_resources()`: Resource usage monitoring
- `handle_dead_letter()`: Failed task recovery

### 2. Job Queues

The system uses multiple queues for task prioritization:

- **default**: General purpose tasks
- **data_refresh**: Data ingestion and updates
- **alerts**: Time-sensitive notifications
- **file_processing**: File upload handling
- **failed**: Dead letter queue for failed tasks

### 3. Scheduled Tasks

Periodic tasks run automatically via Celery Beat:

```python
# Every 6 hours - MFL data refresh
'refresh-mfl-data': {
    'task': 'app.tasks.data_refresh.refresh_mfl_data',
    'schedule': crontab(minute=0, hour='*/6'),
}

# Every 15 minutes - Player news processing
'process-player-news': {
    'task': 'app.tasks.alert_processing.process_player_news',
    'schedule': crontab(minute='*/15'),
}

# Every 30 minutes - Deadline checks
'check-deadlines': {
    'task': 'app.tasks.alert_processing.check_upcoming_deadlines',
    'schedule': crontab(minute='*/30'),
}

# Daily at 2 AM - Cleanup old data
'cleanup-old-tasks': {
    'task': 'app.tasks.data_refresh.cleanup_old_task_results',
    'schedule': crontab(minute=0, hour=2),
}

# Twice daily - Projection aggregation
'refresh-projections': {
    'task': 'app.tasks.data_refresh.refresh_projection_aggregates',
    'schedule': crontab(minute=0, hour='8,20'),
}
```

## Configuration

### Celery Settings

Key configuration options in `app/core/celery.py`:

```python
# Retry configuration
task_default_retry_delay=60  # 1 minute
task_max_retries=3
task_acks_late=True
task_reject_on_worker_lost=True

# Time limits
task_time_limit=30 * 60      # 30 minutes hard limit
task_soft_time_limit=25 * 60 # 25 minutes soft limit

# Worker settings
worker_prefetch_multiplier=1
worker_max_tasks_per_child=1000
```

### Environment Variables

Required environment variables:

```bash
DATABASE_URL=postgresql://user:pass@localhost:5432/fantasy_db
REDIS_URL=redis://localhost:6379
ENVIRONMENT=development
```

## Deployment

### Docker Compose

The system includes Docker services for all components:

```yaml
# Celery Worker
worker:
  command: celery -A app.core.celery:celery_app worker --loglevel=info --concurrency=4 --queues=default,data_refresh,alerts,file_processing,failed

# Celery Beat Scheduler
beat:
  command: celery -A app.core.celery:celery_app beat --loglevel=info

# Celery Flower (monitoring UI)
flower:
  command: celery -A app.core.celery:celery_app flower --port=5555
```

### Manual Startup

Start components individually:

```bash
# Start worker
python backend/scripts/start_worker.py

# Start beat scheduler
python backend/scripts/start_beat.py

# Start with Docker Compose
docker-compose up worker beat

# Start with monitoring UI
docker-compose --profile monitoring up
```

## API Endpoints

### Job Management (`/api/v1/jobs`)

#### Monitoring
- `GET /jobs/health` - System health check
- `GET /jobs/diagnostics` - Comprehensive diagnostics
- `GET /jobs/active` - List active tasks
- `GET /jobs/scheduled` - List scheduled tasks
- `GET /jobs/workers` - Worker statistics
- `GET /jobs/queues` - Queue lengths
- `GET /jobs/stats` - Overall job statistics

#### Task Management
- `GET /jobs/task/{task_id}` - Get task status
- `POST /jobs/task/{task_id}/retry` - Retry failed task
- `DELETE /jobs/task/{task_id}` - Cancel running task

#### Manual Triggers
- `POST /jobs/trigger/mfl-refresh` - Trigger MFL data refresh
- `POST /jobs/trigger/projection-refresh` - Trigger projection update
- `POST /jobs/trigger/player-refresh/{league_id}` - Refresh league players
- `POST /jobs/trigger/lineup-alerts/{league_id}/{week}` - Create lineup alerts
- `POST /jobs/trigger/breaking-news` - Process urgent news

#### Maintenance
- `GET /jobs/failed` - List failed tasks
- `POST /jobs/purge-queue/{queue_name}` - Purge queue

## Command Line Management

Use the management script for job operations:

```bash
# List active and scheduled tasks
python backend/scripts/manage_jobs.py list-active
python backend/scripts/manage_jobs.py list-scheduled

# Get system statistics
python backend/scripts/manage_jobs.py worker-stats
python backend/scripts/manage_jobs.py queue-lengths

# Trigger tasks manually
python backend/scripts/manage_jobs.py trigger-mfl
python backend/scripts/manage_jobs.py trigger-projections
python backend/scripts/manage_jobs.py trigger-deadlines

# Manage specific tasks
python backend/scripts/manage_jobs.py status <task-id>
python backend/scripts/manage_jobs.py cancel <task-id>
python backend/scripts/manage_jobs.py retry <task-id>

# Maintenance
python backend/scripts/manage_jobs.py purge
```

## Error Handling and Retry Logic

### Automatic Retries

Tasks are configured with automatic retry logic:

```python
@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 60})
def example_task(self):
    # Task implementation
    pass
```

### Dead Letter Queue

Failed tasks are moved to the `failed` queue for manual inspection:

```python
@celery_app.task(bind=True, queue='failed')
def handle_dead_letter(self, original_task_id, error, task_name, args, kwargs):
    # Log failure details
    # Potentially attempt recovery
    # Send administrator alerts
    pass
```

### Failure Handling

The system provides multiple failure handling mechanisms:

1. **Automatic Retries**: Tasks retry with exponential backoff
2. **Dead Letter Queue**: Failed tasks are preserved for analysis
3. **Error Logging**: Comprehensive error logging with context
4. **Health Monitoring**: Continuous system health checks
5. **Manual Recovery**: API endpoints for manual task management

## Monitoring and Observability

### Celery Flower

Access the Flower monitoring UI at `http://localhost:5555` when running with the monitoring profile:

```bash
docker-compose --profile monitoring up flower
```

### Health Checks

The system includes multiple health check mechanisms:

1. **Basic Health Check**: Verifies Celery worker connectivity
2. **System Diagnostics**: Tests database, Redis, and Celery components
3. **Resource Monitoring**: Tracks CPU, memory, and disk usage
4. **Queue Monitoring**: Monitors queue lengths and processing rates

### Logging

Structured logging is implemented throughout:

```python
import logging
logger = logging.getLogger(__name__)

# Task execution logging
logger.info(f"Starting task {task_name} with args {args}")
logger.error(f"Task {task_id} failed: {error}")
```

## Testing

Run the background job tests:

```bash
# Basic Celery configuration tests
python -m pytest backend/tests/test_celery_basic.py -v

# Full integration tests (requires external services)
python -m pytest backend/tests/test_background_jobs.py -v
```

## Performance Considerations

### Worker Scaling

Scale workers based on load:

```bash
# Single worker with 4 concurrent processes
celery -A app.core.celery:celery_app worker --concurrency=4

# Multiple workers
celery -A app.core.celery:celery_app worker --hostname=worker1@%h &
celery -A app.core.celery:celery_app worker --hostname=worker2@%h &
```

### Queue Management

Monitor and manage queue lengths:

```python
# Get queue lengths
monitor = JobMonitor()
queue_lengths = monitor.get_queue_lengths()

# Purge queues if needed
celery_app.control.purge()
```

### Resource Limits

Configure appropriate resource limits:

```python
# Task time limits
task_time_limit=30 * 60      # 30 minutes
task_soft_time_limit=25 * 60 # 25 minutes

# Worker limits
worker_max_tasks_per_child=1000  # Restart worker after 1000 tasks
worker_prefetch_multiplier=1     # Process one task at a time
```

## Troubleshooting

### Common Issues

1. **Redis Connection Errors**
   ```bash
   # Check Redis connectivity
   redis-cli ping
   
   # Verify Redis URL in environment
   echo $REDIS_URL
   ```

2. **Database Connection Issues**
   ```bash
   # Test database connectivity
   python -c "from app.core.database import SessionLocal; db = SessionLocal(); db.execute('SELECT 1')"
   ```

3. **Task Import Errors**
   ```bash
   # Verify task registration
   celery -A app.core.celery:celery_app inspect registered
   ```

4. **Worker Not Processing Tasks**
   ```bash
   # Check worker status
   celery -A app.core.celery:celery_app inspect active
   celery -A app.core.celery:celery_app inspect stats
   ```

### Debug Mode

Enable debug logging:

```bash
celery -A app.core.celery:celery_app worker --loglevel=debug
```

### Task Debugging

Debug specific tasks:

```python
# Run task synchronously for debugging
from app.tasks.data_refresh import refresh_mfl_data
result = refresh_mfl_data.apply()
print(result.result)
```

## Security Considerations

### Network Security
- Redis should not be exposed to public networks
- Use Redis AUTH if running in shared environments
- Consider Redis SSL/TLS for production deployments

### Task Security
- Validate all task inputs
- Sanitize file uploads before processing
- Use least-privilege database connections
- Log security-relevant events

### Resource Protection
- Set appropriate task time limits
- Monitor resource usage
- Implement rate limiting for API-triggered tasks
- Use queue-specific workers to isolate workloads