# Projections Aggregation System

The Projections Aggregation System is a core component of the AI Fantasy Assistant that combines multiple projection sources using weighted ensemble algorithms, provides backtesting frameworks for optimization, and includes caching mechanisms for improved performance.

## Overview

The system addresses the challenge of having multiple projection sources (FantasyPros, ESPN, Yahoo, NFL, etc.) with varying levels of accuracy. Instead of relying on a single source, it intelligently combines them using optimized weights based on historical performance.

## Key Features

### 1. Weighted Ensemble Aggregation
- Combines multiple projection sources using configurable weights
- Automatically normalizes weights for available sources
- Handles missing sources gracefully
- Calculates weighted averages with proper variance estimation

### 2. Backtesting Framework
- Evaluates historical accuracy of projection sources
- Uses cross-validation against ensemble averages
- Calculates multiple accuracy metrics (MAE, RMSE, custom accuracy score)
- Optimizes source weights based on performance

### 3. Confidence Interval Calculation
- Provides uncertainty quantification for aggregated projections
- Calculates confidence intervals using weighted variance
- Adjusts for effective sample size
- Ensures non-negative fantasy point projections

### 4. Caching System
- Two-level caching: source weights and aggregated projections
- Configurable TTL (Time-To-Live) for cache entries
- Selective cache invalidation by player, week, or season
- Cache statistics and monitoring

## Architecture

```
┌─────────────────────┐    ┌──────────────────────┐    ┌─────────────────────┐
│   Raw Projections   │    │  Projections         │    │  Aggregated         │
│                     │───▶│  Aggregator          │───▶│  Projections        │
│ - FantasyPros       │    │                      │    │                     │
│ - ESPN              │    │ - Weight Optimization│    │ - Weighted Average  │
│ - Yahoo             │    │ - Ensemble Calculation│    │ - Confidence Interval│
│ - NFL               │    │ - Backtesting        │    │ - Variance          │
└─────────────────────┘    └──────────────────────┘    └─────────────────────┘
                                      │
                                      ▼
                           ┌──────────────────────┐
                           │  Cache Manager       │
                           │                      │
                           │ - Projection Cache   │
                           │ - Weight Cache       │
                           │ - TTL Management     │
                           └──────────────────────┘
```

## Core Components

### ProjectionsAggregator

The main service class that handles projection aggregation:

```python
from app.services.projections_aggregator import ProjectionsAggregator

# Create aggregator
aggregator = ProjectionsAggregator(db_session)

# Aggregate projections for a player
result = aggregator.aggregate_projections(
    player_id="player_123",
    week=1,
    season=2024,
    min_sources=2
)
```

**Key Methods:**
- `aggregate_projections()` - Aggregate projections for a specific player/week
- `aggregate_all_projections()` - Aggregate for all players in a week/season
- `optimize_source_weights()` - Calculate optimal weights using backtesting
- `run_backtesting()` - Evaluate source accuracy
- `get_source_statistics()` - Get statistics for a projection source

### ProjectionsCacheManager

Manages caching of aggregated projections:

```python
from app.services.projections_aggregator import ProjectionsCacheManager

# Create cache manager
cache_manager = ProjectionsCacheManager(db_session, aggregator)

# Get cached projection (with automatic computation if not cached)
result = cache_manager.get_cached_projection(
    player_id="player_123",
    week=1,
    season=2024
)

# Clear cache
cache_manager.clear_cache()
```

**Key Methods:**
- `get_cached_projection()` - Get projection with caching
- `invalidate_cache()` - Invalidate specific cache entries
- `clear_cache()` - Clear all cache entries
- `get_cache_stats()` - Get cache performance statistics

## API Endpoints

The system exposes RESTful API endpoints for integration:

### Get Aggregated Projection
```http
GET /api/v1/projections/aggregate/{player_id}?week=1&season=2024
```

### Get All Aggregated Projections
```http
GET /api/v1/projections/aggregate?week=1&season=2024&limit=100
```

### Get Projection Sources
```http
GET /api/v1/projections/sources?season=2024
```

### Get Source Statistics
```http
GET /api/v1/projections/sources/{source}/statistics?season=2024
```

### Run Backtesting
```http
GET /api/v1/projections/backtesting?season=2024&weeks_back=8
```

### Get Optimized Weights
```http
GET /api/v1/projections/weights?season=2024
```

### Cache Management
```http
POST /api/v1/projections/cache/refresh
DELETE /api/v1/projections/cache?player_id=player_123
GET /api/v1/projections/cache/stats
```

## Data Models

### AggregatedProjection
```python
@dataclass
class AggregatedProjection:
    player_id: str
    week: Optional[int]
    season: int
    projected_points: Decimal
    confidence_interval: Tuple[Decimal, Decimal]
    variance: Decimal
    source_count: int
    source_weights: Dict[str, Decimal]
    metadata: Dict[str, Any]
```

### BacktestResult
```python
@dataclass
class BacktestResult:
    source: str
    mae: float  # Mean Absolute Error
    rmse: float  # Root Mean Square Error
    accuracy_score: float  # Custom accuracy metric
    sample_size: int
    optimal_weight: float
```

## Configuration

### Weight Optimization
The system automatically optimizes source weights based on backtesting results. You can configure:

- **Backtesting period**: Number of weeks to look back (default: 8)
- **Minimum samples**: Minimum projections required per source (default: 10)
- **Cache duration**: How long to cache optimized weights (default: 6 hours)

### Caching
Cache behavior can be configured:

- **Projection TTL**: How long to cache aggregated projections (default: 2 hours)
- **Weight cache TTL**: How long to cache optimized weights (default: 6 hours)
- **Cache invalidation**: Automatic or manual cache clearing

## Usage Examples

### Basic Aggregation
```python
# Get aggregated projection for a player
aggregator = ProjectionsAggregator(db)
result = aggregator.aggregate_projections("player_123", week=1, season=2024)

if result:
    print(f"Projected points: {result.projected_points}")
    print(f"Confidence interval: {result.confidence_interval}")
    print(f"Source weights: {result.source_weights}")
```

### Backtesting and Optimization
```python
# Run backtesting to evaluate sources
backtest_results = aggregator.run_backtesting(season=2024)
for result in backtest_results:
    print(f"{result.source}: MAE={result.mae:.2f}, Accuracy={result.accuracy_score:.3f}")

# Get optimized weights
weights = aggregator.optimize_source_weights(season=2024)
print(f"Optimized weights: {weights}")
```

### Caching
```python
# Use caching for better performance
cache_manager = ProjectionsCacheManager(db, aggregator)

# First call - computed and cached
result1 = cache_manager.get_cached_projection("player_123", week=1, season=2024)

# Second call - retrieved from cache (faster)
result2 = cache_manager.get_cached_projection("player_123", week=1, season=2024)

# Check cache statistics
stats = cache_manager.get_cache_stats()
print(f"Cache entries: {stats['total_entries']}")
```

## Performance Considerations

### Optimization Strategies
1. **Caching**: Use the cache manager for frequently accessed projections
2. **Batch processing**: Use `aggregate_all_projections()` for bulk operations
3. **Source filtering**: Set appropriate `min_sources` to balance quality vs availability
4. **Background processing**: Run backtesting and weight optimization as background jobs

### Scalability
- Database queries are optimized with proper indexing
- Caching reduces computational overhead
- Backtesting can be run periodically rather than on-demand
- Weight optimization results are cached to avoid repeated calculations

## Testing

The system includes comprehensive unit tests:

```bash
# Run projections aggregator tests
python -m pytest tests/test_projections_aggregator.py -v

# Run API tests
python -m pytest tests/test_projections_api.py -v
```

### Test Coverage
- Weighted ensemble calculation
- Confidence interval computation
- Backtesting algorithms
- Cache management
- API endpoints
- Error handling

## Integration

### With Other Systems
The projections aggregation system integrates with:

- **Data Ingestion**: Consumes projections from MFL API and file uploads
- **Rules Engine**: Applies league-specific scoring rules to projections
- **Decision Engines**: Provides aggregated projections for keeper, draft, and lineup optimization
- **Alert System**: Triggers notifications when projections change significantly

### Database Schema
The system uses the existing `projections` table with additional fields:
- `source_weight`: Weight assigned to the projection source
- `confidence_level`: Confidence level of the projection
- `variance`: Variance estimate for uncertainty quantification

## Monitoring and Maintenance

### Health Checks
- Monitor cache hit ratios
- Track backtesting performance
- Alert on source availability issues
- Monitor aggregation success rates

### Maintenance Tasks
- Periodic cache clearing
- Backtesting result archival
- Source weight history tracking
- Performance metric collection

## Future Enhancements

### Planned Features
1. **Machine Learning Integration**: Use ML models for weight optimization
2. **Real-time Updates**: Stream processing for live projection updates
3. **Advanced Uncertainty**: Bayesian methods for better confidence intervals
4. **Source Quality Metrics**: More sophisticated accuracy measurements
5. **A/B Testing**: Framework for testing different aggregation strategies

### Extensibility
The system is designed to be extensible:
- New aggregation algorithms can be added
- Additional backtesting metrics can be implemented
- Custom caching strategies can be plugged in
- New data sources can be easily integrated