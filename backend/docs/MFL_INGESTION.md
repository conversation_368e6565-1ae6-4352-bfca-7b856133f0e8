# MFL Data Ingestion Service

The MFL Data Ingestion Service provides comprehensive integration with MyFantasyLeague (MFL) API to import league data, player information, rosters, and configurations into the AI Fantasy Assistant system.

## Overview

The service consists of several key components:

- **MFLIngestionService**: Main service class that orchestrates the complete ETL process
- **MFLDataNormalizer**: Handles data transformation from MFL format to canonical schema
- **DataProvenance**: Tracks data lineage and source attribution
- **ETL Pipeline**: Command-line script for batch processing and scheduled updates

## Features

### Data Ingestion
- **League Configuration**: Scoring rules, roster slots, keeper rules
- **Player Data**: Names, positions, teams, injury status, bye weeks
- **Franchise Information**: Team names, owners, salary caps, FAAB budgets
- **Roster Management**: Current rosters, starting lineups, keeper designations

### Data Normalization
- Converts MFL-specific data formats to canonical schema
- Maps MFL positions to standardized position enums
- Normalizes injury status codes
- Handles scoring rule variations (standard, PPR, half-PPR)

### Provenance Tracking
- Records data source and ingestion timestamp for all imported data
- Maintains audit trail for data lineage
- Supports data validation and integrity checks

### Error Handling
- Graceful handling of API failures with retry logic
- Detailed error reporting and logging
- Partial ingestion support (continues on non-critical errors)

## Usage

### Python API

```python
from app.services.mfl_ingestion import create_mfl_ingestion_service

# Create service instance
service = create_mfl_ingestion_service()

# Ingest league data
result = service.ingest_league_data(
    league_id="12345",
    api_key="your_mfl_api_key"
)

# Validate ingested data
validation = service.validate_ingested_data("mfl_12345")
```

### Command Line Interface

```bash
# Ingest single league
python backend/scripts/mfl_etl.py --league-id 12345 --api-key YOUR_API_KEY

# Ingest multiple leagues from config file
python backend/scripts/mfl_etl.py --config backend/config/mfl_leagues.json

# Create sample configuration
python backend/scripts/mfl_etl.py --create-config my_leagues.json

# Skip validation
python backend/scripts/mfl_etl.py --league-id 12345 --api-key YOUR_API_KEY --no-validate
```

### REST API Endpoints

```bash
# Ingest league data
POST /api/v1/mfl/ingest
{
  "league_id": "12345",
  "api_key": "your_api_key",
  "validate": true
}

# Validate existing data
POST /api/v1/mfl/validate
{
  "league_id": "mfl_12345"
}

# Get league status
GET /api/v1/mfl/leagues/12345/status

# List all leagues
GET /api/v1/mfl/leagues

# Refresh league data
POST /api/v1/mfl/leagues/12345/refresh?api_key=YOUR_KEY&validate=true
```

## Configuration

### League Configuration File

```json
{
  "leagues": [
    {
      "league_id": "12345",
      "api_key": "your_mfl_api_key_here",
      "validate": true
    }
  ],
  "delay_between_leagues": 5,
  "settings": {
    "log_level": "INFO",
    "backup_before_ingestion": false,
    "max_retries": 3,
    "timeout_seconds": 30
  }
}
```

### Environment Variables

```bash
# Optional: Default MFL API settings
MFL_DEFAULT_API_KEY=your_default_api_key
MFL_API_TIMEOUT=30
MFL_MAX_RETRIES=3
```

## Data Schema Mapping

### League Data
- **MFL** → **Canonical**
- `id` → `mfl_league_id`
- `name` → `name`
- `season` → `season`
- `scoring` → `scoring_rules` (normalized)
- `rosterSize` → `roster_slots` (normalized)
- `keeperRules` → `keeper_rules` (normalized)

### Player Data
- **MFL** → **Canonical**
- `id` → `mfl_id`
- `name` → `name`
- `position` → `position` (enum mapped)
- `team` → `team`
- `injury_status` → `injury_status` (enum mapped)
- `bye_week` → `bye_week`

### Franchise Data
- **MFL** → **Canonical**
- `id` → `mfl_franchise_id`
- `name` → `name`
- `owner_name` → `owner_name`
- `salary_cap` → `salary_cap`
- `faab_budget` → `faab_budget`

## Position Mapping

| MFL Position | Canonical Position |
|--------------|-------------------|
| QB           | QB                |
| RB           | RB                |
| WR           | WR                |
| TE           | TE                |
| K            | K                 |
| Def          | DEF               |
| DL           | DL                |
| LB           | LB                |
| DB           | DB                |

## Injury Status Mapping

| MFL Code | Canonical Status |
|----------|------------------|
| ""       | HEALTHY          |
| Q        | QUESTIONABLE     |
| D        | DOUBTFUL         |
| O        | OUT              |
| IR       | IR               |
| PUP      | PUP              |
| SUSP     | SUSPENDED        |

## Validation Checks

The service performs comprehensive validation of ingested data:

### League Validation
- League configuration exists and is complete
- Scoring rules contain required fields
- Roster slots are properly defined

### Player Validation
- All players have valid positions and teams
- No duplicate player records
- Injury status codes are valid

### Roster Validation
- All franchises have associated rosters
- Roster players reference valid player records
- No orphaned roster assignments
- Keeper designations are consistent

### Data Integrity
- Foreign key relationships are maintained
- No circular references
- Provenance tracking is complete

## Error Handling

### Common Errors

1. **MFL API Authentication Failure**
   - Check API key validity
   - Verify league access permissions

2. **Network Timeouts**
   - Increase timeout settings
   - Check network connectivity

3. **Data Format Changes**
   - Update normalization mappings
   - Check MFL API documentation

4. **Database Constraints**
   - Verify database schema is up to date
   - Check for conflicting data

### Logging

The service provides detailed logging at multiple levels:

- **INFO**: Normal operation progress
- **WARNING**: Non-critical issues (e.g., missing optional data)
- **ERROR**: Critical failures that prevent ingestion
- **DEBUG**: Detailed operation traces

Logs are written to both console and file (`mfl_etl.log`).

## Performance Considerations

### Batch Processing
- Process multiple leagues with configurable delays
- Support for partial ingestion on failures
- Background job processing for large datasets

### Database Optimization
- Bulk insert operations where possible
- Proper indexing on frequently queried fields
- Connection pooling for concurrent operations

### API Rate Limiting
- Respect MFL API rate limits
- Configurable delays between API calls
- Exponential backoff on failures

## Testing

### Unit Tests
```bash
python -m pytest backend/tests/test_mfl_ingestion.py -v
```

### Integration Tests
The test suite includes comprehensive integration tests with mock MFL data:

- Data normalization accuracy
- Error handling scenarios
- Validation logic
- API endpoint functionality

### Sandbox Testing
Use the provided sandbox data for development and testing:

```python
from backend.tests.test_mfl_ingestion import MOCK_MFL_SANDBOX_DATA
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python path configuration

2. **Database Connection Issues**
   - Verify database is running and accessible
   - Check connection string in environment variables

3. **MFL API Issues**
   - Verify API key and league access
   - Check MFL service status
   - Review API rate limiting

4. **Data Validation Failures**
   - Review validation error messages
   - Check for data inconsistencies in MFL
   - Verify schema compatibility

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
python backend/scripts/mfl_etl.py --league-id 12345 --api-key YOUR_KEY --log-level DEBUG
```

## Future Enhancements

- **Incremental Updates**: Only sync changed data since last ingestion
- **Real-time Webhooks**: Support for MFL webhook notifications
- **Multi-season Support**: Historical data ingestion and management
- **Advanced Validation**: Machine learning-based data quality checks
- **Performance Monitoring**: Metrics and alerting for ingestion health

## Dependencies

- **pymfl**: MFL API wrapper library
- **SQLAlchemy**: Database ORM
- **FastAPI**: REST API framework
- **Pydantic**: Data validation and serialization
- **pytest**: Testing framework

## Support

For issues or questions regarding the MFL ingestion service:

1. Check the logs for detailed error messages
2. Review the validation results for data quality issues
3. Consult the MFL API documentation for format changes
4. Run the test suite to verify system integrity