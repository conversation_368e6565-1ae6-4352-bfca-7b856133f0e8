#!/usr/bin/env bash
set -euo pipefail

# Start the MFL app locally without Docker: backend (FastAPI, SQLite) + frontend (Next.js)
# - Uses ./.venv for backend Python deps
# - Uses ./frontend/node_modules for frontend deps
# - Reads secrets from ./.env (backend loads it automatically)
# - NEXT_PUBLIC_API_URL is set for the frontend (default http://localhost:8000)

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

# Ensure log and pid directories use absolute paths so redirections work after cd frontend
LOG_DIR_DEFAULT="${SCRIPT_DIR}/.logs"
PID_DIR_DEFAULT="${SCRIPT_DIR}/.pids"
LOG_DIR="${LOG_DIR:-$LOG_DIR_DEFAULT}"
PID_DIR="${PID_DIR:-$PID_DIR_DEFAULT}"
# If user supplied relative paths, make them absolute relative to SCRIPT_DIR
case "$LOG_DIR" in
  /*) ;;
  *) LOG_DIR="${SCRIPT_DIR}/$LOG_DIR" ;;
esac
case "$PID_DIR" in
  /*) ;;
  *) PID_DIR="${SCRIPT_DIR}/$PID_DIR" ;;
esac
mkdir -p "$LOG_DIR" "$PID_DIR"

say() { echo "[LOCAL] $*" | tee -a "$LOG_DIR/start-local.log"; }

# Pick Python (prefer 3.11)
PY_BIN="python3.11"
if ! command -v "$PY_BIN" >/dev/null 2>&1; then
  PY_BIN="python3"
fi

# Ensure virtualenv
if [ ! -d ".venv" ]; then
  say "Creating Python virtualenv (.venv)"
  "$PY_BIN" -m venv .venv
fi

# Activate venv
# shellcheck source=/dev/null
source .venv/bin/activate

# Install backend deps if needed
say "Installing backend dependencies (backend/requirements.txt)"
pip install --upgrade pip >/dev/null 2>&1 || true
pip install -r backend/requirements.txt | tee -a "$LOG_DIR/start-local.log"

# Ensure frontend deps
if [ ! -d "frontend/node_modules" ]; then
  say "Installing frontend dependencies (frontend/)"
  (cd frontend && npm ci | tee -a "$LOG_DIR/start-local.log")
fi

# Determine backend port and NEXT_PUBLIC_API_URL (frontend)
BACKEND_PORT="${BACKEND_PORT:-8000}"
port_in_use() {
  command -v lsof >/dev/null 2>&1 && lsof -i TCP:"$1" -sTCP:LISTEN -t >/dev/null 2>&1
}
if port_in_use "$BACKEND_PORT"; then
  BACKEND_PORT=8001
fi

NEXT_API_DEFAULT="http://localhost:${BACKEND_PORT}"
NEXT_API="$NEXT_API_DEFAULT"
if [ -f .env ]; then
  # Pull NEXT_PUBLIC_API_URL if present; keep secret values unprinted
  env_line=$(grep -E '^NEXT_PUBLIC_API_URL=' .env || true)
  if [ -n "$env_line" ]; then
    NEXT_API="${env_line#NEXT_PUBLIC_API_URL=}"
  fi
fi
say "Using NEXT_PUBLIC_API_URL=$NEXT_API (backend port ${BACKEND_PORT})"

# Ensure Python can import the backend as top-level 'app' when running locally
export PYTHONPATH="${SCRIPT_DIR}/backend:${PYTHONPATH:-}"

# Choose frontend port (prefer 3000, fall back to 3001 if busy)
FRONTEND_PORT="${FRONTEND_PORT:-3000}"
if port_in_use "$FRONTEND_PORT"; then
  FRONTEND_PORT=3001
fi

# Initialize SQLite tables if needed (in development, backend falls back to sqlite:///./fantasy.db)
# Only if file missing
if [ ! -f "fantasy.db" ]; then
  say "Initializing local SQLite database (fantasy.db)"
  "$PY_BIN" backend/scripts/init_db.py --tables-only | tee -a "$LOG_DIR/start-local.log" || true
fi

# Start backend (FastAPI) on 127.0.0.1:${BACKEND_PORT}
say "Starting backend on 127.0.0.1:${BACKEND_PORT} (minimal mode for quick health)"
(
  set -e
  # .env is read by pydantic settings; no need to export secrets here
  MINIMAL_START=1 uvicorn backend.app.main:app --host 127.0.0.1 --port "${BACKEND_PORT}" --loop asyncio --http h11 \
    >"$LOG_DIR/backend.log" 2>&1 & echo $! >"$PID_DIR/backend.pid"
) || { say "Failed to start backend"; exit 1; }

# Start frontend (Next.js) on $FRONTEND_PORT
say "Starting frontend on 127.0.0.1:${FRONTEND_PORT}"
(
  set -e
  cd frontend
  PORT="$FRONTEND_PORT" NEXT_PUBLIC_API_URL="$NEXT_API" npm run dev \
    >"$LOG_DIR/frontend.log" 2>&1 & echo $! >"$PID_DIR/frontend.pid"
) || { say "Failed to start frontend"; exit 1; }

# Wait for backend health
say "Waiting for backend health..."
for i in {1..60}; do
  if curl -fsS "http://127.0.0.1:${BACKEND_PORT}/health" >/dev/null 2>&1; then
    say "Backend is healthy"
    break
  fi
  sleep 1
  if [ "$i" -eq 60 ]; then
    say "Backend did not become healthy in time. Check $LOG_DIR/backend.log"
  fi

done
# Wait for frontend to respond
say "Waiting for frontend..."
for i in {1..60}; do
  if curl -fsS "http://127.0.0.1:${FRONTEND_PORT}" >/dev/null 2>&1; then
    say "Frontend is responding"
    break
  fi
  sleep 1
  if [ "$i" -eq 60 ]; then
    say "Frontend did not become ready in time. Check $LOG_DIR/frontend.log"
  fi

done

# Open browser
open "http://localhost:${FRONTEND_PORT}" || true
osascript -e "display notification \"Local app started (backend:${BACKEND_PORT}, frontend:${FRONTEND_PORT}). Logs in ./.logs\" with title \"MFL-ai\"" || true
say "Done. Use 'Stop Local App.command' to stop processes."
