# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/

# Database
*.db
*.sqlite3

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Ignore other env variants but keep examples tracked
.env.*
frontend/.env.*
!.env.example
!frontend/.env.local.example

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Allow committing frontend/src/lib changes (override ignore if present)
!frontend/src/lib/

# Next.js
.next/
out/

# TypeScript build info
*.tsbuildinfo

# Production builds
/build

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Docker
.dockerignore

# Logs
logs/
*.log

# Coverage reports
htmlcov/
.coverage
.pytest_cache/

# Temporary files
*.tmp
*.temp

# Local Python virtualenv and pid dir
.venv/
.pids/
